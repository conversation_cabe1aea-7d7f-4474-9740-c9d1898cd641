package com.dawnpro.dfpv.carfilemanager.module.business.share.dao.impl;

import java.io.Serializable;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceProcdureLogDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceProcdureLog;

public class InterfaceProcdureLogDAOImpl extends GenericHibernateDAOImpl<InterfaceProcdureLog> implements InterfaceProcdureLogDAO {

	public void addInterfaceProcdureLog(InterfaceProcdureLog prodlog) {
		// TODO Auto-generated method stub

	}

	public void updateInterfaceProcdureLog(InterfaceProcdureLog prodlog) {
		// TODO Auto-generated method stub

	}

	public void deleteInterfaceProcdureLog(Serializable id) {
		// TODO Auto-generated method stub

	}

	public InterfaceProcdureLog loadInterfaceProcdureLog(Serializable id) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<InterfaceProcdureLog> findInterfaceProcdureLog(String sql, Object[] params) {
		// TODO Auto-generated method stub
		return null;
	}

}
