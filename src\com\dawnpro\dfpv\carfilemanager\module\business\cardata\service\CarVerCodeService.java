package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Modelver;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProeHBBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Proenvironment;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProenvironmentId;

public interface CarVerCodeService {

	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
	public Modelver loadMODELVER(String model,String factory) throws DataAccessException;
	
	public CarInfo loadModelObj(String vin)throws DataAccessException;

	public void updateModel(CarInfo obj)throws DataAccessException;
	public String loadPcode(String model);

}
