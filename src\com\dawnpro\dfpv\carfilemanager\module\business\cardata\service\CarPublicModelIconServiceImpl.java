package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarPublicModelIconDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarPublicModelIcon;

public class CarPublicModelIconServiceImpl implements CarPublicModelIconService {
	private PaginationService paginationService = null;
	private CarPublicModelIconDAO carPublicModelIconDAO = null;
	private PublicNoticeCarModelService publicNoticeCarModelService = null;
	
	
	public CarPublicModelIconDAO getCarPublicModelIconDAO() {
		return carPublicModelIconDAO;
	}

	public void setCarPublicModelIconDAO(CarPublicModelIconDAO carPublicModelIconDAO) {
		this.carPublicModelIconDAO = carPublicModelIconDAO;
	}

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public PublicNoticeCarModelService getPublicNoticeCarModelService() {
		return publicNoticeCarModelService;
	}

	public void setPublicNoticeCarModelService(
			PublicNoticeCarModelService publicNoticeCarModelService) {
		this.publicNoticeCarModelService = publicNoticeCarModelService;
	}

	public void addCarPublicModelIcon(CarPublicModelIcon obj)
			throws DataAccessException {
		try{
			carPublicModelIconDAO.addCarPublicModelIcon(obj);
		}catch(DataAccessException e){
			throw new SystemException("addCarPublicModelIcon Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCarPublicModelIcon Method Error:",e);
		}
	}

	public void deleteCarPublicModelIcon(Serializable id)
			throws DataAccessException {
		try{
			carPublicModelIconDAO.deleteCarPublicModelIcon(id);
		}catch(DataAccessException e){
			throw new SystemException("deleteCarPublicModelIcon Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCarPublicModelIcon Method Error:",e);
		}
	}

	public void deleteCarPublicModelIcon(Serializable[] id)
			throws DataAccessException {
		try{
			carPublicModelIconDAO.deleteCarPublicModelIcon(id);
		}catch(DataAccessException e){
			throw new SystemException("deleteCarPublicModelIcon Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCarPublicModelIcon Method Error:",e);
		}
	}

	public Boolean existPublicModel(String pmodel) throws DataAccessException {	
		CarPublicModel pm = publicNoticeCarModelService.findCarModel(pmodel);
		if(pm != null){
			return true;
		}
		return false;
	}

	public CarPublicModelIcon loadCarPublicModelIcon(Serializable id)
			throws DataAccessException {
		CarPublicModelIcon pmi = null;
		try{
			pmi = this.carPublicModelIconDAO.loadCarPublicModelIcon(id);
		}catch(DataAccessException e){
			throw new SystemException("loadCarPublicModelIcon Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarPublicModelIcon Method Error:",e);
		}
		return pmi;
	}

	public List<?> pagination(Page page, Map<String, String> params)
			throws DataAccessException {
		List results = null;
		try{

			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.pmodel) from CarPublicModelIcon g";
				sql = "from CarPublicModelIcon g order by  g.pmodel ";
			}else{
				String pmodel = params.get("pmodel");
				String printIcon = params.get("printIcon");
				
				if(pmodel!=null&&!pmodel.equals("")){
					sub = " and g.pmodel like '%" + pmodel + "%' ";
				}
				if(printIcon!=null&&!printIcon.equals("")){
					sub = sub+" and g.printicon ='" + printIcon + "' ";	
				}
				
				countSql = "select count(g.pmodel) from CarPublicModelIcon g where 1=1 "+sub;
				sql = "from CarPublicModelIcon g where 1=1 "+sub+" order by g.pmodel ";
			}

			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		
		
		return results;
	}

	public void updateCarPublicModelIcon(CarPublicModelIcon obj)
			throws DataAccessException {
		try{
			carPublicModelIconDAO.updateCarPublicModelIcon(obj);
		}catch(DataAccessException e){
			throw new SystemException("updateCarPublicModelIcon Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCarPublicModelIcon Method Error:",e);
		}
	}

}
