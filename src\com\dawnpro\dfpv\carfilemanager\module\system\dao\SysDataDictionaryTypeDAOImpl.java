package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import org.hibernate.HibernateException;
import org.springframework.dao.DataAccessResourceFailureException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;

public class SysDataDictionaryTypeDAOImpl extends GenericHibernateDAOImpl<DataDictionaryType> implements SysDataDictionaryTypeDAO{

	public void addDataDictionaryType(DataDictionaryType dictionaryType)
			throws DataAccessException {
		try{
			this.add(dictionaryType);
		}catch (DataAccessResourceFailureException e) {
			throw new DataAccessException("addDataDictionaryType Error:",e);
		} catch (HibernateException e) {
			throw new DataAccessException("addDataDictionaryType Error:",e);
		} catch (IllegalStateException e) {
			throw new DataAccessException("addDataDictionaryType Error:",e);
		} catch(DataAccessException e){
			throw new DataAccessException("addDataDictionaryType Error:",e);
		}
	}

	public void deleteDataDictionaryType(DataDictionaryType[] dictionaryType)
			throws DataAccessException {
		try{
			this.deleteBatch(dictionaryType);
		}catch(Exception e){
			throw new DataAccessException("deleteDataDictionaryType Method Error:",e);
		}
	}

	public List<DataDictionaryType> findAllDataDictionaryType()
			throws DataAccessException {
		try{
			List<DataDictionaryType> result = this.find("from DataDictionaryType g");
			return result;
		}catch(Exception e){
			throw new DataAccessException("findAllDataDictionaryType Method Error:",e);
		}
	}

	public List<DataDictionaryType> findDataDictionaryType(String sql,
			String[] params) throws DataAccessException {
		try{
			return this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findDataDictionaryType Method Error:",e);
		}
	}

	public List<DataDictionaryType> findDataDictionaryType(String sql)
			throws DataAccessException {
		try{
			return this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findDataDictionaryType Method Error:",e);
		}
	}

	public DataDictionaryType loadDataDictionaryTypeObj(String name)
			throws DataAccessException {
		DataDictionaryType gg = null;
		try{
			List<DataDictionaryType> result = find("from DataDictionaryType g where g.name=?",new String[]{name});
			if(result!=null && result.size()>0)
				gg=result.get(0);
		}catch(Exception e){
			throw new DataAccessException("loadDataDictionaryTypeObj Method Error:",e);
		}
		return gg;
	}

	public void updateDataDictionaryType(DataDictionaryType dictionaryType)
			throws DataAccessException {
		try{
			this.update(dictionaryType);
		}catch(Exception e){
			throw new DataAccessException("updateDataDictionaryType Method Error:",e);
		}
	}

	public void deleteDataDictionaryType(DataDictionaryType dictionaryType)
			throws DataAccessException {
		try{
			this.delete(dictionaryType);
		}catch(Exception e){
			throw new DataAccessException("deleteDataDictionaryType Method Error:",e);
		}
	}

}
