package com.dawnpro.dfpv.carfilemanager.common.tools;

import java.text.DecimalFormat;
import java.util.regex.Pattern;

public class Test {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		try{
			
			//String pat = "[-+]{0,1}\\d+\\.\\d*|[-+]{0,1}\\d*\\.\\d+";
			
			//boolean isDouble = Pattern.compile("^-?([1-9]\\d*(\\.\\d*)?|0\\.\\d*[1-9]\\d*|0?\\.0+|0)$").matcher("").find();
			
			//System.out.println(isDouble);
			
			
			
			System.out.println(Double.parseDouble("12.5"));
			System.out.println(Long.toString(Math.round(Double.parseDouble("12.5"))));
			
			
			DecimalFormat decimalFormat0 = new DecimalFormat("###");
			System.out.println(decimalFormat0.format(Double.parseDouble("12")));
			/*
			COCCertificateBeanDataSource dataSource = new COCCertificateBeanDataSource();
			COCReportBean coc = new COCReportBean();
			dataSource.setCocBean(coc);
//			JasperPrint jpt =  JasperFillManager.fillReport(this.getClass().getResourceAsStream("COC.jasper"),null,dataSource);
			//isSuccess = JasperPrintManager.printReport(jpt, false); 
			System.out.println("1111111111111111111111");
			File reportFile = new File("e:/COC.jasper");
			byte[] bytes=JasperRunManager.runReportToPdf(reportFile.getPath(),null,dataSource);
			BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream("e:/coc.pdf"));
			
				stream.write(bytes);
			
			stream.flush();
			
			stream.close();
			*/
		}catch(Exception e){
			e.printStackTrace();
		}
		
	}

}
