<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhoto" table="COC_PHOTO" lazy="false">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhotoId">
            <key-property name="model" type="java.lang.String">
                <column name="MODEL" length="50" />
            </key-property>
            <key-property name="filename" type="java.lang.String">
                <column name="FILENAME" length="20" not-null="true" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
            	<column name="VERCODE" length="15" />
        	</key-property>
        </composite-id>
        <property name="path" type="java.lang.String">
            <column name="PATH" length="100" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
         <property name="effecttime" type="java.lang.String">
            <column name="EFFECT_TIME" length="19" />
        </property>
    </class>
</hibernate-mapping>
