package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FuelUploadBean;

public class XshzhUploadBeanMapper implements RowMapper<List<FuelUploadBean>> {

	public List<FuelUploadBean> mapRow(ResultSet rs) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<FuelUploadBean> mapRow(Collection rs) {
		List<FuelUploadBean> list = new ArrayList<FuelUploadBean>();
		int point = 0;
		for(Object obj : rs){
			point = 0;
			Object[] objs = (Object[])obj;
			FuelUploadBean bean = new FuelUploadBean();
			bean.setVin(objs[point] == null ? "" : (objs[point]+"").trim());
			bean.setCxxh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFdjxh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFdjscc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setProddate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEngineno(objs[++point] == null ? "" : (objs[point]+"").trim());
			list.add(bean);
		}
		return list;
	}

}
