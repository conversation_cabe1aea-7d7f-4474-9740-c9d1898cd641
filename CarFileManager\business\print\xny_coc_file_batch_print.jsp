<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/jquery/jquery-cookie.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#print").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("print")!=-1){
					$("#print").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });

	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 250,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
			$('#public_notice_dialog').find('#vin').attr('readonly',false);
		}
		});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 420,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'关闭': function() {
				$(this).dialog('close');

				var startDate = $('#startDate').val();
				var endDate = $('#endDate').val();
				var z12StartDate = $('#z12StartDate').val();
				var z12EndDate = $('#z12EndDate').val();
				var printmodel = $('#printmodel').val();
				var obj = $('#startDate');
				if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					return ;
				}
				obj = $('#endDate');
				if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					return ;
				}
				obj = $('#z12StartDate');
				if(z12StartDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					return ;
				}
				obj = $('#z12EndDate');
				if(z12EndDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					return ;
				}

				location.href="certificateManager!cocQuery.action?printPoint=XNY&startDate="+startDate+"&endDate="+endDate+"&z12StartDate="+z12StartDate+"&z12EndDate="+z12EndDate+"&printmodel="+printmodel+"&menuid="+menuid;
				
			},
			'打印': function() {
				var dlgButton = $('.ui-dialog-buttonpane').find('button:last');//	
				dlgButton.attr('disabled', 'disabled');
		       	dlgButton.addClass('ui-state-disabled');
		        
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				if(type=="print"){		
					var coc_printer = $.cookie("cocprinter");
					var photo_printer = $.cookie("photoprinter");
					var printmodel = $('#printmodel').val();
					var messageObj = $('#message_dialog');
		   	   	
					if(coc_printer==null){
						$("#print_info_div").hide();
						messageObj.find('#message').text('警告:请设置COC证书打印机！');
			   	   		messageObj.dialog('open');
			   	   		
						return ;
					}
					
					if(photo_printer==null){  
						$("#print_info_div").hide();
						messageObj.find('#message').text('警告:请设置车型照片打印机！');
			   	   		messageObj.dialog('open');
			   	   		
						return ;
					}
					
					window.document.cocBatch.printCOCBatch(formObj.find("#vin")[0].value,printmodel); 
				}
			}
		}
	});

	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#print").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				if(id==""){
					id = this.value;
					info = "vin:"+this.value;
				}else{
					id = id+"@"+this.value; 
					info = info+"&"+"vin:"+this.value;
				}
             }
      	});
		
   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要打印的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "print";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定打印['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#vin').val(id);
   	   	}
	
	});
	
	$("#query").click(function(){
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		var z12StartDate = $('#z12StartDate').val();
		var z12EndDate = $('#z12EndDate').val();
		var printmodel = $('#printmodel').val();
		if(startDate==""&&endDate==""&&z12StartDate==""&&z12EndDate==""&&printmodel==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入查询条件！');
			messageObj.dialog("open");
		}else{
			if(printmodel==""){
				var messageObj = $('#message_dialog');
				messageObj.find('#message').text('警告:请选择打印方式！');
				messageObj.dialog("open");
				
				return;
			}
			
			var obj = $('#startDate');
			if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#endDate');
			if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#z12StartDate');
			if(z12StartDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#z12EndDate');
			if(z12EndDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			location.href="certificateManager!cocQuery.action?printPoint=XNY&startDate="+startDate+"&endDate="+endDate+"&z12StartDate="+z12StartDate+"&z12EndDate="+z12EndDate+"&printmodel="+printmodel+"&menuid="+menuid;
		}
	});
	
	$("#export").click(function(){
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		var z12StartDate = $('#z12StartDate').val();
		var z12EndDate = $('#z12EndDate').val();
		var printmodel = $('#printmodel').val();
		if(startDate==""&&endDate==""&&z12StartDate==""&&z12EndDate==""&&printmodel==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入查询条件！');
			messageObj.dialog("open");
		}else{
			if(printmodel==""){
				var messageObj = $('#message_dialog');
				messageObj.find('#message').text('警告:请选择打印方式！');
				messageObj.dialog("open");
				
				return;
			}
			
			var obj = $('#startDate');
			if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#endDate');
			if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#z12StartDate');
			if(z12StartDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#z12EndDate');
			if(z12EndDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			location.href="business/certificateManager!export.action?printPoint=XNY&startDate="+startDate+"&endDate="+endDate+"&z12StartDate="+z12StartDate+"&z12EndDate="+z12EndDate+"&printmodel="+printmodel;
//			location.href="certificateManager!export.action?startDate="+startDate+"&endDate="+endDate+"&z12StartDate="+z12StartDate+"&z12EndDate="+z12EndDate+"&printmodel="+printmodel;

		}
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
	        url: 'business/carInfo!carModelInfo.action',		           
	        data: 'vin='+id, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#public_notice_display_dialog');
	        	
				setDialogValue(dialogObj,carObj);
								
	       	   	dialogObj.dialog('open');
	        }
	    });

		return false;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#vin').val(jsonObj.vin);
		dialogObj.find('#engineNo').val(jsonObj.engineNo);
		dialogObj.find('#model').val(jsonObj.model);
		dialogObj.find('#engineType').val(jsonObj.engineType);
		dialogObj.find('#color').val(jsonObj.color);
		dialogObj.find('#prodDate').val(jsonObj.prodDate);
		dialogObj.find('#cocNum').val(jsonObj.cocNum);
		dialogObj.find('#cocColor').val(jsonObj.cocColor);
		dialogObj.find('#remark').val(jsonObj.remark);
		dialogObj.find('#state').val(jsonObj.state);
		dialogObj.find('#gvercode').val(jsonObj.gvercode);
		dialogObj.find('#cvercode').val(jsonObj.cvercode);
		dialogObj.find('#zvercode').val(jsonObj.zvercode);
	}
	
	function clear(dialogObj){
		dialogObj.find('#vin').val("");
		dialogObj.find('#engineNo').val("");
		dialogObj.find('#model').val("");
		dialogObj.find('#engineType').val("");
		dialogObj.find('#color').val("");
		dialogObj.find('#prodDate').val("");
		dialogObj.find('#cocNum').val("");
		dialogObj.find('#cocColor').val("");
		dialogObj.find('#remark').val("");
		dialogObj.find('#state').val("");
		dialogObj.find('#gvercode').val("");
		dialogObj.find('#cvercode').val("");
		dialogObj.find('#zvercode').val("");

		dialogObj.find('#vin').attr('readonly',false);
		dialogObj.find('#engineNo').attr('readonly',false);
		dialogObj.find('#model').attr('readonly',false);
		dialogObj.find('#engineType').attr('readonly',false);
		dialogObj.find('#color').attr('readonly',false);
		dialogObj.find('#prodDate').attr('readonly',false);
		dialogObj.find('#cocNum').attr('readonly',false);
		dialogObj.find('#cocColor').attr('readonly',false);
		dialogObj.find('#gvercode').attr('readonly',false);
		dialogObj.find('#cvercode').attr('readonly',false);
		dialogObj.find('#zvercode').attr('readonly',false);
		
		type = null;
	}

	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var startDate = $('#startDate').val();
			var endDate = $('#endDate').val();
			var z12StartDate = $('#z12StartDate').val();
			var z12EndDate = $('#z12EndDate').val();
			var printmodel = $('#printmodel').val();
			
			location.href="certificateManager!cocQuery.action?printPoint=XNY&currentPage="+$('#jump').val()+"&z12StartDate="+z12StartDate+"&z12EndDate="+z12EndDate+"&startDate="+startDate+"&endDate="+endDate+"&printmodel="+printmodel+"&menuid="+menuid;   
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var startDate = $('#startDate').val();
				var endDate = $('#endDate').val();
				var z12StartDate = $('#z12StartDate').val();
				var z12EndDate = $('#z12EndDate').val();
				var printmodel = $('#printmodel').val();
				location.href=$(this).attr('value')+"&printPoint=XNY&z12StartDate="+z12StartDate+"&z12EndDate="+z12EndDate+"&startDate="+startDate+"&endDate="+endDate+"&printmodel="+printmodel+"&menuid="+menuid;
		 });
	  });

	$("#allCheck").click(function() {
		var checkedObj = $('#users-contain').find("[name='checkPK']"); 
		checkedObj.each(function(){
			if($(this).attr("checked")==false){
				$(this).attr("checked",true);
			}else{
				$(this).attr("checked",false);
			}
		});
	});
    
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#startDate").datepicker($.datepicker.regional['zh']);
	$("#endDate").datepicker($.datepicker.regional['zh']);

	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#z12StartDate").datepicker($.datepicker.regional['zh']);
	$("#z12EndDate").datepicker($.datepicker.regional['zh']);
    
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

           //给这行添加class值为over，并且当鼠标一出该行时执行函数

           $(this).removeClass("over");})    //移除该行的class
});

function displayMessage(message){
	var messageObj = $('#operate_dialog');
  	messageObj.find('#message').text(message);
}

function readPrinterCookie(){
	var cookie = new Array(2);
	cookie[0] = $.cookie("cocprinter");
	cookie[1] = $.cookie("photoprinter");
	
	return cookie;
}
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
  <input type="hidden" name="printPoint" value="XNY"></input>
			<tr>
				<td align="left" colspan="5"><p>生产日期:<input type="text" id="startDate" class="text ui-widget-content " size="10" <s:if test="#request.startDate!=null"> value="<s:property value="#request.startDate" />" </s:if>/>-<input type="text" id="endDate" class="text ui-widget-content"  size="10" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />" </s:if>/>
				&nbsp;&nbsp;&nbsp;Z12过点日期<input type="text" id="z12StartDate" class="text ui-widget-content " size="10" <s:if test="#request.z12StartDate!=null"> value="<s:property value="#request.z12StartDate" />" </s:if>/>-<input type="text" id="z12EndDate" class="text ui-widget-content"  size="10" <s:if test="#request.z12EndDate!=null"> value="<s:property value="#request.z12EndDate" />" </s:if>/>
				&nbsp;&nbsp;&nbsp;打印方式:<s:select name="printmodel" list="#request.printmodelMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.printmodel"></s:select> 
				</td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			
			<tr><td width="80%"></td>
			  <td width="7%" align="right"></td>
			  <td width="7%" align="right"></td>
			  <td width="60" align="right"></td>
  			  <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			   <td width="60" align="right"><button id="print" class="ui-button ui-state-default ui-corner-all">打印</button></td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="6%"><input type='checkbox' id='allCheck' name='allCheck'>全选</th> 
			    <th width="14%">VIN</th>
			    <th width="12%">COC流水号</th>
			    <th width="8%">生产车型</th>
				<th width="6%">颜色</th>
				<th width="8%">发动机号</th>
				<th width="14%">发动机类型</th>
				<th width="8%">生产日期</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="cocNum" /></td>
			  		<td><s:property value="model" /></td>	
			  		<td><s:property value="color" /></td>
			  		<td><s:property value="engineNo" /></td>	
			  		<td><s:property value="engineType" /></td>	
			  		<td><s:property value="prodDate" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="vin" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="certificateManager!cocQuery.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="certificateManager!cocQuery.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="certificateManager!cocQuery.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="certificateManager!cocQuery.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="public_notice_display_dialog" title="查看窗口">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>VIN编码</label></td>
				<td><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>发动机号</label></td>
				<td><input type="text" id="engineNo" name="engineNo" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>实例车型</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>发动机型号</label></td>
				<td><input type="text" id="engineType" name="engineType" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
			</tr>
			<tr>	
				<td><label><P>颜色</label></td>
				<td><input type="text" id="color" name="color" class="text ui-widget-content ui-corner-all" size="15" readonly="true"/></td>
				<td><label><P>生产日期</label></td>
				<td><input type="text" id="prodDate" name="prodDate" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC编码</label></td>
				<td><input type="text" id="cocNum" name="cocNum" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC颜色</label></td>
				<td>
				<input type="text" id="cocColor" name="cocColor" class="text ui-widget-content ui-corner-all" size="15" readonly="true" />
				</td>
			</tr>
			<tr>
				<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="gvercode" name="gvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC版本号</label></td>
				<td><input type="text" id="cvercode" name="cvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>召回版本号</label></td>
				<td><input type="text" id="zvercode" name="zvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				
			</tr>
		</Table>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="打印窗口">
	<form id="operateForm" method='post'>
	<p id="message" style="word-wrap: break-word;"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='vin' name='vin'>
	</form>
</div>
<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div align="center"> 
	<jsp:plugin name="cocBatch" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet.class" codebase="." archive = "../applet/com.dawnpro.dfpv.carfilemanager.print.applet.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/Qrcode_encoder.jar" 
	iepluginurl="http://10.5.7.2/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="0" width="0">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="tmp"/>
			<jsp:param name="printPoint" value="XNY"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
</body>
</html>