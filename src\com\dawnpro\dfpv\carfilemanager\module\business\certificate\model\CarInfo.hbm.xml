<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo" table="CAR_INFO" lazy="false" dynamic-insert="true" dynamic-update="true">
        <id name="vin" type="java.lang.String">
            <column name="VIN" length="20" />
            <generator class="assigned" />
        </id>
        <property name="engineNo" type="java.lang.String">
            <column name="ENGINE_NO" length="20" />
        </property>
        <property name="model" type="java.lang.String">
            <column name="MODEL" length="50" />
        </property>
        <property name="engineType" type="java.lang.String">
            <column name="ENGINE_TYPE" length="30" />
        </property>
        <property name="color" type="java.lang.String">
            <column name="COLOR" length="50" />
        </property>
        <property name="prodDate" type="java.lang.String">
            <column name="PROD_DATE" length="10" />
        </property>
        <property name="cocNum" type="java.lang.String">
            <column name="COC_NUM" length="20" />
        </property>
        <property name="cocColor" type="java.lang.String">
            <column name="COC_COLOR" length="10" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="gvercode" type="java.lang.String">
            <column name="GVERCODE" length="15" />
        </property>
        <property name="cvercode" type="java.lang.String">
            <column name="CVERCODE" length="15" />
        </property>
        <property name="evercode" type="java.lang.String">
            <column name="EVERCODE" length="15" />
        </property>
        <property name="gasman" type="java.lang.String">
            <column name="GASMAN" length="10" />
        </property>
        <property name="cocman" type="java.lang.String">
            <column name="COCMAN" length="10" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="cocPrintTime" type="java.lang.String">
            <column name="COC_PRINT_TIME" length="19" />
        </property>
        <property name="zvercode" type="java.lang.String">
            <column name="ZVERCODE" length="15" />
        </property>
        <property name="date1" type="java.lang.String">
            <column name="date1" length="19" />
        </property>
        <property name="time1" type="java.lang.String">
            <column name="time1" length="6" />
        </property>
        <property name="pvercode" type="java.lang.String">
            <column name="PVERCODE" length="15" />
        </property>
         <property name="materialNo" type="java.lang.String">
            <column name="MATERIALNO" length="15" />
        </property>
        
         <property name="delreason" type="java.lang.String">
            <column name="DELREASON" length="20" />
        </property>
        
         <property name="modreason" type="java.lang.String">
            <column name="MODREASON" length="20" />
        </property>
        
         <property name="dlareason" type="java.lang.String">
            <column name="DLAREASON" length="20" />
        </property>
        
         <property name="fuelupload" type="java.lang.String">
            <column name="FUELUPLOAD" length="2" />
        </property>
        
         <property name="fueldel" type="java.lang.String">
            <column name="FUELDEL" length="2" />
        </property>
        
         <property name="adtfuel" type="java.util.Date">
            <column name="ADT_FUEL" />
        </property>
        
         <property name="fuelmodified" type="java.lang.String">
            <column name="FUELMODIFIED" length="2" />
        </property>
        
         <property name="fueldelay" type="java.lang.String">
            <column name="FUELDELAY" length="2" />
        </property>
        
         <property name="fuelresponse" type="java.lang.String">
            <column name="FUELRESPONSE" length="100" />
        </property>
        
         <property name="factory" type="java.lang.String">
            <column name="FACTORY" length="10" />
        </property>
        
        <property name="xshzhupload" type="java.lang.String">
            <column name="XSHZHUPLOAD" length="1" />
        </property>
        
        <property name="adtxshzh" type="java.util.Date">
            <column name="ADT_XSHZH" />
        </property>
         
        <property name="cocupload" type="java.lang.String">
            <column name="COCUPLOAD" length="1" />
        </property>
        
        <property name="cocresponse" type="java.lang.String">
            <column name="COCRESPONSE" length="200" />
        </property>
        
        <property name="adtcoc" type="java.util.Date">
            <column name="ADT_COC" />
        </property>
         
        <property name="fueluploadcount" type="java.lang.Long">
            <column name="FUELUPLOADCOUNT" />
        </property>         
        <property name="cocuploadcount" type="java.lang.Long">
            <column name="COCUPLOADCOUNT" />
        </property>    
        
        <!-- 2018 12 17 -->     
         <property name="motorNo" type="java.lang.String">
            <column name="MOTOR_NO" length="20" />
        </property>
        
         <property name="motorType" type="java.lang.String">
            <column name="MOTOR_TYPE" length="20" />
        </property>       
    </class>
</hibernate-mapping>
