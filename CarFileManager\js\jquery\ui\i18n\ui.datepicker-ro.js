/* Romanian initialisation for the jQuery UI date picker plugin.
 *
 * Written by <PERSON> (<EMAIL>)
 * and <PERSON><PERSON> (<EMAIL>)
 */
jQuery(function($){
	$.datepicker.regional['ro'] = {
		closeText: 'Închide',
		prevText: '&laquo; Luna precedentă',
		nextText: '<PERSON> următoare &raquo;',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','August','Septembrie','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>embrie'],
		monthNamesShort: ['<PERSON>', 'Feb', '<PERSON>', 'Apr', '<PERSON>', 'Iun',
		'Iul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Du<PERSON>', 'Lu<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','Ma','Mi','Jo','Vi','Sâ'],
		dateFormat: 'dd MM yy', firstDay: 1,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['ro']);
});
