package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelScjcUploadBean;

public class ScjcPrintBeanMapper implements RowMapper<FuelScjcUploadBean> {

	public FuelScjcUploadBean mapRow(ResultSet rs) {
		List<FuelScjcUploadBean> list = new ArrayList<FuelScjcUploadBean>();
		FuelScjcUploadBean bean = new FuelScjcUploadBean();
		try {
		
			bean.setPmodelcode(rs.getString("pmodelcode"));
			bean.setVin(rs.getString("vin"));
			
			bean.setXxgkhao(rs.getString("xxgkhao"));
			bean.setHbsb(rs.getString("hbsb"));
			bean.setScgdz(rs.getString("scgdz"));
			bean.setProddate(rs.getString("proddate"));
			bean.setEngineno(rs.getString("engineno"));
			bean.setFdjcp(rs.getString("fdjcp"));
			bean.setFdjscdz(rs.getString("fdjscdz"));
			bean.setClzzname(rs.getString("clzzname"));
			bean.setRh(rs.getString("rh"));
			bean.setEt(rs.getString("et"));
			bean.setAp(rs.getString("ap"));
			bean.setTesttype(rs.getString("testtype"));
			bean.setTestNo(rs.getString("testno"));
			bean.setTestDate(rs.getString("testdate"));
			bean.setApass(rs.getString("apass"));
			bean.setOpass(rs.getString("opass"));
			bean.setOtestdate(rs.getString("otestdate"));
			bean.setEpass(rs.getString("epass"));
			bean.setCtest(rs.getString("ctest"));
			bean.setCtestlocation(rs.getString("ctestlocation"));
//			bean.setFinalresult(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setObd(rs.getString("obd"));
			bean.setOdo(rs.getString("Odo"));
			bean.setEcmmoduleid(rs.getString("ecmmoduleid"));
			bean.setEcmcalid(rs.getString("ecmcalid"));
			bean.setEcmcvn(rs.getString("ecmcvn"));
			bean.setTcmmoduleid(rs.getString("tcmmoduleid"));
			bean.setTcmcalid(rs.getString("tcmcalid"));
			bean.setTcmcvn(rs.getString("tcmcvn"));
			bean.setEcm2moduleid(rs.getString("ecm2moduleid"));
			bean.setEcm2calid(rs.getString("ecm2calid"));
			bean.setEcm2cvn(rs.getString("ecm2cvn"));
			bean.setDmcmmoduleid(rs.getString("dmcmmoduleid"));
			bean.setDmcmcalid(rs.getString("dmcmcalid"));
			bean.setDmcmcvn(rs.getString("dmcmcvn"));
			bean.setScrmoduleid(rs.getString("scrmoduleid"));
			bean.setScrcalid(rs.getString("scrcalid"));
			bean.setScrcvn(rs.getString("scrcvn"));
			bean.setHvbecmmoduleid(rs.getString("hvbecmmoduleid"));
			bean.setHvbecmcalid(rs.getString("hvbecmcalid"));
			bean.setHvbecmcvn(rs.getString("hvbecmcvn"));
			bean.setBcmmoduleid(rs.getString("bcmmoduleid"));
			bean.setBcmcalid(rs.getString("bcmcalid"));
			bean.setBcmcvn(rs.getString("bcmcvn"));
			bean.setOthmoduleid(rs.getString("othmoduleid"));
			bean.setOthcalid(rs.getString("othcalid"));
			bean.setOthcvn(rs.getString("othcvn"));
			bean.setVrhc(rs.getString("vrhc"));
			bean.setVlhc(rs.getString("vlhc"));
			bean.setVrco(rs.getString("vrco"));
			bean.setVlco(rs.getString("vlco"));
			bean.setVrnox(rs.getString("vrnox"));
			bean.setVlnox(rs.getString("vlnox"));
			bean.setAnalyManuf(rs.getString("analymanuf"));
			bean.setAnalyName(rs.getString("analyname"));
			bean.setAnalyModel(rs.getString("analymodel"));
			bean.setAnalyDate(rs.getString("analydate"));
			bean.setDynoModel(rs.getString("dynomodel"));
			bean.setDynoManuf(rs.getString("dynomanuf"));
			bean.setJcuploadflg(rs.getString("jcuploadflg"));
			bean.setAdtjc(rs.getString("adtjc"));
			
			bean.setSecvrhc(rs.getString("secvrhc"));
			bean.setSecvlhc(rs.getString("secvlhc"));
			bean.setSecvrco(rs.getString("secvrco"));
			bean.setSecvlco(rs.getString("secvlco"));
			bean.setSecvrnox(rs.getString("secvrnox"));
			bean.setSecvlnox(rs.getString("secvlnox"));
			
			bean.setPfbz(rs.getString("pfbz"));
			bean.setBsqxs(rs.getString("bsqxs"));
			bean.setChzhq(rs.getString("chzhq"));
			bean.setJzzl(rs.getString("jzzl"));
			bean.setZdzzl(rs.getString("zdzzl"));
			bean.setFdjxh(rs.getString("fdjxh"));
			bean.setFdjgc(rs.getString("fdjgc"));
			bean.setFdjpl(rs.getString("fdjpl"));
			bean.setQgs(rs.getString("qgs"));
			bean.setRygjfs(rs.getString("rygjfs"));
			bean.setDdjxh(rs.getString("ddjxh"));	
			bean.setCnzl(rs.getString("cnzl"));
			bean.setDcrl(rs.getString("dcrl"));
			bean.setObdplcae(rs.getString("obdplcae"));
			bean.setChecker(rs.getString("checker"));
			bean.setOcommunchk(rs.getString("ocommunchk"));
			
			
		} catch (SQLException e) {
			throw new DataAccessException("mapRow Method SQLException Error:",e);
		}
		return bean;
	}

	public FuelScjcUploadBean mapRow(Collection rs) {

		return null;
	}

}
