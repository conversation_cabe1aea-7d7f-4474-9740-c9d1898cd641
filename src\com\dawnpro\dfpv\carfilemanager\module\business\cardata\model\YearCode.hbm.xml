<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.YearCode" table="T_COC_YEAR_CODE" lazy="false">
       
       <id name="id" type="java.lang.String">
            <column name="ID" length="50" />
            <generator class="assigned" />
        </id>
        
        <property name="year" type="java.lang.String">
            <column name="YEAR" length="20">
            </column>
        </property>
        
        <property name="code" type="java.lang.String">
            <column name="CODE" length="10">
            </column>
        </property>
          
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20">
                <comment>创建人</comment>
            </column>
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7">
                <comment>时间</comment>
            </column>
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
    </class>
</hibernate-mapping>
