package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

/**
 * Bv02 generated by MyEclipse Persistence Tools
 */

public class Bv02 implements java.io.Serializable {

	// Fields

	private String id;

	private String ggcx;

	private String vercode;

	private String slcx;

	private String senddate;

	private String sendtime;

	// Constructors

	/** default constructor */
	public Bv02() {
	}

	/** minimal constructor */
	public Bv02(String id, String ggcx, String vercode, String slcx) {
		this.id = id;
		this.ggcx = ggcx;
		this.vercode = vercode;
		this.slcx = slcx;
	}

	/** full constructor */
	public Bv02(String id, String ggcx, String vercode, String slcx,
			String senddate, String sendtime) {
		this.id = id;
		this.ggcx = ggcx;
		this.vercode = vercode;
		this.slcx = slcx;
		this.senddate = senddate;
		this.sendtime = sendtime;
	}

	// Property accessors

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getGgcx() {
		return this.ggcx;
	}

	public void setGgcx(String ggcx) {
		this.ggcx = ggcx;
	}

	public String getVercode() {
		return this.vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

	public String getSlcx() {
		return this.slcx;
	}

	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}

	public String getSenddate() {
		return this.senddate;
	}

	public void setSenddate(String senddate) {
		this.senddate = senddate;
	}

	public String getSendtime() {
		return this.sendtime;
	}

	public void setSendtime(String sendtime) {
		this.sendtime = sendtime;
	}

}