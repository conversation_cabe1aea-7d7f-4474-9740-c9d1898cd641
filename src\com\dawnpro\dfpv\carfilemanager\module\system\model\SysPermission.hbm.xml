<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.SysPermission" table="SYS_PERMISSION" >
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.system.model.SysPermissionId">
            <key-property name="roleid" type="java.lang.String">
                <column name="ROLEID" length="36" />
            </key-property>
            <key-property name="menuid" type="java.lang.String">
                <column name="MENUID" length="36" />
            </key-property>
        </composite-id>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
    </class>
    
</hibernate-mapping>
