package com.dawnpro.dfpv.carfilemanager.module.business.share.model;
// Generated 2018-9-18 13:11:19 by Hibernate Tools 3.6.0.Final

import java.util.Date;

/**
 * InterfaceDefine generated by hbm2java
 */
public class InterfaceDefine implements java.io.Serializable {

	private String id;
	private String jkno;
	private String jkname;
	private String irun;
	private String mark;
	private String jktype;
	private String jkfltype;
	private Date datadate;
	private String userid;

	public InterfaceDefine() {
	}

	public InterfaceDefine(String id) {
		this.id = id;
	}

	public InterfaceDefine(String id, String jkno, String jkname, String irun, String mark, String jktype,
			String jkfltype, Date datadate, String userid) {
		this.id = id;
		this.jkno = jkno;
		this.jkname = jkname;
		this.irun = irun;
		this.mark = mark;
		this.jktype = jktype;
		this.jkfltype = jkfltype;
		this.datadate = datadate;
		this.userid = userid;
	}

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getJkno() {
		return this.jkno;
	}

	public void setJkno(String jkno) {
		this.jkno = jkno;
	}

	public String getJkname() {
		return this.jkname;
	}

	public void setJkname(String jkname) {
		this.jkname = jkname;
	}

	public String getIrun() {
		return this.irun;
	}

	public void setIrun(String irun) {
		this.irun = irun;
	}

	public String getMark() {
		return this.mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

	public String getJktype() {
		return this.jktype;
	}

	public void setJktype(String jktype) {
		this.jktype = jktype;
	}

	public String getJkfltype() {
		return this.jkfltype;
	}

	public void setJkfltype(String jkfltype) {
		this.jkfltype = jkfltype;
	}

	public Date getDatadate() {
		return this.datadate;
	}

	public void setDatadate(Date datadate) {
		this.datadate = datadate;
	}

	public String getUserid() {
		return this.userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

}
