<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Proenvironment" table="PROENVIRONMENT" >
          <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProenvironmentId">
            <key-property name="slcx" type="java.lang.String">
                <column name="SLCX" length="20" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="15" />
            </key-property>
        </composite-id>
        <property name="pzextno" type="java.lang.String">
            <column name="PZEXTNO" length="18">
                <comment>配置扩展号</comment>
            </column>
        </property>
        <property name="jybgno" type="java.lang.String">
            <column name="JYBGNO" length="14">
                <comment>检验报告编号</comment>
            </column>
        </property>
        <property name="jyjgmc" type="java.lang.String">
            <column name="JYJGMC" length="100">
                <comment>检验机构名称</comment>
            </column>
        </property>
        <property name="ccjyjg" type="java.lang.String">
            <column name="CCJYJG" length="100">
                <comment>出厂检验结果</comment>
            </column>
        </property>
        <property name="ccjyjg2" type="java.lang.String">
            <column name="CCJYJG2" length="100">
                <comment>出厂检验结果2</comment>
            </column>
        </property>
        <property name="fdjxh" type="java.lang.String">
            <column name="FDJXH" length="100">
                <comment>发动机型号</comment>
            </column>
        </property>
        <property name="fdjgc" type="java.lang.String">
            <column name="FDJGC" length="100">
                <comment>发动机生产厂</comment>
            </column>
        </property>
        <property name="chzhq" type="java.lang.String">
            <column name="CHZHQ" length="100">
                <comment>催化转换器</comment>
            </column>
        </property>
        <property name="chzhqgc" type="java.lang.String">
            <column name="CHZHQGC" length="100">
                <comment>催化转换器生产厂</comment>
            </column>
        </property>
        <property name="ryzfkzqxh" type="java.lang.String">
            <column name="RYZFKZQXH" length="100">
                <comment>燃油装置控制型号</comment>
            </column>
        </property>
        <property name="ryzfkzqgc" type="java.lang.String">
            <column name="RYZFKZQGC" length="100">
                <comment>燃油装置控制生产厂</comment>
            </column>
        </property>
        <property name="egrxh" type="java.lang.String">
            <column name="EGRXH" length="100">
                <comment>EGR型号</comment>
            </column>
        </property>
        <property name="egrgc" type="java.lang.String">
            <column name="EGRGC" length="100">
                <comment>EGR生产厂</comment>
            </column>
        </property>
        <property name="obdxh" type="java.lang.String">
            <column name="OBDXH" length="100">
                <comment>OBD型号</comment>
            </column>
        </property>
        <property name="obdgc" type="java.lang.String">
            <column name="OBDGC" length="100">
                <comment>OBD生产厂</comment>
            </column>
        </property>
        <property name="iuprnox" type="java.lang.String">
            <column name="IUPRNOX" length="100">
                <comment>IUPRX检测功能</comment>
            </column>
        </property>
        <property name="ecuxh" type="java.lang.String">
            <column name="ECUXH" length="100">
                <comment>ECU型号</comment>
            </column>
        </property>
        <property name="ecugc" type="java.lang.String">
            <column name="ECUGC" length="100">
                <comment>ECU生产厂</comment>
            </column>
        </property>
        <property name="bsqxs" type="java.lang.String">
            <column name="BSQXS" length="100">
                <comment>变速器型式</comment>
            </column>
        </property>
        <property name="bsqdws" type="java.lang.String">
            <column name="BSQDWS" length="100">
                <comment>变速器当位数</comment>
            </column>
        </property>
        <property name="xsqxh" type="java.lang.String">
            <column name="XSQXH" length="100">
                <comment>消声器型号</comment>
            </column>
        </property>
        <property name="xsqgc" type="java.lang.String">
            <column name="XSQGC" length="100">
                <comment>消声器生产厂</comment>
            </column>
        </property>
        <property name="zyqxh" type="java.lang.String">
            <column name="ZYQXH" length="100">
                <comment>增压器型号</comment>
            </column>
        </property>
        <property name="zyqgc" type="java.lang.String">
            <column name="ZYQGC" length="100">
                <comment>增压器生产厂</comment>
            </column>
        </property>
        <property name="zlqxs" type="java.lang.String">
            <column name="ZLQXS" length="100">
                <comment>中冷器型式</comment>
            </column>
        </property>
        <property name="fdjbs" type="java.lang.String">
            <column name="FDJBS" length="100">
                <comment>发动机</comment>
            </column>
        </property>
        <property name="fdjpath" type="java.lang.String">
            <column name="FDJPATH" length="100">
                <comment>发动机图片路径</comment>
            </column>
        </property>
        <property name="chzhqbs" type="java.lang.String">
            <column name="CHZHQBS" length="100">
                <comment>发动机</comment>
            </column>
        </property>
        <property name="chzhqpath" type="java.lang.String">
            <column name="CHZHQPATH" length="100">
                <comment>发动机图片路径</comment>
            </column>
        </property>
        <property name="ryzfkzqbs" type="java.lang.String">
            <column name="RYZFKZQBS" length="100">
                <comment>燃油装置控制</comment>
            </column>
        </property>
        <property name="ryzfkzqpath" type="java.lang.String">
            <column name="RYZFKZQPATH" length="100">
                <comment>燃油装置控制图片路径</comment>
            </column>
        </property>
        <property name="ycgqbs" type="java.lang.String">
            <column name="YCGQBS" length="100">
                <comment>氧传感器</comment>
            </column>
        </property>
        <property name="ycgqpath" type="java.lang.String">
            <column name="YCGQPATH" length="100">
                <comment>氧传感器图片路径</comment>
            </column>
        </property>
        <property name="egrbs" type="java.lang.String">
            <column name="EGRBS" length="100">
                <comment>EGR</comment>
            </column>
        </property>
        <property name="egrpath" type="java.lang.String">
            <column name="EGRPATH" length="100">
                <comment>EGR图片路径</comment>
            </column>
        </property>
        <property name="ecubs" type="java.lang.String">
            <column name="ECUBS" length="100">
                <comment>ECU</comment>
            </column>
        </property>
        <property name="ecupath" type="java.lang.String">
            <column name="ECUPATH" length="100">
                <comment>ECU图片路径</comment>
            </column>
        </property>
        <property name="xsqbs" type="java.lang.String">
            <column name="XSQBS" length="100">
                <comment>消声器</comment>
            </column>
        </property>
        <property name="xsqpath" type="java.lang.String">
            <column name="XSQPATH" length="100">
                <comment>消声器图片路径</comment>
            </column>
        </property>
        <property name="zyqbs" type="java.lang.String">
            <column name="ZYQBS" length="100">
                <comment>增压器</comment>
            </column>
        </property>
        <property name="zyqpath" type="java.lang.String">
            <column name="ZYQPATH" length="100">
                <comment>增压器图片路径</comment>
            </column>
        </property>
        <property name="mark" type="java.lang.String">
            <column name="MARK" length="100">
                <comment>备注</comment>
            </column>
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20">
                <comment>创建人</comment>
            </column>
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="2">
                <comment>状态 0：维护 1：生效 9：历史</comment>
            </column>
        </property>
        <property name="datadate" type="java.util.Date">
            <column name="DATADATE" length="7">
                <comment>创建时间</comment>
            </column>
        </property>
        <property name="ggcx" type="java.lang.String">
            <column name="GGCX" length="20">
                <comment>公告车型</comment>
            </column>
        </property>
        <property name="effectTime" type="java.lang.String">
            <column name="EFFECT_TIME" length="19">
                <comment>生效印时间</comment>
            </column>
        </property>
        <property name="fdjbsname" type="java.lang.String">
            <column name="FDJBSNAME" length="100" />
        </property>
        <property name="chzhqbsname" type="java.lang.String">
            <column name="CHZHQBSNAME" length="50" />
        </property>
        <property name="ryzfkzqbsname" type="java.lang.String">
            <column name="RYZFKZQBSNAME" length="50" />
        </property>
        <property name="ycgqbsname" type="java.lang.String">
            <column name="YCGQBSNAME" length="50" />
        </property>
        <property name="egrbsname" type="java.lang.String">
            <column name="EGRBSNAME" length="50" />
        </property>
        <property name="ecubsname" type="java.lang.String">
            <column name="ECUBSNAME" length="50" />
        </property>
        <property name="xsqbsname" type="java.lang.String">
            <column name="XSQBSNAME" length="50" />
        </property>
        <property name="zyqbsname" type="java.lang.String">
            <column name="ZYQBSNAME" length="50" />
        </property>
        <property name="ycgqxh" type="java.lang.String">
            <column name="YCGQXH" length="100">
                <comment>氧传感器型号</comment>
            </column>
        </property>
        <property name="ycgqgc" type="java.lang.String">
            <column name="YCGQGC" length="100">
                <comment>氧传感器生产厂</comment>
            </column>
        </property>
        <property name="qzspfxh" type="java.lang.String">
            <column name="QZSPFXH" length="100">
                <comment>曲轴箱排放控制装置型号</comment>
            </column>
        </property>
        <property name="qzspfgc" type="java.lang.String">
            <column name="QZSPFGC" length="100">
                <comment>曲轴箱排放控制装置生产厂</comment>
            </column>
        </property>
        <property name="jyjg1" type="java.lang.String">
            <column name="JYJG1" length="100">
                <comment>检验机构1</comment>
            </column>
        </property>
        <property name="jyjg2" type="java.lang.String">
            <column name="JYJG2" length="100">
                <comment>检验机构2</comment>
            </column>
        </property>
        <property name="jyjg3" type="java.lang.String">
            <column name="JYJG3" length="100">
                <comment>检验机构3</comment>
            </column>
        </property>
        <property name="jyjg4" type="java.lang.String">
            <column name="JYJG4" length="100">
                <comment>检验机构4</comment>
            </column>
        </property>
        <property name="jcjr1" type="java.lang.String">
            <column name="JCJR1" length="100">
                <comment>检测结论1</comment>
            </column>
        </property>
        <property name="jcjr2" type="java.lang.String">
            <column name="JCJR2" length="100">
                <comment>检测结论2</comment>
            </column>
        </property>
        <property name="jcjr3" type="java.lang.String">
            <column name="JCJR3" length="100">
                <comment>检测结论3</comment>
            </column>
        </property>
        <property name="jcjr4" type="java.lang.String">
            <column name="JCJR4" length="100">
                <comment>检测结论4</comment>
            </column>
        </property>
        <property name="hbtc" type="java.lang.String">
            <column name="HBTC" length="100">
                <comment>涂层</comment>
            </column>
        </property>
        <property name="hbzt" type="java.lang.String">
            <column name="HBZT" length="100">
                <comment>载体</comment>
            </column>
        </property>
        <property name="hbfzscc" type="java.lang.String">
            <column name="HBFZSCC" length="100">
                <comment>封装生产厂</comment>
            </column>
        </property>
        <property name="frdb" type="java.lang.String">
            <column name="FRDB" length="100">
                <comment>法人代表</comment>
            </column>
        </property>
        <property name="dz" type="java.lang.String">
            <column name="DZ" length="100">
                <comment>地址</comment>
            </column>
        </property>
        <property name="tel" type="java.lang.String">
            <column name="TEL" length="100">
                <comment>联系电话</comment>
            </column>
        </property>
        <property name="hbdws" type="java.lang.String">
            <column name="HBDWS" length="100">
                <comment>变速器型式档位数</comment>
            </column>
        </property>
        <property name="ecubb" type="java.lang.String">
            <column name="ECUBB" length="100">
                <comment>ECU版本号</comment>
            </column>
        </property>
        <property name="rqhhq" type="java.lang.String">
            <column name="RQHHQ" length="100">
                <comment>燃气混合器型号</comment>
            </column>
        </property>
        <property name="rqhhqgc" type="java.lang.String">
            <column name="RQHHQGC" length="100">
                <comment>燃气混合器生产厂</comment>
            </column>
        </property>
        <property name="rqpsdy" type="java.lang.String">
            <column name="RQPSDY" length="100">
                <comment>燃气喷射单元型号</comment>
            </column>
        </property>
        <property name="rqpsdygc" type="java.lang.String">
            <column name="RQPSDYGC" length="100">
                <comment>燃气喷射单元生产厂</comment>
            </column>
        </property>
        <property name="cnzl" type="java.lang.String">
            <column name="CNZL" length="100">
                <comment>储能装置型号</comment>
            </column>
        </property>
        <property name="cnzlgc" type="java.lang.String">
            <column name="CNZLGC" length="100">
                <comment>储能装置生产厂</comment>
            </column>
        </property>
        <property name="dcrl" type="java.lang.String">
            <column name="DCRL" length="100">
                <comment>电池容量</comment>
            </column>
        </property>
        <property name="dhlc" type="java.lang.String">
            <column name="DHLC" length="100">
                <comment>续航里程</comment>
            </column>
        </property>
        <property name="ddjxh" type="java.lang.String">
            <column name="DDJXH" length="100">
                <comment>电动机型号</comment>
            </column>
        </property>
        <property name="ddjgc" type="java.lang.String">
            <column name="DDJGC" length="100">
                <comment>电动机生产厂</comment>
            </column>
        </property>
        <property name="ddjecu" type="java.lang.String">
            <column name="DDJECU" length="100">
                <comment>电动机ECU型号</comment>
            </column>
        </property>
        <property name="ddjecubbh" type="java.lang.String">
            <column name="DDJECUBBH" length="100">
                <comment>电动机ECU版本号</comment>
            </column>
        </property>
        <property name="ddjecuscc" type="java.lang.String">
            <column name="DDJECUSCC" length="100">
                <comment>电动机ECU生产厂</comment>
            </column>
        </property>
        <property name="rllx" type="java.lang.String">
            <column name="RLLX" length="100">
                <comment>环保类型</comment>
            </column>
        </property>
        <property name="xxgkhao" type="java.lang.String">
            <column name="XXGKHAO" length="100">
                <comment>信息公开编号</comment>
            </column>
        </property>
        <property name="clxh" type="java.lang.String">
            <column name="CLXH" length="100">
                <comment>车辆型号</comment>
            </column>
        </property>
        <property name="hbsb" type="java.lang.String">
            <column name="HBSB" length="100">
                <comment>商 标</comment>
            </column>
        </property>
        <property name="qcfl" type="java.lang.String">
            <column name="QCFL" length="100">
                <comment>汽车分类</comment>
            </column>
        </property>
        <property name="cxsb" type="java.lang.String">
            <column name="CXSB" length="100"> 
                <comment>车型的识别方法和位置</comment>
            </column>
        </property>
        <property name="clzzname" type="java.lang.String">
            <column name="CLZZNAME" length="100">
                <comment>车辆制造商名称</comment>
            </column>
        </property>
        <property name="scgdz" type="java.lang.String">
            <column name="SCGDZ" length="100">
                <comment>生产厂地址</comment>
            </column>
        </property>
        <property name="pfjd" type="java.lang.String">
            <column name="PFJD" length="100">
                <comment>排放阶段</comment>
            </column>
        </property>
        <property name="dbz" type="java.lang.String">
            <column name="DBZ" length="500">
                <comment>声明信息</comment>
            </column>
        </property>
         <property name="jcjr5" type="java.lang.String">
            <column name="JCJR5" length="100">
                <comment>检测结论5</comment>
            </column>
        </property>
        <property name="jyjg5" type="java.lang.String">
            <column name="JYJG5" length="100">
                <comment>检测机构5</comment>
            </column>
        </property>
        <property name="fdjbh" type="java.lang.String">
            <column name="FDJBH" length="100">
                <comment>发动机编号</comment>
            </column>
        </property>
        <property name="jzzl" type="java.lang.String">
            <column name="JZZL" length="100">
                <comment>基准质量</comment>
            </column>
        </property>
        <property name="hbtc2" type="java.lang.String">
            <column name="HBTC2" length="100">
                <comment>涂层2</comment>
            </column>
        </property>
        <property name="hbzt2" type="java.lang.String">
            <column name="HBZT2" length="100">
                <comment>载体2</comment>
            </column>
        </property>
        <property name="hbfzscc2" type="java.lang.String">
            <column name="HBFZSCC2" length="100">
                <comment>封装生产厂2</comment>
            </column>
        </property>
        <property name="klbjqxh" type="java.lang.String">
            <column name="KLBJQXH" length="100">
                <comment>颗粒捕集器型号</comment>
            </column>
        </property>
        <property name="klbjqscc" type="java.lang.String">
            <column name="KLBJQSCC" length="100">
                <comment>颗粒捕集器生产厂</comment>
            </column>
        </property>
        <property name="tgxh" type="java.lang.String">
            <column name="TGXH" length="100">
                <comment>炭罐型号</comment>
            </column>
        </property>
        <property name="tgscc" type="java.lang.String">
            <column name="TGSCC" length="100">
                <comment>炭罐生产厂</comment>
            </column>
        </property>
        <property name="pfbz" type="java.lang.String">
            <column name="PFBZ" length="100">
                <comment>排放阶段</comment>
            </column>
        </property>
        <!-- 20191021 -->
         <property name="fdjcp" type="java.lang.String">
            <column name="FDJCP" length="100">
                <comment>发动机厂牌</comment>
            </column>
        </property>  
         <property name="fdjscdz" type="java.lang.String">
            <column name="FDJSCDZ" length="100">
                <comment>发动机生产地址</comment>
            </column>
        </property>  
         <property name="rygjfs" type="java.lang.String">
            <column name="RYGJFS" length="100">
                <comment>燃油供给方式</comment>
            </column>
        </property>  
        <property name="obdplcae" type="java.lang.String">
            <column name="OBDPLCAE" length="100">
                <comment>OBD接口位置</comment>
            </column>
        </property>
        <property name="dpxh" type="java.lang.String">
            <column name="DPXH" length="100">
                <comment>底盘型号</comment>
            </column>
        </property> 
        <property name="dpscc" type="java.lang.String">
            <column name="DPSCC" length="100">
                <comment>底盘生产企业</comment>
            </column>
        </property> 
        <property name="ktzljzl" type="java.lang.String">
            <column name="KTZLJZL" length="100">
                <comment>车用空调制冷剂种类</comment>
            </column>
        </property> 
        <property name="ktzljjzl" type="java.lang.String">
            <column name="KTZLJJZL" length="100">
                <comment>车用空调制冷剂加注量</comment>
            </column>
        </property> 
        <property name="xxjyyj" type="java.lang.String">
            <column name="XXJYYJ" length="100">
                <comment>下线检验信息依据</comment>
            </column>
        </property> 
        <property name="xxjyjl" type="java.lang.String">
            <column name="XXJYJL" length="100">
                <comment>下线检验信息结论</comment>
            </column>
        </property> 
             
    </class>
</hibernate-mapping>
