package com.dawnpro.dfpv.carfilemanager.security.interceptor;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;

import com.dawnpro.dfpv.carfilemanager.base.action.Action;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.action.CertificateManagerAction;
import com.dawnpro.dfpv.carfilemanager.module.system.action.LoginAction;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysMenuDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysOperateDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysLog;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysMenu;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysOperate;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysLogService;
import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.AbstractInterceptor;

/**
 * 描述:用户登录效验拦截机
 * @author:张波
 * @since: 2008-4-25
 * **/
public class UserLoginValidateInterceptor extends AbstractInterceptor {
	private static final long serialVersionUID = -4425303266665828722L;
	private Logger logger = Logger.getLogger(UserLoginValidateInterceptor.class.getName());

	private SysLogService service=null;
	private SysMenuDAO menuDao=null;
	private SysOperateDAO operationDao=null;
	
	public void setService(SysLogService service) {
		this.service = service;
	}
	
	public void setMenuDao(SysMenuDAO menuDao) {
		this.menuDao = menuDao;
	}

	public void setOperationDao(SysOperateDAO operationDao) {
		this.operationDao = operationDao;
	}

	public String intercept(ActionInvocation invocation) {
		try{
			//logger.info("UserLoginValidateInterceptor start");
			SysUser user = (SysUser) invocation.getInvocationContext().getSession().get(Action.SYSTEM_USER);
			//logger.info("check user:"+user);
			if(invocation.getAction() instanceof LoginAction){
				//logger.info("instanceof LoginAction!!!!!!!!!!!!!!!!!!!!!!!!!!!");
				return invocation.invoke();
			}else if(invocation.getAction() instanceof CertificateManagerAction){
				//logger.info("instanceof CertificateManagerAction!!!!!!!!!!!!!!!!!!!!!");
				return invocation.invoke();
			}else if(user!=null){
				SysLog log=new SysLog();
				log.setDatadate(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));
				log.setDepdatetime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));
				
				String actionName=invocation.getProxy().getActionName();
				String action=actionName;
				List<SysMenu> allMenu=this.menuDao.findSysMenu("from SysMenu");
				for(SysMenu menu:allMenu){
					if(menu.getPath()!=null&&menu.getPath().indexOf(actionName+".action")>0){
						action=menu.getName();
					}
				}
				
				String method=invocation.getProxy().getMethod();
				String oper=null;
				List<SysOperate> allOperation=this.operationDao.allOperateList();
				for(SysOperate operation:allOperation){
					String key=operation.getFlag().substring(0,operation.getFlag().length()-1);
					if(method.indexOf(key)==0){
						oper=operation.getName();
					}
				}
				
				if(oper!=null&&oper.length()>0){
					HttpServletRequest request = ServletActionContext.getRequest (); 
					log.setDescript(action+":"+oper);
					log.setIpaddress(request.getRemoteHost());
					log.setLoginname(user.getUsername()+"("+user.getLoginname()+")");
					log.setMenuid(action);
					service.addLog(log);
				}
				//logger.info("intercept user not null!!!!!!!!!!!!!!!"+invocation.getProxy().getActionName()+" "+invocation.getProxy().getMethod()+" "+invocation.getProxy().getNamespace());
				return invocation.invoke();
			}
			
			return Action.LOGIN;
		}catch(Exception e){
			logger.error("Intercept Error:",e);
			throw new SystemException(" Intercept Error:",e);
		}
	}

}
