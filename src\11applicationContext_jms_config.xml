<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
		xmlns:context="http://www.springframework.org/schema/context" xmlns:jee="http://www.springframework.org/schema/jee"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	
	<!-- JMS JNDI CONFIG -->
	<bean id="jmsConnectionFactory" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
        	<value>java:comp/env/jms/queueConnectionFactory</value>
      	</property>
	</bean>
	
	<bean id="receiveQueueForMES" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName" value="java:comp/env/jms/MES.CFM.SERVICE.RECEIVEQ1" />
	</bean>
	
	<!--  Spring JmsTemplate config -->
    <bean id="jmsTemplate" class="org.springframework.jms.core.JmsTemplate">
        <property name="connectionFactory">
            <bean
                class="org.springframework.jms.connection.SingleConnectionFactory">
                <property name="targetConnectionFactory"
                    ref="jmsConnectionFactory"/>
            </bean>
        </property>
        <property name="receiveTimeout" value="10000"/>
    </bean>
    
    <!-- POJO which send Message uses  Spring JmsTemplate -->
 	<!-- 
 	 <bean id="messageProducer" class="com.dawnpro.dfpv.carfilemanager.business.module.share.jms.MessageRecevie">
        <property name="template" ref="jmsTemplate"/>
        <property name="receiveQueueForMES" ref="receiveQueueForMES"/>
    </bean>
    -->
   	<bean id="xmlConvertObjectFactory" class="com.dawnpro.dfpv.carfilemanager.module.business.share.factory.XMLConvertObjectFactory"/>
	
    <bean id="messageListener" class="com.dawnpro.dfpv.carfilemanager.module.business.share.jms.MessageConsumer">
    	<property name="objectFactory" ref="xmlConvertObjectFactory"/>
    	<property name="interfaceLogService" ref="interfaceLogService"/>
    </bean>
    
  	<bean id="listenerContainer"
        class="org.springframework.jms.listener.DefaultMessageListenerContainer">
        <property name="connectionFactory" ref="jmsConnectionFactory"/>
        <property name="destination" ref="receiveQueueForMES"/>
        <property name="messageListener" ref="messageListener"/>
        <!--  设置超时时间 -->
        <property name="receiveTimeout" value="10000"/>
        <!-- JMS事务控制 -->
        <!-- <property name="sessionTransacted" value="true"/> -->
    </bean>

</beans>
