<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="autodetect" xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	
	<bean id="certificateDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.dao.CertificateDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>

	<bean id="interfaceLogDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceLogDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="publicNoticeCarModelDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.PublicNoticeCarModelDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="carRecallDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarRecallDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="carInfoDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarInfoDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="carInfoVoidDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarInfoVoidDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="gasVerDao" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.GasVerDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="cocVerDao" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.CocVerDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="hwCocVerDao" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.HwCocVerDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="proenvDao" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.ProenvDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="scjcPrintDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.ScjcPrintDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="modelverDao" class="com.dawnpro.dfpv.carfilemanager.module.business.modelver.dao.impl.ModelverDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	
	<bean id="productionCarModelDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.ProductionCarModelDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	<bean id="cocPhotoDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.COCPhotoDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	<bean id="carColorDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarColorDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	
	<bean id="lqzNeutralDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.LqzNeutralDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	<bean id="gasPrinterLogDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.GASPrinterLogDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="cocModelTypeDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.COCModelTypeDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="yearCodeDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.YearCodeDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="hwCOCYearSeqDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.HWCOCYearSeqDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	
	<bean id="scjcNeutralDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.ScjcNeutralDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	<bean id="typicalityNeutralDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.TypicalityNeutralDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	<bean id="typicalityNeutralPhotoDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.TypicalityNeutralPhotoDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
		
	<bean id="certificateSupplementInfoDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CertificateSupplementInfoDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	<!-- fuelupload start-->	
	<bean id="fuelUploadDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.FuelUploadDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	<bean id="uploadUserDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.UploadUserDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	<bean id="holidayDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.HolidayDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	<bean id="filterModelDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.FilterModelDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	<bean id="carPublicModelIconDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarPublicModelIconDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>		
	<!-- fuelupload end -->	
</beans>
