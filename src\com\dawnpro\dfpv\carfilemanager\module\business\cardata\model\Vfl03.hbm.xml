<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Vfl03" table="VFL_03" schema="VFILE">
        <id name="vin" type="java.lang.String">
            <column name="VIN" length="17" />
            <generator class="assigned" />
        </id>
        <property name="rh" type="java.lang.String">
            <column name="RH" length="15">
                <comment>相对湿度</comment>
            </column>
        </property>
        <property name="et" type="java.lang.String">
            <column name="ET" length="24">
                <comment>环境温度</comment>
            </column>
        </property>
        <property name="ap" type="java.lang.String">
            <column name="AP" length="50">
                <comment>大气压力</comment>
            </column>
        </property>
        <property name="testtype" type="java.lang.String">
            <column name="TESTTYPE" length="15">
                <comment>排气检测方法</comment>
            </column>
        </property>
        <property name="testno" type="java.lang.String">
            <column name="TESTNO" length="24">
                <comment>检测报告编号</comment>
            </column>
        </property>
        <property name="testdate" type="java.lang.String">
            <column name="TESTDATE" length="50">
                <comment>检测日期</comment>
            </column>
        </property>
        <property name="epass" type="java.lang.String">
            <column name="EPASS" length="1">
                <comment>排放检验结果</comment>
            </column>
        </property>
        <property name="vrhc" type="java.lang.Double">
            <column name="VRHC" precision="3">
                <comment>简易瞬态 HC 结果</comment>
            </column>
        </property>
        <property name="vlhc" type="java.lang.Double">
            <column name="VLHC" precision="3">
                <comment>简易瞬态 HC 限值</comment>
            </column>
        </property>
        <property name="vrco" type="java.lang.Double">
            <column name="VRCO" precision="3">
                <comment>简易瞬态 CO 结果</comment>
            </column>
        </property>
        <property name="vlco" type="java.lang.Double">
            <column name="VLCO" precision="3">
                <comment>简易瞬态 CO 限值</comment>
            </column>
        </property>
        <property name="vrnox" type="java.lang.Double">
            <column name="VRNOX" precision="3">
                <comment>简易瞬态 NOx 结果</comment>
            </column>
        </property>
        <property name="vlnox" type="java.lang.Double">
            <column name="VLNOX" precision="3">
                <comment>简易瞬态 NOx 限值</comment>
            </column>
        </property>
        <property name="serialno" type="java.lang.String">
            <column name="SERIALNO" length="50">
                <comment>唯一流水号</comment>
            </column>
        </property>
        <property name="uuid" type="java.lang.String">
            <column name="UUID" length="50" />
        </property>
        <property name="cratedate" type="java.lang.String">
            <column name="CRATEDATE" length="50">
                <comment>创建时间</comment>
            </column>
        </property>
        <property name="analymanuf" type="java.lang.String">
            <column name="ANALYMANUF" length="30">
                <comment>尾气分析仪制造厂</comment>
            </column>
        </property>
        <property name="analyname" type="java.lang.String">
            <column name="ANALYNAME" length="30">
                <comment>尾气分析仪名称</comment>
            </column>
        </property>
        <property name="analymodel" type="java.lang.String">
            <column name="ANALYMODEL" length="50">
                <comment>尾气分析仪型号</comment>
            </column>
        </property>
        <property name="analydate" type="java.lang.String">
            <column name="ANALYDATE" length="8">
                <comment>尾气分析仪检定日期</comment>
            </column>
        </property>
        <property name="dynomodel" type="java.lang.String">
            <column name="DYNOMODEL" length="50">
                <comment>底盘测功机型号</comment>
            </column>
        </property>
        <property name="dynomanuf" type="java.lang.String">
            <column name="DYNOMANUF" length="50">
                <comment>底盘测功机生产厂</comment>
            </column>
        </property>
        <property name="finalresult" type="java.lang.String">
            <column name="FINALRESULT" length="1">
                <comment>最终判定</comment>
            </column>
        </property>
        <property name="secvrhc" type="java.lang.Double">
            <column name="SECVRHC" precision="3">
                <comment>第二种燃料简易瞬态 HC 结果</comment>
            </column>
        </property>
        <property name="secvlhc" type="java.lang.Double">
            <column name="SECVLHC" precision="3">
                <comment>第二种燃料简易瞬态 HC 限值</comment>
            </column>
        </property>
        <property name="secvrco" type="java.lang.Double">
            <column name="SECVRCO" precision="3">
                <comment>第二种燃料简易瞬态 CO 结果</comment>
            </column>
        </property>
        <property name="secvlco" type="java.lang.Double">
            <column name="SECVLCO" precision="3">
                <comment>第二种燃料简易瞬态 CO 限值</comment>
            </column>
        </property>
        <property name="secvrnox" type="java.lang.Double">
            <column name="SECVRNOX" precision="3">
                <comment>第二种燃料简易瞬态 NOx 结果</comment>
            </column>
        </property>
        <property name="secvlnox" type="java.lang.Double">
            <column name="SECVLNOX" precision="3">
                <comment>第二种燃料简易瞬态 NOx 限值</comment>
            </column>
        </property>
    </class>
</hibernate-mapping>
