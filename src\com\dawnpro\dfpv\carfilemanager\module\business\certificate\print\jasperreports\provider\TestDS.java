package com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.jasperreports.provider;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRDataSourceProvider;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;
import net.sf.jasperreports.engine.JasperReport;

public class TestDS implements JRDataSource,JRDataSourceProvider{
	private int index = 1;
	private Object[] result;
	
	public void setData(Object[] data){
		result = data;
	}
	
	public Object getFieldValue(JRField field) throws JRException {
		// TODO Auto-generated method stub
		if(field.getName().equals("date")){
			return this.result[0];
		}else if(field.getName().equals("count")){
			return this.result[5];
		}
		return null;
	}

	public boolean next() throws JRException {
		if(index==1){
			index++;
			return true;
		}
		
		return false;
	}

	public JRDataSource create(JasperRep<PERSON> arg0) throws JRException {
		// TODO Auto-generated method stub
		//arg0.
		return this;
	}

	public void dispose(JRDataSource arg0) throws JRException {
		// TODO Auto-generated method stub
		
	}

	public JRField[] getFields(JasperReport arg0) throws JRException, UnsupportedOperationException {
		// TODO Auto-generated method stub
		//arg0.
		return null;
	}

	public boolean supportsGetFieldsOperation() {
		// TODO Auto-generated method stub
		return false;
	}
}
