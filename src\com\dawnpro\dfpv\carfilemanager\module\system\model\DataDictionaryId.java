package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * DataDictionaryId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class DataDictionaryId implements java.io.Serializable {

	// Fields

	private String name;
	private String value;
	private String type;

	// Constructors

	/** default constructor */
	public DataDictionaryId() {
	}

	/** full constructor */
	public DataDictionaryId(String name, String value, String type) {
		this.name = name;
		this.value = value;
		this.type = type;
	}

	// Property accessors

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getValue() {
		return this.value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof DataDictionaryId))
			return false;
		DataDictionaryId castOther = (DataDictionaryId) other;

		return ((this.getName() == castOther.getName()) || (this.getName() != null
				&& castOther.getName() != null && this.getName().equals(
				castOther.getName())))
				&& ((this.getValue() == castOther.getValue()) || (this
						.getValue() != null
						&& castOther.getValue() != null && this.getValue()
						.equals(castOther.getValue())))
				&& ((this.getType() == castOther.getType()) || (this.getType() != null
						&& castOther.getType() != null && this.getType()
						.equals(castOther.getType())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getName() == null ? 0 : this.getName().hashCode());
		result = 37 * result
				+ (getValue() == null ? 0 : this.getValue().hashCode());
		result = 37 * result
				+ (getType() == null ? 0 : this.getType().hashCode());
		return result;
	}

}