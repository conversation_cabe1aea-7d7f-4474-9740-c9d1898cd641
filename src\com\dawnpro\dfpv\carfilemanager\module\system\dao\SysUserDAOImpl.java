package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import org.hibernate.HibernateException;
import org.springframework.dao.DataAccessResourceFailureException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

/**
 * ����:�û���ݷ�����
 * @author:�Ų�
 * @since: 2008-4-22
 * **/
public class SysUserDAOImpl extends GenericHibernateDAOImpl<SysUser> implements SysUserDA0{
	
	/**
	 * ����:�����û�����
	 * @param	user:�û�����
	 * **/
	public void addUser(SysUser user) {
		try{
			this.add(user);
		}catch (DataAccessResourceFailureException e) {
			throw new DataAccessException("addUser Error:",e);
		} catch (HibernateException e) {
			throw new DataAccessException("addUser Error:",e);
		} catch (IllegalStateException e) {
			throw new DataAccessException("addUser Error:",e);
		} catch(DataAccessException e){
			throw new DataAccessException("addUser Error:",e);
		}
	}
	
	/**
	 * ����:ɾ���û�����
	 * @param	user:�û�����
	 * **/
	public void deleteUser(SysUser[] user) {
		try{
			this.deleteBatch(user);
		}catch(Exception e){
			throw new DataAccessException("deleteUser Method Error:",e);
		}
	}
	
	/**
	 * ����:�༭�û�����
	 * @param	user:�û�����
	 * **/
	public void updateUser(SysUser user) {
		try{
			this.update(user);
		}catch(Exception e){
			throw new DataAccessException("updateUser Method Error:",e);
		}
	}
	
//	/**
//	 * ����:��ѯ�û�����
//	 * @param	sql:	��ѯ���
//	 * @param	params:	��ѯ����
//	 * **/
//	public List<SysUser> findUser(String sql, RowMapper mapper) {
//		try{
//			return this.find(sql, mapper);
//		}catch(Exception e){
//			throw new DataAccessException("findUser Method Error:",e);
//		}
//	}
//	
//	/**
//	 * ����:��ѯ�û�����
//	 * @param	sql:	��ѯ���
//	 * @param	params:	��ѯ����
//	 * **/
//	public List findRole(String sql, RowMapper mapper) {
//		try{
//			return this.find(sql, mapper);
//		}catch(Exception e){
//			throw new DataAccessException("findUser Method Error:",e);
//		}
//	}
	
	public List<SysUser> findUser(String sql, String[] params){
		try{
			return this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findUser Method Error:",e);
		}
	}

	public List<SysUser> findUser(String sql){
		try{
			return this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findUser Method Error:",e);
		}
	}
	
	public SysUser loadUserObj(String loginName){
		SysUser gg = null;
		try{
			List<SysUser> result = findUser("from SysUser g where g.loginname=?",new String[]{loginName});
			if(result!=null && result.size()>0)
				gg=result.get(0);
		}catch(Exception e){
			throw new DataAccessException("loadUserObj Method Error:",e);
		}
		return gg;
	}
}
