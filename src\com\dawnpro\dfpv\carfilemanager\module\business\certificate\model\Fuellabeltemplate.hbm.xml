<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate" table="FUELLABELTEMPLATE" lazy="false">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuellabeltemplateId">
            <key-property name="slcx" type="java.lang.String">
                <column name="SLCX" length="20" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="15" />
            </key-property>
        </composite-id>
        <property name="qebz" type="java.lang.String">
            <column name="QEBZ" length="40" />
        </property>
        <property name="scqe" type="java.lang.String">
            <column name="SCQE" length="200" />
        </property>
        <property name="cxxh" type="java.lang.String">
            <column name="CXXH" length="20" />
        </property>
        <property name="fdjxh" type="java.lang.String">
            <column name="FDJXH" length="20" />
        </property>
        <property name="rllx" type="java.lang.String">
            <column name="RLLX" length="20" />
        </property>
        <property name="pl" type="java.lang.String">
            <column name="PL" length="10" />
        </property>
        <property name="edgl" type="java.lang.String">
            <column name="EDGL" length="10" />
        </property>
        <property name="bsqlx" type="java.lang.String">
            <column name="BSQLX" length="10" />
        </property>
        <property name="qdxs" type="java.lang.String">
            <column name="QDXS" length="20" />
        </property>
        <property name="zbzl" type="java.lang.String">
            <column name="ZBZL" length="10" />
        </property>
        <property name="jdzzl" type="java.lang.String">
            <column name="JDZZL" length="10" />
        </property>
        <property name="qtxx" type="java.lang.String">
            <column name="QTXX" length="200" />
        </property>
        <property name="sqgk" type="java.lang.String">
            <column name="SQGK" length="10" />
        </property>
        <property name="zhgk" type="java.lang.String">
            <column name="ZHGK" length="10" />
        </property>
        <property name="sjgk" type="java.lang.String">
            <column name="SJGK" length="10" />
        </property>
        <property name="dyxz" type="java.lang.String">
            <column name="DYXZ" length="10" />
        </property>
        <property name="dexz" type="java.lang.String">
            <column name="DEXZ" length="10" />
        </property>
        <property name="cccgzx" type="java.lang.String">
            <column name="CCCGZX" length="100" />
        </property>
        <property name="qyrq" type="java.lang.String">
            <column name="QYRQ" length="20" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="effectTime" type="java.lang.String">
            <column name="EFFECT_TIME" length="19" />
        </property>
        
        <property name="tymc" type="java.lang.String">
            <column name="TYMC" length="20" />
        </property>        
        <property name="clzl" type="java.lang.String">
            <column name="CLZL" length="50" />
        </property>        
        <property name="yyc" type="java.lang.String">
            <column name="YYC" length="16" />
        </property> 
        <property name="zgcs" type="java.lang.String">
            <column name="ZGCS" length="50" />
        </property> 
        <property name="edzk" type="java.lang.String">
            <column name="EDZK" length="50" />
        </property>                        
        <property name="ltgg" type="java.lang.String">
            <column name="LTGG" length="50" />
        </property> 
        <property name="qhlj" type="java.lang.String">
            <column name="QHLJ" length="50" />
        </property>                        
        <property name="zj" type="java.lang.String">
            <column name="ZJ" length="50" />
        </property> 
        <property name="qgs" type="java.lang.String">
            <column name="QGS" length="10" />
        </property>                        
        <property name="zdjgl" type="java.lang.String">
            <column name="ZDJGL" length="10" />
        </property> 
        <property name="zhcopl" type="java.lang.String">
            <column name="ZHCOPL" length="50" />
        </property> 
                               
        <property name="jcjgmc" type="java.lang.String">
            <column name="JCJGMC" length="100" />
        </property>                        
        <property name="bgbh" type="java.lang.String">
            <column name="BGBH" length="30" />
        </property> 
        <property name="jkjxs" type="java.lang.String">
            <column name="JKJXS" length="100" />
        </property>                        
        <property name="bsqdws" type="java.lang.String">
            <column name="BSQDWS" length="3" />
        </property>                        
        <property name="scqe1" type="java.lang.String">
            <column name="SCQE1" length="200" />
        </property>                        
        <property name="zwps" type="java.lang.String">
            <column name="ZWPS" length="3" />
        </property>    
        <property name="cddzgcs" type="java.lang.String">
            <column name="CDDZGCS" length="3" />
        </property>         
<property name="dczlzbzlb" type="java.lang.String">
	<column name="DCZLZBZLB" length="10" />
</property>
	<property name="dcbnl" type="java.lang.String">
		<column name="DCBNL" length="10" />
	</property>
	<property name="dczbcdy" type="java.lang.String">
		<column name="DCZBCDY" length="8" />
	</property>
	<property name="dczednl" type="java.lang.String">
		<column name="DCZEDNL" length="8" />
	</property>
	<property name="dczzl" type="java.lang.String">
		<column name="DCZZL" length="32" />
	</property>
	<property name="djedgl" type="java.lang.String">
		<column name="DJEDGL" length="8" />
	</property>
	<property name="djfznj" type="java.lang.String">
		<column name="DJFZNJ" length="12" />
	</property>
	<property name="djlx" type="java.lang.String">
		<column name="DJLX" length="32" />
	</property>
	<property name="zhgkdnxhl" type="java.lang.String">
		<column name="ZHGKDNXHL" length="10" />
	</property>
	<property name="zhgkxslc" type="java.lang.String">
		<column name="ZHGKXSLC" length="10" />
	</property>
	<property name="hhdljgxs" type="java.lang.String">
		<column name="HHDLJGXS" length="8" />
	</property>
	<property name="hhdlzddglb" type="java.lang.String">
		<column name="HHDLZDDGLB" length="8" />
	</property>
	<property name="xsmssdxzgn" type="java.lang.String">
		<column name="XSMSSDXZGN" length="4" />
	</property>  
	<property name="jcjnjs" type="java.lang.String">
		<column name="JCJNJS" length="100" />
	</property> 
	<property name="chqbh" type="java.lang.String">
		<column name="CHQBH" length="12" />
	</property>		
	<property name="gjbz" type="java.lang.String">
		<column name="GJBZ" length="30" />
	</property>		
	<property name="dymb" type="java.lang.String">
		<column name="DYMB" length="2" />
	</property>		
	<property name="factory" type="java.lang.String">
		<column name="FACTORY" length="10" />
	</property>		
	<property name="dndl" type="java.lang.String">
		<column name="DNDL" length="10" />
	</property>	
	<!-- 2015/9/30 增加5个字段 
	<property name="hhdljgxs" type="java.lang.String">
		<column name="ADT_COC" length="50" />
	</property>
	<property name="hhdlzddglb" type="java.lang.String">
		<column name="COCUPLOAD" length="8" />
	</property>
	<property name="xsmssdxzgn" type="java.lang.String">
		<column name="COCDEL" length="4" />
	</property>  
	<property name="jcjnjs" type="java.lang.String">
		<column name="ADT_DELCOC" length="50" />
	</property> 
	<property name="chqbh" type="java.lang.String">
		<column name="COCRESPONSE" length="200" />
	</property>		
	        -->  
	   <!-- 2018-11-20 增加 最低荷电状态燃料消耗量 字段 -->     
	  <property name="zdhdztrlxhl" type="java.lang.String">
		<column name="ZDHDZTRLXHL" length="20" />
	  </property>	 
	  
	   <!-- 2021-06-19 新增 工况认证方式   NEDC认证  WLTC认证 字段 -->     
	  <property name="gkrzfs" type="java.lang.String">
		<column name="gkrzfs" length="10" />
	  </property>	
	   <!-- 2021-06-19 新增 WLTC认证领跑值 字段 -->     
	  <property name="wltclpz" type="java.lang.String">
		<column name="wltclpz" length="10" />
	  </property>	
	   <!-- 2021-06-19 新增 WLTC认证限值 字段 -->     
	  <property name="wltclxz" type="java.lang.String">
		<column name="wltclxz" length="10" />
	  </property>
	  <!-- 2024-04-23 新增10个 字段 -->     
	  <property name="dsgk" type="java.lang.String">
		<column name="DSGK" length="10" />
	  </property>	
	  <property name="zsgk" type="java.lang.String">
		<column name="ZSGK" length="10" />
	  </property>
	  <property name="gsgk" type="java.lang.String">
		<column name="GSGK" length="10" />
	  </property>
	  <property name="cgsgk" type="java.lang.String">
		<column name="CGSGK" length="10" />
	  </property>
	  <property name="rlxhlbt" type="java.lang.String">
		<column name="RLXHLBT" length="100" />
	  </property>
	  <property name="rlxhlsj" type="java.lang.String">
		<column name="RLXHLSJ" length="100" />
	  </property>
	  <property name="ydzhrlxhl" type="java.lang.String">
		<column name="YDZHRLXHL" length="10" />
	  </property>
	  <property name="ygnycb" type="java.lang.String">
		<column name="YGNYCB" length="10" />
	  </property>
	  <property name="rlxhlbj1" type="java.lang.String">
		<column name="RLXHLBJ1" length="100" />
	  </property>
	  <property name="rlxhlbj2" type="java.lang.String">
		<column name="RLXHLBJ2" length="100" />
	  </property>  
	  <property name="bacode" type="java.lang.String">
		<column name="BACODE" length="50" />
	  </property>                               
    </class>
</hibernate-mapping>
