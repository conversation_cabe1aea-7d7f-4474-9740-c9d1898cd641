package com.dawnpro.dfpv.carfilemanager.module.business.share.dao.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceDefineDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDefine;

public class InterfaceDefineDAOImpl extends GenericHibernateDAOImpl<InterfaceDefine> implements InterfaceDefineDAO {

	public void addInterfaceDefine(InterfaceDefine def) {
		// TODO Auto-generated method stub

	}

	public void deleteInterfaceDefine(InterfaceDefine def) {
		// TODO Auto-generated method stub

	}

	public void updateInterfaceDefine(InterfaceDefine def) {
		// TODO Auto-generated method stub

	}

	public Object loadInterfaceDefine(Class cla, String id) {
		// TODO Auto-generated method stub
		return null;
	}

	public List findData(String sql, Object[] params) {
		// TODO Auto-generated method stub
		return null;
	}

}
