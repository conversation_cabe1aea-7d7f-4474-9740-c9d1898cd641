package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * CarColorMapping entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CertificateSupplementInfo implements java.io.Serializable {

	// Fields

	private CertificateSupplementInfoId id;
	private String cocnum;
	private String type;
	private String reason;
	private String reasonName;
	private String description;

	// Constructors

	/** default constructor */
	public CertificateSupplementInfo() {
	}

	/** minimal constructor */
	public CertificateSupplementInfo(CertificateSupplementInfoId id) {
		this.id = id;
	}

	/** full constructor */
	public CertificateSupplementInfo(CertificateSupplementInfoId id,String cocnum,String type,String reason,String description) {
		this.id = id;
		this.cocnum = cocnum;
		this.type = type;
		this.reason = reason;
		this.description = description;
	}
	
	public CertificateSupplementInfo(String vin, String printtime,String cocnum,String type,String reason,String description, String reasonName) {
		this.id = new CertificateSupplementInfoId(vin, printtime);
		this.cocnum = cocnum;
		this.type = type;
		this.reason = reason;
		this.description = description;
		this.reasonName = reasonName;
	}

	// Property accessors
	public CertificateSupplementInfoId getId() {
		return id;
	}

	public void setId(CertificateSupplementInfoId id) {
		this.id = id;
	}


	public String getCocnum() {
		return cocnum;
	}

	public void setCocnum(String cocnum) {
		this.cocnum = cocnum;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getReasonName() {
		return reasonName;
	}

	public void setReasonName(String reasonName) {
		this.reasonName = reasonName;
	}

	
	
}