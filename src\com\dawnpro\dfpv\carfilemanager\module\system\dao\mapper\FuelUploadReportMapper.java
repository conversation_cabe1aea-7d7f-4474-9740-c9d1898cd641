package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelReportBean;

public class FuelUploadReportMapper implements RowMapper<List> {

	public List mapRow(ResultSet rs) {
		// TODO Auto-generated method stub
		return null;
	}

	public List mapRow(Collection rs) {
		// TODO Auto-generated method stub
		List list = new ArrayList();
		for(Object obj : rs){
			Object[] objs = (Object[])obj;
			FuelReportBean bean = new FuelReportBean();
			bean.setTime(objs[0] == null ? "" : ((String)objs[0]).trim());
			bean.setCount(objs[1] == null ? "" : ((String)objs[1]).trim());
			bean.setAvg_zhgk(objs[2] == null ? "" : ((String)objs[2]).trim());
			bean.setAvg_zhcopl(objs[3] == null ? "" : ((String)objs[3]).trim());
			list.add(bean);
			
		}
		
		return list;
	}

}
