package com.dawnpro.dfpv.carfilemanager.module.business.cardata.action;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CarPublicModelIconService;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarPublicModelIcon;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryService;

public class CarPublicModelIconAction extends BaseSupportAction{
	private static final long serialVersionUID = 6371005823002910434L;
	private Logger logger = Logger.getLogger(CarPublicModelIconAction.class.getName());
	private CarPublicModelIconService carPublicModelIconService = null;
	private SysDataDictionaryService sysDataDictionaryService = null;
	
	private String currentPage = null;
	private String pmodel;
	private String printicon;
	
	private String qpmodel;
	private String qprinticon;
	
	private String ids = null;
	
	public CarPublicModelIconService getCarPublicModelIconService() {
		return carPublicModelIconService;
	}
	public void setCarPublicModelIconService(
			CarPublicModelIconService carPublicModelIconService) {
		this.carPublicModelIconService = carPublicModelIconService;
	}
	
	public SysDataDictionaryService getSysDataDictionaryService() {
		return sysDataDictionaryService;
	}
	public void setSysDataDictionaryService(
			SysDataDictionaryService sysDataDictionaryService) {
		this.sysDataDictionaryService = sysDataDictionaryService;
	}
	public String getCurrentPage() {
		return currentPage;
	}
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}
	public Logger getLogger() {
		return logger;
	}
	public void setLogger(Logger logger) {
		this.logger = logger;
	}
	public String getPmodel() {
		return pmodel;
	}
	public void setPmodel(String pmodel) {
		this.pmodel = pmodel;
	}
	public String getPrinticon() {
		return printicon;
	}
	public void setPrinticon(String printicon) {
		this.printicon = printicon;
	}
	public String getQpmodel() {
		return qpmodel;
	}
	public void setQpmodel(String qpmodel) {
		this.qpmodel = qpmodel;
	}
	public String getQprinticon() {
		return qprinticon;
	}
	public void setQprinticon(String qprinticon) {
		this.qprinticon = qprinticon;
	}
	public String getIds() {
		return ids;
	}
	public void setIds(String ids) {
		this.ids = ids;
	}
	public String execute() {
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());

			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			this.qpmodel = qpmodel == null ? "" : qpmodel;
			
			this.qprinticon = qprinticon == null ? "" : qprinticon;

			if (!"".equals(this.getQpmodel()))
				params.put("pmodel", this.getQpmodel());
			if (!"".equals(this.getQprinticon()))
				params.put("printIcon", this.getQprinticon());
			results = carPublicModelIconService.pagination(page, params);

			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("carPublicModelIconPageData", results);

			List printiconDict = sysDataDictionaryService
					.findDataDictionary("PRINTICON");
			this.getServletRequest().setAttribute("printiconDict", printiconDict);
			this.getServletRequest().setAttribute("qpmodel", qpmodel);
			this.getServletRequest().setAttribute("qprinticon", qprinticon);

		} catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
	
	public String addCarPublicModelIcon() {
		SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
		CarPublicModelIcon carPublicModelIcon = new CarPublicModelIcon();
		
		carPublicModelIcon.setPmodel(pmodel);
		carPublicModelIcon.setPrinticon(printicon);
		carPublicModelIcon.setState("0");
		carPublicModelIcon.setCreatedate(new Date());
		carPublicModelIcon.setCreator(user.getUsername());
		
		try{
			this.carPublicModelIconService.addCarPublicModelIcon(carPublicModelIcon);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String updateCarPublicModelIcon() {
		CarPublicModelIcon carPublicModelIcon = new CarPublicModelIcon();
		
		carPublicModelIcon = carPublicModelIconService.loadCarPublicModelIcon(pmodel);
		carPublicModelIcon.setPrinticon(printicon);
		carPublicModelIcon.setUpdatedate(new Date());
		try{
			this.carPublicModelIconService.updateCarPublicModelIcon(carPublicModelIcon);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String deleteCarPublicModelIcon() {
		String[] id = ids.split("&");
		
		try{
			this.carPublicModelIconService.deleteCarPublicModelIcon(id);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}

	public String existPublicModel(){
		try{
			boolean exist = carPublicModelIconService.existPublicModel(pmodel);
			this.setJson(String.valueOf(exist));			
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}
	
	public String existCarPublicModelIcon(){
		try{
			CarPublicModelIcon carPublicModelIcon = carPublicModelIconService.loadCarPublicModelIcon(pmodel);
			if(carPublicModelIcon != null){
				this.setJson(String.valueOf(true));
			}else{
				this.setJson(String.valueOf(false));
			}
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}
	
	public String getCarPublicModelIcon(){
		try{
			CarPublicModelIcon carPublicModelIcon = carPublicModelIconService.loadCarPublicModelIcon(pmodel);
			JSONObject jsonObject = JSONObject.fromObject(carPublicModelIcon);
			if (carPublicModelIcon != null)
				this.setJson(jsonObject.toString());				
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}

}
