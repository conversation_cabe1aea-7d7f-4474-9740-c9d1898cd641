package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Uploaduser;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.UploadUserDAO;

public class UploadUserServiceImpl implements UploadUserService {
	private UploadUserDAO uploadUserDAO = null;

	public UploadUserDAO getUploadUserDAO() {
		return uploadUserDAO;
	}

	public void setUploadUserDAO(UploadUserDAO uploadUserDAO) {
		this.uploadUserDAO = uploadUserDAO;
	}

	public void addOrUpdateUploadUser(Uploaduser obj)
			throws DataAccessException {
		try{
			List<Uploaduser> list = uploadUserDAO.findUploadUser(" from Uploaduser obj where obj.flag='"+obj.getFlag()+"'");
			if(list != null && list.size() > 0){
				Uploaduser tmp = list.get(0);
				if(tmp.getUsername().equals(obj.getUsername())){
					tmp.setFlag(obj.getFlag());
					tmp.setQyjmxx(obj.getQyjmxx());
					tmp.setPassword(obj.getPassword());
					uploadUserDAO.updateUploadUser(tmp);
				}else{
					uploadUserDAO.deleteUploadUser(tmp);
					uploadUserDAO.addUploadUser(obj);
				}
			}else{
				uploadUserDAO.addUploadUser(obj);
			}
		}catch (DataAccessException e) {
			throw new SystemException("addFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("addFilterModel Method Error:", e);
		}

	}

	public Uploaduser loadUploadUser(String uploadUserType) throws DataAccessException {
		Uploaduser obj = null;
		try{
			List<Uploaduser> list = uploadUserDAO.findUploadUser(" from Uploaduser t where t.flag='"+uploadUserType+"'");
			if(list != null && list.size() > 0){
				obj = list.get(0);
			}
		}catch (DataAccessException e) {
			throw new SystemException("loadUploadUser Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("loadUploadUser Method Error:", e);
		}
		return obj;
	}

}
