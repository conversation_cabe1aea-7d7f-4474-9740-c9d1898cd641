<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.SystemConfigParams" table="SYSTEM_CONFIG_PARAMS" lazy="false">
        <id name="id" type="java.lang.Integer">
            <column name="ID" />
            <generator class="sequence">
              	<param name="sequence">SEQ_SYSCFG</param>
         	</generator>
        </id>
        <property name="name" type="java.lang.String">
            <column name="NAME" length="30" />
        </property>
        <property name="value" type="java.lang.String">
            <column name="VALUE" length="20" />
        </property>
        <property name="min" type="java.lang.String">
            <column name="MIN" length="10" />
        </property>
        <property name="max" type="java.lang.String">
            <column name="MAX" length="10" />
        </property>
        <property name="dicname" type="java.lang.String">
            <column name="DICNAME" length="20" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="2"/>
        </property>
    </class>
</hibernate-mapping>
