package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelScjcUploadBean;

public class WqbfUploadBeanMapper implements RowMapper<List<FuelScjcUploadBean>> {

	public List<FuelScjcUploadBean> mapRow(ResultSet rs) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<FuelScjcUploadBean> mapRow(Collection rs) {
		List<FuelScjcUploadBean> list = new ArrayList<FuelScjcUploadBean>();
		int point = 0;
		for(Object obj : rs){
			point = 0;
			Object[] objs = (Object[])obj;
			FuelScjcUploadBean bean = new FuelScjcUploadBean();
			bean.setVin(objs[point] == null ? "" : (objs[point]+"").trim());
					
			bean.setRh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEt(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAp(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTesttype(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTestNo(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTestDate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEpass(objs[++point] == null ? "" : (objs[point]+"").trim());
			
		
			bean.setVrhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVlhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVrco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVlco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVrnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVlnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyManuf(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyName(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyModel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyDate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDynoModel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDynoManuf(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCocprinttime(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setRllx(objs[++point] == null ? "" : (objs[point]+"").trim());
			
			bean.setSecvrhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvlhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvrco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvlco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvrnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvlnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			list.add(bean);
		}
		return list;
	}

}
