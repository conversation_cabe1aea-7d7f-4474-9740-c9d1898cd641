<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate" table="CARTYPETEMPLATE">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId">
            <key-property name="c1" type="java.lang.String">
                <column name="C1" length="10" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="15" />
            </key-property>
        </composite-id>
        <property name="c2" type="java.lang.String">
            <column name="C2" length="40" />
        </property>
        <property name="c3" type="java.lang.String">
            <column name="C3" length="200" />
        </property>
        <property name="c4" type="java.lang.String">
            <column name="C4" length="20" />
        </property>
        <property name="c5" type="java.lang.String">
            <column name="C5" length="10" />
        </property>
        <property name="c6" type="java.lang.String">
            <column name="C6" length="20" />
        </property>
        <property name="c7" type="java.lang.String">
            <column name="C7" length="20" />
        </property>
        <property name="c8" type="java.lang.String">
            <column name="C8" length="200" />
        </property>
        <property name="c9" type="java.lang.String">
            <column name="C9" length="200" />
        </property>
        <property name="c10" type="java.lang.String">
            <column name="C10" length="200" />
        </property>
        <property name="c11" type="java.lang.String">
            <column name="C11" length="200" />
        </property>
        <property name="c12" type="java.lang.String">
            <column name="C12" length="50" />
        </property>
        <property name="c13" type="java.lang.String">
            <column name="C13" length="60" />
        </property>
        <property name="c14" type="java.lang.String">
            <column name="C14" length="50" />
        </property>
        <property name="c15" type="java.lang.String">
            <column name="C15" length="20" />
        </property>
        <property name="c16" type="java.lang.String">
            <column name="C16" length="20" />
        </property>
        <property name="c17" type="java.lang.String">
            <column name="C17" length="2" />
        </property>
        <property name="c18" type="java.lang.String">
            <column name="C18" length="2" />
        </property>
        <property name="c19" type="java.lang.String">
            <column name="C19" length="10" />
        </property>
        <property name="c20" type="java.lang.String">
            <column name="C20" length="10" />
        </property>
        <property name="c21" type="java.lang.String">
            <column name="C21" length="10" />
        </property>
        <property name="c22" type="java.lang.String">
            <column name="C22" length="10" />
        </property>
        <property name="c23" type="java.lang.String">
            <column name="C23" length="10" />
        </property>
        <property name="c24" type="java.lang.String">
            <column name="C24" length="10" />
        </property>
        <property name="c25" type="java.lang.String">
            <column name="C25" length="10" />
        </property>
        <property name="c26" type="java.lang.String">
            <column name="C26" length="10" />
        </property>
        <property name="c27" type="java.lang.String">
            <column name="C27" length="10" />
        </property>
        <property name="c28" type="java.lang.String">
            <column name="C28" length="10" />
        </property>
        <property name="c29" type="java.lang.String">
            <column name="C29" length="5" />
        </property>
        <property name="c30" type="java.lang.String">
            <column name="C30" length="5" />
        </property>
        <property name="c31" type="java.lang.String">
            <column name="C31" length="5" />
        </property>
        <property name="c32" type="java.lang.String">
            <column name="C32" length="5" />
        </property>
        <property name="c33" type="java.lang.String">
            <column name="C33" length="5" />
        </property>
        <property name="c34" type="java.lang.String">
            <column name="C34" length="5" />
        </property>
        <property name="c35" type="java.lang.String">
            <column name="C35" length="5" />
        </property>
        <property name="c36" type="java.lang.String">
            <column name="C36" length="5" />
        </property>
        <property name="c37" type="java.lang.String">
            <column name="C37" length="10" />
        </property>
        <property name="c38" type="java.lang.String">
            <column name="C38" length="10" />
        </property>
        <property name="c39" type="java.lang.String">
            <column name="C39" length="10" />
        </property>
        <property name="c40" type="java.lang.String">
            <column name="C40" length="10" />
        </property>
        <property name="c41" type="java.lang.String">
            <column name="C41" length="10" />
        </property>
        <property name="c42" type="java.lang.String">
            <column name="C42" length="100" />
        </property>
        <property name="c43" type="java.lang.String">
            <column name="C43" length="20" />
        </property>
        <property name="c44" type="java.lang.String">
            <column name="C44" length="50" />
        </property>
        <property name="c45" type="java.lang.String">
            <column name="C45" length="4" />
        </property>
        <property name="c46" type="java.lang.String">
            <column name="C46" length="20" />
        </property>
        <property name="c47" type="java.lang.String">
            <column name="C47" length="5" />
        </property>
        <property name="c48" type="java.lang.String">
            <column name="C48" length="20" />
        </property>
        <property name="c49" type="java.lang.String">
            <column name="C49" length="4" />
        </property>
        <property name="c50" type="java.lang.String">
            <column name="C50" length="10" />
        </property>
        <property name="c51" type="java.lang.String">
            <column name="C51" length="30" />
        </property>
        <property name="c52" type="java.lang.String">
            <column name="C52" length="30" />
        </property>
        <property name="c53" type="java.lang.String">
            <column name="C53" length="100" />
        </property>
        <property name="c54" type="java.lang.String">
            <column name="C54" length="20" />
        </property>
        <property name="c55" type="java.lang.String">
            <column name="C55" length="20" />
        </property>
        <property name="c56" type="java.lang.String">
            <column name="C56" length="20" />
        </property>
        <property name="c57" type="java.lang.String">
            <column name="C57" length="40" />
        </property>
        <property name="c58" type="java.lang.String">
            <column name="C58" length="40" />
        </property>
        <property name="c59" type="java.lang.String">
            <column name="C59" length="20" />
        </property>
        <property name="c60" type="java.lang.String">
            <column name="C60" length="20" />
        </property>
        <property name="c61" type="java.lang.String">
            <column name="C61" length="20" />
        </property>
        <property name="c62" type="java.lang.String">
            <column name="C62" length="20" />
        </property>
        <property name="c63" type="java.lang.String">
            <column name="C63" length="40" />
        </property>
        <property name="c64" type="java.lang.String">
            <column name="C64" length="40" />
        </property>
        <property name="c65" type="java.lang.String">
            <column name="C65" length="100" />
        </property>
        <property name="c66" type="java.lang.String">
            <column name="C66" length="10" />
        </property>
        <property name="c67" type="java.lang.String">
            <column name="C67" length="5" />
        </property>
        <property name="c68" type="java.lang.String">
            <column name="C68" length="5" />
        </property>
        <property name="c69" type="java.lang.String">
            <column name="C69" length="100" />
        </property>
        <property name="c70" type="java.lang.String">
            <column name="C70" length="20" />
        </property>
        <property name="c71" type="java.lang.String">
            <column name="C71" length="5" />
        </property>
        <property name="c72" type="java.lang.String">
            <column name="C72" length="5" />
        </property>
        <property name="c73" type="java.lang.String">
            <column name="C73" length="5" />
        </property>
        <property name="c74" type="java.lang.String">
            <column name="C74" length="10" />
        </property>
        <property name="c75" type="java.lang.String">
            <column name="C75" length="10" />
        </property>
        <property name="c76" type="java.lang.String">
            <column name="C76" length="20" />
        </property>
        <property name="c77" type="java.lang.String">
            <column name="C77" length="20" />
        </property>
        <property name="c78" type="java.lang.String">
            <column name="C78" length="5" />
        </property>
        <property name="c79" type="java.lang.String">
            <column name="C79" length="5" />
        </property>
        <property name="c80" type="java.lang.String">
            <column name="C80" length="5" />
        </property>
        <property name="c81" type="java.lang.String">
            <column name="C81" length="5" />
        </property>
        <property name="c82" type="java.lang.String">
            <column name="C82" length="5" />
        </property>
        <property name="c83" type="java.lang.String">
            <column name="C83" length="20" />
        </property>
        <property name="c84" type="java.lang.String">
            <column name="C84" length="50" />
        </property>
        <property name="c85" type="java.lang.String">
            <column name="C85" length="5" />
        </property>
        <property name="c86" type="java.lang.String">
            <column name="C86" length="5" />
        </property>
        <property name="c87" type="java.lang.String">
            <column name="C87" length="5" />
        </property>
        <property name="c88" type="java.lang.String">
            <column name="C88" length="5" />
        </property>
        <property name="c89" type="java.lang.String">
            <column name="C89" length="5" />
        </property>
        <property name="c90" type="java.lang.String">
            <column name="C90" length="5" />
        </property>
        <property name="c91" type="java.lang.String">
            <column name="C91" length="5" />
        </property>
        <property name="c92" type="java.lang.String">
            <column name="C92" length="5" />
        </property>
        <property name="c93" type="java.lang.String">
            <column name="C93" length="5" />
        </property>
        <property name="c94" type="java.lang.String">
            <column name="C94" length="200" />
        </property>
        <property name="c95" type="java.lang.String">
            <column name="C95" length="20" />
        </property>
        <property name="c96" type="java.lang.String">
            <column name="C96" length="20" />
        </property>
        <property name="c97" type="java.lang.String">
            <column name="C97" length="20" />
        </property>
        <property name="c98" type="java.lang.String">
            <column name="C98" length="20" />
        </property>
        <property name="c99" type="java.lang.String">
            <column name="C99" length="20" />
        </property>
        <property name="c100" type="java.lang.String">
            <column name="C100" length="20" />
        </property>
        <property name="c101" type="java.lang.String">
            <column name="C101" length="20" />
        </property>
        <property name="c102" type="java.lang.String">
            <column name="C102" length="20" />
        </property>
        <property name="c103" type="java.lang.String">
            <column name="C103" length="20" />
        </property>
        <property name="c104" type="java.lang.String">
            <column name="C104" length="20" />
        </property>
        <property name="c105" type="java.lang.String">
            <column name="C105" length="20" />
        </property>
        <property name="c106" type="java.lang.String">
            <column name="C106" length="20" />
        </property>
        <property name="c107" type="java.lang.String">
            <column name="C107" length="20" />
        </property>
        <property name="c108" type="java.lang.String">
            <column name="C108" length="20" />
        </property>
        <property name="c109" type="java.lang.String">
            <column name="C109" length="20" />
        </property>
        <property name="c110" type="java.lang.String">
            <column name="C110" length="20" />
        </property>
        <property name="c111" type="java.lang.String">
            <column name="C111" length="20" />
        </property>
        <property name="c112" type="java.lang.String">
            <column name="C112" length="20" />
        </property>
        <property name="c113" type="java.lang.String">
            <column name="C113" length="20" />
        </property>
        <property name="c114" type="java.lang.String">
            <column name="C114" length="20" />
        </property>
        <property name="c115" type="java.lang.String">
            <column name="C115" length="20" />
        </property>
        <property name="c116" type="java.lang.String">
            <column name="C116" length="20" />
        </property>
        <property name="c117" type="java.lang.String">
            <column name="C117" length="20" />
        </property>
        <property name="c118" type="java.lang.String">
            <column name="C118" length="20" />
        </property>
        <property name="c119" type="java.lang.String">
            <column name="C119" length="20" />
        </property>
        <property name="c120" type="java.lang.String">
            <column name="C120" length="500" />
        </property>
        <property name="c121" type="java.lang.String">
            <column name="C121" length="500" />
        </property>
        <property name="c122" type="java.lang.String">
            <column name="C122" length="20" />
        </property>
        <property name="c123" type="java.lang.String">
            <column name="C123" length="20" />
        </property>
        <property name="c124" type="java.lang.String">
            <column name="C124" length="20" />
        </property>
        <property name="c125" type="java.lang.String">
            <column name="C125" length="20" />
        </property>
        <property name="c126" type="java.lang.String">
            <column name="C126" length="20" />
        </property>
        <property name="c127" type="java.lang.String">
            <column name="C127" length="20" />
        </property>
        <property name="c128" type="java.lang.String">
            <column name="C128" length="20" />
        </property>
        <property name="c129" type="java.lang.String">
            <column name="C129" length="20" />
        </property>
        <property name="c130" type="java.lang.String">
            <column name="C130" length="20" />
        </property>
        <!-- begin 纯电动字段 -->
        <property name="c131" type="java.lang.String">
            <column name="C131" length="50" />
        </property>
        <property name="c132" type="java.lang.String">
            <column name="C132" length="50" />
        </property>
        <property name="c133" type="java.lang.String">
            <column name="C133" length="10" />
        </property>
        <property name="c134" type="java.lang.String">
            <column name="C134" length="50" />
        </property>
        <property name="c135" type="java.lang.String">
            <column name="C135" length="10" />
        </property>
        <property name="c136" type="java.lang.String">
            <column name="C136" length="50" />
        </property>
        <property name="c137" type="java.lang.String">
            <column name="C137" length="50" />
        </property>
        <property name="c138" type="java.lang.String">
            <column name="C138" length="10" />
        </property>
        <property name="c139" type="java.lang.String">
            <column name="C139" length="10" />
        </property>
        <property name="c140" type="java.lang.String">
            <column name="C140" length="10" />
        </property>
        <property name="c141" type="java.lang.String">
            <column name="C141" length="10" />
        </property>
        <property name="c142" type="java.lang.String">
            <column name="C142" length="10" />
        </property>
        <property name="c143" type="java.lang.String">
            <column name="C143" length="10" />
        </property>
        <property name="c144" type="java.lang.String">
            <column name="C144" length="10" />
        </property>
        <property name="c145" type="java.lang.String">
            <column name="C145" length="10" />
        </property>
        <property name="c146" type="java.lang.String">
            <column name="C146" length="10" />
        </property>
        <property name="testtype" type="java.lang.String">
            <column name="TESTTYPE" length="10" />
        </property>
        <!-- end 纯电动字段-->
        <!-- A4纸模板 新增字段 -->
        <property name="c147" type="java.lang.String">
            <column name="C147" length="10" />
        </property>
        <property name="c148" type="java.lang.String">
            <column name="C148" length="10" />
        </property>
        <property name="c149" type="java.lang.String">
            <column name="C149" length="10" />
        </property>
        <property name="c150" type="java.lang.String">
            <column name="C150" length="10" />
        </property>
        <property name="c151" type="java.lang.String">
            <column name="C151" length="20" />
        </property>
        <property name="c152" type="java.lang.String">
            <column name="C152" length="20" />
        </property>
        <property name="c153" type="java.lang.String">
            <column name="C153" length="10" />
        </property>
        <property name="c154" type="java.lang.String">
            <column name="C154" length="10" />
        </property>
        <property name="c155" type="java.lang.String">
            <column name="C155" length="10" />
        </property>
        <property name="c156" type="java.lang.String">
            <column name="C156" length="20" />
        </property>
        <property name="c157" type="java.lang.String">
            <column name="C157" length="100" />
        </property>
        <!-- end -->
        
        <!-- 2023年COC改版 新增字段 -->
<property name="c194" type="java.lang.String"><column name="C194" length="10" /></property>
<property name="c191" type="java.lang.String"><column name="C191" length="50" /></property>
<property name="c158" type="java.lang.String"><column name="C158" length="10" /></property>
<property name="c159" type="java.lang.String"><column name="C159" length="10" /></property>
<property name="c160" type="java.lang.String"><column name="C160" length="50" /></property>
<property name="c161" type="java.lang.String"><column name="C161" length="50" /></property>
<property name="c162" type="java.lang.String"><column name="C162" length="50" /></property>
<property name="c163" type="java.lang.String"><column name="C163" length="10" /></property>
<property name="c164" type="java.lang.String"><column name="C164" length="10" /></property>
<property name="c165" type="java.lang.String"><column name="C165" length="50" /></property>
<property name="c166" type="java.lang.String"><column name="C166" length="10" /></property>
<property name="c167" type="java.lang.String"><column name="C167" length="10" /></property>
<property name="c168" type="java.lang.String"><column name="C168" length="10" /></property>
<property name="c169" type="java.lang.String"><column name="C169" length="10" /></property>
<property name="c195" type="java.lang.String"><column name="C195" length="10" /></property>
<property name="c170" type="java.lang.String"><column name="C170" length="50" /></property>
<property name="c171" type="java.lang.String"><column name="C171" length="10" /></property>
<property name="c172" type="java.lang.String"><column name="C172" length="10" /></property>
<property name="c173" type="java.lang.String"><column name="C173" length="10" /></property>
<property name="c174" type="java.lang.String"><column name="C174" length="10" /></property>
<property name="c175" type="java.lang.String"><column name="C175" length="10" /></property>
<property name="c176" type="java.lang.String"><column name="C176" length="10" /></property>
<property name="c177" type="java.lang.String"><column name="C177" length="10" /></property>
<property name="c178" type="java.lang.String"><column name="C178" length="10" /></property>
<property name="c179" type="java.lang.String"><column name="C179" length="10" /></property>
<property name="c180" type="java.lang.String"><column name="C180" length="10" /></property>
<property name="c181" type="java.lang.String"><column name="C181" length="10" /></property>
<property name="c182" type="java.lang.String"><column name="C182" length="10" /></property>
<property name="c183" type="java.lang.String"><column name="C183" length="10" /></property>
<property name="c184" type="java.lang.String"><column name="C184" length="10" /></property>
<property name="c185" type="java.lang.String"><column name="C185" length="10" /></property>
<property name="c186" type="java.lang.String"><column name="C186" length="10" /></property>
<property name="c187" type="java.lang.String"><column name="C187" length="10" /></property>
<property name="c188" type="java.lang.String"><column name="C188" length="10" /></property>
<property name="c190" type="java.lang.String"><column name="C190" length="10" /></property>
<property name="c192" type="java.lang.String"><column name="C192" length="50" /></property>
<property name="c193" type="java.lang.String"><column name="C193" length="50" /></property>
<property name="c196" type="java.lang.String"><column name="C196" length="20" /></property>     
<property name="c197" type="java.lang.String"><column name="C197" length="50" /></property>    
        <!-- end -->
        
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="ggcx" type="java.lang.String">
            <column name="GGCX" length="20" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="published" type="java.lang.String">
            <column name="PUBLISHED" length="1" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7" />
        </property>
        <property name="dexz" type="java.lang.String">
            <column name="DEXZ" length="10" />
        </property>
         <property name="effecttime" type="java.lang.String">
            <column name="EFFECT_TIME" length="19" />
        </property>
         <property name="printtype" type="java.lang.String">
            <column name="PRINTTYPE" length="2" />
        </property>
         <property name="factory" type="java.lang.String">
            <column name="FACTORY" length="10" />
        </property>
    </class>
</hibernate-mapping>
