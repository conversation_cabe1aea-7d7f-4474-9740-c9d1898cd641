<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';
	
	//$("#create").attr("disabled", true);
	//$("#delete").attr("disabled", true);
	//$("#import").attr("disabled", true);

	
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
            	if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#import").attr("disabled", false);
				}
            }
        }
    });

	$("#query").click(function(){
		var model = $('#model').val();
		location.href="cocModelTypeAction.action?model="+encodeURI(encodeURI(model))+"&menuid="+menuid;
	});
	
	$("#create").click(function() {      	  		
   		type = "add";
   	 	$('#typicality_neutral_dialog').data('title.dialog', '新增').dialog('open');
   	 	$('#vtable').find('tr:eq(0)').show(); 	
	});
	
	$('#import').click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>business/car/send_car_model_upload.jsp',winName,params) 
	});
	
	

	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
       	var state="";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
         		index++;					
				if(id==""){
					id = this.value;
					info = this.parentElement.parentElement.children[1].innerText;
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+this.parentElement.parentElement.children[1].innerText;
				}				
             }
      	});
		
   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
 			type = "delete";
  	   		messageObj = $('#operate_dialog');
  	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
  	   		messageObj.dialog('open');
  	   		messageObj.find('#ids').val(id);
   	   		
   	   	}
	
	});
	
	
	$("#typicality_neutral_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 500,
		height: 450,
		modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				
				if(validate('#typicality_neutral_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
			        
			        var amodel = $('#typicality_neutral_dialog').find('#model').val();
			        
					if(type=="add"){
						jQuery.ajax({
				            url: 'business/cocModelTypeAction!isCOCModelTypeExist.action',
					        type: 'POST',
					        data: {'model' : amodel}, 
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#typicality_neutral_dialog');
					            if(json2Bean(data).json=="true"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'数据已经存在不能新增！');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].action="business/cocModelTypeAction!addCOCModelTYpe.action?currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						  var currentPage=$('#currentPage_temp').val();
						  $(this).find('#createForm')[0].action="<%=path%>/business/cocModelTypeAction!updateTypicalityNeutral.action?currentPage="+currentPage;
						  $(this).find('#createForm')[0].submit();
					
					}
				}
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			clear($(this));
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			type = null;
			//$('#typicality_neutral_dialog').find('#vin').attr('readonly',false);
		}
	});

	$("#car_color_mapping_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 500,height: 400,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
		}
	});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				var parent = $('#typicality_neutral_dialog');
				updateTips($(parent).find('#validateTips'),'');
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/cocModelTypeAction!deleteCOCModelType.action?currentPage="+currentPage;
					formObj[0].submit();
				}
			}
		},
		close: function() {
			var parent = $('#typicality_neutral_dialog');
			updateTips($(parent).find('#validateTips'),'');
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
    function display(id,title){
		jQuery.ajax({
	        url: 'business/cocModelTypeAction!getCOCModelType.action',		           
	        data: {'id' : id}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	            var dialogObj = $('#car_color_mapping_display_dialog');
				setDialogValue(dialogObj,carObj);
				dialogObj.data('title.dialog', title).dialog('open');
	        }
	    });

		return false;
	}
	
	$(".display").each(function(i){
		  $(this).click(function() {
			var id=  $(this).attr('value');	  	
			display(id,"查看窗口");
		 });
	  });	

    function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#id').val(jsonObj.id);
		dialogObj.find('#model').val(jsonObj.model);
		dialogObj.find('#modelType').val(jsonObj.modelType);
		dialogObj.find('#engineNum').val(jsonObj.engineNum);
		dialogObj.find('#motorNum').val(jsonObj.motorNum);
		dialogObj.find('#cocPrintType').val(jsonObj.cocPrintType);
	}
    
	function clear(dialogObj){		
		dialogObj.find('#id').val("");
		dialogObj.find('#minzl').val("");
		dialogObj.find('#maxzl').val("");
		dialogObj.find('#lpz1').val("");
		dialogObj.find('#lpz2').val("");
		dialogObj.find('#xs').val("");
		dialogObj.find('#cyear').val("");
	}

	function validate(parent){
		//return true;
		var obj = $(parent).find('#model');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最小值必填,并且长度不能超过20！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#modelType');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车辆类型必填,并且长度不能超过10！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#engineNum');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'单车发动机数量必填,并且长度不能超过10！');
			obj.focus();			
			return false;
		}
		var obj = $(parent).find('#motorNum');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'单车电动机数量必填,并且长度不能超过10！');
			obj.focus();			
			return false;
		}
		var obj = $(parent).find('#cocPrintType');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'COC打印模板必填,并且长度不能超过10！');
			obj.focus();			
			return false;
		}
		
		return true;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var qdxcx = $('#qdxcx').val();
			location.href="cocModelTypeAction.action?currentPage="+$('#jump').val()+"&qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qdxcx = $('#qdxcx').val();
				location.href=$(this).attr('value')+"&qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
		 });
	  });
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class

	$('#typicality_neutral_dialog').find('#minzl').attr('readonly',true);
	$('#typicality_neutral_dialog').find('#maxzl').attr('readonly',true);
});


</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan="6"><p>公告车型:<input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.model!=null"> value="<s:property value="#request.model" />"</s:if>/>				 				
				</td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			
			<tr>
			   <td width="80%"></td>
			   <td width="60" align="right"></td>
			   <td width="60" align="right"></td>
			   <td width="60" align="right"></td>
			   <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
  <!-- 			   <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td> -->
  			   <!-- <td width="60" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td> -->
			   <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
			    <th width="5%">选择</th> 
                <th width="10%">车型</th> 
			    <th width="6%">车辆类型</th>
			    <th width="5%">单车发动机数量</th>
			    <th width="5%">单车电动机数量</th>
			    <th width="7%">COC打印模板</th>
			    <th width="7%">创建人</th>
			    <th width="7%">创建时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.cocModelTypePageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id" />' ></td>
			  		<td><s:property value="model" /></td>
			  		<td name='col_modelType'>
			  			<s:if test="modelType==1">燃油</s:if>
			  			<s:elseif test="modelType==2">非插电混动动力</s:elseif>
			  			<s:elseif test="modelType==3">纯电动</s:elseif>
			  			<s:elseif test="modelType==5">插电式混合动力</s:elseif>
			  		</td>
			  		<td><s:property value="engineNum" /></td>	
			  		<td><s:property value="motorNum" /></td>	
			  		<td name='col_cocPrintType'>
			  			<s:if test="cocPrintType==1">汽油</s:if>
			  			<s:elseif test="cocPrintType==2">非插电混动</s:elseif>
			  			<s:elseif test="cocPrintType==3">纯电动</s:elseif>
			  			<s:elseif test="cocPrintType==4">两用燃料</s:elseif>
			  			<s:elseif test="cocPrintType==5">插电混动</s:elseif>
			  			<s:elseif test="cocPrintType==6">NGCNG</s:elseif>
			  			<s:elseif test="cocPrintType==7">氢燃料电池</s:elseif>
			  		</td>
			  			
			  		<td><s:property value="creator" /></td>	
			  		<td><s:date  name="createdate" format="yyyy-MM-dd hh:mm:ss" /></td>			  		
			  		<td><a class='display' onclick="return false;" href='#' value='<s:property value="id" />'>查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocModelTypeAction.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocModelTypeAction.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocModelTypeAction.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocModelTypeAction.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<!--<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  
			  	<td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              
			  	<td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
				-->
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="typicality_neutral_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%" id="vtable" name="vtable">
	  	  
	    	<tr>
				<td><label><P>车型:</label></td>
				<td>
					<input type="text" id="model" name="model"  class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
				</td>
			</tr>
			<tr>
				<td><label><P>车辆类型:</label></td>
				<td>
					<select id="modelType" name="modelType" >
						<option value="">请选择</option>
						<option value="1">燃油</option>
						<option value="2">非插电混动动力</option>
						<option value="3">纯电动</option>
						<option value="5">插电式混合动力</option>
					</select>
				</td>
			</tr>
	    	<tr>
				<td><label><P>单车发动机数量:</label></td>
				<td>
					<input type="text" id="engineNum" name="engineNum"  class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
				</td>
			</tr>
			<tr>
				<td><label><P>单车电动机数量:</label></td>
				<td>
					<input type="text" id="motorNum" name="motorNum" class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
				</td>
			</tr>
			<tr>
				<td><label><P>COC打印模板:</label></td>
				<td>
					<select id="cocPrintType" name="cocPrintType"  onchange="printtypechange()">
						<option value="">请选择</option>
						<option value="1">汽油</option>
						<option value="2">非插电混动</option>
						<option value="3">纯电动</option>
						<option value="4">两用燃料</option>
						<option value="5">插电混动</option>
						<option value="6">NGCNG</option>
						<option value="7">氢燃料电池</option>
					</select>
				</td>
			</tr>				
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="car_color_mapping_display_dialog" title="查看窗口" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>车型:</label></td>
				<td>
					<input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
				</td>
			</tr>
			<tr>
				<td><label><P>车辆类型:</label></td>
				<td>
					<select id="modelType" name="modelType" >
						<option value="">请选择</option>
						<option value="1">燃油</option>
						<option value="2">非插电混动动力</option>
						<option value="3">纯电动</option>
						<option value="5">插电式混合动力</option>
					</select>
				</td>
			</tr>
	    	<tr>
				<td><label><P>单车发动机数量:</label></td>
				<td>
					<input type="text" id="engineNum" name="engineNum" class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
				</td>
			</tr>
			<tr>
				<td><label><P>单车电动机数量:</label></td>
				<td>
					<input type="text" id="motorNum" name="motorNum" class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
				</td>
			</tr>
			<tr>
				<td><label><P>COC打印模板:</label></td>
				<td>
					<select id="cocPrintType" name="cocPrintType"  >
						<option value="">请选择</option>
						<option value="1">汽油</option>
						<option value="2">非插电混动</option>
						<option value="3">纯电动</option>
						<option value="4">两用燃料</option>
						<option value="5">插电混动</option>
						<option value="6">NGCNG</option>
						<option value="7">氢燃料电池</option>
					</select>
				</td>
			</tr>					
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="tree_dialog" title="车型选择" style="display:none">
	<form id="operateForm" method='post'>
	<table>
		<tr>
			<td width="100%">
				<div id="treeboxbox_tree" style="width:446; height:300;background-color:#f5f5f5;border :1px solid Silver; overflow:auto; "/>
			</td>
		</tr>
		<input type="hidden" name="flg" id="flg"/>		
	</table>	
	</form>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<script type="text/javascript">
function showInfo(c1,zxcx,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}
	function doLog(str){			
		//var log = document.getElementById("logarea");
		//log.innerHTML = log.innerHTML+str+"</br>"
		//log.scrollTop = log.scrollHeight;
	}
	var tree;
	function getAllLeaftValue(){
		var valuesTemp=tree.getAllChecked();
		if(valuesTemp!=null&&valuesTemp!=""){
			var values=valuesTemp.split(",");
			var result="";
			for(var i=0;i<values.length;i++){
				var value=values[i];
				if(value.indexOf("##_p_")!=0){
					if(result!="")
						result+=",";
					result+=value.split(".")[1];
				}
			}
			return result;
		}
		return "";
	}
	function tonclick(id){			
		doLog("Item "+tree.getItemText(id)+" was selected");			
	};
	function tondblclick(id){			
		doLog("Item "+tree.getItemText(id)+" was doubleclicked");			
	};			
	function tondrag(id,id2){			
		return confirm("Do you want to move node "+tree.getItemText(id)+" to item "+tree.getItemText(id2)+"?");			
	};
	function tonopen(id,mode){			
		return confirm("Do you want to "+(mode>0?"close":"open")+" node "+tree.getItemText(id)+"?");			
	};
	
	function toncheck(id,state){			
		doLog("Item "+tree.getItemText(id)+" was " +((state)?"checked":"unchecked"));		
	};		
	function createTree(){
		tree=new dhtmlXTreeObject("treeboxbox_tree","100%","100%",0);
		tree.setImagePath("<%=path%>/images/csh_yellowbooks/");
		tree.enableCheckBoxes(1);
		tree.enableDragAndDrop(1);
		
		tree.enableThreeStateCheckboxes(true);
		tree.setOnDblClickHandler(tondblclick);
		tree.setDragHandler(tondrag);
		tree.loadXML('business/carModelSelect!getCarModel.action?type=<%=request.getAttribute("type")%>');		
	}	
	
	function printtypechange() {
		var dailog = $("#public_coc_dialog");
		var printtype = dailog.find("#printtype").val();
	}
</script>
</body>
</html>