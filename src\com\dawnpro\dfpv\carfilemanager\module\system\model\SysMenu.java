package com.dawnpro.dfpv.carfilemanager.module.system.model;

import java.util.ArrayList;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.common.tools.data.structure.tree.TreeData;

/**
 * SysResource entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SysMenu implements java.io.Serializable,TreeData{

	// Fields

	private String menuid;
	private String name;
	private String parentid;
	private String parentname;
	private String path;
	private String remark;
	private int orderflag = 1;
	private String creator;
	private String time;
	private int size = 0;
	private List<TreeData> childs = null;
	
	// Constructors

	/** default constructor */
	public SysMenu() {
		childs = new ArrayList<TreeData>();
	}

	/** minimal constructor */
	public SysMenu(String menuid, String creator, String time) {
		this.menuid = menuid;
		this.creator = creator;
		this.time = time;
	}

	/** full constructor */
	public SysMenu(String menuid, String name,
			String parentid, String path,int orderflag, String remark, String creator,
			String time) {
		this.menuid = menuid;
		this.name = name;
		this.parentid = parentid;
		this.path = path;
		this.orderflag = orderflag;
		this.remark = remark;
		this.creator = creator;
		this.time = time;
	}
	
	public int getSize() {
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}
	
	public String getParentname() {
		return parentname;
	}

	public void setParentname(String parentname) {
		this.parentname = parentname;
	}

	public String getMenuid() {
		return menuid;
	}

	public void setMenuid(String menuid) {
		this.menuid = menuid;
	}

	public String getParentid() {
		return parentid;
	}

	public void setParentid(String parentid) {
		this.parentid = parentid;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPath() {
		return this.path;
	}

	public void setPath(String path) {
		this.path = path;
	}
	
	public int getOrderflag() {
		return orderflag;
	}

	public void setOrderflag(int orderflag) {
		this.orderflag = orderflag;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}
	
	public List<TreeData> getChilds() {
		
		return childs;
	}

	public void addChild(TreeData child) {
		
		this.childs.add(child);
	}

}