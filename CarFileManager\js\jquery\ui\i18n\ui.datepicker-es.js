/* Inicializaci�n en espa�ol para la extensi�n 'UI date picker' para jQuery. */
/* Traducido por Vester (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['es'] = {
		closeText: 'Cerrar',
		prevText: '&#x3c;Ant',
		nextText: 'Sig&#x3e;',
		currentText: 'Hoy',
		monthNames: ['En<PERSON>','Febrero','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON>','Agosto','Septiembre','Octubre','Noviembre','Diciembre'],
		monthNamesShort: ['Ene','Feb','Mar','Abr','May','Jun',
		'Jul','Ago','Sep','Oct','Nov','Dic'],
		dayNames: ['<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Mi&eacute;rcoles','<PERSON><PERSON>','Viernes','S&aacute;bado'],
		dayNamesShort: ['Dom','Lun','<PERSON>','Mi&eacute;','Juv','Vie','S&aacute;b'],
		dayNamesMin: ['<PERSON>','Lu','Ma','Mi','Ju','Vi','S&aacute;'],
		dateFormat: 'dd/mm/yy', firstDay: 0,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['es']);
});