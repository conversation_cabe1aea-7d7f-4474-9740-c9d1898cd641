<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';
	
	
	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#production_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 330,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#production_dialog');
					allFields = $([]).add(parent.find('#model')).add(parent.find('#modelName')).add(parent.find('#pmodelCode').add(parent.find('#flag')));
				}
				allFields.removeClass('ui-state-error');
		        
				if(validate('#production_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
			        
					if(type=="add"){
						jQuery.ajax({
				            url: 'business/productionCarModelManager!isCarModelExist.action',
					        data: 'model='+$(this).find('#model').val(),
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#production_dialog');
					            if(json2Bean(data).json=="true"){
									updateTips(dialog.find('#validateTips'),'生产车型型号:['+dialog.find('#model').val()+']已经存在不能新增！');		

									var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
								}else{
									dialog.find('#createForm')[0].action="business/productionCarModelManager!addCarModel.action";
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						var dialog = $(this);
						dialog.find("#currentPage").val('<s:property value="#request.productionPage.currentPage" />');
						dialog.find("#qmodel").val($('#qmodel').val());
						dialog.find("#qpmodel").val($('#qpmodel').val());
						dialog.find("#qstate").val($('#qstate').val());
						
						$(this).find('#createForm')[0].action="business/productionCarModelManager!updateCarModel.action";
						$(this).find('#createForm')[0].submit();
					}
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			$('#production_dialog').find('#model').attr('readonly',false);
		}
		});

	$("#production_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 330,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
			}
		},
		close: function() {
		}
		});
	
	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 520,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
			}
			//,
			//'下一页': function() {
			//	wizardToDisplay();
			//}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$('#public_notice_dialog').find('#c1').attr('readonly',false);

			//wizardModel = "one";
			//wizardToDisplay();
		}
	});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				dialog.find("#currentPage").val('<s:property value="#request.productionPage.currentPage" />');
				dialog.find("#qmodel").val($('#qmodel').val());
				dialog.find("#qpmodel").val($('#qpmodel').val());
				dialog.find("#qstate").val($('#qstate').val());
				
				if(type=="delete"){					
					formObj[0].action = "business/productionCarModelManager!deleteCarModels.action";
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/productionCarModelManager!effectCarModel.action";
					formObj[0].submit();
				}else if(type=="published"){
					formObj[0].action = "business/productionCarModelManager!publishedCarModel.action";
					formObj[0].submit();
				}
			}
		}
	});
	
	$("#product_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var dialogObj = $("#production_dialog");
				var pmodelCodeObj = $(dialogObj).find("#model");
				var slcx = $("#slcxList").val();
				$(pmodelCodeObj).val(slcx);

				$(this).dialog('close');
			}
		}
	});
	$("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var dialogObj = $("#production_dialog");
				var pmodelCodeObj = $(dialogObj).find("#pmodelCode");
				var h_vin = $(dialogObj).find("#h_vin");
				var pmodelListObj = $("#pmodelList");
				var flagObj = $(dialogObj).find("#flag");
				var mpflagObj = $(dialogObj).find("#mpflag");
				
				
				var value = $(pmodelListObj).val().split(',');

				$(pmodelCodeObj).val(value[0]);
				$(flagObj).val(value[2]);
				$(h_vin).val(value[3]);
				parseVin(dialogObj, value[3]);

				$(mpflagObj).val(value[4]);
				$(this).dialog('close');
			}
		}
	});
	
	function parseVin(dialogObj, vin){//2012-12-7
		if(vin.length < 8){
			return;
		}
		var value=vin.substr(3,1);
		$(dialogObj).find("#brandCode").val(value);
		//setSelectValue($(dialogObj).find("#brandCode"),value);
		value=vin.substr(4,1);
		//setSelectValue($(dialogObj).find("#modelTypeCode"),value);
		$(dialogObj).find("#modelTypeCode").val(value);
		value=vin.substr(5,1);
		//setSelectValue($(dialogObj).find("#engineTypeCode"),value);
		$(dialogObj).find("#engineTypeCode").val(value);
		value=vin.substr(6,1);
		//setSelectValue($(dialogObj).find("#safetyRestraintCode"),value);
		$(dialogObj).find("#safetyRestraintCode").val(value);
		value=vin.substr(7,1);
		//setSelectValue($(dialogObj).find("#transmissionCode"),value);
		$(dialogObj).find("#transmissionCode").val(value);
		/*$("#production_dialog").find('#pmodelList option').each(function(){
			if($(this).val()==clpl_code)){
				$(this).attr("selected",true);
			}
		});*/
	}
	
	function setSelectValue(obj, value){
		$(obj).find("option").each(function(){
			if($(this).val()==value){
				$(this).attr("selected",true);
			}
		});
	}
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
    $("#confirm_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				type = "delete";
	   	   		messageObj = $('#operate_dialog');
	   	   		messageObj.find('#message').text('提示:确定删除选择的数据？');
	   	   		messageObj.dialog('open');
	   	   		//alert($(this).find('#c1').val());
	   	   		messageObj.find('#model').val($(this).find('#model').val()); 
	   	   		$(this).dialog('close'); 
			}		
		}
	});	
	
	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

    	if(index==1){
        	var params = "model="+id;
   			jQuery.ajax({
	            url: 'business/productionCarModelManager!carModelInfo.action',		           
	            data: params,
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#production_dialog');
	            	type = "add";		
					setDialogValue(dialogObj,model);
					dialogObj.data('title.dialog', '新增生产车型').dialog('open');	
					dialogObj.find('#model').val("");
	            }
	        });
   			
   	    	
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
   	  		type = "add";		
			$('#production_dialog').data('title.dialog', '新增生产车型').dialog('open');	
   	   	 }
	});

	 $("#update").click(function() {
	       	var id = "";
	       	var index = 0;
	       	var messageObj = null;
	        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
	      	checkedObj.each(function(){
	         	if(this.checked==true){
					index++;
					id = this.value; 
	             }
	      	});

	   		if(index==1){
	   			type = "update";
	   			var params = "model="+id;
	   			jQuery.ajax({
		            url: 'business/productionCarModelManager!carModelInfo.action',		           
		            data: params, 
			        type: 'POST',
			        dataType:'json', 
		            beforeSend: function() {
		            
		            },
		            error: function(request) {
		               
		            },
		            success: function(data) {
		            	var content = data.json;
			            var carObj = eval("("+content.toString()+")"); 
		            	var dialogObj = $('#production_dialog');
						if(carObj.state=="0"){
							setDialogValue(dialogObj,carObj);

			       	    	dialogObj.find('#model').attr('readonly',true);
			       	    	dialogObj.data('title.dialog', '修改生产车型').dialog('open');
						}else{
							messageObj = $('#message_dialog');
				   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
				   	   		messageObj.dialog('open');
						}
		            }
		        });
	   			
	   	    	
	   	   	}else if(index<1){
	   	   	 	messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
	   	   		messageObj.dialog('open');
	   	   	 }else if(index>1){
				messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
		   		messageObj.dialog('open');
	   	   	 }
	    	
		});

	$("#delete").click(function() {
			var id = "";
			var info = "";
			var tmp = "";
	       	var index = 0;
	       	var messageObj = null;
	        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
	      	checkedObj.each(function(){
	         	if(this.checked==true){
					index++;
					tmp = this.value.split(',');
					if(id==""){
						id = this.value;
						info = "生产车型型号:"+tmp[0]+" 版本号:"+tmp[1];
					}else{
						id = id+"&"+this.value; 
						info = info+"&"+"生产车型型号:"+tmp[0]+" 版本号:"+tmp[1];
					}
	             }
	      	});

	   		if(index==0){
	   			messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
	   	   		messageObj.dialog('open');
	   	   	}else if(index>1){
	   			messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:一次只能删除一条数据！');
	   	   		messageObj.dialog('open');	   	   		
	   	   	}else{
	   			type = "delete";
	   	   		messageObj = $('#confirm_dialog');
	   	   		messageObj.find('#message').text('提示:是否删除['+info+'] 共'+index+'条数据');
	   	   		messageObj.dialog('open');
	   	   		messageObj.find('#model').val(id);
	   	   	}
		
		});

	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "生产车型型号:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型型号:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		type = "effect";
     		var params = "model="+id;
   	   		jQuery.ajax({
            	url: 'business/productionCarModelManager!carModelInfo.action',		           
            	data: params, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
        
					if(carObj.state=="0"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共'+index+'条数据');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#model').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
            }
        });

   	   		
   	   	}
	});

	$("#published").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){ 
					id = this.value;
					info = "生产车型型号:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"@"+this.value; 
					info = info+"&"+"生产车型型号:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要提交到待发布的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   	   	   
     		type = "published";
     		var params = "model="+id;
     		
   	   		jQuery.ajax({
            	url: 'business/productionCarModelManager!carModelInfo.action',		           
            	data: params, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
            		var dialogObj = $('#production_dialog');
					if(carObj.state=="1"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定发布【'+info+'】！ 共'+index+'条数据,操作成功后会自动跳转到接口数据管理页面。');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#model').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能发布状态为[生效]的数据！');
			   	   		messageObj.dialog('open');
					}
            	}
        	});
   	   	}
	});
	
	$("#imp").click(function() {
		var parent = $('#production_dialog');
		var obj = $(parent).find('#pmodelCode');
		updateTips($(parent).find('#validateTips'),'');
		if(!checkLength(obj,0,20)||checkLength(obj,0,0)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且最大长度为20！');			
			return false;
		}
		jQuery.ajax({
	        url: 'business/publicNoticeCarModelManager!effectCarModelList.action?c1='+$(obj).val(),		           
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var jsonObj = eval("("+content.toString()+")"); 
	            
	            var pmodelListObj = $('#pmodelList');  
	            
	            $('#pmodelList option').remove();
	            for(var i=0;i<jsonObj.length;i++){
	            	//2012-12-7 带入vin
	    			$(pmodelListObj).append("<option value='"+jsonObj[i].id.c1+","+jsonObj[i].id.vercode+","+jsonObj[i].flag+","+jsonObj[i].c5+","+jsonObj[i].mpflag+"'>"+jsonObj[i].id.c1+"("+jsonObj[i].flag+")</option>");   
	            }
	        }
	    });
		var messageObj = $('#pubilc_notice_dialog');
  			messageObj.dialog('open');
  			
  			return ;
	});
	
	$("#impSlcx").click(function() {
		var parent = $('#production_dialog');
		var obj = $(parent).find('#pmodelCode');
		updateTips($(parent).find('#validateTips'),'');
		if(!checkLength(obj,0,20)||checkLength(obj,0,0)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且最大长度为20！');			
			return false;
		}
		jQuery.ajax({
	        url: 'business/cocVer!findGgcxFromBv01.action?c1='+$(obj).val(),	           
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var jsonObj = eval("("+content.toString()+")"); 
	            
	            var pmodelListObj = $('#pmodelList1');  
	            
	            $('#pmodelList1 option').remove();
	            $("<option value=''>请选择...</option>").appendTo("#pmodelList1");	
	            for(var i=0;i<jsonObj.length;i++){
	            	//2012-12-7 带入vin
	    			$(pmodelListObj).append("<option value='"+jsonObj[i].ggcx+","+jsonObj[i].vercode+"'>"+jsonObj[i].ggcx+"("+jsonObj[i].vercode+")</option>");   
	            }
	            
	            $(pmodelListObj).bind('change', function() {
	            	//alert($(this).val());
					changeSlcx($(this).val());
				});
	            
	    		var p1=$(pmodelListObj).val();
  	            changeSlcx(p1);
	        }
	    });
		var messageObj = $('#product_notice_dialog');
  			messageObj.dialog('open');
  			
  			return ;
	});
	
	function changeSlcx(param){
		jQuery.ajax({
	        url: 'business/publicNoticeCarModelManager!findSlcxFromBv01.action',		           
	        data: {"c1":param}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	         	var content1 = json2Bean(data).json;
	         	$("#slcxList").empty();//清空下拉框 
				$("<option value=''>请选择...</option>").appendTo("#slcxList");			         	
	         	if(content1!=''){
		         	var carObj = eval("("+content1.toString()+")");														
					$.each( carObj, function(i, n){
						//市区 c88, 市郊c89, 综合 c90
						if(n!=''){
							var opt="<option value='"+n +"'>"+n+"</option>";
							$(opt).appendTo("#slcxList")//添加下拉框的option
						}
										
					});
				}					
	        }
	    });			
	}
	
	$("#dis").click(function(){
		var pmodelListObj = $('#pubilc_notice_dialog').find('#pmodelList');
		if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
			var value = $(pmodelListObj).val().split(',');
			var param = "c1="+value[0]+","+value[1];
			jQuery.ajax({
		        url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
		        data: param, 
		        type: 'POST',
		        beforeSend: function() {
		        
		        },
		        error: function(request) {
		            
		        },
		        success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
		        	var dialogObj = $('#public_notice_display_dialog');
		        
		        	setDialogValue2(dialogObj,carObj);
									
		       	   	dialogObj.dialog('open');
		        }
		    });
		}
	});
	
	$("#query").click(function(){
		var qmodel = $('#qmodel').val();
		var qstate = $('#qstate').val();
		var qpmodel = $('#qpmodel').val();
		if(qmodel==""&&qstate==""&&qpmodel==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="productionCarModelManager.action?qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate+"&qpmodel="+encodeURI(encodeURI(qpmodel))+"&menuid="+menuid;
		}
	});
	
	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		var param = "model="+id;
		jQuery.ajax({
	        url: 'business/productionCarModelManager!carModelInfo.action',		           
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var jsonObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#production_display_dialog');
	        
	    		dialogObj.find('#model').val(jsonObj.id.model);
	    		dialogObj.find('#modelName').val(jsonObj.modelName);
	    		dialogObj.find('#pmodelCode').val(jsonObj.pmodelCode);
	    		dialogObj.find('#flag').val(jsonObj.flag);
	    		dialogObj.find('#mpflag').val(jsonObj.mpflag);
	    		dialogObj.find('#deliveryCapacity').val(jsonObj.deliveryCapacity);
	  			dialogObj.find("#brandName").val(jsonObj.brandName); 
	    		dialogObj.find("#decorationStaName").val(jsonObj.decorationStaName); 
	    		dialogObj.find("#emissionStaName").val(jsonObj.emissionStaName); 
	    		dialogObj.find("#engineTypeName").val(jsonObj.engineTypeName);
	    		dialogObj.find("#sunRoofName").val(jsonObj.isSunRoofName);
	    		dialogObj.find("#modelKindName").val(jsonObj.modelKindName);
	    		dialogObj.find("#modelTypeName").val(jsonObj.modelTypeName);
	    		dialogObj.find("#safetyRestraintName").val(jsonObj.safetyRestraintName);
	    		dialogObj.find("#transmissionName").val(jsonObj.transmissionName);
	   			
	       	   	dialogObj.dialog('open');

	        }
	    });

		return false;
	}

	$('#template').click(function() {
		location.href="productionCarModelManager!download.action";  
		
	});

	$('#import').click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>business/car/production_upload.jsp',winName,params) 
	});
	
	$('#export').click(function() {
		var qmodel = $('#qmodel').val();
		var qstate = $('#qstate').val();
		var qpmodel = $('#qpmodel').val();
		location.href="productionCarModelManager!exportData.action?qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate+"&qpmodel="+encodeURI(encodeURI(qpmodel));  
	});
	/*
	function checkVin(parent,pmodel){
		var bln=true;
		jQuery.ajax({
	        url: 'business/publicNoticeCarModelManager!effectCarModelList.action?c1='+$(parent).find('#pmodelCode').val(),		           
	        data: param, 
	        type: 'POST',
	        async: false,
	        beforeSend: function() {},
	        error: function(request) {},
	        success: function(data) {
	            var content = eval("("+data+")").json;
	            var jsonObj = eval("("+content.toString()+")"); 
				//alert(jsonObj);
	            for(var i=0;i<jsonObj.length;i++){
		            alert(jsonObj[i].flag);
		            alert($(parent).find('#flag').val());
	            	if(jsonObj[i].flag==$(parent).find('#flag').val()){
	            		tmp=jsonObj[i].c5;
	            	}
	            	
	            }
	            bln = compareVin(parent, tmp);
	            
	        }
	    });
	    return bln;			
	}
	
	function compareVin(dialogObj,vin){
		if(vin.length < 8){
			updateTips($(parent).find('#validateTips'),'公告车型的车辆识别代码为空，请检查 ！');	
			return false;
		}
		var value=vin.substr(3,1);
		if($(dialogObj).find("#brandCode").val() != value){
			updateTips($(parent).find('#validateTips'),'品牌名称与vin不匹配！');	
			return false;
		}
		value=vin.substr(4,1);
		if($(dialogObj).find("#modelTypeCode").val() != value){
			updateTips($(parent).find('#validateTips'),'公告车型的车辆识别代码为空，请检查 ！');	
			return false;
		}
		value=vin.substr(5,1);
		if($(dialogObj).find("#engineTypeCode").val() != value){
			updateTips($(parent).find('#validateTips'),'发动机类别与vin不匹配！');
			return false;	
		}
		value=vin.substr(6,1);
		if($(dialogObj).find("#safetyRestraintCode").val()!=value){
			updateTips($(parent).find('#validateTips'),'公告车型的车辆识别代码为空，请检查 ！');
			return false;
		}
		value=vin.substr(7,1);
		if($(dialogObj).find("#transmissionCode").val(){
			updateTips($(parent).find('#validateTips'),'公告车型的车辆识别代码为空，请检查 ！');
			return false;
		}
	}*/
	
	function validate(parent){
		var brandCode="";
		var modelTypeCode="";
		var engineTypeCode="";
		var safetyRestraintCode="";
		var transmissionCode="";
		var vin = "";//2012-12-7
			
		var obj = $(parent).find('#model');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产车型型号字段不能为空，最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#modelName');
		if(!checkLength(obj,1,40)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'名称字段最大长度为40！');			
			return false;
		}
		obj = $(parent).find('#pmodelCode');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'产品型号字段最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#flag');
		if(!checkLength(obj,1,2)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'识别码字段最大长度为2！');			
			return false;
		}obj = $(parent).find('#mpflag');
		if(!checkLength(obj,1,2)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'铭牌识别码字段最大长度为2！');			
			return false;
		}
		
		var tmp = "";
		jQuery.ajax({
	        url: 'business/publicNoticeCarModelManager!effectCarModelList.action?c1='+$(parent).find('#pmodelCode').val(),		           
	        data: param, 
	        type: 'POST',
	        async: false,
	        beforeSend: function() {},
	        error: function(request) {},
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var jsonObj = eval("("+content.toString()+")"); 
				//alert(jsonObj);
	            for(var i=0;i<jsonObj.length;i++){
		            //alert(jsonObj[i].flag);
		            //alert($(parent).find('#flag').val());
	            	if(jsonObj[i].flag==$(parent).find('#flag').val()){
	            		tmp=jsonObj[i].c5;
	            	}
	            }
	            
	        }
	    });
	    
	    //alert(tmp);
	    if(tmp.length<8){
	    	updateTips($(parent).find('#validateTips'),'公告车型的车辆识别代码为空，请检查 ！');		
	    	return false;
	    }
	    		
		obj = $(parent).find('#deliveryCapacity');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'排量字段最大长度为20！');			
			return false;
		}
		
		obj = $(parent).find('#brandCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择品牌名称！');			
			return false;
		}else if(tmp.substr(3,1)!=$(obj).val()){
			updateTips($(parent).find('#validateTips'),'品牌名称与vin不匹配！');			
			return false;			
		}
		
		
		obj = $(parent).find('#decorationStaCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择装饰等级！');			
			return false;
		}
		obj = $(parent).find('#emissionStaCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择排放标准！');			
			return false;
		}
		obj = $(parent).find('#engineTypeCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择发动机类别！');			
			return false;
		}else if(tmp.substr(5,1)!=$(obj).val()){
			updateTips($(parent).find('#validateTips'),'发动机类别与vin不匹配！');			
			return false;			
		}
		
		
		obj = $(parent).find('#isSunRoof');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择是否带天窗！');			
			return false;
		}
		obj = $(parent).find('#modelKindCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择车型系列代号名称！');			
			return false;
		}
		obj = $(parent).find('#modelTypeCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择车辆类别名称！');			
			return false;
		}else if(tmp.substr(4,1)!=$(obj).val()){
			updateTips($(parent).find('#validateTips'),'车辆类别与vin不匹配！');			
			return false;			
		}
		
		obj = $(parent).find('#safetyRestraintCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择约束类型！');			
			return false;
		}else if(tmp.substr(6,1)!=$(obj).val()){
			updateTips($(parent).find('#validateTips'),'约束类型与vin不匹配！');			
			return false;			
		}
		
		obj = $(parent).find('#transmissionCode');
		if(obj.val()==""){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请选择变速箱！');			
			return false;
		}else if(tmp.substr(7,1)!=$(obj).val()){
			updateTips($(parent).find('#validateTips'),'变速箱与vin不匹配！');			
			return false;			
		}
		
		//vin = brandCode+modelTypeCode+engineTypeCode+safetyRestraintCode+transmissionCode;
		//alert(vin);

		
		
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#model').val(jsonObj.id.model);
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		dialogObj.find('#modelName').val(jsonObj.modelName);
		dialogObj.find('#pmodelCode').val(jsonObj.pmodelCode);
		dialogObj.find('#flag').val(jsonObj.flag);
		dialogObj.find('#mpflag').val(jsonObj.mpflag);
		dialogObj.find('#deliveryCapacity').val(jsonObj.deliveryCapacity);
		dialogObj.find('#state').val(jsonObj.state);

		var selectObj = dialogObj.find("#brandCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.brandName){
				$(this).attr("selected","true"); 
			} 
		});
		
		selectObj = dialogObj.find("#decorationStaCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.decorationStaName){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#emissionStaCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.emissionStaName){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#engineTypeCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.engineTypeName){
				$(this).attr("selected","true"); 
			} 
		});
		
		
		selectObj = dialogObj.find("#isSunRoof"); 
		$(selectObj).children().each(function(){
			if ($(this).text()==jsonObj.isSunRoofName){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#modelKindCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.modelKindName){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#modelTypeCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.modelTypeName){
				$(this).attr("selected","true"); 
			} 
		});
		
		
		selectObj = dialogObj.find("#safetyRestraintCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.safetyRestraintName){
				$(this).attr("selected","true"); 
			} 
		});
		
		
		selectObj = dialogObj.find("#transmissionCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==jsonObj.transmissionName){
				$(this).attr("selected","true"); 
			} 
		});

	}

	function clear(dialogObj){
		dialogObj.find('#model').val("");
		dialogObj.find('#modelName').val("");
		dialogObj.find('#pmodelCode').val("");
		dialogObj.find('#flag').val("");
		dialogObj.find('#mpflag').val("");
		dialogObj.find('#deliveryCapacity').val("");
		dialogObj.find('#vercode').val("");
		dialogObj.find('#state').val("");
		
		dialogObj.find('#model').attr('readonly',false);
		dialogObj.find('#modelName').attr('readonly',false);
		dialogObj.find('#pmodelCode').attr('readonly',false);

		var value = "请选择";
		var selectObj = dialogObj.find("#brandCode"); 
		$(selectObj).children().each(function(){
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#decorationStaCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#emissionStaCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#engineTypeCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#isSunRoof"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#modelKindCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#modelTypeCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#safetyRestraintCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});

		selectObj = dialogObj.find("#transmissionCode"); 
		$(selectObj).children().each(function(){ 
			if ($(this).text()==value){
				$(this).attr("selected","true"); 
			} 
		});
	}
	
	function setDialogValue2(dialogObj,jsonObj){
		dialogObj.find('#c1').val(jsonObj.id.c1);
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		dialogObj.find('#flag').val(jsonObj.flag);

		dialogObj.find('#mpflag').val(jsonObj.mpflag);
		dialogObj.find('#c2').val(jsonObj.c2);
		dialogObj.find('#c3').val(jsonObj.c3);
		dialogObj.find('#c4').val(jsonObj.c4);
		dialogObj.find('#c27').val(jsonObj.c27);
		dialogObj.find('#c28').val(jsonObj.c28);
		dialogObj.find('#c29').val(jsonObj.c29);
		dialogObj.find('#c30').val(jsonObj.c30);
//		dialogObj.find('#c31').val(jsonObj.c31);
//		dialogObj.find('#c32').val(jsonObj.c32);
//		dialogObj.find('#c33').val(jsonObj.c33);
//		dialogObj.find('#c34').val(jsonObj.c34);
		dialogObj.find('#c5').val(jsonObj.c5);
		dialogObj.find('#c6').val(jsonObj.c6);
		dialogObj.find('#c7').val(jsonObj.c7);
		dialogObj.find('#c8').val(jsonObj.c8);
		dialogObj.find('#c9').val(jsonObj.c9);
		dialogObj.find('#c10').val(jsonObj.c10);
		dialogObj.find('#c11').val(jsonObj.c11);
//		dialogObj.find('#c18').val(jsonObj.c18);
		dialogObj.find('#c19').val(jsonObj.c19);
//		dialogObj.find('#c20').val(jsonObj.c20);
//		dialogObj.find('#c25').val(jsonObj.c25);
		dialogObj.find('#c26').val(jsonObj.c26);
		dialogObj.find('#c37').val(jsonObj.c37);
		dialogObj.find('#c38').val(jsonObj.c38);
		dialogObj.find('#c39').val(jsonObj.c39);
		dialogObj.find('#c40').val(jsonObj.c40);
		dialogObj.find('#c41').val(jsonObj.c41);
		dialogObj.find('#c42').val(jsonObj.c42);
//		dialogObj.find('#c43').val(jsonObj.c43);
//		dialogObj.find('#c44').val(jsonObj.c44);
//		dialogObj.find('#c45').val(jsonObj.c45);
		dialogObj.find('#c46').val(jsonObj.c46);
		dialogObj.find('#c47').val(jsonObj.c47);
//		dialogObj.find('#c48').val(jsonObj.c48);
		dialogObj.find('#c49').val(jsonObj.c49);
		dialogObj.find('#c50').val(jsonObj.c50);
		dialogObj.find('#c51').val(jsonObj.c51);
//		dialogObj.find('#c52').val(jsonObj.c52);
		dialogObj.find('#c53').val(jsonObj.c53);
//		dialogObj.find('#c54').val(jsonObj.c54);
//		dialogObj.find('#c55').val(jsonObj.c55);
//		dialogObj.find('#c56').val(jsonObj.c56);
		dialogObj.find('#c57').val(jsonObj.c57);
//		dialogObj.find('#c58').val(jsonObj.c58);
		dialogObj.find('#c59').val(jsonObj.c59);
		dialogObj.find('#c60').val(jsonObj.c60);
//		dialogObj.find('#c61').val(jsonObj.c61);
		dialogObj.find('#c62').val(jsonObj.c62);
		dialogObj.find('#c63').val(jsonObj.c63);
//		dialogObj.find('#c64').val(jsonObj.c64);
//		dialogObj.find('#c65').val(jsonObj.c65);
//		dialogObj.find('#c66').val(jsonObj.c66);
//		dialogObj.find('#c67').val(jsonObj.c67);
//		dialogObj.find('#c68').val(jsonObj.c68);
//		dialogObj.find('#c69').val(jsonObj.c69);
//		dialogObj.find('#c70').val(jsonObj.c70);
//		dialogObj.find('#c71').val(jsonObj.c71);
//		dialogObj.find('#c72').val(jsonObj.c72);
		dialogObj.find('#c73').val(jsonObj.c73);
		dialogObj.find('#c86').val(jsonObj.c86);
		dialogObj.find('#c88').val(jsonObj.c88);
		dialogObj.find('#c89').val(jsonObj.c89);
		dialogObj.find('#c79').val(jsonObj.c79);
		dialogObj.find('#c77').val(jsonObj.c77);
		dialogObj.find('#c80').val(jsonObj.c80);
		dialogObj.find('#c76').val(jsonObj.c76);
			
		dialogObj.find('#state').val(jsonObj.state);
	}
	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var qmodel = $('#qmodel').val();
			var qstate = $('#qstate').val();
			location.href="productionCarModelManager.action?currentPage="+$('#jump').val()+"&qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate+"&menuid="+menuid+"&qpmodel="+encodeURI(encodeURI(qpmodel));   
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qmodel = $('#qmodel').val();
				var qstate = $('#qstate').val();
				var qpmodel = $('#qpmodel').val();
				location.href=$(this).attr('value')+"&qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate+"&menuid="+menuid+"&qpmodel="+encodeURI(encodeURI(qpmodel));
		 });
	  });
	
});
$(document).ready(function() {
	
	 if($("#qstate").val()=='2'){
	 	$('#users-contain').find("[name='col_state']").each(function(){
	 	
	 		$(this).text('已发布');
	 	}); 
	 }
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

            //给这行添加class值为over，并且当鼠标一出该行时执行函数

            $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="8"><p>生产车型:<input type="text" id="qmodel" name='qmodel' class="text ui-widget-content " size="10" <s:if test="#request.qmodel!=null"> value="<s:property value="#request.qmodel" />"</s:if> />	
				公告车型:<input type="text" id="qpmodel" name='qpmodel' class="text ui-widget-content " size="10" <s:if test="#request.qpmodel!=null"> value="<s:property value="#request.qpmodel" />"</s:if> />	
				  状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr><td width="80%">
			  <td colspan="3"></td>
			  <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			  
  			  <td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
			   <td width="60" align="right"><button id="published" class="ui-button ui-state-default ui-corner-all">待发布</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="5%">选择</th> 
			    <th width="10%">生产车型型号</th>
			    <th width="10%">名称</th>
				<th width="11%">产品型号</th>	
				<th width="8%">识别码</th>	
				<th width="7%">状态</th>
				<th width="10%">版本</th>
				<th width="7%">创建人</th>
				<th width="11%">创建时间</th>
				<th width="11%">生效时间</th>
				<th width="11%">发布时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.productionPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.model" />,<s:property value="id.vercode" />' ></td>
			  		<td><s:property value="id.model" /></td>
			  		<td><s:property value="modelName" /></td>
   		      		<td><s:property value="pmodelCode" /></td>	
   		      		<td><s:property value="flag" /></td>				
			  		<td name='col_state'>
			  			<s:if test="state==0">未生效</s:if>
			  			<s:elseif test="state==1">生效</s:elseif>
			  			<s:elseif test="state==12">待发布</s:elseif>
			  			<s:elseif test="state==2">已发布</s:elseif>
			  		</td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="creator" /></td>		
			  		<td><s:property value="time" /></td>	
			  		<td><s:property value="activetime" /></td>	
			  		<td><s:property value="publishtime" /></td>	
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.model" />,<s:property value="id.vercode" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.productionPage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="productionCarModelManager.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.productionPage.currentPage==#request.productionPage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="productionCarModelManager.action?currentPage=<s:property value="#request.productionPage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.productionPage.currentPage>=#request.productionPage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="productionCarModelManager.action?currentPage=<s:property value="#request.productionPage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.productionPage.currentPage==#request.productionPage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="productionCarModelManager.action?currentPage=<s:property value="#request.productionPage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.productionPage.currentPage" />/总页数 <s:property value="#request.productionPage.maxPage" /> 总记录数 <s:property value="#request.productionPage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  <td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              <td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="production_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table width="100%">
	    		<tr>
	    		<td width="9%"><label><P>生产车型型号</label></td>
				<td width="17%"><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="14" />
				<button name="impSlcx" id="impSlcx" class="ui-button ui-state-default ui-corner-all" style="position:static">选择</button>
				</td>
				<td width="9%"><label><P>名称</label></td>
				<td width="17%"><input type="text" id="modelName" name="modelName" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td width="7%"><label><P>产品型号</label></td>
				<td width="19%"><input type="text" id="pmodelCode" name="pmodelCode" class="text ui-widget-content ui-corner-all" size="8" />
				<button name="imp" id="imp" class="ui-button ui-state-default ui-corner-all" style="position:static">导入</button>
				</td>
				<td width="8%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>
				<td width="8%"><label><P>排量</label></td>
				<td width="17%"><input type="text" id="deliveryCapacity" name="deliveryCapacity" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td width="7%"><label><P>品牌名称</label></td>
				<td width="16%"><s:select name="brandCode" list="#request.brandMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
				<td><label><P>装饰等级</label></td>
				<td><s:select name="decorationStaCode" list="#request.decorationStaMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
				<td><label><P>排放标准</label></td>
				<td><s:select name="emissionStaCode" list="#request.emissionStaMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate" cssStyle="font-size:8pt;"></s:select></td>
			</tr>
			<tr>
				<td><label><P>发动机类别</label></td>
				<td><s:select name="engineTypeCode" list="#request.engineTypeMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
				<td><label><P>是否带天窗</label></td>
				<td><s:select name="isSunRoof" list="#request.sunRoofMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>			
				<td><label><P>车型系列代号名称</label></td>
				<td><s:select name="modelKindCode" list="#request.modelKindMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
				<td><label><P>车辆类别名称</label></td>
				<td><s:select name="modelTypeCode" list="#request.modelTypeMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
			</tr>	
			<tr>
				<td><label><P>约束类型</label></td>
				<td><s:select name="safetyRestraintCode" list="#request.safetyRestraintMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
				<td><label><P>变速箱</label></td>
				<td><s:select name="transmissionCode" list="#request.transmissionMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select></td>
				<td ><label><P>铭牌识别码</label></td>
				<td ><input type="text" id="mpflag" name="mpflag" class="text ui-widget-content ui-corner-all" size="14" /></td>
				
			</tr>
		</Table>
		<input type='hidden' id='vercode' name='vercode'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		<input type='hidden' id='currentPage' name='currentPage' />
		<input type='hidden' id='qmodel' name='qmodel' />
		<input type='hidden' id='qstate' name='qstate' />
		</form>
	</fieldset>
</div>

<div id="production_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	  <table width="100%">
	    		<tr>
	    		<td width="9%"><label><P>生产车型型号</label></td>
				<td width="17%"><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>名称</label></td>
				<td width="17%"><input type="text" id="modelName" name="modelName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="pmodelCode" name="pmodelCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td width="8%"><label><P>排量</label></td>
				<td width="17%"><input type="text" id="deliveryCapacity" name="deliveryCapacity" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="7%"><label><P>品牌名称</label></td>
				<td width="16%"><input type="text" id="brandName" name="brandName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>装饰等级</label></td>
				<td><input type="text" id="decorationStaName" name="decorationStaName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>排放标准</label></td>
				<td><input type="text" id="emissionStaName" name="emissionStaName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>发动机类别</label></td>
				<td><input type="text" id="engineTypeName" name="engineTypeName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>是否带天窗</label></td>
				<td><input type="text" id="sunRoofName" name="sunRoofName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>			
				<td><label><P>车型系列代号名称</label></td>
				<td><input type="text" id="modelKindName" name="modelKindName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>车辆类别名称</label></td>
				<td><input type="text" id="modelTypeName" name="modelTypeName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>约束类型</label></td>
				<td><input type="text" id="safetyRestraintName" name="safetyRestraintName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>变速箱</label></td>
				<td><input type="text" id="transmissionName" name="transmissionName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>铭牌识别码</label></td>
				<td><input type="text" id="mpflag" name="mpflag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				
			</tr>		
		</Table>
		<input type='hidden' id='h_vin' name='h_vin'>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
		<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
		<input type='hidden' id='model' name='model'>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		<input type='hidden' id='currentPage' name='currentPage' />
		<input type='hidden' id='qmodel' name='qmodel' />
		<input type='hidden' id='qpmodel' name='qpmodel' />
		<input type='hidden' id='qstate' name='qstate' />
	</form>
</div>

<div id="pubilc_notice_dialog" title="操作窗口" style="display:none">
  	公告车型型号:<select id='pmodelList' name='pmodelList'></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<button name="dis" id="dis" class="ui-button ui-state-default ui-corner-all" style="position:static">查看</button>
</div>

<div id="product_notice_dialog" title="操作窗口" style="display:none">
  	公告车型型号:<select id='pmodelList1' name='pmodelList1'></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  	<br>
  	<br>
  	实例车型型号:<select id='slcxList' name='slcxList'></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div id="confirm_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='model' name='model'>
</div>

<div id="public_notice_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table id="one" width="100%">
	    		<tr>
	    		<td width="9%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>产品名称</label></td>
				<td width="17%"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>中文品牌</label></td>
				<td width="17%"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td width="7%"><label><P>英文品牌</label></td>
				<td width="16%"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业名称</label></td>
				<td><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业地址</label></td>
				<td><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>注册地址</label></td>
				<td><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>生产地址</label></td>
				<td><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>识别代号</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>前轮距</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>后轮距</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>发动机</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机企业</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机排量</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机功率</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>底盘类别</label></td>
				<td><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>批次</label></td>
				<td><input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>长</label></td>
				<td><input type="text" id="c37" name="c37" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>宽</label></td>
				<td><input type="text" id="c38"  name="c38" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>高</label></td>
				<td><input type="text" id="c39" name="c39" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>燃料种类</label></td>
				<td><input type="text" id="c40" name="c40" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>依据标准</label></td>
				<td><input type="text" id="c41" name="c41" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>转向形式</label></td>
				<td><input type="text" id="c42"  name="c42" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>		
				<td><label><P>轴数</label></td>
				<td><input type="text" id="c46"  name="c46" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>轴距</label></td>
				<td><input type="text" id="c47" name="c47" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎数</label></td>
				<td><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎规格</label></td>
				<td><input type="text" id="c50"  name="c50" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>总质量</label></td>
				<td><input type="text" id="c51" name="c51" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>			
				<td width="9%"><label><P>整备质量</label></td>
				<td width="17%"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>额定载客</label></td>
				<td><input type="text" id="c57" name="c57" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>接近离去角</label></td>
				<td><input type="text" id="c59" name="c59" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>前悬后悬</label></td>
				<td><input type="text" id="c60" name="c60" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>传动型式</label></td>
				<td><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>最高车速</label></td>
				<td><input type="text" id="c63" name="c63" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>起动方式</label></td>
				<td><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>排放水平</label></td>
				<td><input type="text" id="c86"  name="c86" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>油耗</label></td>
				<td><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轴荷</label></td>
				<td><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮数</label></td>
				<td><input type="text" id="c79" name="c79" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>	
			<tr>	
				<td><label><P>产品商标</label></td>
				<td><input type="text" id="c77" name="c77" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>联系人</label></td>
				<td><input type="text" id="c80" name="c80" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>法人代表</label></td>
				<td><input type="text" id="c76"  name="c76" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>铭牌识别码</label></td>
				<td><input type="text" id="mpflag"  name="mpflag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>			
		</Table>
		</form>
	</fieldset>
</div>

</body>
</html>