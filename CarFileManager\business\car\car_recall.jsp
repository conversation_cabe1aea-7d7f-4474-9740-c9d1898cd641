<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; /**position: relative;**/ text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	//$("#compare").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("compare")!=-1){
					$("#compare").attr("disabled", false);
				}
            }
        }
    });
    
	
	$("#compare").click(function(){
       	var dialogObj = $('#carRecall_compare_dialog');     	  	
       	dialogObj.data('title.dialog','召回参数版本比较').dialog('open');      	
	});
	
	$("#compare1").click(function(){
		if($('#carRecallVer1').val()=='' || $('#carRecallVer2').val()==''){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择比较的版本！');
   	   		messageObj.dialog('open');
   	   		return;
		}       	         	
		jQuery.ajax({
	        url: 'business/carRecall!findCompareCarRecallVer.action',		           
	        data: {'carRecallVer1' : $('#carRecallVer1').val(), 'carRecallVer2' : $('#carRecallVer2').val()}, 
	     	type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            alert("系统错误，请与管理员联系！");
	        },
	        success: function(data) {
	        	clearCompareDialog('2');
	         	var content = json2Bean(data).json;
	         	var carObj = eval("("+content.toString()+")"); 
	         	var dialogObj = $('#carRecall_compare_dialog');
		       	dialogObj.find("#tbl1").find("tr").each(function(i){
		       		var item = $(this).attr("name");
		       		if($.trim(carObj[0][item])!=$.trim(carObj[1][item])){//不同时
		       			$(this).attr("style","background-color: yellow;");
		       		}
		 			$(this).find("td:eq(1)").text(carObj[0][item]);      		
		 			$(this).find("td:eq(2)").text(carObj[1][item]);      		
		       	});
	
	        }
	    });       	  	       	
       	
	});	
	
	function clearCompareDialog(flg){
		//1:表示清除第二版本的选择数据 2：清楚版本比较数据
		if(flg=='1'){
		$("#carRecallVer2").empty();
		$("<option value=''>请选择...</option>").appendTo("#carRecallVer2");	
		$("#carRecallVer1").get(0).selectedIndex=0;	
		}else if(flg=='2'){
	        var dialogObj = $('#carRecall_compare_dialog');        
	       	dialogObj.find("#tbl1").find("tr").each(function(i){
	 			$(this).find("td:eq(1)").text('');      		
	 			$(this).find("td:eq(2)").text(''); 
	 			$(this).attr("style","background-color: #f1f9f3;"); 
	       	});		
       	}
	}
	$("#carRecall_compare_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 800,
		height: 555,
		modal: true,
		buttons: {
			'关闭': function() {
				$(this).dialog('close');
			}
		},
		close: function() {
			clearCompareDialog('1');				
			clearCompareDialog('2');				
		}				
	});
    
	
	$("#public_notice_dialog").dialog({bgiframe: true,autoOpen: false,width: 650,height: 400,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#public_notice_dialog');
					allFields = $([]).add(parent.find('#c1')).add(parent.find('#vercode'))
					.add(parent.find('#c2')).add(parent.find('#c3')).add(parent.find('#c4')).add(parent.find('#c5'))
					.add(parent.find('#c6')).add(parent.find('#c7')).add(parent.find('#c8')).add(parent.find('#c9'))
					.add(parent.find('#c10')).add(parent.find('#c11')).add(parent.find('#c12'))
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#public_notice_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					if(type=="add"){
						var c1=$(this).find('#c1').val();
						var vercode=$(this).find('#vercode').val();
						jQuery.ajax({
				            url: 'business/carRecall!isCarModelExist.action',
							data: {'c1' : c1,'vercode':vercode},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {       									
					            var dialog = $('#public_notice_dialog');
					            if(json2Bean(data).json=="true"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'车型:['+c1+'] 版本号:['+vercode+']已经存在不能新增！');		
								}else{
									var smodel= $('#smodel').val();
									var state= $('#state').val();
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].action="business/carRecall!addCarModel.action?smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						var smodel= $('#smodel').val();
						var state= $('#state').val();
						var currentPage=$('#currentPage_temp').val();
						$(this).find('#createForm')[0].action="business/carRecall!updateCarModel.action?smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&currentPage="+currentPage;
						$(this).find('#createForm')[0].submit();
					}else if(type=="effect"){
						c1=$(this).find('#c1').val();
						vercode=$(this).find('#vercode').val();
						var messageObj = $('#operate_dialog');
						jQuery.ajax({
				            url: 'business/carRecall!carModelInfo.action',		           
				            data: {'c1' : c1,'vercode':vercode},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {
					            var content = json2Bean(data).json;
					            var carObj = eval("("+content.toString()+")"); 
				            	var dialogObj = $('#public_notice_dialog');
				            	var dlgButton = $('.ui-dialog-buttonpane button');
								dlgButton.attr('disabled', false);
						        dlgButton.removeClass('ui-state-disabled');
								if(carObj.state=="1"){
									updateTips(dialogObj.find('#validateTips'),'车型:['+c1+'] 版本号:['+vercode+']已经生效！');
								}else{
									messageObj.find('#message').text('提示:确定生效车型:['+c1+'] 版本号:['+vercode+']共1条数据');
						   	   		messageObj.dialog('open');
						   	   		messageObj.find('#c1').val(c1+","+vercode);
								}
				            }
				        });
						
			   	   		
					}
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			clear($(this));
			updateTips($(this).find('#validateTips'),'');
		}
		});
	
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var smodel= $('#smodel').val();
				var state= $('#state').val();
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/carRecall!deleteCarModels.action?smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/carRecall!updateCarModelEffect.action?smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&currentPage="+currentPage;
					formObj[0].submit();
				}
			}
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
      	
    	if(index==1){
   			jQuery.ajax({
   				url: 'business/carRecall!carModelInfo.action',		           
	            data: {'c1' : id.split(",")[0],'vercode':id.split(",")[1]},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#public_notice_dialog');	          	
	            	type = "add";
					setDialogValue(dialogObj,model);
					dialogObj.data('title.dialog', '新增召回车辆').dialog('open');
					dialogObj.find('#vercode').val('');
					getMaxVercode();
	            }
	        });
   			
   	    	
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
   	   		type = "add";
			$('#public_notice_dialog').data('title.dialog', '新增召回车辆').dialog('open');	
   	   	 }
		
	});

	$('#public_notice_dialog').find('#c1').bind('keyup',function(event) { 
		if(type=="add")
			getMaxVercode();
    });

    function getMaxVercode()
    {
    	var obj = $('#public_notice_dialog').find('#c1');
		if(obj.val().gblen()==18){
			jQuery.ajax({
	            url: 'business/carRecall!getMaxCarModelVercode.action',		           
	            data: {"c1":obj.val()},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            		       
	            	var content = data.json;
		          	var dialogObj = $('#public_notice_dialog');
					dialogObj.find('#vercode').val(content);
	            }
	        });
		}  
    }
	
    $("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			jQuery.ajax({
	            url: 'business/carRecall!carModelInfo.action',		           
	            data: {'c1' : id.split(",")[0],'vercode':id.split(",")[1]},
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_notice_dialog');
					if(carObj.state=="1"){
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}else{
						setDialogValue(dialogObj,carObj);						
						dialogObj.find('#c1').attr('readonly',true);
		       	    	dialogObj.data('title.dialog', '修改车辆召回').dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var state = "";
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				state=$(this).parent().find("#state").val();
         		if(state=="1" || state=="9")	{
         			effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
         		}
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});
      	if(effIndex>0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效或是历史状态，不能删除！');
   	   		messageObj.dialog('open');   	   			
	   	}else if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#c1').val(id);
   	   	}
	});
	
	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var state = "";
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				state=$(this).parent().find("#state").val();
         		if(state=="1")	{
         			effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
         		}
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	}else if(effIndex>0){
    			messageObj = $('#message_dialog');
       	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效，不能再次生效！');
       	   		messageObj.dialog('open');	   			
    	}else{
     		type = "effect";
   	   		jQuery.ajax({
            	url: 'business/carRecall!carModelInfo.action',		           
            	data: {'c1' : id.split(",")[0],'vercode':id.split(",")[1]},
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
            		var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_notice_dialog');
					if(carObj.state=="1"){
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}else{
						setDialogValue(dialogObj,carObj);						
						dialogObj.find('input').attr('readonly',true);
		       	    	dialogObj.data('title.dialog', '生效召回车辆').dialog('open');
		       	    	$('.ui-dialog-buttonpane button').eq(3).attr('value','生效');
					}
            }
        });

   	   		
   	   	}
	});

	$("#query").click(function(){
		var smodel = $('#smodel').val();
		var state = $('#state').val();
		if(smodel==""&&state==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="carRecall.action?smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&menuid="+menuid;
		}
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
	        url: 'business/carRecall!carModelInfo.action',		           
	        data: {'c1' : id.split(",")[0],'vercode':id.split(",")[1]},
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#public_notice_dialog');
	        	type="look";
				setDialogValue(dialogObj,carObj);
				$('input').attr("readonly", true);
				$('.ui-dialog-buttonpane button[value="保存"]').css("display","none");
				dialogObj.data('title.dialog', '查看召回车辆').dialog('open');
	        }
	    });

		return false;
	}

	$('#template').click(function() {
		location.href="business/carRecall!download.action";  
		
	});

	$('#import').click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('car/car_recall_upload.jsp',winName,params) 
	});
	
	$('#export').click(function() {
		var smodel = $('#smodel').val();
		var state = $('#state').val();
		location.href="business/carRecall!exportData.action?smodel="+encodeURI(encodeURI(smodel))+"&state="+state; 
	});
	
	function validate(parent){
		var obj = $(parent).find('#c1');
		var max = 18;
		if(!checkLength(obj,18,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段长度必须为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#vercode');
		max = 21;
		if(!checkLength(obj,1,21)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段不能为空，最大长度为'+max+'！');			
			return false;
		}
		obj = $(parent).find('#model');
		max = 20;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c2');
		max = 30;
		if(!checkLength(obj,1,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段不能为空,最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c3');
		max = 20;
		if(!checkLength(obj,1,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段不能为空,最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c4');
		max = 100;
		if(!checkLength(obj,1,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段不能为空,最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c5');
		max = 100;
		if(!checkLength(obj,1,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段不能为空,最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c6');
		max = 100;
		if(!checkLength(obj,1,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段不能为空,最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c7');
		max = 50;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c8');
		max = 50;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c9');
		max = 50;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c10');
		max = 50;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c11');
		max = 50;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		obj = $(parent).find('#c12');
		max = 50;
		if(!checkLength(obj,0,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段最大长度为'+max+'！');			
			obj.focus();
			return false;
		}
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#c1').val(jsonObj.id.c1);
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		dialogObj.find('#model').val(jsonObj.model);
		dialogObj.find('#c2').val(jsonObj.c2);
		dialogObj.find('#c3').val(jsonObj.c3);
		dialogObj.find('#c4').val(jsonObj.c4);
		dialogObj.find('#c5').val(jsonObj.c5);
		dialogObj.find('#c6').val(jsonObj.c6);
		dialogObj.find('#c7').val(jsonObj.c7);
		dialogObj.find('#c8').val(jsonObj.c8);
		dialogObj.find('#c9').val(jsonObj.c9);
		dialogObj.find('#c10').val(jsonObj.c10);
		dialogObj.find('#c11').val(jsonObj.c11);
		dialogObj.find('#c12').val(jsonObj.c12);
		dialogObj.find('#state').val(jsonObj.state);
	}
	
	function clear(dialogObj){
		dialogObj.find('input').attr('value','');
		$('input').attr("readonly", false);
		dialogObj.find('#vercode').attr('readonly',true);
		$('.ui-dialog-buttonpane button').eq(3).attr('value','保存');
		$('.ui-dialog-buttonpane button').eq(3).css("display","");
		//$('.ui-dialog-buttonpane button[value="保存"]').css("display","");
		type = null;
	}

	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var smodel = $('#smodel').val();
			var state = $('#state').val();
			location.href="carRecall.action?currentPage="+$('#jump').val()+"&smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&menuid="+menuid;  
   		}
   		
    });
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var smodel = $('#smodel').val();
				var state = $('#state').val();
				location.href=$(this).attr('value')+"&smodel="+encodeURI(encodeURI(smodel))+"&state="+state+"&menuid="+menuid;
		 });
	});
    
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

           //给这行添加class值为over，并且当鼠标一出该行时执行函数

           $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="8"><p>
					生产车型：<input type="text" id="smodel" name="smodel" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.smodel!=null"> value="<s:property value="#request.smodel" />"</s:if>/>
					状态:<s:select name="state" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.state"></s:select> 
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr><td width="80%"></td>
			  <td colspan="3"></td>
			  <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
  			  <td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
  			  <td width="60" align="right"><button id="compare" class="ui-button ui-state-default ui-corner-all">版本比较</button></td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="6%">选择</th> 
			    <th width="10%">生产车型</th>
			    <th width="10%">版本</th>
			    <th width="10%">公告车型型号</th>
				<th width="7%">状态</th>
				<th width="10%">创建人</th>
				<th width="18%">创建时间</th>
				<th width="18%">生效时间</th>
				<th width="6%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.c1" />,<s:property value="id.vercode" />' >
			  			<input type="hidden" name="state" id="state" value='<s:property value="state"/>'></td>
			  		<td><s:property value="id.c1" /></td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="model" /></td>
			  		<td>
			  			<s:if test="state==1">生效</s:if>
			  			<s:else>
			  				<s:if test="state==9">历史</s:if>
			  				<s:else>未生效</s:else>
						</s:else>
			  		</td>
			  		<td><s:property value="creator" /></td>		
			  		<td><s:date name="createdate" format="yyyy-MM-dd HH:mm:ss"/></td>	
			  		<td><s:date name="effectTime" format="yyyy-MM-dd HH:mm:ss"/></td>
			  		<td><a class='display' onclick="return false;" href='#' value='<s:property value="id.c1" />,<s:property value="id.vercode" />'>查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecall.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecall.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecall.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecall.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value='<s:property value="#request.page.currentPage"/>'/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all" style="display:none">模板</button></td>           	  
			  	<td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all" style="display:none">导入</button></td>              
			  	<td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="public_notice_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>生产车型</label></td>
				<td><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="20" maxlength="18"/></td>
				<td><label><P>版本号</label></td>
				<td><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="25" readonly=true/></td>
			</tr>
			<tr>
			    <td><label><P>公告车型型号</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>车身型式</label></td>
				<td><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>
				<td><label><P>变速器型号</label></td>
				<td><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="18"/></td>	
				<td><label><P>安全带装备情况</label></td>
				<td><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>	
				<td><label><P>安全气囊装备情况</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>制动系统装备情况<br>（是否装备ABS）</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>
				<td><label><P>其它系统配置1</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>其它系统配置2</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>	
				<td><label><P>其它系统配置3</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>其它系统配置4</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>
				<td><label><P>其它系统配置5</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>其它系统配置6</label></td>
				<td><input type="text" id="c12" name="c12" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
		</Table>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="carRecall_compare_dialog" style="display:none;overflow-y:auto" >
	<p id="validateTips"></p>
	
	<form id="createForm" method="post" onSubmit="return false;">
		<table>
			<tr>
				<td ><label><P>版本号1：</label></td>
				<td name='ver1'><s:select cssClass='command' name="carRecallVer1" list="#request.carRecallVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.carRecallVer1" ></s:select></td>
				<td ><label><P>版本号2：</label></td>
				<td name='ver2'><s:select cssClass='command' name="carRecallVer2" list="#request.carRecallVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.carRecallVer1" ></s:select></td><td width="60" align="right"><button id="compare1" class="ui-button ui-state-default ui-corner-all">比较</button></td></td>
			</tr>			
		</table>
		<table id="tbl1" width="100%" >
			<tr name="model" style="background:#f1f9f3;">
				<td width="20%" ><label><P>公告车型型号</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c2" style="background:#f1f9f3;">
				<td width="20%" ><label><P>车身型式</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c3" style="background:#f1f9f3;">
				<td width="20%" ><label><P>变速器型号</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c4" style="background:#f1f9f3;">
				<td width="20%" ><label><P>安全带装备情况</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c5" style="background:#f1f9f3;">
				<td width="20%" ><label><P>安全气囊装备情况</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c6" style="background:#f1f9f3;">
				<td width="20%" ><label><P>制动系统装备情况(是否装备ABS)</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c7" style="background:#f1f9f3;">
				<td width="20%" ><label><P>其它系统配置1</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c8" style="background:#f1f9f3;">
				<td width="20%" ><label><P>其它系统配置2</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c9" style="background:#f1f9f3;">
				<td width="20%" ><label><P>其它系统配置3</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c10" style="background:#f1f9f3;">
				<td width="20%" ><label><P>其它系统配置4</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c11" style="background:#f1f9f3;">
				<td width="20%" ><label><P>其它系统配置5</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
			<tr name="c12" style="background:#f1f9f3;">
				<td width="20%" ><label><P>其它系统配置6</label></td><td width="40%" ></td><td width="40%" ></td>
			</tr>
		</table>
	</form>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='c1' name='c1'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>
<script type="text/javascript">
function findOtherVer(obj){
	var val=obj.value;
	$('#carRecallVer2').focus();
	jQuery.ajax({
        url: 'business/carRecall!findcarRecallVer.action',		           
        data: {'carRecallVer1' : val}, 
     	type: 'POST',
        beforeSend: function() {
        
        },
        error: function(XmlHttpRequest,textStatus, errorThrown) {
        	//alert(XmlHttpRequest.responseText);
            alert("系统错误，请与管理员联系！");
        },
        success: function(data) {
         	var content = json2Bean(data).json;
         	var carObj = eval("("+content.toString()+")");
			$("#carRecallVer2").empty();//清空下拉框 
			$("<option value=''>请选择...</option>").appendTo("#carRecallVer2");
			$.each( carObj, function(i, n){
				var tmp=n.id.c1+","+n.id.vercode;
				var stateName='';
				if(tmp!=val){
					if(n.state=='0'){
						stateName='未生效';
					}else if(n.state=='1'){
						stateName='生效';
					}else if(n.state=='9'){
						stateName='历史';
					}
					var opt="<option value='"+tmp +"'>"+n.id.c1+"("+n.id.vercode+stateName+")</option>";
					$(opt).appendTo("#carRecallVer2")//添加下拉框的option
				}				
			});			     	
        }
    });	
}
</script>