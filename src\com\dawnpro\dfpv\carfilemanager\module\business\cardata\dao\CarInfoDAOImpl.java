package com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;

public class CarInfoDAOImpl extends GenericHibernateDAOImpl<CarInfo> implements CarInfoDAO {

	public void addCarModel(CarInfo obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCarModel Method Error:",e);
		}
	}
	
	
	public void updateCarModel(CarInfo obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCarModel Method Error:",e);
		}
	}
	
	
	public void deleteCarModel(CarInfo obj) {
		try{
			this.delete(obj);
		}catch(Exception e){
			throw new DataAccessException("deleteCarModel Method Error:",e);
		}
	}
	
	public void deleteCarModels(CarInfo[] obj) {
		try{
			this.deleteBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("deleteCarModels Method Error:",e);
		}
	}
	
	public CarInfo loadCarModelObj(String vin) {
		CarInfo gg = null;
		try{
			List<CarInfo> result = findCarModel("from CarInfo g where g.vin=?", new Object[]{vin});
			if(result!=null && result.size()>0)
				gg=result.get(0);
		}catch(Exception e){
			throw new DataAccessException("loadCarModelObj Method Error:",e);
		}
		return gg;
	}

	public List<CarInfo> findCarModel(String sql) {
		List<CarInfo> results = null;
		try{
			results = this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findCarModel Method Error:",e);
		}
		return results;
	}

	public List<CarInfo> findCarModel(String sql, Object[] params) {
		List<CarInfo> results = null;
		try{
			results = this.find(sql,params);
		}catch(Exception e){
			throw new DataAccessException("findCarModel Method Error:",e);
		}
		return results;
	}

	public int updateCarModel(String sql,Object[] value){
		int num = 0;
		try{
			num = this.updateOrDeleteToHSQL(sql,value);
		}catch(Exception e){
			throw new DataAccessException("updateCarModel Method Error:",e);
		}
		return num;
	}
}
