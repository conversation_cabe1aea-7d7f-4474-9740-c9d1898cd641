<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.SysGasuploadLog" table="SYS_GASUPLOAD_LOG">
        <id name="id" type="java.lang.Long">
            <column name="ID" precision="22" scale="0" />
            <generator class="assigned" />
        </id>
        <property name="loginname" type="java.lang.String">
            <column name="LOGINNAME" length="20" />
        </property>
        <property name="ipaddress" type="java.lang.String">
            <column name="IPADDRESS" length="15" />
        </property>
        <property name="descript" type="java.lang.String">
            <column name="DESCRIPT" length="500" />
        </property>
        <property name="depdatetime" type="java.lang.String">
            <column name="DEPDATETIME" length="19" />
        </property>
        <property name="menuid" type="java.lang.String">
            <column name="MENUID" length="32" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="10" />
        </property>
        <property name="datadate" type="java.lang.String">
            <column name="DATADATE" length="19" />
        </property>
    </class>
</hibernate-mapping>
