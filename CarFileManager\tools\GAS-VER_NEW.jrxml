<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GAS-VER" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="c1" class="java.lang.String">
		<fieldDescription><![CDATA[c1]]></fieldDescription>
	</field>
	<field name="carConsistencyNumber" class="java.lang.String">
		<fieldDescription><![CDATA[carConsistencyNumber]]></fieldDescription>
	</field>
	<field name="cityStatus" class="java.lang.String">
		<fieldDescription><![CDATA[cityStatus]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="colligateStatus" class="java.lang.String">
		<fieldDescription><![CDATA[colligateStatus]]></fieldDescription>
	</field>
	<field name="date" class="java.lang.String">
		<fieldDescription><![CDATA[date]]></fieldDescription>
	</field>
	<field name="derailleurType" class="java.lang.String">
		<fieldDescription><![CDATA[derailleurType]]></fieldDescription>
	</field>
	<field name="driveType" class="java.lang.String">
		<fieldDescription><![CDATA[driveType]]></fieldDescription>
	</field>
	<field name="engineModel" class="java.lang.String">
		<fieldDescription><![CDATA[engineModel]]></fieldDescription>
	</field>
	<field name="enterprise" class="java.lang.String">
		<fieldDescription><![CDATA[enterprise]]></fieldDescription>
	</field>
	<field name="environsStatus" class="java.lang.String">
		<fieldDescription><![CDATA[environsStatus]]></fieldDescription>
	</field>
	<field name="fuelType" class="java.lang.String">
		<fieldDescription><![CDATA[fuelType]]></fieldDescription>
	</field>
	<field name="limitOne" class="java.lang.String">
		<fieldDescription><![CDATA[limitOne]]></fieldDescription>
	</field>
	<field name="limitTwo" class="java.lang.String">
		<fieldDescription><![CDATA[limitTwo]]></fieldDescription>
	</field>
	<field name="maxDesignSumQuality" class="java.lang.String">
		<fieldDescription><![CDATA[maxDesignSumQuality]]></fieldDescription>
	</field>
	<field name="model" class="java.lang.String">
		<fieldDescription><![CDATA[model]]></fieldDescription>
	</field>
	<field name="quality" class="java.lang.String">
		<fieldDescription><![CDATA[quality]]></fieldDescription>
	</field>
	<field name="range" class="java.lang.String">
		<fieldDescription><![CDATA[range]]></fieldDescription>
	</field>
	<field name="ratingPower" class="java.lang.String">
		<fieldDescription><![CDATA[ratingPower]]></fieldDescription>
	</field>
	<field name="remark" class="java.lang.String">
		<fieldDescription><![CDATA[remark]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="qebz" class="java.lang.String"/>
	<field name="gjbz" class="java.lang.String"/>
	<field name="dymb" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="6" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="5" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="100" splitType="Stretch">
			<staticText>
				<reportElement x="194" y="28" width="346" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="26" isBold="true" isItalic="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[汽车燃料消耗量标识]]></text>
			</staticText>
			<staticText>
				<reportElement x="194" y="58" width="346" height="30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="13" isBold="true" isItalic="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[AUTOMOBILE FUEL CONSUMPTION LABEL]]></text>
			</staticText>
			<textField>
				<reportElement x="51" y="28" width="143" height="60"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="32"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{qebz}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="610" splitType="Stretch">
			<staticText>
				<reportElement x="51" y="18" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[生产企业:]]></text>
			</staticText>
			<textField>
				<reportElement x="98" y="38" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{model}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="51" y="38" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆型号:]]></text>
			</staticText>
			<staticText>
				<reportElement x="51" y="58" width="60" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机型号:]]></text>
			</staticText>
			<textField>
				<reportElement x="110" y="58" width="88" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{engineModel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="80" y="78" width="118" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{range}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="51" y="78" width="30" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[排量:]]></text>
			</staticText>
			<textField>
				<reportElement x="110" y="98" width="88" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{derailleurType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="51" y="98" width="60" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[变速器类型:]]></text>
			</staticText>
			<textField>
				<reportElement x="120" y="118" width="78" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{quality}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="51" y="118" width="70" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[整车整备质量:]]></text>
			</staticText>
			<textField>
				<reportElement x="98" y="138" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="51" y="138" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[其它信息:]]></text>
			</staticText>
			<textField>
				<reportElement x="437" y="58" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fuelType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="437" y="98" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{driveType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="437" y="78" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ratingPower}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="390" y="118" width="78" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[最大设计总质量:]]></text>
			</staticText>
			<textField>
				<reportElement x="467" y="118" width="70" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{maxDesignSumQuality}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="390" y="78" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[额定功率:]]></text>
			</staticText>
			<staticText>
				<reportElement x="133" y="215" width="147" height="47"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" size="16" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[燃油消耗量]]></text>
			</staticText>
			<textField>
				<reportElement x="349" y="206" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cityStatus}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="302" y="206" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[市区工况:]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="226" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[综合工况:]]></text>
			</staticText>
			<textField>
				<reportElement x="349" y="226" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{colligateStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="349" y="246" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{environsStatus}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="302" y="246" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[市郊工况:]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="284" width="77" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[适用国家标准为]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="304" width="259" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[自2016年01月01日开始执行，]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="324" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[对应限值为]]></text>
			</staticText>
			<textField>
				<reportElement x="356" y="324" width="43" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{limitOne}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="400" y="324" width="54" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[L/100km;]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="58" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[燃料类型:]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="98" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[驱动类型:]]></text>
			</staticText>
			<textField>
				<reportElement x="110" y="566" width="202" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vin}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="63" y="576" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[备案号:]]></text>
			</staticText>
			<textField>
				<reportElement x="111" y="586" width="221" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{carConsistencyNumber}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="352" y="576" width="48" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[启用日期:]]></text>
			</staticText>
			<textField>
				<reportElement x="399" y="576" width="84" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{date}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="99" y="18" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{enterprise}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="379" y="284" width="100" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="黑体"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{gjbz}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="7" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="41" splitType="Stretch"/>
	</summary>
</jasperReport>
