<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="HB_24_CDHD"

		 language="groovy"		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="555"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="true"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="chzhqbimage" isForPrompting="false" class="java.lang.String">
		<defaultValueExpression ><![CDATA["‪C:\\110_0.jpg"]]></defaultValueExpression>
	</parameter>
	<parameter name="fdjimage" isForPrompting="false" class="java.lang.String">
		<defaultValueExpression ><![CDATA["‪C:\\110_0.jpg"]]></defaultValueExpression>
	</parameter>
	<parameter name="cjdate" isForPrompting="true" class="java.lang.String"/>
	<parameter name="showflag" isForPrompting="true" class="java.lang.Boolean"/>

	<field name="slcx" class="java.lang.String">
		<fieldDescription><![CDATA[slcx]]></fieldDescription>
	</field>
	<field name="vercode" class="java.lang.String">
		<fieldDescription><![CDATA[vercode]]></fieldDescription>
	</field>
	<field name="pzextno" class="java.lang.String">
		<fieldDescription><![CDATA[pzextno]]></fieldDescription>
	</field>
	<field name="jybgno" class="java.lang.String">
		<fieldDescription><![CDATA[jybgno]]></fieldDescription>
	</field>
	<field name="jyjgmc" class="java.lang.String">
		<fieldDescription><![CDATA[jyjgmc]]></fieldDescription>
	</field>
	<field name="ccjyjg" class="java.lang.String">
		<fieldDescription><![CDATA[ccjyjg]]></fieldDescription>
	</field>
	<field name="fdjxh" class="java.lang.String">
		<fieldDescription><![CDATA[fdjxh]]></fieldDescription>
	</field>
	<field name="fdjgc" class="java.lang.String">
		<fieldDescription><![CDATA[fdjgc]]></fieldDescription>
	</field>
	<field name="chzhq" class="java.lang.String">
		<fieldDescription><![CDATA[chzhq]]></fieldDescription>
	</field>
	<field name="chzhqgc" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqgc]]></fieldDescription>
	</field>
	<field name="ryzfkzqxh" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqxh]]></fieldDescription>
	</field>
	<field name="ryzfkzqgc" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqgc]]></fieldDescription>
	</field>
	<field name="egrxh" class="java.lang.String">
		<fieldDescription><![CDATA[egrxh]]></fieldDescription>
	</field>
	<field name="egrgc" class="java.lang.String">
		<fieldDescription><![CDATA[egrgc]]></fieldDescription>
	</field>
	<field name="obdxh" class="java.lang.String">
		<fieldDescription><![CDATA[obdxh]]></fieldDescription>
	</field>
	<field name="obdgc" class="java.lang.String">
		<fieldDescription><![CDATA[obdgc]]></fieldDescription>
	</field>
	<field name="iuprnox" class="java.lang.String">
		<fieldDescription><![CDATA[iuprnox]]></fieldDescription>
	</field>
	<field name="ecuxh" class="java.lang.String">
		<fieldDescription><![CDATA[ecuxh]]></fieldDescription>
	</field>
	<field name="ecugc" class="java.lang.String">
		<fieldDescription><![CDATA[ecugc]]></fieldDescription>
	</field>
	<field name="bsqxs" class="java.lang.String">
		<fieldDescription><![CDATA[bsqxs]]></fieldDescription>
	</field>
	<field name="bsqdws" class="java.lang.String">
		<fieldDescription><![CDATA[bsqdws]]></fieldDescription>
	</field>
	<field name="xsqxh" class="java.lang.String">
		<fieldDescription><![CDATA[xsqxh]]></fieldDescription>
	</field>
	<field name="xsqgc" class="java.lang.String">
		<fieldDescription><![CDATA[xsqgc]]></fieldDescription>
	</field>
	<field name="zyqxh" class="java.lang.String">
		<fieldDescription><![CDATA[zyqxh]]></fieldDescription>
	</field>
	<field name="zyqgc" class="java.lang.String">
		<fieldDescription><![CDATA[zyqgc]]></fieldDescription>
	</field>
	<field name="zlqxs" class="java.lang.String">
		<fieldDescription><![CDATA[zlqxs]]></fieldDescription>
	</field>
	<field name="fdjbs" class="java.lang.String">
		<fieldDescription><![CDATA[fdjbs]]></fieldDescription>
	</field>
	<field name="fdjpath" class="java.lang.String">
		<fieldDescription><![CDATA[fdjpath]]></fieldDescription>
	</field>
	<field name="chzhqbs" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqbs]]></fieldDescription>
	</field>
	<field name="chzhqpath" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqpath]]></fieldDescription>
	</field>
	<field name="ryzfkzqbs" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqbs]]></fieldDescription>
	</field>
	<field name="ryzfkzqpath" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqpath]]></fieldDescription>
	</field>
	<field name="ycgqbs" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqbs]]></fieldDescription>
	</field>
	<field name="ycgqpath" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqpath]]></fieldDescription>
	</field>
	<field name="egrbs" class="java.lang.String">
		<fieldDescription><![CDATA[egrbs]]></fieldDescription>
	</field>
	<field name="egrpath" class="java.lang.String">
		<fieldDescription><![CDATA[egrpath]]></fieldDescription>
	</field>
	<field name="ecubs" class="java.lang.String">
		<fieldDescription><![CDATA[ecubs]]></fieldDescription>
	</field>
	<field name="ecupath" class="java.lang.String">
		<fieldDescription><![CDATA[ecupath]]></fieldDescription>
	</field>
	<field name="xsqbs" class="java.lang.String">
		<fieldDescription><![CDATA[xsqbs]]></fieldDescription>
	</field>
	<field name="xsqpath" class="java.lang.String">
		<fieldDescription><![CDATA[xsqpath]]></fieldDescription>
	</field>
	<field name="zyqbs" class="java.lang.String">
		<fieldDescription><![CDATA[zyqbs]]></fieldDescription>
	</field>
	<field name="zyqpath" class="java.lang.String">
		<fieldDescription><![CDATA[zyqpath]]></fieldDescription>
	</field>
	<field name="mark" class="java.lang.String">
		<fieldDescription><![CDATA[mark]]></fieldDescription>
	</field>
	<field name="creator" class="java.lang.String">
		<fieldDescription><![CDATA[creator]]></fieldDescription>
	</field>
	<field name="state" class="java.lang.String">
		<fieldDescription><![CDATA[state]]></fieldDescription>
	</field>
	<field name="datadate" class="java.util.Date">
		<fieldDescription><![CDATA[datadate]]></fieldDescription>
	</field>
	<field name="ggcx" class="java.lang.String">
		<fieldDescription><![CDATA[ggcx]]></fieldDescription>
	</field>
	<field name="effectTime" class="java.lang.String">
		<fieldDescription><![CDATA[effectTime]]></fieldDescription>
	</field>
	<field name="fdjbsname" class="java.lang.String">
		<fieldDescription><![CDATA[fdjbsname]]></fieldDescription>
	</field>
	<field name="chzhqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqbsname]]></fieldDescription>
	</field>
	<field name="ryzfkzqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqbsname]]></fieldDescription>
	</field>
	<field name="ycgqbsname" class="java.lang.String"/>
	<field name="egrbsname" class="java.lang.String">
		<fieldDescription><![CDATA[egrbsname]]></fieldDescription>
	</field>
	<field name="ecubsname" class="java.lang.String">
		<fieldDescription><![CDATA[ecubsname]]></fieldDescription>
	</field>
	<field name="xsqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[xsqbsname]]></fieldDescription>
	</field>
	<field name="zyqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[zyqbsname]]></fieldDescription>
	</field>
	<field name="ycgqxh" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqxh]]></fieldDescription>
	</field>
	<field name="ycgqgc" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqgc]]></fieldDescription>
	</field>
	<field name="qzspfxh" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfxh]]></fieldDescription>
	</field>
	<field name="qzspfgc" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfgc]]></fieldDescription>
	</field>
	<field name="jyjg1" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg1]]></fieldDescription>
	</field>
	<field name="jyjg2" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg2]]></fieldDescription>
	</field>
	<field name="jyjg3" class="java.lang.String"/>
	<field name="jyjg4" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg4]]></fieldDescription>
	</field>
	<field name="jcjr1" class="java.lang.String"/>
	<field name="jcjr2" class="java.lang.String"/>
	<field name="jcjr3" class="java.lang.String"/>
	<field name="jcjr4" class="java.lang.String">
		<fieldDescription><![CDATA[jcjr4]]></fieldDescription>
	</field>
	<field name="hbtc" class="java.lang.String">
		<fieldDescription><![CDATA[hbtc]]></fieldDescription>
	</field>
	<field name="frdb" class="java.lang.String">
		<fieldDescription><![CDATA[frdb]]></fieldDescription>
	</field>
	<field name="hbzt" class="java.lang.String"/>
	<field name="hbfzscc" class="java.lang.String"/>
	<field name="dz" class="java.lang.String">
		<fieldDescription><![CDATA[dz]]></fieldDescription>
	</field>
	<field name="tel" class="java.lang.String">
		<fieldDescription><![CDATA[tel]]></fieldDescription>
	</field>
	<field name="hbdws" class="java.lang.String">
		<fieldDescription><![CDATA[hbdws]]></fieldDescription>
	</field>
	<field name="ecubb" class="java.lang.String">
		<fieldDescription><![CDATA[ecubb]]></fieldDescription>
	</field>
	<field name="rqhhq" class="java.lang.String">
		<fieldDescription><![CDATA[rqhhq]]></fieldDescription>
	</field>
	<field name="rqhhqgc" class="java.lang.String">
		<fieldDescription><![CDATA[rqhhqgc]]></fieldDescription>
	</field>
	<field name="rqpsdy" class="java.lang.String"/>
	<field name="rqpsdygc" class="java.lang.String"/>
	<field name="cnzl" class="java.lang.String"/>
	<field name="cnzlgc" class="java.lang.String"/>
	<field name="dcrl" class="java.lang.String"/>
	<field name="dhlc" class="java.lang.String">
		<fieldDescription><![CDATA[dhlc]]></fieldDescription>
	</field>
	<field name="ddjxh" class="java.lang.String"/>
	<field name="ddjgc" class="java.lang.String"/>
	<field name="ddjecu" class="java.lang.String">
		<fieldDescription><![CDATA[ddjecu]]></fieldDescription>
	</field>
	<field name="ddjecubbh" class="java.lang.String">
		<fieldDescription><![CDATA[ddjecubbh]]></fieldDescription>
	</field>
	<field name="ddjecuscc" class="java.lang.String"/>
	<field name="xxgkhao" class="java.lang.String"/>
	<field name="clxh" class="java.lang.String"/>
	<field name="hbsb" class="java.lang.String"/>
	<field name="qcfl" class="java.lang.String"/>
	<field name="cxsb" class="java.lang.String"/>
	<field name="clzzname" class="java.lang.String"/>
	<field name="field1" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="pfjd" class="java.lang.String">
		<fieldDescription><![CDATA[pfjd]]></fieldDescription>
	</field>
	<field name="scgdz" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="dbz" class="java.lang.String"/>
	<field name="fdjbh" class="java.lang.String"/>
	<field name="jzzl" class="java.lang.String"/>
	<field name="klbjqxh" class="java.lang.String"/>
	<field name="hbtc2" class="java.lang.String">
		<fieldDescription><![CDATA[hbtc2]]></fieldDescription>
	</field>
	<field name="tgxh" class="java.lang.String"/>
	<field name="jyjg5" class="java.lang.String"/>
	<field name="jcjr5" class="java.lang.String"/>
	<field name="klbjqscc" class="java.lang.String"/>
	<field name="tgscc" class="java.lang.String"/>
	<field name="pfbz" class="java.lang.String"/>
	<field name="hbzt2" class="java.lang.String"/>
	<field name="hbfzscc2" class="java.lang.String"/>
	<field name="vin" class="java.lang.String"/>
	<field name="ccjyjg2" class="java.lang.String"/>
	<field name="dpxh" class="java.lang.String"/>
	<field name="dpscc" class="java.lang.String"/>
	<field name="ktzljzl" class="java.lang.String"/>
	<field name="ktzljjzl" class="java.lang.String"/>
	<field name="xxjyyj" class="java.lang.String"/>
	<field name="xxjyjl" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="793"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="144"
						y="118"
						width="267"
						height="40"
						key="staticText">
							<printWhenExpression><![CDATA[$P{showflag}]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="方正大标宋简体" pdfFontName="STSong-Light" size="30" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[中华人民共和国]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="88"
						y="158"
						width="379"
						height="40"
						key="staticText">
							<printWhenExpression><![CDATA[$P{showflag}]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="方正大标宋简体" pdfFontName="STSong-Light" size="30" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[机动车环保信息随车清单]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="161"
						y="213"
						width="233"
						height="50"
						key="staticText">
							<printWhenExpression><![CDATA[$P{showflag}]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="方正大标宋简体" pdfFontName="STSong-Light" size="24" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[（中国第六阶段）]]></text>
				</staticText>
				<image  scaleImage="RetainShape" vAlign="Middle" hAlign="Center" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="197"
						y="657"
						width="160"
						height="22"
						key="image"/>
					<box>					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{fdjimage}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="217"
						y="592"
						width="120"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="黑体" pdfFontName="STSong-Light" size="15" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[信息公开编号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="142"
						y="620"
						width="270"
						height="20"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="Arial" pdfFontName="STSong-Light" size="15" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xxgkhao}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="199"
						y="684"
						width="156"
						height="20"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="Arial"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["VIN:"+$F{vin}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="117"
						y="540"
						width="320"
						height="38"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="宋体" pdfFontName="STSong-Light" size="25" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clzzname}]]></textFieldExpression>
				</textField>
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="794"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="31"
						y="179"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆制造商名称：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="127"
						width="82"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="140"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{hbsb}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="166"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[排放阶段：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="289"
						y="153"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车型的识别方法和位置：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="58"
						y="140"
						width="55"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[标：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="127"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="114"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第一部分 车辆信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="153"
						width="82"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[汽车分类：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="192"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产厂地址：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="13"
						y="22"
						width="433"
						height="66"
						key="textField"
						stretchType="RelativeToBandHeight"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["   " + $F{dbz}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="14"
						y="107"
						width="526"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<staticText>
					<reportElement
						x="13"
						y="235"
						width="137"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第二部分 检验信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="248"
						width="101"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[型式检验信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="261"
						width="76"
						height="12"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[依据的标准]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="179"
						y="261"
						width="66"
						height="12"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[检验机构]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="410"
						y="261"
						width="76"
						height="12"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[检验结论]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="273"
						width="100"
						height="13"
						key="staticText"
						isRemoveLineWhenBlank="true">
							<printWhenExpression><![CDATA[((($F{jcjr5} == null && $F{jyjg5} == null) || ($F{jcjr5}.equals("") && $F{jyjg5}.equals(""))) ? new Boolean(false) : new Boolean(true))]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[GB 18352.6-2016]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="295"
						width="100"
						height="13"
						key="staticText"
						isRemoveLineWhenBlank="true">
							<printWhenExpression><![CDATA[((($F{jcjr3} == null && $F{jyjg3} == null) || ($F{jcjr3}.equals("") && $F{jyjg3}.equals(""))) ? new Boolean(false) : new Boolean(true))]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[GB 1495-2002]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="316"
						width="130"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[下线检验信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="350"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第三部分 污染控制技术信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="363"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="415"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[催化转化器(TWC)型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="437"
						width="145"
						height="17"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[涂层/载体/封装生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="536"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[炭罐型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="549"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[氧传感器型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="562"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[EGR型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="575"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[OBD系统供应商：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="588"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ECU型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="32"
						y="601"
						width="144"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[变速器型式/档位数：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="614"
						width="144"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[排气消声器型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="640"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[增压器型号/生产企业：]]></text>
				</staticText>
				<image  scaleImage="RetainShape" vAlign="Middle" hAlign="Center" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="469"
						y="22"
						width="65"
						height="65"
						key="image"/>
					<box>					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{chzhqbimage}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="12"
						y="662"
						width="164"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第四部分 生产企业/进口企业信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="675"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[法定代表人：    ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="688"
						width="117"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[址： ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="701"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[联系电话：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="31"
						y="714"
						width="455"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["本清单内容及污染控制装置永久性标识相关信息可查询本企业官方网站（ http://www.dfmc.com.cn ）"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="12"
						y="727"
						width="419"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[和生态环境部机动车环保信息公开平台（https://www.vecc.org.cn）。]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="402"
						y="761"
						width="59"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆生产日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="273"
						width="219"
						height="22"
						key="textField"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jyjg5}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="295"
						width="219"
						height="22"
						key="textField"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jyjg3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="411"
						y="273"
						width="100"
						height="13"
						key="textField"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jcjr5}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="411"
						y="295"
						width="100"
						height="13"
						key="textField"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jcjr3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="675"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{frdb}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="688"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dz}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="701"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{tel}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="415"
						width="360"
						height="22"
						key="textField"
						stretchType="RelativeToTallestObject"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{chzhq}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="363"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{fdjxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="437"
						width="360"
						height="44"
						key="textField"
						stretchType="RelativeToBandHeight"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{hbtc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="536"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{tgxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="549"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ycgqxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="588"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ecuxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="575"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{obdgc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="601"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{bsqxs}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="614"
						width="360"
						height="26"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xsqxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="562"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{egrxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="640"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zyqxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="yyyy年MM月dd日" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="460"
						y="761"
						width="76"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{cjdate}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="153"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{qcfl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="166"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{pfjd}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="153"
						width="151"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{cxsb}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="179"
						width="139"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clzzname}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="192"
						width="139"
						height="22"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{scgdz}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="248"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[15]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="192"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[06]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="316"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[16]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="127"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[01]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="140"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[02]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="153"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[03]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="140"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[商]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="166"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[04]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="271"
						y="153"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[10]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="179"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[05]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="363"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[17]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="415"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[21]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="536"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[23]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="549"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[24]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="562"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[25]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="575"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[26]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="588"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[27]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="601"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[28]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="614"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[29]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="640"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[30]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="675"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[31]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="688"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[32]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="701"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[33]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="688"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[地]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="214"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[07]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="214"
						width="139"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{fdjbh}.substring($F{fdjbh}.indexOf('/')+1)]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="214"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动电机顺序号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="481"
						width="360"
						height="22"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{klbjqxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="503"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[涂层/载体/封装生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="481"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[22]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="503"
						width="360"
						height="33"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{hbtc2}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="481"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[颗粒捕集器(GPF)型号/生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="271"
						y="166"
						width="15"
						height="13"
						key="staticText-1"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[11]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="289"
						y="166"
						width="100"
						height="13"
						key="staticText-2"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[底盘型号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="166"
						width="151"
						height="13"
						key="textField-1"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dpxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="271"
						y="140"
						width="15"
						height="13"
						key="staticText-3"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[09]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="289"
						y="140"
						width="100"
						height="13"
						key="staticText-4"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[基准质量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="140"
						width="22"
						height="13"
						key="textField-2"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jzzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="411"
						y="140"
						width="36"
						height="13"
						key="staticText-5"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="289"
						y="179"
						width="100"
						height="13"
						key="staticText-6"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[底盘生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="179"
						width="151"
						height="13"
						key="textField-3"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dpscc}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="271"
						y="179"
						width="15"
						height="13"
						key="staticText-7"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[12]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="271"
						y="192"
						width="15"
						height="13"
						key="staticText-8"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[13]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="289"
						y="192"
						width="100"
						height="13"
						key="staticText-9"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车用空调制冷剂种类：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="192"
						width="151"
						height="13"
						key="textField-4"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ktzljzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="289"
						y="214"
						width="100"
						height="13"
						key="staticText-10"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车用空调制冷剂加注量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="214"
						width="151"
						height="13"
						key="textField-5"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ktzljjzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="271"
						y="214"
						width="15"
						height="13"
						key="staticText-11"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[14]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="32"
						y="329"
						width="100"
						height="13"
						key="staticText-12"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[依据：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="329"
						width="139"
						height="13"
						key="textField-6"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xxjyyj}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="329"
						width="100"
						height="13"
						key="staticText-13"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[结论：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="390"
						y="329"
						width="151"
						height="13"
						key="textField-7"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xxjyjl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="435"
						y="735"
						width="59"
						height="13"
						key="staticText-14"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[(企业盖章处)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="289"
						y="127"
						width="100"
						height="13"
						key="staticText-15"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机顺序号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="389"
						y="127"
						width="151"
						height="13"
						key="textField-8"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{fdjbh}.substring(0, $F{fdjbh}.indexOf('/'))]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="271"
						y="127"
						width="15"
						height="13"
						key="staticText-16"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[08]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="376"
						width="145"
						height="13"
						key="staticText-17"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动电机型号/生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="376"
						width="360"
						height="13"
						key="textField-9"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ddjxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="376"
						width="15"
						height="13"
						key="staticText-18"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[18]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="389"
						width="145"
						height="13"
						key="staticText-19"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[能量储存装置型号/生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="389"
						width="360"
						height="13"
						key="textField-10"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{cnzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="389"
						width="15"
						height="13"
						key="staticText-20"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[19]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="402"
						width="145"
						height="13"
						key="staticText-21"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[储能装置总储电量/纯电续航里程：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="402"
						width="360"
						height="13"
						key="textField-11"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dcrl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="402"
						width="15"
						height="13"
						key="staticText-22"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[20]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
