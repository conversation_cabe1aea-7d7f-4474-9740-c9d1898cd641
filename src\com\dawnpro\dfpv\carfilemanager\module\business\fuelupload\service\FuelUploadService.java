package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;

public interface FuelUploadService {
	public List<Object> findFuelUploadStateModify(Page page, Map<String,String> params) throws DataAccessException;
	public List<Object> pagination(Page page, Map<String,String> params) throws DataAccessException;
}
