<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse - Hibernate Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.TypicalityNeutralPhoto" table="TYPICALITY_NEUTRAL_PHOTO" lazy="false">
        <id name="id" type="java.lang.String">
            <column name="ID" length="50" />
            <generator class="assigned" />
        </id>
        <property name="dxcx" type="java.lang.String">
            <column name="DXCX" length="20" not-null="true" unique="true" />
        </property>
        <property name="zxcx" type="java.lang.String">
            <column name="ZXCX" length="512" />
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7" />
        </property>
        <property name="updatetime" type="java.util.Date">
            <column name="UPDATETIME" length="7" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
    </class>
</hibernate-mapping>
