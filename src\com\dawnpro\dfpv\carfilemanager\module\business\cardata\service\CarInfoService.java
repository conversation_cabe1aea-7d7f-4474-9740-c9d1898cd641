package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;

public interface CarInfoService {
	public void addCarModel(CarInfo obj)throws DataAccessException;
	
	public void updateCarModel(CarInfo obj)throws DataAccessException;
	
	public void deleteCarModel(CarInfo obj)throws DataAccessException;
	
	public void deleteCarModels(CarInfo[] obj)throws DataAccessException;
	
	public CarInfo loadCarModelObj(String vin)throws DataAccessException;
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
	
	public boolean isCarModelExist(String vin) throws DataAccessException;
	
	public void updafteCarModelState(CarInfo obj) throws DataAccessException;
	
	public void cancelCarModel(String vin) throws DataAccessException;
	
	public List<CarInfo> allCarModel() throws DataAccessException;
	
	public List<CarInfo> findCarModel(Object[] params) throws DataAccessException;
	
	public void updateModelVersion(CarInfo[] carInfos) throws DataAccessException;
	
	public boolean isStateT(String vin) throws DataAccessException;
	
	public List<CarInfo> findCarCustom(String sql) throws DataAccessException;
}
