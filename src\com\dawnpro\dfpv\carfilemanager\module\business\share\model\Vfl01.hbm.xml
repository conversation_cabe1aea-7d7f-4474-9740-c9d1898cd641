<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Vfl01" table="VFL_01">
        <id name="vin" type="java.lang.String">
            <column name="VIN" length="17" />
            <generator class="assigned" />
        </id>
        <property name="orderNo" type="java.lang.String">
            <column name="ORDER_NO" length="10" />
        </property>
        <property name="materialNo" type="java.lang.String">
            <column name="MATERIAL_NO" length="18" not-null="true" />
        </property>
        <property name="factory" type="java.lang.String">
            <column name="FACTORY" length="4" />
        </property>
        <property name="vehicleNo" type="java.lang.String">
            <column name="VEHICLE_NO" length="10" />
        </property>
        <property name="staition" type="java.lang.String">
            <column name="STAITION" length="10" not-null="true" />
        </property>
        <property name="date1" type="java.lang.String">
            <column name="DATE1" length="8" not-null="true" />
        </property>
        <property name="time1" type="java.lang.String">
            <column name="TIME1" length="6" />
        </property>
        <property name="reqSerialNo" type="java.lang.String">
                <column name="REQ_SERIAL_NO" length="54" />
        </property>
    </class>
</hibernate-mapping>
