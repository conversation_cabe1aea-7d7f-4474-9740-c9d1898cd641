package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CocPhoto entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CocPhoto implements java.io.Serializable {

	// Fields
	private CocPhotoId id;
	private String path;
	private String state;

	private String creator;
	private String time;
	private String effecttime;
	
	private String sourcepath;

	// Constructors

	/** default constructor */
	public CocPhoto() {
	}

	/** minimal constructor */
	public CocPhoto( CocPhotoId id) {
		this.id = id;
	}

	/** full constructor */
	public CocPhoto( CocPhotoId id, String path,String state, String creator,
			String time,String effecttime) {
		this.id = id;
		this.path = path;
		this.state = state;
		this.creator = creator;
		this.time = time;
		this.effecttime = effecttime;
	}

	// Property accessors

	public CocPhotoId getId() {
		return id;
	}

	public void setId(CocPhotoId id) {
		this.id = id;
	}

	public String getPath() {
		return this.path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}
	
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}
	
	public String getEffecttime() {
		return effecttime;
	}

	public void setEffecttime(String effecttime) {
		this.effecttime = effecttime;
	}

	public String getSourcepath() {
		return sourcepath;
	}

	public void setSourcepath(String sourcepath) {
		this.sourcepath = sourcepath;
	}
	
}