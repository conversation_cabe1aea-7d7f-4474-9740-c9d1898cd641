package com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcs;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcsId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.CocVerDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.HwCocVerDAO;

public class HwCocVerDAOImpl extends GenericHibernateDAOImpl<HwBaseCOCcs> 
	implements HwCocVerDAO {

	public void addCocVer(HwBaseCOCcs obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCocVer Method Error:",e);
		}
	}

	public void deleteCocVer(HwBaseCOCcs[] id) {
		try{
			this.deleteBatch(id);
		}catch(Exception e){
			throw new DataAccessException("deleteCocVer Method Error:",e);
		}
	}

	public HwBaseCOCcs loadCocVer(HwBaseCOCcsId id) {
		HwBaseCOCcs obj = null;
		try{
			obj = (HwBaseCOCcs)this.load(HwBaseCOCcs.class,id);
			if(obj != null)
			{
				obj.getC2();
			}
		}catch(Exception e){
			throw new DataAccessException("loadCocVer Method Error:",e);
		}
		return obj;
	}

	public void updateCocVer(HwBaseCOCcs obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCocVer Method Error:",e);
		}
	}

	public HwBaseCOCcs findMaxCocVerBySccx(String slcx) {
		HwBaseCOCcs obj = null;
		try{
			List<HwBaseCOCcs> results = null;
			String sql = "from HwBaseCOCcs t where t.id.slcx=? order by substr(t.id.vercode,length(t.id.vercode)-1,2) desc";
			Object[] params = new Object[]{slcx};
			results = this.find(sql,params);
			if(results != null && results.size()>0)
				obj = results.get(0);
		}catch(Exception e){
			throw new DataAccessException("findMaxCocVerBySccx Method Error:",e);
		}
		return obj;
	}

	public List<HwBaseCOCcs> findCocVer(String hql) {
		List<HwBaseCOCcs> list = null;
		try{
			list = this.find(hql);
		}catch(Exception e){
			throw new DataAccessException("findCocVer Method Error:",e);
		}
		return list;
	}

	public void addCocVer(HwBaseCOCcs[] obj) {
		if(obj == null ) return;
		try{
			this.addBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("addCocVer Method Error:",e);
		}
	}

	public void updateCocVer(HwBaseCOCcs[] obj) {
		if(obj == null ) return;
		try{
			this.updateBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCocVer Method Error:",e);
		}
	}

}
