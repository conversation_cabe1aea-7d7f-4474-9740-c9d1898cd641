package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * SysOperatePermissionId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SysOperatePermissionId implements java.io.Serializable {

	// Fields

	private String menuId;
	private String operateId;
	private String roleid;

	// Constructors

	/** default constructor */
	public SysOperatePermissionId() {
	}

	/** full constructor */
	public SysOperatePermissionId(String menuId, String operateId, String roleid) {
		this.menuId = menuId;
		this.operateId = operateId;
		this.roleid = roleid;
	}

	// Property accessors

	public String getMenuId() {
		return this.menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getOperateId() {
		return this.operateId;
	}

	public void setOperateId(String operateId) {
		this.operateId = operateId;
	}

	public String getRoleid() {
		return this.roleid;
	}

	public void setRoleid(String roleid) {
		this.roleid = roleid;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof SysOperatePermissionId))
			return false;
		SysOperatePermissionId castOther = (SysOperatePermissionId) other;

		return ((this.getMenuId() == castOther.getMenuId()) || (this
				.getMenuId() != null
				&& castOther.getMenuId() != null && this.getMenuId().equals(
				castOther.getMenuId())))
				&& ((this.getOperateId() == castOther.getOperateId()) || (this
						.getOperateId() != null
						&& castOther.getOperateId() != null && this
						.getOperateId().equals(castOther.getOperateId())))
				&& ((this.getRoleid() == castOther.getRoleid()) || (this
						.getRoleid() != null
						&& castOther.getRoleid() != null && this.getRoleid()
						.equals(castOther.getRoleid())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getMenuId() == null ? 0 : this.getMenuId().hashCode());
		result = 37 * result
				+ (getOperateId() == null ? 0 : this.getOperateId().hashCode());
		result = 37 * result
				+ (getRoleid() == null ? 0 : this.getRoleid().hashCode());
		return result;
	}

}