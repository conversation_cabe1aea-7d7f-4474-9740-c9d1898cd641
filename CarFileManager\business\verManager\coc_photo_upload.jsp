<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
    String sessionid = request.getSession().getId();
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .4em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/common.js"/></script>
<script type="text/javascript">	
$(document).ready(function() { 
	var validate = false;
	
	$("#uploadFile").uploadify({
		'uploader'       : 'js/jquery/uploadify/uploadify.swf',
		'script'         : 'business/cocPhotoManager!uploadPhoto.action;jsessionid=<%=sessionid%>',
		'cancelImg'      : 'js/jquery/uploadify/images/cancel.png',
		'fileDataName'	 : 'uploadFile',
		'queueID'        : 'fileQueue',
		'simUploadLimit' : 1,
		'queueSizeLimit' : 1,
		'auto'           : false,
		'multi'          : false,
		'fileDesc' 		 : 'JPEG' ,
		'fileExt'        : '*.jpg;',
		'onError'		 : function(event,queueId,fileObj,errorObj){
			alert("errtype:" + errorObj.type);
			alert(errorObj.info);
		},
		'onProgress'	 : function(event,queueId,fileObj){
			var filename = fileObj.name.substring(0,fileObj.name.indexOf("."));
			var suffix = fileObj.name.substring(fileObj.name.indexOf("."));
			//alert(suffix);
			if(filename.gblen()!=18&&validate==false){
				alert("车型照片文件名称，长度必须为18！");
				validate = true;
				$(this).uploadifyClearQueue();				
				uploadifyClearQueue();
				
				return ;
			}
			/*
			var index = new Array(2);
			index[0] = fileObj.name.indexOf("-");
			index[1] = fileObj.name.lastIndexOf("-");
			if((index[0]!=3||index[1]!=7)&&validate==false){
				alert("图片命名格式不符合要求！命名格式:XXX-XXX-XXXXXXX");
				validate = true;
				$(this).uploadifyClearQueue();
			}
			*/
			return;
		},
		'onComplete'     : function(event,queueId,fileObj,response,data){
			var info = eval("("+response+")").json;
			if(info=="error"){
				alert("上传车型照片出错！");
			}else{
				if(validate==false){
				    parent.window.close();
					var values = eval(response);
					var photoObj = self.opener.document.getElementById("photo");
					var photopathObj = self.opener.document.getElementById("photopath");
					var photonameObj = self.opener.document.getElementById("photoname");
					//alert("fileObj.name=" +fileObj.name);
					$(photoObj).attr("value",fileObj.name);
					//alert($(photoObj).attr("value"));
					$(photopathObj).val(values[0].fileName);
					//alert($(photopathObj).val());
					$(photoObj).html(fileObj.name);
					$(photonameObj).val(fileObj.name);
					//$(photoObj).show();
					photoObj.style.display="";
				}
				
			}
			
		}
	});
});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div id="fileQueue">
	<input type="file" name="uploadFile" id="uploadFile" />
</div>
<a href="javascript:$('#uploadFile').uploadifyUpload();">上传</>&nbsp;&nbsp;&nbsp;<a href="javascript:$('#uploadFile').uploadifyClearQueue();">取消</>
</body>
</html>