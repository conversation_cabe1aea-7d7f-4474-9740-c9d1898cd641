<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="autodetect" xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	
	<bean id="loginAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.LoginAction" lazy-init="true" scope="request" >
		<property name="sysUserService">
			<ref bean="sysUserService" />
		</property>
	</bean>
	
	<bean id="sysMenuNavigationAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysMenuNavigationAction" lazy-init="true" scope="request" >
		<property name="sysMenuService">
			<ref bean="sysMenuService" />
		</property>
	</bean>
	<bean id="sysUserAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysUserAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="sysUserService" />
		</property>
	</bean>
	<bean id="sysPasswordAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysPasswordAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="sysUserService" />
		</property>
	</bean>
	<bean id="sysRoleAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysRoleAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="sysRoleService" />
		</property>
		<property name="sysMenuService">
			<ref bean="sysMenuService" />
		</property>
		<property name="sysOperateService">
			<ref bean="sysOperateService" />
		</property>
	</bean>
	<bean id="sysLogAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysLogAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="sysLogService" />
		</property>
	</bean>
	<bean id="securityInterceptor" class="com.dawnpro.dfpv.carfilemanager.security.interceptor.UserLoginValidateInterceptor" lazy-init="true" scope="singleton" >
		<property name="service">
			<ref bean="sysLogService" />
		</property>
		<property name="menuDao">
			<ref bean="sSysMenuDAO" />
		</property>
		<property name="operationDao">
			<ref bean="sSysOperateDAO" />
		</property>
	</bean>
	<bean id="sysOperateAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysOperateAction" lazy-init="true" scope="request" >
		<property name="sysOperateService">
			<ref bean="sysOperateService" />
		</property>
	</bean>
	
	<bean id="sysPermissionAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysPermissionAction" lazy-init="true" scope="request" >
		<property name="sysPermissionService">
			<ref bean="sysPermissionService" />
		</property>
	</bean>
	<bean id="sysDataDictionaryAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysDataDictionaryAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="sysDataDictionaryService" />
		</property>
		<property name="typeService">
			<ref bean="sysDataDictionaryTypeService" />
		</property>
	</bean>
	
	<bean id="sysPrintTypeAction" class="com.dawnpro.dfpv.carfilemanager.module.system.action.SysPrintTypeAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="systemConfigParamsService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
</beans>
