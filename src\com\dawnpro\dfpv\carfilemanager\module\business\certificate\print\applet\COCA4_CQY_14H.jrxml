<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="COCA4_CQY_14H"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="555"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />


	<field name="barCodeImageUrl" class="java.lang.String">
		<fieldDescription><![CDATA[barCodeImageUrl]]></fieldDescription>
	</field>
	<field name="c1" class="java.lang.String">
		<fieldDescription><![CDATA[c1]]></fieldDescription>
	</field>
	<field name="c10" class="java.lang.String">
		<fieldDescription><![CDATA[c10]]></fieldDescription>
	</field>
	<field name="c100" class="java.lang.String">
		<fieldDescription><![CDATA[c100]]></fieldDescription>
	</field>
	<field name="c101" class="java.lang.String">
		<fieldDescription><![CDATA[c101]]></fieldDescription>
	</field>
	<field name="c102" class="java.lang.String">
		<fieldDescription><![CDATA[c102]]></fieldDescription>
	</field>
	<field name="c103" class="java.lang.String">
		<fieldDescription><![CDATA[c103]]></fieldDescription>
	</field>
	<field name="c104" class="java.lang.String">
		<fieldDescription><![CDATA[c104]]></fieldDescription>
	</field>
	<field name="c105" class="java.lang.String">
		<fieldDescription><![CDATA[c105]]></fieldDescription>
	</field>
	<field name="c106" class="java.lang.String">
		<fieldDescription><![CDATA[c106]]></fieldDescription>
	</field>
	<field name="c107" class="java.lang.String">
		<fieldDescription><![CDATA[c107]]></fieldDescription>
	</field>
	<field name="c108" class="java.lang.String">
		<fieldDescription><![CDATA[c108]]></fieldDescription>
	</field>
	<field name="c109" class="java.lang.String">
		<fieldDescription><![CDATA[c109]]></fieldDescription>
	</field>
	<field name="c11" class="java.lang.String">
		<fieldDescription><![CDATA[c11]]></fieldDescription>
	</field>
	<field name="c110" class="java.lang.String">
		<fieldDescription><![CDATA[c110]]></fieldDescription>
	</field>
	<field name="c112" class="java.lang.String">
		<fieldDescription><![CDATA[c112]]></fieldDescription>
	</field>
	<field name="c113" class="java.lang.String">
		<fieldDescription><![CDATA[c113]]></fieldDescription>
	</field>
	<field name="c114" class="java.lang.String">
		<fieldDescription><![CDATA[c114]]></fieldDescription>
	</field>
	<field name="c115" class="java.lang.String">
		<fieldDescription><![CDATA[c115]]></fieldDescription>
	</field>
	<field name="c116" class="java.lang.String">
		<fieldDescription><![CDATA[c116]]></fieldDescription>
	</field>
	<field name="c117" class="java.lang.String">
		<fieldDescription><![CDATA[c117]]></fieldDescription>
	</field>
	<field name="c118" class="java.lang.String">
		<fieldDescription><![CDATA[c118]]></fieldDescription>
	</field>
	<field name="c119" class="java.lang.String">
		<fieldDescription><![CDATA[c119]]></fieldDescription>
	</field>
	<field name="c12" class="java.lang.String">
		<fieldDescription><![CDATA[c12]]></fieldDescription>
	</field>
	<field name="c120" class="java.lang.String">
		<fieldDescription><![CDATA[c120]]></fieldDescription>
	</field>
	<field name="c121" class="java.lang.String">
		<fieldDescription><![CDATA[c121]]></fieldDescription>
	</field>
	<field name="c13" class="java.lang.String">
		<fieldDescription><![CDATA[c13]]></fieldDescription>
	</field>
	<field name="c14" class="java.lang.String">
		<fieldDescription><![CDATA[c14]]></fieldDescription>
	</field>
	<field name="c15" class="java.lang.String">
		<fieldDescription><![CDATA[c15]]></fieldDescription>
	</field>
	<field name="c16" class="java.lang.String">
		<fieldDescription><![CDATA[c16]]></fieldDescription>
	</field>
	<field name="c17" class="java.lang.String">
		<fieldDescription><![CDATA[c17]]></fieldDescription>
	</field>
	<field name="c18" class="java.lang.String">
		<fieldDescription><![CDATA[c18]]></fieldDescription>
	</field>
	<field name="c19" class="java.lang.String">
		<fieldDescription><![CDATA[c19]]></fieldDescription>
	</field>
	<field name="c2" class="java.lang.String">
		<fieldDescription><![CDATA[c2]]></fieldDescription>
	</field>
	<field name="c20" class="java.lang.String">
		<fieldDescription><![CDATA[c20]]></fieldDescription>
	</field>
	<field name="c21" class="java.lang.String">
		<fieldDescription><![CDATA[c21]]></fieldDescription>
	</field>
	<field name="c22" class="java.lang.String">
		<fieldDescription><![CDATA[c22]]></fieldDescription>
	</field>
	<field name="c23" class="java.lang.String">
		<fieldDescription><![CDATA[c23]]></fieldDescription>
	</field>
	<field name="c24" class="java.lang.String">
		<fieldDescription><![CDATA[c24]]></fieldDescription>
	</field>
	<field name="c25" class="java.lang.String">
		<fieldDescription><![CDATA[c25]]></fieldDescription>
	</field>
	<field name="c26" class="java.lang.String">
		<fieldDescription><![CDATA[c26]]></fieldDescription>
	</field>
	<field name="c27" class="java.lang.String">
		<fieldDescription><![CDATA[c27]]></fieldDescription>
	</field>
	<field name="c28" class="java.lang.String">
		<fieldDescription><![CDATA[c28]]></fieldDescription>
	</field>
	<field name="c29" class="java.lang.String">
		<fieldDescription><![CDATA[c29]]></fieldDescription>
	</field>
	<field name="c3" class="java.lang.String">
		<fieldDescription><![CDATA[c3]]></fieldDescription>
	</field>
	<field name="c30" class="java.lang.String">
		<fieldDescription><![CDATA[c30]]></fieldDescription>
	</field>
	<field name="c31" class="java.lang.String">
		<fieldDescription><![CDATA[c31]]></fieldDescription>
	</field>
	<field name="c32" class="java.lang.String">
		<fieldDescription><![CDATA[c32]]></fieldDescription>
	</field>
	<field name="c33" class="java.lang.String">
		<fieldDescription><![CDATA[c33]]></fieldDescription>
	</field>
	<field name="c34" class="java.lang.String">
		<fieldDescription><![CDATA[c34]]></fieldDescription>
	</field>
	<field name="c35" class="java.lang.String">
		<fieldDescription><![CDATA[c35]]></fieldDescription>
	</field>
	<field name="c36" class="java.lang.String">
		<fieldDescription><![CDATA[c36]]></fieldDescription>
	</field>
	<field name="c37" class="java.lang.String">
		<fieldDescription><![CDATA[c37]]></fieldDescription>
	</field>
	<field name="c38" class="java.lang.String">
		<fieldDescription><![CDATA[c38]]></fieldDescription>
	</field>
	<field name="c39" class="java.lang.String">
		<fieldDescription><![CDATA[c39]]></fieldDescription>
	</field>
	<field name="c4" class="java.lang.String">
		<fieldDescription><![CDATA[c4]]></fieldDescription>
	</field>
	<field name="c40" class="java.lang.String">
		<fieldDescription><![CDATA[c40]]></fieldDescription>
	</field>
	<field name="c41" class="java.lang.String">
		<fieldDescription><![CDATA[c41]]></fieldDescription>
	</field>
	<field name="c42" class="java.lang.String">
		<fieldDescription><![CDATA[c42]]></fieldDescription>
	</field>
	<field name="c43" class="java.lang.String">
		<fieldDescription><![CDATA[c43]]></fieldDescription>
	</field>
	<field name="c44" class="java.lang.String">
		<fieldDescription><![CDATA[c44]]></fieldDescription>
	</field>
	<field name="c45" class="java.lang.String">
		<fieldDescription><![CDATA[c45]]></fieldDescription>
	</field>
	<field name="c46" class="java.lang.String">
		<fieldDescription><![CDATA[c46]]></fieldDescription>
	</field>
	<field name="c47" class="java.lang.String">
		<fieldDescription><![CDATA[c47]]></fieldDescription>
	</field>
	<field name="c48" class="java.lang.String">
		<fieldDescription><![CDATA[c48]]></fieldDescription>
	</field>
	<field name="c49" class="java.lang.String">
		<fieldDescription><![CDATA[c49]]></fieldDescription>
	</field>
	<field name="c5" class="java.lang.String">
		<fieldDescription><![CDATA[c5]]></fieldDescription>
	</field>
	<field name="c50" class="java.lang.String">
		<fieldDescription><![CDATA[c50]]></fieldDescription>
	</field>
	<field name="c51" class="java.lang.String">
		<fieldDescription><![CDATA[c51]]></fieldDescription>
	</field>
	<field name="c52" class="java.lang.String">
		<fieldDescription><![CDATA[c52]]></fieldDescription>
	</field>
	<field name="c53" class="java.lang.String">
		<fieldDescription><![CDATA[c53]]></fieldDescription>
	</field>
	<field name="c54" class="java.lang.String">
		<fieldDescription><![CDATA[c54]]></fieldDescription>
	</field>
	<field name="c55" class="java.lang.String">
		<fieldDescription><![CDATA[c55]]></fieldDescription>
	</field>
	<field name="c56" class="java.lang.String">
		<fieldDescription><![CDATA[c56]]></fieldDescription>
	</field>
	<field name="c57" class="java.lang.String">
		<fieldDescription><![CDATA[c57]]></fieldDescription>
	</field>
	<field name="c58" class="java.lang.String">
		<fieldDescription><![CDATA[c58]]></fieldDescription>
	</field>
	<field name="c59" class="java.lang.String">
		<fieldDescription><![CDATA[c59]]></fieldDescription>
	</field>
	<field name="c6" class="java.lang.String">
		<fieldDescription><![CDATA[c6]]></fieldDescription>
	</field>
	<field name="c60" class="java.lang.String">
		<fieldDescription><![CDATA[c60]]></fieldDescription>
	</field>
	<field name="c61" class="java.lang.String">
		<fieldDescription><![CDATA[c61]]></fieldDescription>
	</field>
	<field name="c62" class="java.lang.String">
		<fieldDescription><![CDATA[c62]]></fieldDescription>
	</field>
	<field name="c63" class="java.lang.String">
		<fieldDescription><![CDATA[c63]]></fieldDescription>
	</field>
	<field name="c64" class="java.lang.String">
		<fieldDescription><![CDATA[c64]]></fieldDescription>
	</field>
	<field name="c65" class="java.lang.String">
		<fieldDescription><![CDATA[c65]]></fieldDescription>
	</field>
	<field name="c66" class="java.lang.String">
		<fieldDescription><![CDATA[c66]]></fieldDescription>
	</field>
	<field name="c67" class="java.lang.String">
		<fieldDescription><![CDATA[c67]]></fieldDescription>
	</field>
	<field name="c68" class="java.lang.String">
		<fieldDescription><![CDATA[c68]]></fieldDescription>
	</field>
	<field name="c69" class="java.lang.String">
		<fieldDescription><![CDATA[c69]]></fieldDescription>
	</field>
	<field name="c7" class="java.lang.String">
		<fieldDescription><![CDATA[c7]]></fieldDescription>
	</field>
	<field name="c70" class="java.lang.String">
		<fieldDescription><![CDATA[c70]]></fieldDescription>
	</field>
	<field name="c71" class="java.lang.String">
		<fieldDescription><![CDATA[c71]]></fieldDescription>
	</field>
	<field name="c72" class="java.lang.String">
		<fieldDescription><![CDATA[c72]]></fieldDescription>
	</field>
	<field name="c73" class="java.lang.String">
		<fieldDescription><![CDATA[c73]]></fieldDescription>
	</field>
	<field name="c74" class="java.lang.String">
		<fieldDescription><![CDATA[c74]]></fieldDescription>
	</field>
	<field name="c75" class="java.lang.String">
		<fieldDescription><![CDATA[c75]]></fieldDescription>
	</field>
	<field name="c76" class="java.lang.String">
		<fieldDescription><![CDATA[c76]]></fieldDescription>
	</field>
	<field name="c77" class="java.lang.String">
		<fieldDescription><![CDATA[c77]]></fieldDescription>
	</field>
	<field name="c78" class="java.lang.String">
		<fieldDescription><![CDATA[c78]]></fieldDescription>
	</field>
	<field name="c79" class="java.lang.String">
		<fieldDescription><![CDATA[c79]]></fieldDescription>
	</field>
	<field name="c8" class="java.lang.String">
		<fieldDescription><![CDATA[c8]]></fieldDescription>
	</field>
	<field name="c80" class="java.lang.String">
		<fieldDescription><![CDATA[c80]]></fieldDescription>
	</field>
	<field name="c81" class="java.lang.String">
		<fieldDescription><![CDATA[c81]]></fieldDescription>
	</field>
	<field name="c82" class="java.lang.String">
		<fieldDescription><![CDATA[c82]]></fieldDescription>
	</field>
	<field name="c83" class="java.lang.String">
		<fieldDescription><![CDATA[c83]]></fieldDescription>
	</field>
	<field name="c84" class="java.lang.String">
		<fieldDescription><![CDATA[c84]]></fieldDescription>
	</field>
	<field name="c85" class="java.lang.String">
		<fieldDescription><![CDATA[c85]]></fieldDescription>
	</field>
	<field name="c86" class="java.lang.String">
		<fieldDescription><![CDATA[c86]]></fieldDescription>
	</field>
	<field name="c87" class="java.lang.String">
		<fieldDescription><![CDATA[c87]]></fieldDescription>
	</field>
	<field name="c88" class="java.lang.String">
		<fieldDescription><![CDATA[c88]]></fieldDescription>
	</field>
	<field name="c89" class="java.lang.String">
		<fieldDescription><![CDATA[c89]]></fieldDescription>
	</field>
	<field name="c9" class="java.lang.String">
		<fieldDescription><![CDATA[c9]]></fieldDescription>
	</field>
	<field name="c90" class="java.lang.String">
		<fieldDescription><![CDATA[c90]]></fieldDescription>
	</field>
	<field name="c91" class="java.lang.String">
		<fieldDescription><![CDATA[c91]]></fieldDescription>
	</field>
	<field name="c92" class="java.lang.String">
		<fieldDescription><![CDATA[c92]]></fieldDescription>
	</field>
	<field name="c93" class="java.lang.String">
		<fieldDescription><![CDATA[c93]]></fieldDescription>
	</field>
	<field name="c94" class="java.lang.String">
		<fieldDescription><![CDATA[c94]]></fieldDescription>
	</field>
	<field name="c95" class="java.lang.String">
		<fieldDescription><![CDATA[c95]]></fieldDescription>
	</field>
	<field name="c96" class="java.lang.String">
		<fieldDescription><![CDATA[c96]]></fieldDescription>
	</field>
	<field name="c97" class="java.lang.String">
		<fieldDescription><![CDATA[c97]]></fieldDescription>
	</field>
	<field name="c98" class="java.lang.String">
		<fieldDescription><![CDATA[c98]]></fieldDescription>
	</field>
	<field name="c99" class="java.lang.String">
		<fieldDescription><![CDATA[c99]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="cocNum" class="java.lang.String">
		<fieldDescription><![CDATA[cocNum]]></fieldDescription>
	</field>
	<field name="color" class="java.lang.String">
		<fieldDescription><![CDATA[color]]></fieldDescription>
	</field>
	<field name="engineNo" class="java.lang.String">
		<fieldDescription><![CDATA[engineNo]]></fieldDescription>
	</field>
	<field name="engineType" class="java.lang.String">
		<fieldDescription><![CDATA[engineType]]></fieldDescription>
	</field>
	<field name="imageInputStream" class="java.io.InputStream">
		<fieldDescription><![CDATA[imageInputStream]]></fieldDescription>
	</field>
	<field name="prodDate" class="java.lang.String">
		<fieldDescription><![CDATA[prodDate]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="validate" class="java.lang.Boolean">
		<fieldDescription><![CDATA[validate]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="c131" class="java.lang.String"/>
	<field name="c137" class="java.lang.String"/>
	<field name="c133" class="java.lang.String"/>
	<field name="c136" class="java.lang.String"/>
	<field name="c138" class="java.lang.String"/>
	<field name="c139" class="java.lang.String"/>
	<field name="c150" class="java.lang.String"/>
	<field name="c151" class="java.lang.String"/>
	<field name="c152" class="java.lang.String"/>
	<field name="c153" class="java.lang.String"/>
	<field name="c155" class="java.lang.String"/>
	<field name="c148" class="java.lang.String"/>
	<field name="c149" class="java.lang.String"/>
	<field name="c156" class="java.lang.String"/>
	<field name="c157" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="3"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="786"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="173"
						y="17"
						width="225"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="宋体" pdfFontName="STSong-Light" size="14" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[强制性产品认证车辆一致性证书]]></text>
				</staticText>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="439"
						y="51"
						width="123"
						height="84"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$F{barCodeImageUrl}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="21"
						y="51"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement verticalAlignment="Top">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆一致性证书编号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="51"
						width="307"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c2}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="79"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆生产企业名称]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="65"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最终阶段车辆制造国]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="65"
						width="307"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c4}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="121"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[新能源车]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="121"
						width="307"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c150}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="135"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{vin}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="135"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆识别代号]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="107"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产者（制造商）名称]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="107"
						width="307"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c10}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="149"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆识别代号打刻位置]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="149"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c13}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="163"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c12}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="163"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[法定标牌的位置]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="177"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c7}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="177"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车型名称]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="191"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c151}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="191"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆注册类型]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="177"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c6}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="177"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="261"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车轴数量]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="261"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c17}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="79"
						width="307"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="261"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c18}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="261"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车轮数量]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="191"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆类别]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="191"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c97}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="93"
						width="307"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c157}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="93"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆生产企业地址]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="205"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c95}+"/"+$F{c96}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="205"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆品牌（中文/英文）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="205"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{color}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="205"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆颜色]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="219"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机编号在发动机上的打刻位置]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="219"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机/电动机编号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="219"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{engineNo}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="219"
						width="95"
						height="28"
						key="textField"
						stretchType="RelativeToBandHeight"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c14}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="233"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c152}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="233"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车型种类]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="247"
						width="282"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c15}+"/"+$F{c16}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="247"
						width="269"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最终阶段车辆CCC证书编号（版本号）/签发日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="275"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c19}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="275"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[前悬（mm）/后悬（mm）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="275"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动轴位置]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="275"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c27}+"/"+$F{c28}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="289"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[外廓尺寸（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="289"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c24}+"/"+$F{c25}+"/"+$F{c26}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="289"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c20}+"/"+$F{c21}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="289"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[轮距（mm）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="303"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[轴距（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="303"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c22}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="303"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[接近角/离去角（°）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="303"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c29}+"/"+$F{c30}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="317"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c155}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="317"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c61}+"/"+$F{c116}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="317"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[整备质量（kg）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="317"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车门数量和结构]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="331"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c32}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="331"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c33}+"/"+$F{c34}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="331"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大允许总质量对应的轴荷分配（kg）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="331"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大允许总质量（kg）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="345"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[直接喷射]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="345"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c45}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="345"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机型号/燃料种类]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="345"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{engineType}+"/"+$F{c48}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="359"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[排量（ml）/功率（kW）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="359"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[气缸数量和排列]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="359"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c115}+"/"+$F{c46}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="359"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c47}+"/"+$F{c49}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="401"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[轮胎规格]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="401"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["第1轴:"+$F{c55}+"第2轴:"+$F{c56}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="415"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c58}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="415"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[制动装置简要说明]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="429"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c62}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="429"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[是否带防抱死系统]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="429"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[额定载客人数]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="429"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c153}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="457"
						width="111"
						height="26"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[主传动比]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="457"
						width="158"
						height="26"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c54}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="443"
						width="440"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c53}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="443"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[速比]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="457"
						width="177"
						height="26"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆制造日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="457"
						width="105"
						height="26"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{prodDate}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="65"
						y="497"
						width="225"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[定置噪声（dB（A））/对应发动机转速（r/min）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="511"
						width="282"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c68}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="65"
						y="483"
						width="225"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CCC认证引用的标准号及对应的实施阶段]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="65"
						y="511"
						width="225"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[加速行驶车外噪声（dB（A））]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="483"
						width="282"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c65}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="497"
						width="282"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c66}+"/"+$F{c119}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="483"
						width="44"
						height="42"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[声级]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="539"
						width="84"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c71}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="132"
						y="539"
						width="158"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="553"
						width="84"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c73}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="132"
						y="553"
						width="158"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[NOx]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="525"
						width="282"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c69}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="65"
						y="525"
						width="225"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CCC认证引用的标准号及对应的实施阶段]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="525"
						width="44"
						height="70"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[排气排 放物]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="65"
						y="539"
						width="57"
						height="56"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[  试验用液  体燃料:柴 油/汽油]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="567"
						width="84"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c75}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="132"
						y="567"
						width="158"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[烟度（吸收系数（m  ）的校正值）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="374"
						y="553"
						width="88"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[NMHC]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="374"
						y="539"
						width="88"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[THC]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="374"
						y="567"
						width="88"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[微粒物/PM]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="462"
						y="553"
						width="110"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c74}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="462"
						y="539"
						width="110"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c72}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="462"
						y="567"
						width="110"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c76}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="651"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[综合（L/100km）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="623"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c88}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="207"
						y="637"
						width="83"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c86}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="132"
						y="595"
						width="158"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CCC认证引用的标准号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="207"
						y="623"
						width="83"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c85}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="637"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[市郊（L/100km）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="651"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c90}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="637"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c89}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="132"
						y="651"
						width="75"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[综合（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="21"
						y="595"
						width="101"
						height="70"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO2排放量/燃料     消耗量]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="207"
						y="651"
						width="83"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c87}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="595"
						width="282"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c84}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="132"
						y="623"
						width="75"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[市区（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="623"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[市区（L/100km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="132"
						y="637"
						width="75"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[市郊（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="132"
						y="609"
						width="158"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO2排放量]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="609"
						width="282"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[燃料消耗量]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="65"
						y="664"
						width="507"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c94}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="664"
						width="44"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[备注]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="220"
						y="566"
						width="7"
						height="10"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" size="6" isBold="false"/>
					</textElement>
				<text><![CDATA[-1]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="373"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[变速器(型式)]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="373"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c51}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="373"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c52}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="373"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[离合器（型式）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="132"
						y="387"
						width="158"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c156}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="21"
						y="387"
						width="111"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[转向型式]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="467"
						y="387"
						width="105"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c64}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="387"
						width="177"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最高设计车速（km/h）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="132"
						y="581"
						width="158"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[PN]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="581"
						width="84"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c148}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="374"
						y="581"
						width="88"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[N O（国六适用）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="462"
						y="581"
						width="110"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="宋体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c149}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="380"
						y="585"
						width="7"
						height="10"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" size="6" isBold="false"/>
					</textElement>
				<text><![CDATA[2]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="5"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
