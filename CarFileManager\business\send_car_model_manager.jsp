<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("create")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#car_model_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 380,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
			}
			
		},
		close: function() {
			clear($(this));
		}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				if(type=="delete"){					
					formObj[0].action = "business/sendCarModel!deleteCarModel.action";
					formObj[0].submit();
				}else if(type=="published"){
					formObj[0].action = "business/sendCarModel!sendCarModel.action";
					formObj[0].submit();
				}
			}
		}
	});

	$('#import').click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>business/send_car_model_upload.jsp',winName,params) 
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = tmp[0]+','+tmp[1];
					info = "车型型号:"+tmp[2];
				}else{
					id = id+"@"+tmp[0]+','+tmp[1]; 
					info = info+"&"+"车型型号:"+tmp[2];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据.');
   	   		messageObj.dialog('open');
   	 		messageObj.find('#id').val(id);
   	   	}
	
	});
	
	$("#published").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var bm04Count = 0;
        var bm05Count = 0;
        var isCheckCommond = false;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 	
		
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = tmp[0]+','+tmp[1];
					info = "产品型号:"+tmp[2];
				}else{
					id = id+"@"+tmp[0]+','+tmp[1]; 
					info = info+"&"+"产品型号:"+tmp[2];
				}
				if(tmp[1]=='BM04'){
					bm04Count++;
				}else if(tmp[1]=='BM05'){
					bm05Count++;
				}
				
				$(this).parents().map(function () {
					if(this.tagName.toLowerCase()=='tr'){
						if($(this).find("#commond").val()==""){
							isCheckCommond = true;
							
							return ;
						}

						return ;
					}
					
				});
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要发布的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   	   		if(bm04Count>0&&bm05Count>0){
	   	  		messageObj = $('#message_dialog');
	   		  	messageObj.find('#message').text('警告:每次只能选择同一种接口数据进行发布！');
	   			messageObj.dialog('open');
	   	   	}else if(isCheckCommond==true){
	   	   		messageObj = $('#message_dialog');
   	   			messageObj.find('#message').text('警告:请选择要发布数据的标识位！');
   	   			messageObj.dialog('open');
		   	}else{
	   	 		type = "published";
	   			messageObj = $('#operate_dialog');
	   			messageObj.find('#message').text('提示:确定发布【'+info+'】！ 共'+index+'条数据.');
	   			messageObj.dialog('open');
	 			messageObj.find('#id').val(id);
	   	   	 }
   	   	}
	
	});

	$("#allCheck").click(function() {
		var checkedObj = $('#users-contain').find("[name='checkPK']"); 
		checkedObj.each(function(){
			if($(this).attr("checked")==false){
				$(this).attr("checked",true);
			}else{
				$(this).attr("checked",false);
			}
		});
	});
	
	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });


	$(".command").each(function(i){
		
		  $(this).change(function() {
				var id = null;
				var tmp = null;
				var param = null;
				$(this).parents().map(function () {
					if(this.tagName.toLowerCase()=='tr'){
						tmp = $(this).find("#checkPK").val();
						if(tmp!=null&&tmp!=""&&(id==null||id=="")){
							id = tmp.split(",");
							
							return ;
						}
						return ;
					}
					
				});

			  
			  param = "id="+id[0]+","+id[1]+"&command="+$(this).val();
			  jQuery.ajax({
			        url: 'business/sendCarModel!updateCommand.action?id='+id,		           
			        data: param, 
			        type: 'POST',
			        beforeSend: function() {
			        
			        },
			        error: function(request) {
			            alert("调用远程服务失败！");
			        },
			        success: function(data) {
			        }
			    });

		 });
	  });
	
	function display(id){
		jQuery.ajax({
	        url: 'business/sendCarModel!carMoldeInfo.action?id='+id,		           
	        //data: $('#createForm').serialize(), 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#car_model_display_dialog');
		
				setDialogValue(dialogObj,carObj,carObj.tradeCode);
						
	       	   	dialogObj.dialog('open');

	        }
	    });

		return false;
	}

	function setDialogValue(dialogObj,jsonObj,type){
		if(type=="BM04"){
			dialogObj.find('#model').val(jsonObj.model);
			dialogObj.find('#flag').val(jsonObj.flag);
			dialogObj.find('#modelName').val(jsonObj.modelName);
			dialogObj.find('#pmodelCode').val(jsonObj.pmodelCode);
			dialogObj.find('#deliveryCapacity').val(jsonObj.deliveryCapacity);
			dialogObj.find('#brandCode').val(jsonObj.brandCode);
			dialogObj.find('#brandName').val(jsonObj.brandName);
			dialogObj.find('#decorationStaCode').val(jsonObj.decorationStaCode);
			dialogObj.find('#decorationStaName').val(jsonObj.decorationStaName);
			dialogObj.find('#emissionStaCode').val(jsonObj.emissionStaCode);
			dialogObj.find('#emissionStaName').val(jsonObj.emissionStaName);
			dialogObj.find('#engineTypeCode').val(jsonObj.engineTypeCode);
			dialogObj.find('#engineTypeName').val(jsonObj.engineTypeName);
			dialogObj.find('#isSunRoof').val(jsonObj.isSunRoof);
			dialogObj.find('#modelKindCode').val(jsonObj.modelKindCode);
			dialogObj.find('#modelKindName').val(jsonObj.modelKindName);
			dialogObj.find('#modelTypeCode').val(jsonObj.modelTypeCode);
			dialogObj.find('#modelTypeName').val(jsonObj.modelTypeName);
			dialogObj.find('#safetyRestraintCode').val(jsonObj.safetyRestraintCode);
			dialogObj.find('#safetyRestraintName').val(jsonObj.safetyRestraintName);
			dialogObj.find('#transmissionCode').val(jsonObj.transmissionCode);
			dialogObj.find('#transmissionName').val(jsonObj.transmissionName);
			
			dialogObj.find('#mpflag').val(jsonObj.mpflag);//2021-05-21 
			
			dialogObj.find('#bm04').show();
			dialogObj.find('#bm05').hide();
		}else if(type=="BM05"){
			dialogObj.find('#model').val(jsonObj.model);
			dialogObj.find('#flag').val(jsonObj.flag);
			dialogObj.find('#modelName').val(jsonObj.modelName);
			dialogObj.find('#pmodelBrand').val(jsonObj.pmodelBrand);
			dialogObj.find('#pmodelFuelType').val(jsonObj.pmodelFuelType);
			dialogObj.find('#pmodelOutlineLength').val(jsonObj.pmodelOutlineLength);
			dialogObj.find('#pmodelOutlineWidth').val(jsonObj.pmodelOutlineWidth);
			dialogObj.find('#pmodelOutlineHeight').val(jsonObj.pmodelOutlineHeight);
			dialogObj.find('#pmodelAxleNum').val(jsonObj.pmodelAxleNum);
			dialogObj.find('#pmodelAxleDistance').val(jsonObj.pmodelAxleDistance);
			dialogObj.find('#pmodelTypeNum').val(jsonObj.pmodelTypeNum);
			dialogObj.find('#pmodelWheeltrackFront').val(jsonObj.pmodelWheeltrackFront);
			dialogObj.find('#pmodelWheeltrackBehind').val(jsonObj.pmodelWheeltrackBehind);
			dialogObj.find('#pmodelDriveMode').val(jsonObj.pmodelDriveMode);
			dialogObj.find('#pmodelTotalMass').val(jsonObj.pmodelTotalMass);
			dialogObj.find('#pmodelShippingMass').val(jsonObj.pmodelShippingMass);
			dialogObj.find('#pmodelSeatingCapacity').val(jsonObj.pmodelSeatingCapacity);
			dialogObj.find('#pmodelEngineType').val(jsonObj.pmodelEngineType);
			dialogObj.find('#pmodelEngineDisp').val(jsonObj.pmodelEngineDisp);
			dialogObj.find('#pmodelRatedPower').val(jsonObj.pmodelRatedPower);
			dialogObj.find('#pmodelMadeCountry').val(jsonObj.pmodelMadeCountry);
			dialogObj.find('#pmodelDesc').val(jsonObj.pmodelDesc);
			dialogObj.find('#goodsName').val(jsonObj.goodsName);//2018-04-24 增加5个字段
			dialogObj.find('#driveMotorModel').val(jsonObj.driveMotorModel);//2018-04-24 增加5个字段
			dialogObj.find('#driveMotorPeakPower').val(jsonObj.driveMotorPeakPower);//2018-04-24 增加5个字段
			dialogObj.find('#powerBatteryRateVoltage').val(jsonObj.powerBatteryRateVoltage);//2018-04-24 增加5个字段
			dialogObj.find('#powerBatteryRatedCapacity').val(jsonObj.powerBatteryRatedCapacity);//2018-04-24 增加5个字段
			dialogObj.find('#vinpre8').val(jsonObj.vinpre8);//2018-08-15 
			
			dialogObj.find('#mpflag').val(jsonObj.mpflag);//2021-05-21 
			
			dialogObj.find('#axleLoad').val(jsonObj.axleLoad);//2024-07-01 

			dialogObj.find('#bm04').hide();
			dialogObj.find('#bm05').show();
		}
	}

	function clear(dialogObj){
		dialogObj.find('#model').val("");
		dialogObj.find('#modelName').val("");
		dialogObj.find('#pmodelCode').val("");
		dialogObj.find('#deliveryCapacity').val("");
		dialogObj.find('#brandCode').val("");
		dialogObj.find('#brandName').val("");
		dialogObj.find('#decorationStaCode').val("");
		dialogObj.find('#decorationStaName').val("");
		dialogObj.find('#emissionStaCode').val("");
		dialogObj.find('#emissionStaName').val("");
		dialogObj.find('#engineTypeCode').val("");
		dialogObj.find('#engineTypeName').val("");
		dialogObj.find('#isSunRoof').val("");
		dialogObj.find('#modelKindCode').val("");
		dialogObj.find('#modelKindName').val("");
		dialogObj.find('#modelTypeCode').val("");
		dialogObj.find('#modelTypeName').val("");
		dialogObj.find('#safetyRestraintCode').val("");
		dialogObj.find('#safetyRestraintName').val("");
		dialogObj.find('#transmissionCode').val("");
		dialogObj.find('#transmissionName').val("");
		dialogObj.find('#mpflag').val("");//2021-05-21 
	
		dialogObj.find('#model').val("");
		dialogObj.find('#modelName').val("");
		dialogObj.find('#pmodelBrand').val("");
		dialogObj.find('#pmodelFuelType').val("");
		dialogObj.find('#pmodelOutlineLength').val("");
		dialogObj.find('#pmodelOutlineWidth').val("");
		dialogObj.find('#pmodelOutlineHeight').val("");
		dialogObj.find('#pmodelAxleNum').val("");
		dialogObj.find('#pmodelAxleDistance').val("");
		dialogObj.find('#pmodelTypeNum').val("");
		dialogObj.find('#pmodelWheeltrackFront').val("");
		dialogObj.find('#pmodelWheeltrackBehind').val("");
		dialogObj.find('#pmodelDriveMode').val("");
		dialogObj.find('#pmodelTotalMass').val("");
		dialogObj.find('#pmodelShippingMass').val("");
		dialogObj.find('#pmodelSeatingCapacity').val("");
		dialogObj.find('#pmodelEngineType').val("");
		dialogObj.find('#pmodelEngineDisp').val("");
		dialogObj.find('#pmodelRatedPower').val("");
		dialogObj.find('#pmodelMadeCountry').val("");
		dialogObj.find('#goodsName').val("");
		dialogObj.find('#driveMotorModel').val("");
		dialogObj.find('#driveMotorPeakPower').val("");
		dialogObj.find('#powerBatteryRateVoltage').val("");
		dialogObj.find('#powerBatteryRatedCapacity').val("");
		dialogObj.find('#pmodelDesc').val("");
		dialogObj.find('#vinpre8').val("");
		dialogObj.find('#mpflag').val("");//2021-05-21 
		dialogObj.find('#axleLoad').val("");//2027-07-01 
	}
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

           //给这行添加class值为over，并且当鼠标一出该行时执行函数

           $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
  <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr><td width="90%"></td>
			  <td width="60" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>
			  <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
  			   <td width="60" align="right"><button id="published" class="ui-button ui-state-default ui-corner-all">发布</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
<div align="center">
<div id="users-contain" class="ui-widget">
	<table id="users" class="ui-widget ui-widget-content" >
		<thead>
			<tr class="ui-widget-header ">
				<th width="5%"><input type='checkbox' id='allCheck' name='allCheck'>全选</th> 
				<th width="6%">接口编号</th>
				<th width="8%">接口描述</th>
				<th width="10%">产品型号</th>
				<th width="8%">标识位</th>
				<th width="8%">状态</th>
				<th width="10%">创建人</th>
				<th width="18%">创建时间</th>
				<th width="5%">操作</th>
			</tr>
		</thead>
		<tbody>
			<s:iterator value="#request.sendCarModelPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
					<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id" />,<s:property value="tradeCode" />,<s:property value="model" />' ></td>	
			  		<td><s:property value="tradeCode" /></td>
			  		<td><s:property value="tradeName" /></td>
			  		<td>
			  			<s:if test="{#tradeCode==BM04}"><s:property value="pmodelCode" /></s:if>
			  			<s:if test="{#tradeCode==BM05}"><s:property value="pmodel" />(<s:property value="flag" />)</s:if>
			  		</td>
			  		<td><s:select cssClass='command' name="commond" list="#request.commondMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.command"></s:select> </td>
   		      		<td>
			  			<s:if test="state==0">待发布</s:if>
			  			<s:elseif test="state==1">已发布</s:elseif>
			  		</td>
   		      		
   		      		<td><s:property value="creator" /></td>				
			  		<td><s:property value="time" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id" />,<s:property value="tradeCode" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
</div>
</div>
</div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		
			  	</td>
				<td width="8%"><p>
					
				</td>
			  	<td width="8%" align="center"><p>
			  		
			  	</td>
			  	<td width="10%" align="center"><p>
			  		
			  	</td>
			  	<td width="35%" align="center"></td>
			  	<td width="15%" align="right"></td>
			  	<td width="7%" align="right">共<s:property value="#request.sendCarModelPageSize" />条</td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="car_model_display_dialog" title="查看窗口">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
  <table id="bm04" width="100%">
	    	<tr>
	    		<td width="9%"><label><P>生产车型型号</label></td>
				<td width="17%"><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>名称</label></td>
				<td width="17%"><input type="text" id="modelName" name="modelName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="pmodelCode" name="pmodelCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td width="8%"><label><P>排量</label></td>
				<td width="17%"><input type="text" id="deliveryCapacity" name="deliveryCapacity" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="7%"><label><P>品牌</label></td>
				<td width="16%"><input type="text" id="brandCode" name="brandCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>品牌名称</label></td>
				<td><input type="text" id="brandName" name="brandName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>装饰等级代码</label></td>
				<td><input type="text" id="decorationStaCode" name="decorationStaCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>装饰等级</label></td>
				<td><input type="text" id="decorationStaName" name="decorationStaName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>排放标准代码</label></td>
				<td><input type="text" id="emissionStaCode" name="emissionStaCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>排放标准</label></td>
				<td><input type="text" id="emissionStaName" name="emissionStaName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机类别代码</label></td>
				<td><input type="text" id="engineTypeCode"  name="engineTypeCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>	
			<tr>	
				<td><label><P>发动机类别</label></td>
				<td><input type="text" id="engineTypeName" name="engineTypeName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>是否带天窗</label></td>
				<td><input type="text" id="isSunRoof" name="isSunRoof" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>车型系列代号</label></td>
				<td><input type="text" id="modelKindCode" name="modelKindCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>车型系列代号名称</label></td>
				<td><input type="text" id="modelKindName" name="modelKindName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>车辆类别代号</label></td>
				<td><input type="text" id="modelTypeCode" name="modelTypeCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>车辆类别名称</label></td>
				<td><input type="text" id="modelTypeName" name="modelTypeName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>约束类型代码</label></td>
				<td><input type="text" id="safetyRestraintCode" name="safetyRestraintCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>约束类型</label></td>
				<td><input type="text" id="safetyRestraintName" name="safetyRestraintName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>变速箱代码</label></td>
				<td><input type="text" id="transmissionCode" name="transmissionCode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>变速箱</label></td>
				<td><input type="text" id="transmissionName" name="transmissionName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>铭牌识别码</label></td>
				<td><input type="text" id="mpflag" name="mpflag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
		</Table>
		<table id="bm05" width="100%" style="display:none">
	    	<tr>
	    		<td width="9%"><label><P>公告车型型号</label></td>
				<td width="17%"><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>公告车型名称</label></td>
				<td width="17%"><input type="text" id="modelName" name="modelName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>厂牌</label></td>
				<td width="17%"><input type="text" id="pmodelBrand" name="pmodelBrand" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td width="7%"><label><P>燃料种类</label></td>
				<td width="16%"><input type="text" id="pmodelFuelType" name="pmodelFuelType" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>外廓尺寸长</label></td>
				<td><input type="text" id="pmodelOutlineLength" name="pmodelOutlineLength" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>外廓尺寸宽</label></td>
				<td><input type="text" id="pmodelOutlineWidth" name="pmodelOutlineWidth" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>外廓尺寸高</label></td>
				<td><input type="text" id="pmodelOutlineHeight" name="pmodelOutlineHeight" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>

			<tr>
				<td><label><P>轴数</label></td>
				<td><input type="text" id="pmodelAxleNum" name="pmodelAxleNum" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轴距</label></td>
				<td><input type="text" id="pmodelAxleDistance" name="pmodelAxleDistance" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎数</label></td>
				<td><input type="text" id="pmodelTypeNum" name="pmodelTypeNum" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮距前</label></td>
				<td><input type="text" id="pmodelWheeltrackFront" name="pmodelWheeltrackFront" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>轮距后</label></td>
				<td><input type="text" id="pmodelWheeltrackBehind" name="pmodelWheeltrackBehind" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动方式</label></td>
				<td><input type="text" id="pmodelDriveMode" name="pmodelDriveMode" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>总质量</label></td>
				<td><input type="text" id="pmodelTotalMass" name="pmodelTotalMass" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>整备质量</label></td>
				<td><input type="text" id="pmodelShippingMass" name="pmodelShippingMass" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>载客人数</label></td>
				<td><input type="text" id="pmodelSeatingCapacity" name="pmodelSeatingCapacity" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机型号</label></td>
				<td><input type="text" id="pmodelEngineType" name="pmodelEngineType" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机排量</label></td>
				<td><input type="text" id="pmodelEngineDisp" name="pmodelEngineDisp" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>额定功率</label></td>
				<td><input type="text" id="pmodelRatedPower" name="pmodelRatedPower" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>制造国</label></td>
				<td><input type="text" id="pmodelMadeCountry" name="pmodelMadeCountry" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>备注</label></td>
				<td><input type="text" id="pmodelDesc" name="pmodelDesc" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>商品名</label></td>
				<td><input type="text" id="goodsName" name="goodsName" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动电机型号</label></td>
				<td><input type="text" id="driveMotorModel" name="driveMotorModel" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			
			</tr>
			<tr>
				<td><label><P>驱动电机峰值功率</label></td>
				<td><input type="text" id="driveMotorPeakPower" name="driveMotorPeakPower" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>动力电池额定电压</label></td>
				<td><input type="text" id="powerBatteryRateVoltage" name="powerBatteryRateVoltage" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>动力电池额定容量</label></td>
				<td><input type="text" id="powerBatteryRatedCapacity" name="powerBatteryRatedCapacity" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>VIN前8位</label></td>
				<td><input type="text" id="vinpre8" name="vinpre8" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			
			</tr>
			<tr>
				<td><label><P>铭牌识别码</label></td>
				<td><input type="text" id="mpflag" name="mpflag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轴荷</label></td>
				<td><input type="text" id="axleLoad" name="axleLoad" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
		</Table>
		</form>
	</fieldset>
</div>
<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div id="operate_dialog" title="操作窗口">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='id' name='id'>
	<input type='hidden' id='type' name='type' value='<%= String.valueOf(request.getAttribute("type"))%>'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>
</body>
</html>