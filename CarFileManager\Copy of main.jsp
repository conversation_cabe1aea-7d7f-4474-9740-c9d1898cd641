<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="c" uri="/jsp-customTags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/dtree/dtree.css" rel="stylesheet" />
<style type="text/css"> 
	.ui-layout-pane { /* all 'panes' */ 
		padding: 10px; 
		background: #FFF; 
		border-top: 1px solid #BBB;
		border-bottom: 1px solid #BBB;
		}
		.ui-layout-pane-north ,
		.ui-layout-pane-south {
			border: 1px solid #BBB;
		} 
		.ui-layout-pane-west {
			border-left: 1px solid #BBB;
		} 
		.ui-layout-pane-east {
			border-right: 1px solid #BBB;
		} 
		.ui-layout-pane-center {
			border-left: 0;
			border-right: 0;
			} 
			.inner-center {
				border: 1px solid #BBB;
			} 

		.outer-west ,
		.outer-east {
			background-color: #EEE;
		}
		.middle-west ,
		.middle-east {
			background-color: #F8F8F8;
		}

	.ui-layout-resizer { /* all 'resizer-bars' */ 
		background: #DDD; 
		}
		.ui-layout-resizer:hover { /* all 'resizer-bars' */ 
			background: #FED; 
		}
		.ui-layout-resizer-west {
			border-left: 1px solid #BBB;
		}
		.ui-layout-resizer-east {
			border-right: 1px solid #BBB;
		}

	.ui-layout-toggler { /* all 'toggler-buttons' */ 
		background: #AAA; 
		} 
		.ui-layout-toggler:hover { /* all 'toggler-buttons' */ 
			background: #FC3; 
		} 

	.outer-center ,
	.middle-center {
		/* center pane that are 'containers' for a nested layout */ 
		padding: 0; 
		border: 2; 
		border-right: 0;
	} 
</style>
<SCRIPT type="text/javascript" src="js/dtree/dtree.js"></SCRIPT>
<SCRIPT type="text/javascript" src="js/jquery/jquery-1.3.2.js"></SCRIPT>
<SCRIPT type="text/javascript" src="js/jquery/plugin/layout/jquery.layout.min.js"></SCRIPT>
<SCRIPT type="text/javascript">
var outerLayout, middleLayout; 
$(document).ready(function () {
	outerLayout = $('body').layout({ 
		center__paneSelector:	".outer-center" 
	,	west__size:				200 
	,	spacing_open:			7 // ALL panes
	,	spacing_closed:			7 // ALL panes
	,	north__spacing_open:	0
	,	south__spacing_open:	0
	,	center__onresize:		"middleLayout.resizeAll" 
	}); 
});

function changeframeurl(url){ 
	mainframe1.location.href="../"+url;
}

</SCRIPT>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<iframe id="mainFrame" name="mainFrame" class="outer-center"
	width="100%" height="100%" frameborder="0" scrolling="auto"
	src=""></iframe>
<DIV class="ui-layout-north">
</DIV>
<DIV class="ui-layout-south"></DIV>
<iframe id="navigationFrame" name="navigationFrame" class="ui-layout-west"
	width="100%" height="100%" frameborder="0" scrolling="auto"
	src="system/sysMenuNavigation.action"></iframe>
</body>
</html>