<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfoAuto" table="CAR_INFO_AUTO" >
        <id name="vin" type="java.lang.String">
            <column name="VIN" length="20" />
            <generator class="assigned" />
        </id>
        <property name="engineNo" type="java.lang.String">
            <column name="ENGINE_NO" length="20" />
        </property>
        <property name="model" type="java.lang.String">
            <column name="MODEL" length="50" />
        </property>
        <property name="engineType" type="java.lang.String">
            <column name="ENGINE_TYPE" length="20" />
        </property>
        <property name="color" type="java.lang.String">
            <column name="COLOR" length="50" />
        </property>
        <property name="prodDate" type="java.lang.String">
            <column name="PROD_DATE" length="10" />
        </property>
        <property name="cocNum" type="java.lang.String">
            <column name="COC_NUM" length="20" />
        </property>
        <property name="cocColor" type="java.lang.String">
            <column name="COC_COLOR" length="10" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="gvercode" type="java.lang.String">
            <column name="GVERCODE" length="15" />
        </property>
        <property name="cvercode" type="java.lang.String">
            <column name="CVERCODE" length="15" />
        </property>
        <property name="gasman" type="java.lang.String">
            <column name="GASMAN" length="10" />
        </property>
        <property name="cocman" type="java.lang.String">
            <column name="COCMAN" length="10" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
    </class>
</hibernate-mapping>