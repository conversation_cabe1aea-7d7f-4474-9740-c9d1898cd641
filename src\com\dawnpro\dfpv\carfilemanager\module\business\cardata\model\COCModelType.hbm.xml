<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.COCModelType" table="COC_MODEL_TYPE" lazy="false">
       
       <id name="id" type="java.lang.String">
            <column name="ID" length="50" />
            <generator class="assigned" />
        </id>
        
        <property name="model" type="java.lang.String">
            <column name="MODEL" length="20">
            </column>
        </property>
        
        <property name="modelType" type="java.lang.String">
            <column name="MODEL_TYPE" length="10">
            </column>
        </property>
        
        <property name="engineNum" type="java.lang.String">
            <column name="ENGINE_NUM" length="10" />
        </property>
        <property name="motorNum" type="java.lang.String">
            <column name="MOTOR_NUM" length="10" />
        </property>
         <property name="cocPrintType" type="java.lang.String">
            <column name="COC_PRINT_TYPE" length="10" />
        </property>

  
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20">
                <comment>创建人</comment>
            </column>
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7">
                <comment>时间</comment>
            </column>
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
    </class>
</hibernate-mapping>
