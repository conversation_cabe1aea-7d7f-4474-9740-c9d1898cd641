package com.dawnpro.dfpv.carfilemanager.module.business.share.jms;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.share.factory.SimpleObjectFactory;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceData;
import com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceLogService;
import com.ibm.mq.MQC;
import com.ibm.mq.MQEnvironment;
import com.ibm.mq.MQException;
import com.ibm.mq.MQGetMessageOptions;
import com.ibm.mq.MQMessage;
import com.ibm.mq.MQQueue;
import com.ibm.mq.MQQueueManager;

public class CopyOfMQIMessageRecevieService extends TimerTask{
	
	private Logger logger = Logger.getLogger(CopyOfMQIMessageRecevieService.class.getName());
	private   String hostName = "**********";
	//private   String hostName = "***********";
	private   String channel = "DFPV.ESB.SVRCONN";
	private  String qManager = "BRK1_QM";
	private MQQueueManager qMg = null;
	
	private SimpleObjectFactory objectFactory = null;
	private InterfaceLogService interfaceLogService = null;
	
	public void setObjectFactory(SimpleObjectFactory objectFactory) {
		this.objectFactory = objectFactory;
	}
	
	public void setInterfaceLogService(InterfaceLogService interfaceLogService) {
		this.interfaceLogService = interfaceLogService;
	}
	
	public void init(){
		try{
			Timer timer1 = new Timer(true);    
			timer1.schedule(this,5000,300000);
//			timer1.schedule(this,5000,5000);
		}catch(Exception e){
			logger.error("init error:",e);
		}
	}
	
	public CopyOfMQIMessageRecevieService(){
		MQEnvironment.hostname = hostName;
		MQEnvironment.channel = channel;
		MQEnvironment.CCSID = 1208;
		MQEnvironment.port = 38801;
//		MQEnvironment.port = 1414;
		MQEnvironment.properties.put(MQC.TRANSPORT_PROPERTY,MQC.TRANSPORT_MQSERIES);
		try {
			qMg = new MQQueueManager(qManager);
		} catch (MQException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public void send(String qName,String text){
		int openOptions = MQC.MQOO_OUTPUT | MQC.MQOO_FAIL_IF_QUIESCING;
		try {
			MQQueue queue = qMg.accessQueue(qName,openOptions,null,null,null);
			MQMessage outMsg = new MQMessage();
			outMsg.format = MQC.MQFMT_STRING;
			outMsg.characterSet = 1208;
			outMsg.write(text.getBytes("UTF-8"));
			queue.put(outMsg);
			qMg.commit();
			queue.close();
//			qMg.disconnect();
		} catch (MQException e) {
		
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void read(String qName){
		List xmls = new ArrayList();
		int openOptions = MQC.MQOO_INPUT_SHARED | MQC.MQOO_FAIL_IF_QUIESCING|MQC.MQOO_INQUIRE ;
		try {
			StringBuffer xml = new StringBuffer(2048);
			MQQueue queue = qMg.accessQueue(qName,openOptions,null,null,null);
			MQGetMessageOptions gmo = new MQGetMessageOptions();
			gmo.options = gmo.options + MQC.MQGMO_SYNCPOINT ;
			gmo.options = gmo.options + MQC.MQGMO_WAIT;
			gmo.options = gmo.options + MQC.MQGMO_FAIL_IF_QUIESCING ;
			gmo.waitInterval = 30000 ;
			
			int depth = queue.getCurrentDepth();
			String message = "";
			if(depth>0){
				MQMessage inMsg = null;
				openOptions = MQC.MQOO_INPUT_SHARED | MQC.MQOO_FAIL_IF_QUIESCING;
				
				for(int i=0;i<depth;i++){
					queue = qMg.accessQueue(qName,openOptions);
					
					inMsg = new MQMessage();
					inMsg.format = MQC.MQFMT_STRING;
					inMsg.characterSet = 1208;
					queue.get(inMsg, gmo);
					
					while(!(message=inMsg.readLine()).equals("")){
						xml.append(message);
					}
					queue.close();
					
					xmls.add(xml.toString());
					
					xml.delete(0,xml.length());
					queue.close();
				}
				accessDatabase(xmls);
				
				qMg.commit();
			}

		} catch (Exception e) {
			try {
				qMg.backout();
			} catch (MQException e1) {
				logger.error("mq backout error:",e);
			}
			
			logger.error("mq read message error:",e);
		}
	}
	
	private void accessDatabase(List<String> xmls){
		try{
			for(String xml:xmls){
				InterfaceData log = this.objectFactory.createObj(xml.trim());
				log.getId().setCreateTime(CalendarUtil.getCurrentDate());
				
				this.interfaceLogService.addLog(log);
			}
		}catch(Exception e){
			throw new RuntimeException(e);
		}
	}
	
	public void run() {
		try{
			this.read("MES.CFM.SERVICE.RECEIVEQ1");
		}catch(Exception e){
			logger.error("task run error:",e);
		}	
	}
}
