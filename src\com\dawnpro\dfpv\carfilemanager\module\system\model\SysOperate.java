package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * SysOperate entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SysOperate implements java.io.Serializable {

	private String operateid;
	private String name;
	private String flag;
	private String remark;
	private String creator;
	private String time;

	// Constructors

	/** default constructor */
	public SysOperate() {
	}

	/** minimal constructor */
	public SysOperate(String operateid) {
		this.operateid = operateid;
	}

	/** full constructor */
	public SysOperate(String operateid, String name, String flag,
			String remark, String creator, String time) {
		this.operateid = operateid;
		this.name = name;
		this.flag = flag;
		this.remark = remark;
		this.creator = creator;
		this.time = time;
	}

	// Property accessors

	public String getOperateid() {
		return this.operateid;
	}

	public void setOperateid(String operateid) {
		this.operateid = operateid;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	

}