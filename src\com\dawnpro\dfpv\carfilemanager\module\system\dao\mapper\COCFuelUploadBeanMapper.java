package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FuelUploadBean;

public class COCFuelUploadBeanMapper implements RowMapper<List<FuelUploadBean>> {

	public List<FuelUploadBean> mapRow(ResultSet rs) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<FuelUploadBean> mapRow(Collection rs) {
		List<FuelUploadBean> list = new ArrayList<FuelUploadBean>();
		int point = 0;
		for(Object obj : rs){
			point = 0;
			Object[] objs = (Object[])obj;
			FuelUploadBean bean = new FuelUploadBean();
			bean.setVin(objs[point] == null ? "" : (objs[point]+"").trim());
			bean.setCxxh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setClzl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setProddate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEngineno(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEnginetype(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCocnum(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCoccolor(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCvercode(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCocresponse(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAdtcoc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCocdel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAdtdelcoc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCocupload(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCxxhname(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setNote(objs[++point] == null ? "" : (objs[point]+"").trim());
			list.add(bean);
		}
		return list;
	}

}
