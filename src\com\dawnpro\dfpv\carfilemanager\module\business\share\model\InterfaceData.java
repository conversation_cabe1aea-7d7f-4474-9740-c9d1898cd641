package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

import java.util.List;



/**
 * InterfaceLog entity. <AUTHOR> Persistence Tools
 */

public class InterfaceData  implements java.io.Serializable {
	
    // Fields    

     private InterfaceDataId id;
     private String tradeTime;
     private String requesterId;
     private String state;
     private String type;
     private String path = null;
     private String errInfo;
     private List<Object> rows = null;

    // Constructors
	/** default constructor */
    public InterfaceData() {
    
     }

	/** minimal constructor */
    public InterfaceData(InterfaceDataId id) {
        this.id = id;
    }
    
    /** full constructor */
    public InterfaceData(InterfaceDataId id, String tradeTime, String requesterId, String state, String type, String errInfo,String path) {
        this.id = id;
        this.tradeTime = tradeTime;
        this.requesterId = requesterId;
        this.state = state;
        this.type = type;
        this.errInfo = errInfo;
        this.path = path;
    }

   
    // Property accessors

    public InterfaceDataId getId() {
        return this.id;
    }
    
    public void setId(InterfaceDataId id) {
        this.id = id;
    }

    public String getTradeTime() {
        return this.tradeTime;
    }
    
    public void setTradeTime(String tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getRequesterId() {
        return this.requesterId;
    }
    
    public void setRequesterId(String requesterId) {
        this.requesterId = requesterId;
    }

    public String getState() {
        return this.state;
    }
    
    public void setState(String state) {
        this.state = state;
    }

    public String getType() {
        return this.type;
    }
    
    public void setType(String type) {
        this.type = type;
    }

    public String getErrInfo() {
        return this.errInfo;
    }
    
    public void setErrInfo(String errInfo) {
        this.errInfo = errInfo;
    }
   
    public void setRows(List rows) {
		this.rows = rows;
	}
    
    public List getRows() {
		return rows;
	}
    
    public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
    
}