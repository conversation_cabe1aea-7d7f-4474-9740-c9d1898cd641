package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

import java.util.Date;

/**
 * CarRecodInfo entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarRecodInfo implements java.io.Serializable {

	// Fields

	private CarRecodInfoId id;
	private String creator;
	private String state;
	private Date createdate;
	private Date effectTime;
	private String c2;
	private String c3;
	private String c4;
	private String c5;
	private String c6;
	private String c7;
	private String c8;
	private String c9;
	private String c10;
	private String c11;
	private String c12;
	private String model;

	// Constructors

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	/** default constructor */
	public CarRecodInfo() {
	}

	/** minimal constructor */
	public CarRecodInfo(CarRecodInfoId id) {
		this.id = id;
	}

	/** full constructor */
	public CarRecodInfo(CarRecodInfoId id, String creator, String state,
			Date createdate, Date effectTime, String c2, String c3, String c4,
			String c5, String c6, String c7, String c8, String c9, String c10,
			String c11, String c12,String model) {
		this.id = id;
		this.creator = creator;
		this.state = state;
		this.createdate = createdate;
		this.effectTime = effectTime;
		this.c2 = c2;
		this.c3 = c3;
		this.c4 = c4;
		this.c5 = c5;
		this.c6 = c6;
		this.c7 = c7;
		this.c8 = c8;
		this.c9 = c9;
		this.c10 = c10;
		this.c11 = c11;
		this.c12 = c12;
		this.model = model;
	}

	// Property accessors

	public CarRecodInfoId getId() {
		return this.id;
	}

	public void setId(CarRecodInfoId id) {
		this.id = id;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public Date getCreatedate() {
		return this.createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}

	public Date getEffectTime() {
		return this.effectTime;
	}

	public void setEffectTime(Date effectTime) {
		this.effectTime = effectTime;
	}

	public String getC2() {
		return this.c2;
	}

	public void setC2(String c2) {
		this.c2 = c2;
	}

	public String getC3() {
		return this.c3;
	}

	public void setC3(String c3) {
		this.c3 = c3;
	}

	public String getC4() {
		return this.c4;
	}

	public void setC4(String c4) {
		this.c4 = c4;
	}

	public String getC5() {
		return this.c5;
	}

	public void setC5(String c5) {
		this.c5 = c5;
	}

	public String getC6() {
		return this.c6;
	}

	public void setC6(String c6) {
		this.c6 = c6;
	}

	public String getC7() {
		return this.c7;
	}

	public void setC7(String c7) {
		this.c7 = c7;
	}

	public String getC8() {
		return this.c8;
	}

	public void setC8(String c8) {
		this.c8 = c8;
	}

	public String getC9() {
		return this.c9;
	}

	public void setC9(String c9) {
		this.c9 = c9;
	}

	public String getC10() {
		return this.c10;
	}

	public void setC10(String c10) {
		this.c10 = c10;
	}

	public String getC11() {
		return this.c11;
	}

	public void setC11(String c11) {
		this.c11 = c11;
	}

	public String getC12() {
		return this.c12;
	}

	public void setC12(String c12) {
		this.c12 = c12;
	}

}