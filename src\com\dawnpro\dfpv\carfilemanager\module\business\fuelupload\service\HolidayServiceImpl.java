package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Holiday;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.HolidayDAO;

public class HolidayServiceImpl implements HolidayService{

	private HolidayDAO holidayDAO = null;
	private PaginationService paginationService = null;
		
	
	public HolidayDAO getHolidayDAO() {
		return holidayDAO;
	}

	public void setHolidayDAO(HolidayDAO holidayDAO) {
		this.holidayDAO = holidayDAO;
	}

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public void addHoliday(Holiday obj) throws DataAccessException {
		try{
			this.holidayDAO.addHoliday(obj);
		}catch (DataAccessException e) {
			throw new SystemException("addTypicalityNeutral Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("addTypicalityNeutral Method Error:", e);
		}
	}

	public List<?> pagination(Page page, Map<String, String> params) throws DataAccessException {
		List results = null;
		try{
			String year = params.get("year") == null ? "" : params.get("year");
			StringBuffer sub = new StringBuffer(30);
			StringBuffer countBuf = new StringBuffer(30);
			StringBuffer sqlBuf = new StringBuffer(30);
			
			countBuf.append("select count(*) from Holiday g ");
			sqlBuf.append("from Holiday g ");
				
			sub.append(" where 1 = 1 ");
			if(!year.equals("")){
				sub.append(" and to_char(g.holiday,'yyyy')='").append(year).append("' ");
			}

			countBuf.append(sub);
			sqlBuf.append(sub+" order by g.holiday desc");
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countBuf.toString());
			results = this.paginationService.pagination(sqlBuf.toString());
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}

	public void updateHoliday(Holiday obj) throws DataAccessException {
		try{
			this.holidayDAO.addHoliday(obj);
		}catch (DataAccessException e) {
			throw new SystemException("addTypicalityNeutral Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("addTypicalityNeutral Method Error:", e);
		}
		
	}

}
