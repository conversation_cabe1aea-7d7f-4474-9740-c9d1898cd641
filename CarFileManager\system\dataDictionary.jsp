<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
            }
        }
    });
	
	$("#public_notice_dialog").dialog({bgiframe: true,autoOpen: false,width: 480,height: 280,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#public_notice_dialog');
					allFields = $([]).add(parent.find('#type')).add(parent.find('#name')).add(parent.find('#value'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#public_notice_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
			        var idValue=$(this).find('#id').val();
			        if(idValue==null || idValue=="")
				        idValue=$(this).find('#name').val()+","+$(this).find('#value').val()+","+$(this).find('#type').val();
					if(type=="add"){
						jQuery.ajax({
				            url: 'system/sysDataDictionary!isModelExist.action',
				            data: 'id='+idValue,
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#public_notice_dialog');
					            if(json2Bean(data).json=="true"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'数据已经存在不能新增！');		
								}else{
									dialog.find('#createForm')[0].action="system/sysDataDictionary!addModel.action";
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						$(this).find('#createForm')[0].action="system/sysDataDictionary!updateModel.action";
						$(this).find('#createForm')[0].submit();
					}
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			$('#public_notice_dialog').find('#name').attr('readonly',false);
		}
		});

	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 480,height: 280,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
			$('#public_notice_dialog').find('#name').attr('readonly',false);
		}
		});
	
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				if(type=="delete"){					
					formObj[0].action = "system/sysDataDictionary!deleteModel.action";
					formObj[0].submit();
				}
			}
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#create").click(function() {
		type = "add";
		$('#public_notice_dialog').data('title.dialog', '新增数据').dialog('open');	
	});

    $("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			jQuery.ajax({
	            url: 'system/sysDataDictionary!modelInfo.action',		           
	            data: 'id='+id, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_notice_dialog');
	            	setDialogValue(dialogObj,carObj);

	       	    	dialogObj.data('title.dialog', '修改数据').dialog('open');
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
         		index++;
				if(id==""){
					id = this.value;
					info = "数据:"+ $('#users-contain').find('#'+this.value).val();
				}else{
					id = id+"&"+ this.value; 
					info = info+"&"+"数据:"+ $('#users-contain').find('#'+this.value).val();
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#id').val(id);
   	   	}
	
	});

	$("#query").click(function(){
		var st = $('#stype').val();
		var sn = $('#sname').val();
		if(st=="" && sn==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="sysDataDictionary.action?stype="+st+"&sname="+encodeURI(encodeURI(sn))+"&menuid="+menuid;
		}
	});
	
	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
	        url: 'system/sysDataDictionary!modelInfo.action',		           
	        data: 'id='+id, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#public_notice_display_dialog');
	        	
				setDialogValue(dialogObj,carObj);
								
	       	   	dialogObj.dialog('open');
	        }
	    });

		return false;
	}
	
	function validate(parent){
		var obj = $(parent).find('#type');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'数据类型字段不能为空，最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#name');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'数据名称字段不能为空，最大长度为30！');			
			return false;
		}
		obj = $(parent).find('#value');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'数据值字段不能为空，最大长度为20！');			
			return false;
		}
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#id').val(jsonObj.id);
		dialogObj.find('#type').val(jsonObj.type);
		dialogObj.find('#name').val(jsonObj.name);
		dialogObj.find('#value').val(jsonObj.value);
	}
	
	function clear(dialogObj){
		dialogObj.find('#type').val("");
		dialogObj.find('#name').val("");
		dialogObj.find('#value').val("");

		dialogObj.find('#type').attr('readonly',false);
		dialogObj.find('#name').attr('readonly',false);
		dialogObj.find('#value').attr('readonly',false);
		
		type = null;
	}

	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var stype=$('#stype').val();
			var sname=$('#sname').val();
			location.href='<%=basePath%>'+ "system/"+"sysDataDictionary.action?currentPage="+$('#jump').val()+"&menuid="+menuid+"&stype="+stype+"&sname="+encodeURI(encodeURI(sname));   
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var stype=$('#stype').val();
				var sname=$('#sname').val();
				location.href='<%=basePath%>'+ "system/" + $(this).attr('value')+"&menuid="+menuid+"&stype="+stype+"&sname="+encodeURI(encodeURI(sname));
		 });
	  });
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class
});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td colspan="3" align="left">
					 数据类型:
					 <s:select name="stype" list="#request.types"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.stype"></s:select>
				    &nbsp;&nbsp;
				            数据名称:
				    <input type="text" id="sname" class="text ui-widget-content ui-corner-all" <s:if test="#request.sname!=null"> value="<s:property value="#request.sname" />"</s:if>/>
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr>
			  <td width="85%"></td>
			  <td width="60" align="center"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  <td width="60" align="center"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  <td width="60" align="center"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="5%">选择</th> 
			    <th width="10%">数据类型</th>
			    <th width="10%">数据名称</th>
				<th width="10%">数据值</th>
				<th width="10%">创建人</th>
				<th width="10%">创建时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id" />' ></td>
			  		<td><s:property value="type" /></td>
			  		<td><s:property value="name" /></td>
			  		<td><s:property value="value" /></td>
			  		<td><s:property value="creator" /></td>
			  		<td><s:property value="time" /></td>
			  		<td>
			  			<input type='hidden' id='<s:property value="id" />' name='<s:property value="id" />' value="<s:property value="name"/>"/>
			  			<a class='display' onclick="return false;" href='#' value="<s:property value="id" />">查看</a>
			  		</td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="sysDataDictionary.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="sysDataDictionary.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="sysDataDictionary.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="sysDataDictionary.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	          
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="public_notice_dialog">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr><td>
			<label><P>数据类型*:</label>
			</td><td>
			<s:select name="type" list="#request.types"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple"></s:select>
			
			</td></tr><tr><td>		
			<label><P>数据名称*:</label>
			</td><td>
			<input type="text" id="name" name="name" class="text ui-widget-content ui-corner-all" />
			</td></tr><tr><td>	
			<label><P>数据值*:</label>
			</td><td>
			<input type="text" id="value" name="value" class="text ui-widget-content ui-corner-all"/>
			</td>
			</tr>
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="public_notice_display_dialog" title="查看窗口">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr><td>
			<label><P>数据类型*:</label>
			</td><td>
			<s:select name="type" list="#request.types"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple"></s:select>
			</td></tr><tr><td>		
			<label><P>数据名称*:</label>
			</td><td>
			<input type="text" id="name" name="name" class="text ui-widget-content ui-corner-all"  readonly="true"/>
			</td></tr><tr><td>	
			<label><P>数据值*:</label>
			</td><td>
			<input type="text" id="value" name="value" class="text ui-widget-content ui-corner-all" readonly="true"/>
			</td>
			</tr>
		</Table>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='id' name='id'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>