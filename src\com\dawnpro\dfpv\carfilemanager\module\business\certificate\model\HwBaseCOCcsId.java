package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * CartypetemplateId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class HwBaseCOCcsId implements java.io.Serializable {

	// Fields

	private String slcx;
	private String vercode;

	// Constructors

	/** default constructor */
	public HwBaseCOCcsId() {
	}

	/** full constructor */
	public HwBaseCOCcsId(String slcx, String vercode) {
		this.slcx = slcx;
		this.vercode = vercode;
	}

	// Property accessors

	

	public String getVercode() {
		return this.vercode;
	}

	public String getSlcx() {
		return slcx;
	}

	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof HwBaseCOCcsId))
			return false;
		HwBaseCOCcsId castOther = (HwBaseCOCcsId) other;

		return ((this.getSlcx() == castOther.getSlcx()) || (this.getSlcx() != null
				&& castOther.getSlcx() != null && this.getSlcx().equals(
				castOther.getSlcx())))
				&& ((this.getVercode() == castOther.getVercode()) || (this
						.getVercode() != null
						&& castOther.getVercode() != null && this.getVercode()
						.equals(castOther.getVercode())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result + (getSlcx() == null ? 0 : this.getSlcx().hashCode());
		result = 37 * result
				+ (getVercode() == null ? 0 : this.getVercode().hashCode());
		return result;
	}

}