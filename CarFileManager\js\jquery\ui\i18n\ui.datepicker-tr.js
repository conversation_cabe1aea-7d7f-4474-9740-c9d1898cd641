/* Turkish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['tr'] = {
		closeText: 'kapat',
		prevText: '&#x3c;geri',
		nextText: 'ileri&#x3e',
		currentText: 'bugün',
		monthNames: ['<PERSON>ca<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','Kasım','Aral<PERSON><PERSON>'],
		monthNamesShort: ['Oca','<PERSON>ub','Mar','<PERSON><PERSON>','May','<PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','Ka<PERSON>','<PERSON>'],
		dayNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON>arş<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['P<PERSON>','Pt','Sa','<PERSON><PERSON>','<PERSON><PERSON>','Cu','Ct'],
		dayNamesMin: ['Pz','Pt','Sa','Ça','Pe','Cu','Ct'],
		dateFormat: 'dd.mm.yy', firstDay: 1,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['tr']);
});