package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CarInfoVoidId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarInfoVoidId implements java.io.Serializable {

	// Fields

	private String vin;
	private String cocNum;

	// Constructors

	/** default constructor */
	public CarInfoVoidId() {
	}

	/** full constructor */
	public CarInfoVoidId(String vin, String cocNum) {
		this.vin = vin;
		this.cocNum = cocNum;
	}

	// Property accessors

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getCocNum() {
		return this.cocNum;
	}

	public void setCocNum(String cocNum) {
		this.cocNum = cocNum;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof CarInfoVoidId))
			return false;
		CarInfoVoidId castOther = (CarInfoVoidId) other;

		return ((this.getVin() == castOther.getVin()) || (this.getVin() != null
				&& castOther.getVin() != null && this.getVin().equals(
				castOther.getVin())))
				&& ((this.getCocNum() == castOther.getCocNum()) || (this
						.getCocNum() != null
						&& castOther.getCocNum() != null && this.getCocNum()
						.equals(castOther.getCocNum())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getVin() == null ? 0 : this.getVin().hashCode());
		result = 37 * result
				+ (getCocNum() == null ? 0 : this.getCocNum().hashCode());
		return result;
	}

}