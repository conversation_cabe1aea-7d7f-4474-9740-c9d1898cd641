<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<!-- Generated 2018-9-18 13:11:19 by Hibernate Tools 3.6.0.Final -->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDefine" table="INTERFACE_DEFINE" schema="VFILE">
        <comment>&#189;&#211;&#191;&#218;&#182;&#168;&#210;&#229;</comment>
        <id name="id" type="string">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="jkno" type="string">
            <column name="JKNO" length="20" />
        </property>
        <property name="jkname" type="string">
            <column name="JKNAME" length="50" />
        </property>
        <property name="irun" type="string">
            <column name="IRUN" length="2">
                <comment>0&#163;&#186;&#205;&#163;&#214;&#185;&#163;&#187;1&#163;&#186;&#212;&#203;&#208;&#208;</comment>
            </column>
        </property>
        <property name="mark" type="string">
            <column name="MARK" length="100" />
        </property>
        <property name="jktype" type="string">
            <column name="JKTYPE" length="2">
                <comment>0&#163;&#186;&#189;&#211;&#202;&#213;&#163;&#187;1&#163;&#186;&#183;&#162;&#203;&#205;</comment>
            </column>
        </property>
        <property name="jkfltype" type="string">
            <column name="JKFLTYPE" length="2">
                <comment>0&#163;&#186;TXT&#163;&#187;1&#163;&#186;XML</comment>
            </column>
        </property>
        <property name="datadate" type="date">
            <column name="DATADATE" length="7" />
        </property>
        <property name="userid" type="string">
            <column name="USERID" length="20" />
        </property>
    </class>
</hibernate-mapping>
