package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FilterModel;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.FilterModelService;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

public class FilterModelAction extends BaseSupportAction{
	private Logger logger = Logger.getLogger(FilterModelAction.class.getName());
	private String currentPage = null;
	private String id = null;
	private String ids = null;
	private String mark = null;
	private String slcx = null;
	private FilterModelService filterModelService = null;
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public FilterModelService getFilterModelService() {
		return filterModelService;
	}
	public void setFilterModelService(FilterModelService filterModelService) {
		this.filterModelService = filterModelService;
	}
	public String getCurrentPage() {
		return currentPage;
	}
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}
	public String getMark() {
		return mark;
	}
	public void setMark(String mark) {
		this.mark = mark;
	}
	public String getSlcx() {
		return slcx;
	}
	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}
	
	
	public String getIds() {
		return ids;
	}
	public void setIds(String ids) {
		this.ids = ids;
	}
	public String execute(){
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());

			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			
			this.slcx = slcx == null ? "" : slcx;
						
			params.put("slcx", slcx);
			
			results = filterModelService.pagination(page, params);
			if(results == null)
				results = new ArrayList();
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("filterModelPageData", results);
			this.getServletRequest().setAttribute("slcx", slcx);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
	
	public String addFilterModel(){
		SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
		String id = java.util.UUID.randomUUID().toString();
		FilterModel filetrModel = new FilterModel();
		filetrModel.setId(id);
		filetrModel.setSlcx(this.slcx);
		filetrModel.setMark(this.mark);
		filetrModel.setCreator(user.getUsername());
		filetrModel.setCreatedate(new Date());
		try{
			filterModelService.addFilterModel(filetrModel);
			this.getServletRequest().setAttribute("slcx", slcx);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	
	public String updateFilterModel(){
		SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
		String id = this.id;
		FilterModel filetrModel = new FilterModel();
		filetrModel.setId(id);
		filetrModel.setSlcx(this.slcx);
		filetrModel.setMark(this.mark);
		filetrModel.setCreator(user.getUsername());
		filetrModel.setCreatedate(new Date());
		try{
			filterModelService.updateFilterModel(filetrModel);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String getFilterModel(){
		String id = this.id;
		FilterModel filetrModel = new FilterModel();

		try{
			filetrModel = filterModelService.loadFilterModel(id);
			JSONObject jsonObject = JSONObject.fromObject(filetrModel);
			if (filetrModel != null)
				this.setJson(jsonObject.toString());
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}
	
	
	public String deteleFilterModel(){
		String[] id = ids.split("&");
		try{
			filterModelService.deleteFilterModel(id);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	
	public String isfilterModelExist(){
		boolean bln = false;
		String oper = this.getServletRequest().getParameter("oper");
		String id = this.getServletRequest().getParameter("id");
		if(oper == null){
			oper = "create";
		}
		try{
			List<FilterModel> list = filterModelService.findFilterModel(this.slcx);
			if(oper.equals("create")){
				if(list != null && list.size() > 0){
					bln = true;
				}
			}else if(oper.equals("update")){
				if(list != null){
					if(list.size() >= 2){
						bln = true;
					}else if(list.size() == 1){
						if(!list.get(0).getId().equals(id)){
							bln = true;
						}
					}
				}
			}
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		this.setJson(String.valueOf(bln));
		return JSON;
	}
	
	
}
