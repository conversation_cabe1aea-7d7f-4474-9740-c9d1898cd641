package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CarProductionModelId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CocPhotoId implements java.io.Serializable {

	// Fields

	private String model;
	private String filename;
	private String vercode;

	// Constructors

	/** default constructor */
	public CocPhotoId() {
	}

	/** full constructor */
	public CocPhotoId(String model, String filename,String vercode) {
		this.model = model;
		this.filename = filename;
		this.vercode = vercode;
	}

	// Property accessors

	public String getModel() {
		return this.model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}
	
	public String getVercode() {
		return vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}


	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof CocPhotoId))
			return false;
		CocPhotoId castOther = (CocPhotoId) other;

		return ((this.getModel() == castOther.getModel()) || (this.getModel() != null
				&& castOther.getModel() != null && this.getModel().equals(
				castOther.getModel())))
				&& ((this.getFilename() == castOther.getFilename()) || (this
						.getFilename() != null
						&& castOther.getFilename() != null && this.getFilename()
						.equals(castOther.getFilename())))
				&& ((this.getVercode() == castOther.getVercode()) || (this
						.getVercode() != null
						&& castOther.getVercode() != null && this.getVercode()
						.equals(castOther.getVercode())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getModel() == null ? 0 : this.getModel().hashCode());
		result = 37 * result
				+ (getFilename() == null ? 0 : this.getFilename().hashCode());
		result = 37 * result
				+ (getVercode() == null ? 0 : this.getVercode().hashCode());
		return result;
	}

}