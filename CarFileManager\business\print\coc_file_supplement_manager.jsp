<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt;background-color: #f6f6f6;
	   	div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		}
	p {font-family:"宋体";font-size: 10pt;font-weight:bold;}
	fieldset { padding:0; border:0; margin-top:25px; }
	input.text { width:12; padding: .4em; }
	input.text1 { width:12; padding: .4em; background:#bcd4ec;}
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align: center; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/jquery-cookie.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	
	$("#coc_dialog").dialog({bgiframe: true,autoOpen: false,width: 500,height: 310,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'打印': function() {
				var reasonObj = $(this).find('#reason');
				var descriptionObj = $(this).find('#description');
				
				if(allFields==null){
					allFields = $([]).add(reasonObj).add(descriptionObj);
				}
				
				allFields.removeClass('ui-state-error');
				
				if($(reasonObj).val()==""){
					$(reasonObj).addClass('ui-state-error');
					updateTips($(this).find('#validateTips'),'请选择补打原因！');			
					
					return ;
				}
				
				if($(descriptionObj).attr('disabled')==false&&!checkLength($(descriptionObj),1,100)){
					$(descriptionObj).addClass('ui-state-error');
					updateTips($(this).find('#validateTips'),'描述字段不能为空,最大长度为100！');		
					
					return ;
				}
				
				//alert($("#vin").val()+","+$("#cocSequence").val());
				//alert($("#vin")[0].value+",$("#cocSequence")[0].value);
				var infos = new Array(2);
				infos[0] = $(reasonObj).val();
				infos[1] = encodeURI(encodeURI($(descriptionObj).val()));
				
				window.document.coc.printCOCSupplement($("#vin")[0].value,$("#cocSequence")[0].value,infos); 
				
				$(this).dialog('close');
				return ;
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			updateTips($(this).find('#validateTips'),'');
			$('#description').attr('disabled',true);
		}
	});
	
	$('#reason').change(function(){
		if($(this).val()=='E'){
			$('#description').attr('disabled',false);
		}else{
			$('#description').val('');
			$('#description').attr('disabled',true);
		}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {}
	});

	$('#vin').bind('keyup',function(event) {   
		if(event.keyCode==13){ 
			var coc_printer = $.cookie("cocprinter");
			var photo_printer = $.cookie("photoprinter");
			var messageObj = $('#message_dialog');
   	   		
			if(coc_printer==null){
				$("#print_info_div").hide();
				messageObj.find('#message').text('警告:请设置COC证书打印机！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}
			
			if(photo_printer==null){
				$("#print_info_div").hide();
				messageObj.find('#message').text('警告:请设置车型照片打印机！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}
     		
			messageObj = $('#coc_dialog');
		   	messageObj.dialog('open');
		  //window.document.coc.printCOCSupplement($("#vin")[0].value,$("#cocSequence")[0].value);  
   		}   
   		//if($.trim($('#vin')[0].value).length==17){
   			//window.document.coc.printCOC($("#vin")[0].value);
   	   	//}
    });
       
	$("#search").click(function() {
		var coc_printer = $.cookie("cocprinter");
		var photo_printer = $.cookie("photoprinter");
		var messageObj = $('#message_dialog');
	   		
		if(coc_printer==null){
			$("#print_info_div").hide();
			messageObj.find('#message').text('警告:请设置COC证书打印机！');
   	   		messageObj.dialog('open');
   	   		
			return ;
		}
		
		if(photo_printer==null){
			$("#print_info_div").hide();
			messageObj.find('#message').text('警告:请设置车型照片打印机！');
   	   		messageObj.dialog('open');
   	   		
			return ;
		}
		
		messageObj = $('#coc_dialog');
	   	messageObj.dialog('open');
	   	
	   	return;
	});
				
}
);

function vinFocus(){
	$("#vin")[0].focus(); 
	$("#vin").addClass("text ui-widget-content ui-corner-all");
}

function showInfo(vin,cocnum,model,color,engineType,engineModel,produceDate){
	$("#vin")[0].value = vin;
	$("#cocSequence")[0].value = cocnum;
	$("#model")[0].value = model;
	$("#color")[0].value = color;
	$("#engineType")[0].value = engineType;
	$("#engineModel")[0].value = engineModel;
	$("#produceDate")[0].value = produceDate;
	
	$("#vin")[0].select();
}

function readPrinterCookie(){
	var cookie = new Array(2);
	cookie[0] = $.cookie("cocprinter");
	cookie[1] = $.cookie("photoprinter");
	
	return cookie;
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<!-- 
<p>VIN:<input type="text" id="vin" class="text ui-widget-content ui-corner-all"/>&nbsp;&nbsp;<button id="create-user" class="ui-button ui-state-default ui-corner-all">查询</button></p>
 -->
<div id="print_info_div" align="center"> 
	<jsp:plugin name="coc" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet.class" codebase="." archive = "../applet/com.dawnpro.dfpv.carfilemanager.print.applet.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/com-jaspersoft-ireport.jar,../applet/Qrcode_encoder.jar,../applet/iText-2.1.0.jar,../applet/iTextAsian.jar" 
	iepluginurl="http://********/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="120" width="900">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="COCSupplement"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
<div align="center">
<table width="750" height="63" border="0">
	<tr>
		<td width="180"  height="19" align="left" style="font-family:arial;font-size: 18pt; " >流水号:</td>
		<td colspan="3" align="left"><input id="cocSequence" type="text" width="100" class="text ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; " /></td>
		<td align="left"></td>
		<td width="24"></td>
	</tr>
	<tr>
		<td height="19" align="left" style="font-family:arial;font-size: 18pt; " >VIN:</td>
		<td colspan="3" align="left">
			<table cellpadding="0" border="0" cellspacing="0">
				<tr>
					<td><input id="vin" type="text" width="100" class="text ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; " /></td>
					<td>&nbsp;&nbsp;<button id="search" class="ui-button ui-state-default ui-corner-all">查询</button></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td align="left" style="font-family:arial;font-size: 18pt; ">发动机型号:</td>
		<td colspan="3" align="left"><input id="engineModel" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt;" readOnly/></td>
	</tr>
	<tr>
		<td height="19" align="left" style="font-family:arial;font-size: 18pt; ">车&nbsp;&nbsp;型:</td>
		<td><input id="model" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt; width:200px;" readOnly></td>
		<td align="left" style="font-family:arial;font-size: 18pt; ">颜&nbsp;&nbsp;色:</td>
		<td><input id="color" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt; width:200px;" readOnly/></td>
		
	</tr>
	<tr>
		<td height="19" align="left" style="font-family:arial;font-size: 18pt; ">发动机编号:</td>
		<td><input id="engineType" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt; width:200px;" readOnly/></td>
		<td width="140" align="left" style="font-family:arial;font-size: 18pt; ">制造日期:</td>
		<td><input id="produceDate" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt; width:200px;" readOnly></td>
		
	</tr>
	
</table>
</div>
<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div id="coc_dialog" title="补打窗口" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" >
		<table width="100%" border="0">
	  		<tr>
	  			<td><label>原因</label></td>
				<td><s:select name="reason" list="#request.supplementMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" ></s:select></td>
	  		</tr>
	  		<tr>
	  			<td><label>描述</label></td>
				<td><textarea id='description' name='description' rows='5' cols='40' disabled></textarea></td>
	  		</tr>
		</Table>
	</form>
	</fieldset>
</div>
</body>
</html>