<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#export").attr("disabled", true);
	
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
    

	$("#query").click(function(){
			var qc1 = $('#qc1').val();
			var qstate = $('#qstate').val();
			if(qc1==""&&qstate==""){
			 	var messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:请输入查询条件！');
	   	   		messageObj.dialog('open');
			}else{
				location.href="publicNoticeCarModelManager.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&menuid="+menuid;
			}
	});    

	$("#query").click(function(){

		location.href="uploadStateMonitor.action";
	});

	$("#update").click(function(){
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = id + this.value + ","; 
             }
      	});
      	if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要修改上传的数据！');
   	   		messageObj.dialog('open');      
   	   		return false;		
      	}else{
      		type='update';
      		$('#uploadStateMonitor_dialog').find("#ids").val(id);
      		$('#uploadStateMonitor_dialog').data('title.dialog', '修改上传').dialog('open');	
      		return false;	
      	}
		
	});
	
	
	$("#delete").click(function(){
		alert();
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = id + this.value + ","; 
             }
      	});
      	if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要上传撤销的数据！');
   	   		messageObj.dialog('open');      
   	   		return false;		
      	}else{
      		type='delete';
      		$('#uploadStateMonitor_dialog').find("#ids").val(id);
      		$('#uploadStateMonitor_dialog').data('title.dialog', '上传撤销').dialog('open');	
      		return false;	
      	}
		
	});
	
	
	$("#uploadStateMonitor_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 300,
		height: 200,
		modal: true,
		buttons: {
			'取消': function() {
				//clear($(this));
				//alert($(this).find("#ids").val());
				$(this).find("#reason").val('');
				$(this).find("#ids").val('');
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#uploadStateMonitor_dialog');
					allFields = $([]).add(parent.find('#reason'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#uploadStateMonitor_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
			        alert(type);
					if(type=="update"){
						jQuery.ajax({
				            url: 'business/uploadStateMonitor!isSuccessState.action',
					        data: {'ids' : $(this).find('#ids').val()},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#uploadStateMonitor_dialog');
					            if(json2Bean(data).json=="false"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'请选择上传成功的记录!');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].action="business/uploadStateMonitor!updateState.action?type=3&currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="delete"){
						jQuery.ajax({
				            url: 'business/uploadStateMonitor!isSuccessState.action',
					        data: {'ids' : $(this).find('#ids').val(),'type':'4'},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#uploadStateMonitor_dialog');
					            if(json2Bean(data).json=="false"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'请选择上传成功的记录!');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].type.value='4';
									dialog.find('#createForm')[0].action="business/uploadStateMonitor!updateState.action?currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });					

					}
				}
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			//clear($(this));
			$(this).find("#reason").val('');
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			type = null;
			//$('#car_color_mapping_dialog').find('#vin').attr('readonly',false);
		}
	});
	
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});


	function validate(parent){
		//return true;
		var obj = $(parent).find('#reason');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过100！');
			obj.focus();			
			return false;
		}

		return true;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var slcx = $('#slcx').val();
			location.href="uploadStateMonitor.action?currentPage="+$('#jump').val()+"&slcx="+encodeURI(encodeURI(slcx))+"&menuid="+menuid;
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var slcx = $('#slcx').val();
				location.href=$(this).attr('value')+"&slcx="+encodeURI(encodeURI(slcx))+"&menuid="+menuid;
		 });
	  });
	  
	
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif',
  	   		buttonImageOnly: true}, $.datepicker.regional['zh']));
	$('#beginDate').datepicker($.datepicker.regional['zh']); 
	$('#endDate').datepicker($.datepicker.regional['zh']); 
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
<!--form id="qfrm" name="qfrm"-->
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">			
			<tr>
			   <td width="80%"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">序号</th> 
			    <th width="6%">状态描述</th>
			    <th width="6%">时间</th>

			  </tr>
		</thead>
		<tbody>
			<s:iterator id="item" value="#request.uploadStateMonitorPageData" status="obj"  > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
				
			  		<td><s:property value="#obj.count" /></td>
			  		<td><s:property value="descript" /></td>
			  		<td><s:property value="depdatetime" /></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadStateMonitor.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadStateMonitor.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadStateMonitor.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadStateMonitor.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
<!--/form-->
</div>

<div id="uploadStateMonitor_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td align="left"><label><P>原因:</label></td>
				<td><input type="text" id="reason" name="reason"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20"/>
					</td>
	    	</tr>			
		</Table>
		<input type='hidden' id='ids' name='ids'/>
		<input type='hidden' id='type' name='type'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

</body>
</html>