/**
 * 
 */
package com.dawnpro.dfpv.carfilemanager.module.business.share.service.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDefine;
import com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDefineService;

/**
 * <AUTHOR>
 *
 */
public class InterfaceDefineServiceImpl implements InterfaceDefineService {

	/* (non-Javadoc)
	 * @see com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDefineService#addInterfaceDefine(com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDefine)
	 */
	public void addInterfaceDefine(InterfaceDefine def) {
		// TODO Auto-generated method stub

	}

	/* (non-Javadoc)
	 * @see com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDefineService#pagination(com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page, java.lang.Object[])
	 */
	public List<?> pagination(Page page, Object[] params) {
		// TODO Auto-generated method stub
		return null;
	}

	/* (non-Javadoc)
	 * @see com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDefineService#load(java.lang.String)
	 */
	public InterfaceDefine load(String id) {
		// TODO Auto-generated method stub
		return null;
	}

	/* (non-Javadoc)
	 * @see com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDefineService#updateInterfaceDefine(java.lang.Object)
	 */
	public void updateInterfaceDefine(Object def) {
		// TODO Auto-generated method stub

	}

	/* (non-Javadoc)
	 * @see com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDefineService#deleteInterfaceDefine(java.lang.Object)
	 */
	public void deleteInterfaceDefine(Object def) {
		// TODO Auto-generated method stub

	}

}
