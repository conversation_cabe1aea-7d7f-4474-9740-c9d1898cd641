<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="business-action" extends="struts-base" namespace="/business">
		<action name="certificateManager" class="certificateManagerAction">
			<result name="auto">print/gas_auto_file_manager.jsp</result>
			<result name="cocQuery">print/coc_file_batch_print.jsp</result>
			<result name="g95cocQuery">print/g95_coc_file_batch_print.jsp</result>
			<result name="xnycocQuery">print/xny_coc_file_batch_print.jsp</result>
			<result name="supplement">print/coc_file_supplement_manager.jsp</result>
			<result name="number">print/coc_file_number_supplement_manager.jsp</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		<action name="publicNoticeCarModelManager" class="publicNoticeCarModelManagerAction">
			<result name="success">car/public_notice_car_model_manager.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">publicNoticeCarModelManager.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="currentPage">${currentPage}</param>
 				<param name="qc1">${qc1}</param>
 				<param name="qstate">${qstate}</param>
  			</result>
  			<result name="send" type="redirect-action">
 				<param name="actionName">sendCarModel.action</param>
 				<param name="type">${type}</param>
 				<param name="menuid">${menuid}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
		</action>
		<action name="carRecall" class="carRecallAction">
			<result name="success">car/car_recall.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">carRecall.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="currentPage">${currentPage}</param>
				<param name="smodel">${smodel}</param>
				<param name="state">${state}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
		</action>
		<action name="carRecallQuery" class="carRecallQueryAction">
			<result name="success">car/car_recall_query.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">carRecallQuery.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
		</action>
		<action name="carInfo" class="carInfoAction">
			<result name="success">car/car_info.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">carInfo.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="currentPage">${currentPage}</param>
				<param name="qvin">${qvin}</param>
				<param name="startDate">${startDate}</param>
				<param name="endDate">${endDate}</param>
				<param name="cocStartDate">${cocStartDate}</param>
				<param name="cocEndDate">${cocEndDate}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
  			<result name="excel1" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>            
		</action>
		
		<action name="carVerCodeAction" class="carVerCodeAction">
			<result name="success">car/carVerCode.jsp</result>
  			<result name="view" type="redirect-action">
 				<param name="actionName">carVerCodeAction.action</param>
 				<param name="qfactory">${qfactory}</param>
 				<param name="qc1">${qc1}</param>
 				<param name="qvin">${qvin}</param>
 				<param name="currentPage">${currentPage}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  		
  			      
		</action>
		
		
		<action name="interfaceLog" class="interfaceLogAction">
			<result name="success">interface_log.jsp</result>
			<result name="text" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">fileStream</param>
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">2048</param>
            </result>
			<result name="error">../../interface_log_error.jsp</result>
		</action>
		<action name="cocCancel" class="cocCancelAction">
			<result name="success">print/coc_cancel_manager.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">cocCancel.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		<action name="gasVer" class="gasVerAction">
			<result name="success">verManager/gas_info.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">gasVer.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="currentPage">${currentPage}</param>
 				<param name="qslcx">${qslcx}</param>
 				<param name="qstate">${qstate}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		<action name="cocVer" class="cocVerAction">
			<result name="success">verManager/coc_info.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">cocVer.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qc1">${qc1}</param>
 				<param name="qstate">${qstate}</param>
 				<param name="currentPage">${currentPage}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		
		<action name="hwcocVer" class="hwcocVerAction">
			<result name="success">verManager/hwcoc_info.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">hwcocVer.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qc1">${qc1}</param>
 				<param name="qstate">${qstate}</param>
 				<param name="currentPage">${currentPage}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		
		<action name="proenv" class="proenvAction">
			<result name="success">verManager/pro_info.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">proenv.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qc1">${qc1}</param>
 				<param name="qstate">${qstate}</param>
 				<param name="currentPage">${currentPage}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		<action name="modelver" class="modelverAction">
			<result name="success">verManager/car_vercode_info.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">modelver.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		<action name="sendCarModel" class="sendCarModelAction">
			<result name="success">send_car_model_manager.jsp</result>	
				<result name="view" type="redirect-action">
 				<param name="actionName">sendCarModel.action</param>
 				<param name="type">${type}</param>
 				<param name="menuid">${menuid}</param>
  			</result>
		</action>		
		<action name="productionCarModelManager" class="productionCarModelManagerAction">
			<result name="success">car/production_car_model_manager.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">productionCarModelManager.action</param>
 				<param name="menuid">${menuid}</param>
 				<param name="currentPage">${currentPage}</param>
 				<param name="qmodel">${qmodel}</param>
 				<param name="qstate">${qstate}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  	
            <result name="send" type="redirect-action">
 				<param name="actionName">sendCarModel.action</param>
 				<param name="type">${type}</param>
 				<param name="menuid">${menuid}</param>
  			</result>
		</action>	
		<action name="cocPhotoManager" class="cocPhotoManagerAction">
			<result name="success">verManager/car_photo_info.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">cocPhotoManager</param>
 				<param name="menuid">${menuid}</param>
 				<param name="currentPage">${currentPage}</param>
 				<param name="qmodel">${qmodel}</param>
 				<param name="qstate">${qstate}</param>
  			</result>
  			<result name="alert_view">verManager/alert_car_photo_info.jsp</result>	
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
		</action>
		<action name="carColor" class="carColorAction">
			<result name="success">car/car_color_mapping.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">carColor</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qcolor">${qcolor}</param>
 				<param name="qbaseColor">${qbaseColor}</param>
 				<param name="currentPage">${currentPage}</param>
 				
  			</result>
		</action>
		
		<action name="lqzNeutralAction" class="lqzNeutralAction">
			<result name="success">car/lqzneutral.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">lqzNeutralAction</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${qdxcx}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>	
		
		<action name="cocModelTypeAction" class="cocModelTypeAction">
			<result name="success">car/coc_model_type.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">cocModelTypeAction</param>
 				<param name="menuid">${menuid}</param>
 				<param name="model">${model}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>
		
		<action name="yearCodeAction" class="yearCodeAction">
			<result name="success">car/year_code.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">yearCodeAction</param>
 				<param name="menuid">${menuid}</param>
 				<param name="year">${year}</param>				
  			</result>			
		</action>
		
		
		<action name="typicalityNeutral" class="typicalityNeutralAction">
			<result name="success">car/typicality_neutral.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">typicalityNeutral</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${qdxcx}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  			
		</action>			
		<action name="typicalityNeutralPhoto" class="typicalityNeutralPhotoAction">
			<result name="success">car/typicality_neutral_photo.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">typicalityNeutralPhoto</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${qdxcx}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  	
		</action>
		<!-- fuelupload start-->			
		<action name="fuelUpload" class="fuelUploadAction">
			<result name="success">fuelupload/fueluploadReport.jsp</result>	
			<result name="view" type="redirect-action">
 				<param name="actionName">fuelUploadAction</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${qdxcx}</param>				
  			</result>
  			
  			<result name="pdf" type="stream">
            	<param name="contentType">application/pdf</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  
            
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>  	              			
		</action>
			
		<action name="uploadUser" class="uploadUserAction">
			<result name="success">fuelupload/uploadUser_manager.jsp</result>	
		</action>	
			
		<action name="holiday" class="holidayAction">
			<result name="success">fuelupload/holiday_manager.jsp</result>	
		</action>	
			
		<action name="filterModel" class="filterModelAction">
			<result name="success">fuelupload/filterModel_manager.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">filterModel</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${slcx}</param>							
  			</result>	
		</action>	
		
		<action name="uploadStateManager" class="uploadStateManagerAction">
			<result name="success">fuelupload/uploadstate_manager.jsp</result>
			<result name="errState">fuelupload/uploadstate_modify.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">uploadStateManager</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${vin}</param>				
 				<param name="qdxcx">${pmodel}</param>				
 				<param name="qdxcx">${uploadType}</param>				
 				<param name="qdxcx">${uploadSate}</param>				
 				<param name="qdxcx">${beginDate}</param>				
 				<param name="qdxcx">${endDate}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>   							
		</action>
		
		<action name="uploadStateQuery" class="uploadStateQueryAction">
			<result name="success">fuelupload/uploadstate_query.jsp</result>
			<result name="errState">fuelupload/uploadstate_modify.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">uploadStateManager</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${vin}</param>				
 				<param name="qdxcx">${pmodel}</param>				
 				<param name="qdxcx">${uploadType}</param>				
 				<param name="qdxcx">${uploadSate}</param>				
 				<param name="qdxcx">${beginDate}</param>				
 				<param name="qdxcx">${endDate}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>   							
		</action>
		
		<action name="uploadScjcQuery" class="UploadScjcQueryAction">
			<result name="success">fuelupload/uploadScjc_query.jsp</result>
			<result name="errState">fuelupload/uploadScjc_modify.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">uploadScjcQuery</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${vin}</param>				
 				<param name="qdxcx">${pmodel}</param>				
 				<param name="qdxcx">${uploadType}</param>				
 				<param name="qdxcx">${uploadSate}</param>				
 				<param name="qdxcx">${beginDate}</param>				
 				<param name="qdxcx">${endDate}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>   							
		</action>
		
		<action name="uploadWqbfQuery" class="UploadWqbfQueryAction">
			<result name="success">fuelupload/uploadWqbf_query.jsp</result>
			<result name="errState">fuelupload/uploadWqbf_modify.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">uploadWqbfQuery</param>
 				<param name="menuid">${menuid}</param>
 				<param name="qdxcx">${vin}</param>				
 				<param name="qdxcx">${pmodel}</param>				
 				<param name="qdxcx">${uploadType}</param>				
 				<param name="qdxcx">${uploadSate}</param>				
 				<param name="qdxcx">${beginDate}</param>				
 				<param name="qdxcx">${endDate}</param>				
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>   							
		</action>				
		
		<action name="COCQuery" class="COCQueryAction">
			<result name="success">fuelupload/uploadstate_cocquery.jsp</result>
			<result name="excel" type="stream">
	            	<param name="contentType">application/vnd.ms-excel</param>   
	                <param name="inputName">excelStream</param>            
	               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
	                <param name="bufferSize">5096</param>
	         </result>  
		</action>
		
		<!-- 
		<action name="worker" class="worker">
			<result name="success">test/interfaceWorker.jsp</result>
		</action>
		 -->
		
		<action name="uploadStateModify" class="uploadStateModifyAction">
			<result name="success">fuelupload/uploadstate_modify.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">uploadStateModify</param>
 				<param name="menuid">${menuid}</param>
  			</result>				
		</action>	
		<!-- 2018-05-16 -->
		<action name="cocUploadStateModify" class="cocUploadStateModifyAction">
			<result name="success">fuelupload/uploadstate_cocmodify.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">cocUploadStateModify</param>
 				<param name="menuid">${menuid}</param>
  			</result>				
		</action>
		
		<action name="carPublicModelIcon" class="carPublicModelIconAction">
			<result name="success">car/car_public_model_icon.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">carPublicModelIcon</param>
 				<param name="menuid">${menuid}</param>
  			</result>				
		</action>	
		
		<action name="uploadStateMonitor" class="uploadStateMonitorAction">
			<result name="success">fuelupload/uploadstate_monitor.jsp</result>				
			<result name="gasuploadlog">fuelupload/gasupload_log.jsp</result>				
		</action>	
		
		<!-- fuelupload end -->	
				
		<action name="carModelSelect" class="carModelSelectAction">
			<result name="success">car/carModelSelect.jsp</result>	
		</action>	
		
		<!-- 
		<action name="interfaceWorker" class="interfaceWorker">
			<result name="success">test/interfaceWorker.jsp</result>
		</action>	
		 -->		
		 
		 <action name="certificateExport" class="certificateExportAction">
			<result name="success">fuelupload/certificateExport.jsp</result>
			<result name="excel" type="stream">
	            	<param name="contentType">application/vnd.ms-excel</param>   
	                <param name="inputName">excelStream</param>            
	               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
	                <param name="bufferSize">5096</param>
	         </result>  
		</action>
	</package>
</struts>