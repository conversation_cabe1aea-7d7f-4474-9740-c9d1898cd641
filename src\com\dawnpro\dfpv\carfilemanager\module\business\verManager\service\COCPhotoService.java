package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhoto;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhotoId;

public interface COCPhotoService {
	
	public void addPhoto(CocPhoto photo);
	
	public void addPhoto(CocPhoto[] photo);
	
	public void updatePhoto(String oldfilename,CocPhoto photo);
	
	public void updatePhoto(CocPhoto[] photo);
	
	public void deletePhoto(CocPhoto photo);
	
	public void deletePhoto(CocPhoto[] photo);
	
	public void effectCarModelPhoto(String version,CocPhoto photo);
	
	public boolean isCarModelPhotoExist(CocPhotoId id);
	
	public CocPhoto loadCarModelPhotoObj(CocPhotoId id);
	
	public String getMexPhotoVercode(CocPhotoId id);
	
	public List<CocPhoto> findCarModelPhoto(CocPhotoId id);
	
	public List<CocPhoto> findCarModelPhoto(CocPhotoId[] id);
	
	public List<CocPhoto> findEffectCarModelPhoto(String model);
	
	public List<CocPhoto> pagination(Page page,Object[] params);
	
	public List<CocPhoto> allEffectCarModelPhoto();
	public List<CocPhoto> findCarModelPhoto(String hql, Object[] params);
	
	public List<CocPhoto> findCarMoldePhoto(Object[] params);
	
	public List findCarModelMaterialno(String materialno);
	
	public List findCarModelMaterialnoNotExist();
}
