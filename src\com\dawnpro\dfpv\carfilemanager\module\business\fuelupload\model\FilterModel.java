package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model;

import java.util.Date;

/**
 * FilterModel generated by MyEclipse Persistence Tools
 */

public class FilterModel implements java.io.Serializable {

	// Fields

	private String id;

	private String slcx;

	private String creator;

	private Date createdate;

	private String mark;

	// Constructors

	/** default constructor */
	public FilterModel() {
	}

	/** minimal constructor */
	public FilterModel(String id) {
		this.id = id;
	}

	/** full constructor */
	public FilterModel(String id, String slcx, String creator, Date createdate,
			String mark) {
		this.id = id;
		this.slcx = slcx;
		this.creator = creator;
		this.createdate = createdate;
		this.mark = mark;
	}

	// Property accessors

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSlcx() {
		return this.slcx;
	}

	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreatedate() {
		return this.createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}

	public String getMark() {
		return this.mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

}