<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var tgvercode = null;
	var tmodel = null;
	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	//$("#export1").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				//if(operatePerrmission[i].flag.indexOf("export1")!=-1){
					//$("#export1").attr("disabled", false);
				//}
            }
        }
    });
	
	$("#query").click(function(){
	    var qc1 = $('#qc1').val();
		var qvin = $('#qvin').val();
		var qfactory = $('#qfactory').val();
	   	var currentPage=$('#currentPage_temp').val();
		if(qc1==""&&qvin==""&&qfactory==""){
		 	var messageObj = $('#message_dialog');
	   		messageObj.find('#message').text('警告:请输入查询条件！');
	   		messageObj.dialog('open');
		}else{
			location.href="carVerCodeAction.action?qc1="+encodeURI(encodeURI(qc1))+"&qvin="+qvin+"&qfactory="+qfactory+"&menuid="+menuid;
		}
	});
	
	$("#update").click(function() {
	       	var id = "";
	       	var index = 0;
	       	var messageObj = null;
	        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
	      	checkedObj.each(function(){
	         	if(this.checked==true){
					index++;
					id = this.value; 
	             }
	      	});

	   		if(index==1){
	   			type = "update";
	   			jQuery.ajax({
		            url: 'business/carVerCodeAction!carVerCode.action',		           
		            data: 'vin='+id, 
			        type: 'POST',
		            beforeSend: function() {
		            
		            },
		            error: function(request) {
		                
		            },
		            success: function(data) {
			            var content = json2Bean(data).json;
			            var carObj = eval("("+content.toString()+")"); 
		            	var dialogObj = $('#public_notice_dialog');
						
						setDialogValue(dialogObj,carObj);
			       	    dialogObj.find('#vin').attr('readonly',true);
			       	    dialogObj.data('title.dialog', '未绑定版本车辆信息').dialog('open');
						
		            }
		        });
	   			
	   	    	
	   	   	}else if(index<1){
	   	   	 	messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
	   	   		messageObj.dialog('open');
	   	   	 }else if(index>1){
				messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
		   		messageObj.dialog('open');
	   	   	 }
	    	
		});
	
	
	
	
	$("#imp").click(function(){//导入相关车型信息
		var parent = $('#public_notice_dialog');
		var obj = $(parent).find('#model');
		updateTips($(parent).find('#validateTips'),'');
		if(!checkLength(obj,0,20)||checkLength(obj,0,0)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且最大长度为20！');			
			return false;
		}
		
		var factory = parent.find('#factory').val();
		jQuery.ajax({
			url: 'business/carVerCodeAction!findcarVerCode.action?model='+$(obj).val()+'&factory='+factory,	
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            if(content!=''){
		            var jsonObj = eval("("+content.toString()+")"); 
		            
		            $('#info_dialog').find("#impgvercode").val(jsonObj.gver1);
			    	$('#info_dialog').find("#impcvercode").val(jsonObj.cver1);
			    	$('#info_dialog').find("#impzvercode").val(jsonObj.zver1);
			    	$('#info_dialog').find("#imppvercode").val(jsonObj.pvercode);
			    	$('#info_dialog').find("#impevercode").val(jsonObj.pver1);
	            }
	    
				
	            
	        }
	    });

		var messageObj = $('#info_dialog');
	   	messageObj.find('#message').text('警告:当前输入的相关车型参数可能会被覆盖，是否继续？');
		messageObj.dialog('open');   		
	});
	
	 $("#info_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 800,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
			    
			    var impgvercode =  $('#info_dialog').find("#impgvercode").val();
			    var impcvercode =  $('#info_dialog').find("#impcvercode").val();
			    var impzvercode =  $('#info_dialog').find("#impzvercode").val();
			    var imppvercode =  $('#info_dialog').find("#imppvercode").val();
			    var impevercode =  $('#info_dialog').find("#impevercode").val(); 
			    $('#public_notice_dialog').find("#gvercode").val(impgvercode);
			    $('#public_notice_dialog').find("#cvercode").val(impcvercode);
			    $('#public_notice_dialog').find("#zvercode").val(impzvercode);
			    $('#public_notice_dialog').find("#pvercode").val(imppvercode);
			    $('#public_notice_dialog').find("#evercode").val(impevercode);
				$(this).dialog('close');
			}
		}
	});
	
	
	
	$("#public_notice_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 370,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#public_notice_dialog');
					allFields = $([]).add(parent.find('#vin')).add(parent.find('#engineNo')).add(parent.find('#model'))
					.add(parent.find('#engineType')).add(parent.find('#color')).add(parent.find('#prodDate'))
					.add(parent.find('#cocNum')).add(parent.find('#cocColor')).add(parent.find('#materialNo'))
					.add(parent.find('#gvercode')).add(parent.find('#cvercode')).add(parent.find('#zvercode'))
					.add(parent.find('#evercode'))
					.add(parent.find('#pvercode'));
				}
				allFields.removeClass('ui-state-error');
				
				//if(validate('#public_notice_dialog')==true){
				    var qc1 = $('#qc1').val();
					var qvin = $('#qvin').val();
					var qfactory = $('#qfactory').val();
				   	var currentPage=$('#currentPage_temp').val();
				   var dialog = $('#public_notice_dialog');
				    dialog.find('#createForm')[0].action="business/carVerCodeAction!updateModel.action?qc1="+encodeURI(encodeURI(qc1))+"&qvin="+qvin+"&qfactory="+qfactory+"&currentPage="+currentPage;
					dialog.find('#createForm')[0].submit();
				//}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			$('#public_notice_dialog').find('#vin').attr('readonly',false);
		}
		});
    
	
	$('#jump').bind('keyup',function(event) {
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		$(this).blur(function(){
			var qc1 = $('#qc1').val();
			var qstate = $('#qstate').val();
			location.href="carVerCodeAction.action?currentPage="+$('#jump').val()+"&qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&menuid="+menuid;
		});
	});
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qc1 = $('#qc1').val();
				var qvin = $('#qvin').val();
				var qfactory = $('#qfactory').val();
				location.href=$(this).attr('value')+"&qc1="+encodeURI(encodeURI(qc1))+"&qvin="+qvin+"&qfactory="+qfactory+"&menuid="+menuid;
		 });
	});
	
});


function validate(parent){
	var obj = $(parent).find('#vin');
	if(!checkLength(obj,1,20)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'VIN编码字段不能为空,最大长度为20！');			
		return false;
	}
	obj = $(parent).find('#engineNo');
	if(!checkLength(obj,0,20)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'发动机号字段最大长度为20！');			
		return false;
	}
	obj = $(parent).find('#model');
	if(!checkLength(obj,0,50)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'生产车型最大长度为50！');			
		return false;
	}
	obj = $(parent).find('#engineType');
	if(!checkLength(obj,0,30)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'发动机型号最大长度为30！');			
		return false;
	}
	obj = $(parent).find('#color');
	if(!checkLength(obj,0,50)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'颜色最大长度为50！');			
		return false;
	}
	obj = $(parent).find('#prodDate');
	if(!checkLength(obj,0,10)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'生产日期最大长度为10！');			
		return false;
	}
	obj = $(parent).find('#cocNum');
	if(!checkLength(obj,0,20)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'COC编码最大长度为20！');			
		return false;
	}
	obj = $(parent).find('#cocColor');
	if(!checkLength(obj,0,20)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'COC颜色最大长度为20！');			
		return false;
	}
	obj = $(parent).find('#materialNo');
	if(!checkLength(obj,0,20)){
		obj.addClass('ui-state-error');
		updateTips($(parent).find('#validateTips'),'车型物料号最大长度为20！');			
		return false;
	}
	return true;
}

function setDialogValue(dialogObj,jsonObj){
	dialogObj.find('#vin').val(jsonObj.vin);
	dialogObj.find('#engineNo').val(jsonObj.engineNo);
	dialogObj.find('#model').val(jsonObj.model);
	dialogObj.find('#engineType').val(jsonObj.engineType);
	dialogObj.find('#color').val(jsonObj.color);
	dialogObj.find('#prodDate').val(jsonObj.prodDate);
	dialogObj.find('#factory').val(jsonObj.factory);
	dialogObj.find('#cocColor').val(jsonObj.cocColor);
	dialogObj.find('#remark').val(jsonObj.remark);
	dialogObj.find('#state').val(jsonObj.state);
	dialogObj.find('#materialNo').val(jsonObj.materialNo);
	dialogObj.find('#gvercode').val(jsonObj.gvercode);
	dialogObj.find('#cvercode').val(jsonObj.cvercode);
	dialogObj.find('#zvercode').val(jsonObj.zvercode);
	dialogObj.find('#pvercode').val(jsonObj.pvercode);
	dialogObj.find('#evercode').val(jsonObj.evercode);
	
}

function clear(dialogObj){
	dialogObj.find('#vin').val("");
	dialogObj.find('#engineNo').val("");
	dialogObj.find('#model').val("");
	dialogObj.find('#engineType').val("");
	dialogObj.find('#color').val("");
	dialogObj.find('#prodDate').val("");
	dialogObj.find('#cocNum').val("");
	dialogObj.find('#cocColor').val("");
	dialogObj.find('#remark').val("");
	dialogObj.find('#state').val("");
	dialogObj.find('#materialNo').val("");
	dialogObj.find('#gvercode').val("");
	dialogObj.find('#cvercode').val("");
	dialogObj.find('#zvercode').val("");
	dialogObj.find('#pvercode').val("");
	dialogObj.find('#evercode').val("");
	
	dialogObj.find('#vin').attr('readonly',false);
	dialogObj.find('#engineNo').attr('readonly',false);
	dialogObj.find('#model').attr('readonly',false);
	dialogObj.find('#engineType').attr('readonly',false);
	dialogObj.find('#color').attr('readonly',false);
	dialogObj.find('#prodDate').attr('readonly',false);
	dialogObj.find('#cocNum').attr('readonly',false);
	dialogObj.find('#materialNo').attr('readonly',false);
	dialogObj.find('#cocColor').attr('readonly',false);
	dialogObj.find('#gvercode').attr('readonly',false);
	dialogObj.find('#cvercode').attr('readonly',false);
	dialogObj.find('#zvercode').attr('readonly',false);
	dialogObj.find('#pvercode').attr('readonly',false);
	dialogObj.find('#evercode').attr('readonly',false);
	type = null;
}
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="6"><p>
				VIN:<input type="text" id="qvin" name="qvin" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.qvin!=null"> value="<s:property value="#request.qvin" />"</s:if>/>  		
				生产车型:<input type="text" id=qc1 name="model" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.qc1!=null"> value="<s:property value="#request.qc1" />"</s:if>/>  	    
				工厂:<s:select name="qfactory" list="#request.factorytype"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qfactory"></s:select> 

				</td>
			</tr>
			
			<tr><td width="80%"></td>
			  <td width="7%" align="right"></td>
			  <td width="7%" align="right"></td>
			  <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
  			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="10%">VIN</th>
			    <th width="10%">实例车型</th>
			    <th width="8%">制造日期</th>
				<th width="8%">燃油版本</th>
				<th width="8%">COC版本</th>
				<th width="8%">召回版本</th>
				<th width="8%">照片版本</th>
				<th width="8%">环保版本</th>
				<th width="8%">过点点位</th>
				<!--  <th width="5%">操作</th>-->
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="model" /></td>
			  		<td><s:property value="prodDate" /></td>
			  		<td width="8%" style="word-wrap:break-word;  word-break: break-all;"><s:property value="gvercode" /></td>
			  		<td width="8%" style="word-wrap:break-word;  word-break: break-all;"><s:property value="cvercode" /></td>
			  		<td width="8%" style="word-wrap:break-word;  word-break: break-all;"><s:property value="zvercode" /></td>
			  		<td width="8%" style="word-wrap:break-word;  word-break: break-all;"><s:property value="pvercode" /></td>
			  		<td width="8%" style="word-wrap:break-word;  word-break: break-all;"><s:property value="evercode" /></td>
			  		<td><s:property value="gddw" /></td><!--
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="vin" />">查看</a></td>-->
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carVerCodeAction.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carVerCodeAction.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carVerCodeAction.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carVerCodeAction.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value='<s:property value="#request.page.currentPage"/>'/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="public_notice_dialog">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>VIN编码</label></td>
				<td><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>工厂</label></td>
				<td><input type="text" id="factory" name="factory" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>实例车型</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="20" />
				<button name="imp" id="imp" class="ui-button ui-state-default ui-corner-all" style="position:static">导入</button>
				</td>
			</tr>
			
			
			<tr>
				<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="gvercode" name="gvercode" class="text ui-widget-content ui-corner-all" size="15"  /></td>
				<td><label><P>COC版本号</label></td>
				<td><input type="text" id="cvercode" name="cvercode" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>召回版本号</label></td>
				<td><input type="text" id="zvercode" name="zvercode" class="text ui-widget-content ui-corner-all" size="15"  /></td>
								
			</tr>
			
			<tr>
				<td><label><P>环保版本号</label></td>
				<td><input type="text" id="evercode" name="evercode" class="text ui-widget-content ui-corner-all" size="15" /></td>							
				<td><label><P>照片版本</label></td>
				<td><input type="text" id="pvercode" name="pvercode" class="text ui-widget-content ui-corner-all" size="15" /></td>							
	
			</tr>
			
		</Table>
		<input type='hidden' id='modreason' name='modreason'/>		
		<input type='hidden' id='time' name='time'/>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='remark' name='remark'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="public_notice_display_dialog" title="查看窗口" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>VIN编码</label></td>
				<td><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>实例车型</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="15" readonly="true" />
				</td>
			</tr>
			<tr>
				<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="gvercode" name="gvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC版本号</label></td>
				<td><input type="text" id="cvercode" name="cvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>召回版本号</label></td>
				<td><input type="text" id="zvercode" name="zvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>环保版本号</label></td>
				<td><input type="text" id="evercode" name="evercode" class="text ui-widget-content ui-corner-all" size="15" /></td>		
			</tr>
			<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</Table>
		</form>
	</fieldset>
</div>

<div id="info_dialog">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
			<tr>
				<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="impgvercode" name="impgvercode" class="text ui-widget-content ui-corner-all" size="15"  /></td>
				<td><label><P>COC版本号</label></td>
				<td><input type="text" id="impcvercode" name="impcvercode" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>召回版本号</label></td>
				<td><input type="text" id="impzvercode" name="impzvercode" class="text ui-widget-content ui-corner-all" size="15"  /></td>
			</tr>
			<tr>
				<td><label><P>环保版本号</label></td>
				<td><input type="text" id="impevercode" name="impevercode" class="text ui-widget-content ui-corner-all" size="15" /></td>							
				<td><label><P>照片版本</label></td>
				<td><input type="text" id="imppvercode" name="imppvercode" class="text ui-widget-content ui-corner-all" size="15" /></td>							
			</tr>
		</Table>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>


</html>