package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * CarInfo entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarInfoAuto implements java.io.Serializable {

	// Fields

	private String vin;
	private String engineNo;
	private String model;
	private String engineType;
	private String color;
	private String prodDate;
	private String cocNum;
	private String cocColor;
	private String state;
	private String gvercode;
	private String cvercode;
	private String gasman;
	private String cocman;
	private String remark;
	private String creator;
	private String time;

	// Constructors

	/** default constructor */
	public CarInfoAuto() {
	}

	/** minimal constructor */
	public CarInfoAuto(String vin) {
		this.vin = vin;
	}
	
	public CarInfoAuto(String vin,String model) {
		this.vin = vin;
		this.model = model;
	}
	
	/** full constructor */
	public CarInfoAuto(String vin, String engineNo, String model,
			String engineType, String color, String prodDate, String cocNum,
			String cocColor, String state, String gvercode, String cvercode,
			String gasman, String cocman, String remark, String creator,
			String time) {
		this.vin = vin;
		this.engineNo = engineNo;
		this.model = model;
		this.engineType = engineType;
		this.color = color;
		this.prodDate = prodDate;
		this.cocNum = cocNum;
		this.cocColor = cocColor;
		this.state = state;
		this.gvercode = gvercode;
		this.cvercode = cvercode;
		this.gasman = gasman;
		this.cocman = cocman;
		this.remark = remark;
		this.creator = creator;
		this.time = time;
	}

	// Property accessors

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getEngineNo() {
		return this.engineNo;
	}

	public void setEngineNo(String engineNo) {
		this.engineNo = engineNo;
	}

	public String getModel() {
		return this.model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getEngineType() {
		return this.engineType;
	}

	public void setEngineType(String engineType) {
		this.engineType = engineType;
	}

	public String getColor() {
		return this.color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public String getProdDate() {
		return this.prodDate;
	}

	public void setProdDate(String prodDate) {
		this.prodDate = prodDate;
	}

	public String getCocNum() {
		return this.cocNum;
	}

	public void setCocNum(String cocNum) {
		this.cocNum = cocNum;
	}

	public String getCocColor() {
		return this.cocColor;
	}

	public void setCocColor(String cocColor) {
		this.cocColor = cocColor;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getGvercode() {
		return this.gvercode;
	}

	public void setGvercode(String gvercode) {
		this.gvercode = gvercode;
	}

	public String getCvercode() {
		return this.cvercode;
	}

	public void setCvercode(String cvercode) {
		this.cvercode = cvercode;
	}

	public String getGasman() {
		return this.gasman;
	}

	public void setGasman(String gasman) {
		this.gasman = gasman;
	}

	public String getCocman() {
		return this.cocman;
	}

	public void setCocman(String cocman) {
		this.cocman = cocman;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

}