<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6; }
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var wizardModel = "two";
	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#public_notice_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 520,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'保存': function() {
				addOrUpdate(this);
			}
			//,
			//'下一页': function() {
			//	wizard();
			//}
		},
		
		close: function() {
			clear($(this));
			updateTips($(this).find('#validateTips'),'');	
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
			$('#public_notice_dialog').find('#c1').attr('readonly',false);

			//wizardModel = "one";
			//wizard();
		}
		
		});

	function addOrUpdate(button){
		if(allFields==null){
			var parent = $('#public_notice_dialog');
			allFields = $([]).add(parent.find('#c1')).add(parent.find('#flag')).add(parent.find('#c2'))
			.add(parent.find('#c3')).add(parent.find('#c4')).add(parent.find('#c27')).add(parent.find('#c28'))
			.add(parent.find('#c29')).add(parent.find('#c30')).add(parent.find('#c5')).add(parent.find('#c6'))
			.add(parent.find('#c7')).add(parent.find('#c8')).add(parent.find('#c9')).add(parent.find('#c10'))
			.add(parent.find('#c11')).add(parent.find('#c19')).add(parent.find('#c26')).add(parent.find('#c37')).add(parent.find('#c38'))
			.add(parent.find('#c39')).add(parent.find('#c40')).add(parent.find('#c41')).add(parent.find('#c42'))
			.add(parent.find('#c46'))
			.add(parent.find('#c47')).add(parent.find('#c49')).add(parent.find('#c50'))
			.add(parent.find('#c51')).add(parent.find('#c53'))
			.add(parent.find('#c57'))
			.add(parent.find('#c59')).add(parent.find('#c60')).add(parent.find('#c62'))
			.add(parent.find('#c63')).add(parent.find('#c73')).add(parent.find('#c86'))
			.add(parent.find('#c88')).add(parent.find('#c89')).add(parent.find('#c79')).add(parent.find('#c77'))
			.add(parent.find('#c80')).add(parent.find('#c76'))
			.add(parent.find('#c96')).add(parent.find('#c97')).add(parent.find('#c98')).add(parent.find('#c99'))
			.add(parent.find('#c100')).add(parent.find('#mpflag')).add(parent.find('#c101'));
		}
		allFields.removeClass('ui-state-error');
		
		if(validate('#public_notice_dialog')==true){
			var dlgButton = $('.ui-dialog-buttonpane button');//
			dlgButton.attr('disabled', 'disabled');
	    	dlgButton.addClass('ui-state-disabled');
	    	
			if(type=="add"){
				var params = "c1="+$('#public_notice_dialog').find('#c1').val()+"&flag="+$('#public_notice_dialog').find('#flag').val();			
				jQuery.ajax({
		            url: 'business/publicNoticeCarModelManager!isCarModelExist.action',		           
		            data: params, 
			        type: 'POST',
		            beforeSend: function() {
		            
		            },
		            error: function(request) {
		                
		            },
		            success: function(data) {					       									
			            var dialog = $('#public_notice_dialog');
			            if(json2Bean(data).json=="true"){
							updateTips(dialog.find('#validateTips'),'产品型号:['+dialog.find('#c1').val()+'] 识别码:'+dialog.find('#flag').val()+' 状态为未生效，此数据已经存在！');		
							var dlgButton = $('.ui-dialog-buttonpane button');
							dlgButton.attr('disabled', false);
					        dlgButton.removeClass('ui-state-disabled');
						}else{
							//dialog.find("#currentPage").val('<s:property value="#request.publicNoticePage.currentPage" />');
							//dialog.find("#qc1").val($('#qc1').val());
							//dialog.find("#qstate").val($('#qstate').val());
							
							dialog.find('#createForm')[0].action="business/publicNoticeCarModelManager!addCarModel.action";
							dialog.find('#createForm')[0].submit();
						}
		            }
		        });
			}else if(type=="update"){
				var dialog = $('#public_notice_dialog');
				dialog.find("#currentPage").val('<s:property value="#request.publicNoticePage.currentPage" />');
				dialog.find("#qc1").val($('#qc1').val());
				dialog.find("#qstate").val($('#qstate').val());
				
				dialog.find('#createForm')[0].action="business/publicNoticeCarModelManager!updateCarModel.action";
				dialog.find('#createForm')[0].submit();
			}
		}
	}

	function wizard(){
		if(wizardModel=="two"){
			$('#public_notice_dialog').dialog('option', 'buttons', { 
				"取消": function() {
					$(this).dialog('close');
				}, 
				"保存": function() { 
					addOrUpdate();
				}, 
				"上一页": function() {  
					wizard();
				}
				} );
			
			$('#public_notice_dialog').find('#one').hide();
			$('#public_notice_dialog').find('#two').show();
			wizardModel = "one";
		}else if(wizardModel=="one"){
			$('#public_notice_dialog').dialog('option', 'buttons', {
				"取消": function() { 
					$(this).dialog('close');
				}, 
				"保存": function() {
					addOrUpdate();
				}, 
				"下一页": function() {  
					wizard();
				}
				} );

				$('#public_notice_dialog').find('#one').show();
				$('#public_notice_dialog').find('#two').hide();
				wizardModel = "two";
			}
	}
	
	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 520,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
			}
			//,
			//'下一页': function() {
			//	wizardToDisplay();
			//}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
			$('#public_notice_dialog').find('#c1').attr('readonly',false);

			//wizardModel = "one";
			//wizardToDisplay();
		}
		});

	function wizardToDisplay(){
		if(wizardModel=="two"){
			$('#public_notice_display_dialog').dialog('option', 'buttons', { 
				"取消": function() {
					$(this).dialog('close');
				}, 
				"上一页": function() {  
					wizardToDisplay();
				}
				} );
			
			$('#public_notice_display_dialog').find('#one').hide();
			$('#public_notice_display_dialog').find('#two').show();
			wizardModel = "one";
		}else if(wizardModel=="one"){
			$('#public_notice_display_dialog').dialog('option', 'buttons', {
				"取消": function() { 
					$(this).dialog('close');
				}, 
				"下一页": function() {  
					wizardToDisplay();
				}
				} );

				$('#public_notice_display_dialog').find('#one').show();
				$('#public_notice_display_dialog').find('#two').hide();
				wizardModel = "two";
			}
	}
	
	$("#upload_dialog").dialog({bgiframe: true,autoOpen: false,width: 650,height: 440,modal: true,
		buttons: {
			'取消': function() {
				$('#upload_dialog').uploadifyClearQueue();
				//updateTips($(this).find('#validateTips'),'');	
				//$(this).dialog('close');
			},
			'上传':function() {
				$('#upload_dialog').uploadifyUpload(); 
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}

			wizardModel = "one";
			wizard();
		}
	});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				dialog.find("#currentPage").val('<s:property value="#request.publicNoticePage.currentPage" />');
				dialog.find("#qc1").val($('#qc1').val());
				dialog.find("#qstate").val($('#qstate').val());
				if(type=="delete"){					
					formObj[0].action = "business/publicNoticeCarModelManager!deleteCarModels.action";
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/publicNoticeCarModelManager!effectCarModel.action";
					formObj[0].submit();
				}else if(type=="published"){
					formObj[0].action = "business/publicNoticeCarModelManager!publishedCarModel.action";
					formObj[0].submit();
				}
			}
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
    $("#confirm_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				type = "delete";
	   	   		messageObj = $('#operate_dialog');
	   	   		messageObj.find('#message').text('提示:确定删除选择的数据？');
	   	   		messageObj.dialog('open');
	   	   		//alert($(this).find('#c1').val());
	   	   		messageObj.find('#c1').val($(this).find('#c1').val()); 
	   	   		$(this).dialog('close'); 
			}		
		}
	});
	
    
	$("#imp").click(function(){//导入相关车型信息
		var parent = $('#public_notice_dialog');	
		var obj = $(parent).find('#c1');		
		updateTips($(parent).find('#validateTips'),'');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且最大长度为20！');			
			return false;
		}
		//type = "imp";
		var gasVer1=$('#public_notice_dialog').find('#c1').val() + ",";
		jQuery.ajax({
	        //url: 'business/publicNoticeCarModelManager!effectCarModelList.action?c1='+$(obj).val(),		           
	        url: 'business/cocVer!findGgcxFromBv01.action?c1='+$(obj).val(),		           
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var jsonObj = eval("("+content.toString()+")"); 
	            
	            var pmodelListObj = $('#pmodelList');  
	            
	            $('#pmodelList option').remove();
	            for(var i=0;i<jsonObj.length;i++){
	    			//$(pmodelListObj).append("<option value='"+jsonObj[i].id.c1+","+jsonObj[i].id.vercode+","+jsonObj[i].flag+"'>"+jsonObj[i].id.c1+"("+jsonObj[i].flag+")</option>");   
	    			$(pmodelListObj).append("<option value='"+jsonObj[i].ggcx+","+jsonObj[i].vercode+"'>"+jsonObj[i].ggcx+"("+jsonObj[i].vercode+")</option>");   
	            }
   
	        }
	    });
		var messageObj = $('#pubilc_notice_dialog');
	   	messageObj.find('#message').text('警告:当前输入的相关车型参数可能会被覆盖，是否继续？');
   		messageObj.dialog('open');
   		
	});
		
	
    $("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 460,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {							
				if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
					var value = $(this).find('#pmodelList').val().split(',');
					impPubData(value[0],value[1]);
				}
				
				
				$(this).dialog('close');
			}
		}
	});	
	
	//根据车型导入相关信息
	function impPubData(slcx,vercode){
		jQuery.ajax({
            //url: 'business/publicNoticeCarModelManager!impDataFromBv01.action',		           
            url: 'business/publicNoticeCarModelManager!impDataFromBv01.action',		           
            data: {'qc1' : slcx+","+vercode},                 
	        type: 'POST',
            beforeSend: function() {
            
            },
            error: function(request) {
                
            },
            success: function(data) {
	            var content = json2Bean(data).json;
	            if(content==""){
					messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:没有找到对应的公告车型记录！');
		   	   		messageObj.dialog('open');	            
	            }else{	    	
		            var carObj = eval("("+content.toString()+")");
	            	var dialogObj = $('#public_notice_dialog');
					setImpData(dialogObj, carObj);
	            }

            }			
		})	
	}
	
	function setImpData(dialogObj, jsonObj){
		//2012-12-6
		dialogObj.find(":text").each(function(i){
			$(this).val(jsonObj[$(this).attr("name")]);
		}) ;
		dialogObj.find('#c41').find("option[text='"+ jsonObj.c41 +"']").attr("selected",true);		
	}		
	
	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

    	if(index==1){
        	var params = "c1="+id;
   			jQuery.ajax({
	            url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
	            data: params,
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#public_notice_dialog');
	            	type = "add";		
					setDialogValue(dialogObj,model);
					dialogObj.data('title.dialog', '新增公告车型').dialog('open');	
					dialogObj.find('#c1').val("");
					dialogObj.find('#flag').val("");
	            }
	        });
   			
   	    	
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
   	  		type = "add";		
			$('#public_notice_dialog').data('title.dialog', '新增公告车型').dialog('open');	
   	   	 }
		
	
		
	});

    $("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			var params = "c1="+id;
   			jQuery.ajax({
	            url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
	            data: params, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var content = data.json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_notice_dialog');
					if(carObj.state=="0"){
						setDialogValue(dialogObj,carObj);

		       	    	dialogObj.find('#c1').attr('readonly',true);
		       	    	//dialogObj.find('#flag').attr('readonly',true);
		       	    	dialogObj.data('title.dialog', '修改公告车型').dialog('open');
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "产品型号:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"产品型号:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能删除一条数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#confirm_dialog');
   	   		messageObj.find('#message').text('提示:是否删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#c1').val(id); 
   	   		   		
   	   	}
	
	});
	
	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "产品型号:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"产品型号:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		type = "effect";
     		var params = "c1="+id;
   	   		jQuery.ajax({
            	url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
            	data: params, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
            		var dialogObj = $('#public_notice_dialog');
					if(carObj.state=="0"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共'+index+'条数据');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#c1').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
            }
        });

   	   		
   	   	}
	});
	
	$("#published").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
       
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){ 
					id = this.value;
					info = "产品型号:"+tmp[0]+" 版本号:"+tmp[1];
				}else{ 
					id = id+"@"+this.value; 
					info = info+"&"+"产品型号:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});
      
   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要提交到待发布确认的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   	   	   
     		type = "published";
     		var params = "c1="+id;
     		
   	   		jQuery.ajax({
            	url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
            	data: params, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) { 
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
            		var dialogObj = $('#public_notice_dialog');
					if(carObj.state=="1"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定发布【'+info+'】！ 共'+index+'条数据,操作成功后会自动跳转到接口数据管理页面。');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#c1').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能发布状态为[生效]的数据！');
			   	   		messageObj.dialog('open');
					}
            	}
        	});
   	   	}
	});

	$("#query").click(function(){
			var qc1 = $('#qc1').val();
			var qstate = $('#qstate').val();
			if(qc1==""&&qstate==""){
			 	var messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('警告:请输入查询条件！');
	   	   		messageObj.dialog('open');
			}else{
				location.href="publicNoticeCarModelManager.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&menuid="+menuid;
			}
	});

	$("#c41").change(function(){ 
		 	$('#public_notice_dialog').find('#c86').val(this.value);
		}		
	);
	
	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		var param = "c1="+id;
		jQuery.ajax({
	        url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#public_notice_display_dialog');
	        
				setDialogValue(dialogObj,carObj);
								
	       	   	dialogObj.dialog('open');

	       	 
	        }
	    });

		return false;
	}

	$('#template').click(function() {
		location.href="publicNoticeCarModelManager!download.action";  
		
	});

	$('#import').click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>business/car/public_notice_upload.jsp',winName,params) 
	});
	
	$('#export').click(function() {
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		
		location.href="publicNoticeCarModelManager!exportData.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate;  
	});
	
	function validate(parent){
		var obj = $(parent).find('#c1');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'产品型号字段不能为空，最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#flag');
		if(!checkLength(obj,1,2)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'识别码字段不能为空，识别码字段最大长度为2！');			
			return false;
		}
		obj = $(parent).find('#mpflag');
		if(!checkLength(obj,1,2)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'铭牌识别码字段不能为空，铭牌识别码字段最大长度为2！');			
			return false;
		}
		obj = $(parent).find('#c101');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'驱动方式字段不能为空，驱动方式字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c2');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'产品名称字段不能为空，车辆名称字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c3');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'中文品牌字段不能为空，中文品牌字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c4');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'英文品牌字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c27');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'企业名称字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c28');
		if(!checkLength(obj,0,200)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'企业地址字段最大长度为200！');			
			return false;
		}
		obj = $(parent).find('#c29');
		if(!checkLength(obj,0,200)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'注册地址字段最大长度为200！');			
			return false;
		}
		obj = $(parent).find('#c30');
		if(!checkLength(obj,0,200)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产地址字段最大长度为200！');			
			return false;
		}
//		obj = $(parent).find('#c31');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'电子信箱字段最大长度为50！');			
//			return false; 
//		}
//		obj = $(parent).find('#c32');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'电话号码字段最大长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c33');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'传真号码字段最大长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c34');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'邮政编码字段最大长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c5');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'识别代号最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c6');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'前轮距字段不能为空，前轮距字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c7');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'后轮距字段不能为空，后轮距字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c8');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机字段不能为空，发动机字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c9');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机企业字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c10');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机排量字段不能为空，发动机排量字段最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#c11');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机功率字段不能为空，发动机功率字段最大长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c18');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'底盘型号字段最大长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c19');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'底盘类别字段最大长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c20');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'产品名称字段最大长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c25');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'产品型号字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c26');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'批次字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c37');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'长字段不能为空，长字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c38');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'宽字段不能为空，宽字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c39');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'高字段不能为空，高字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c40');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'燃料种类字段不能为空，燃料种类字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c41');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'依据标准字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c42');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'转向形式字段长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c43');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'货厢长字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c44');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'货厢宽字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c45');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'货厢高字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c46');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轴数字段不能为空，轴数字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c47');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轴距字段不能为空，轴距字段长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c48');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'弹簧片数字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c49');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轮胎数字段不能为空，轮胎数字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c50');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轮胎规格字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c51');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'总质量字段不能为空，总质量字段长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c52');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'额定质量字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c53');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'整备质量字段不能为空，整备质量字段长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c54');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'挂车质量字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c55');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'载质量字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c56');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'半挂鞍座字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c57');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'额定载客字段不能为空，额定载客字段长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c58');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'前排乘客字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c59');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'接近离去角字段长度为50！');			
			return false;
		}
		var value = obj.val();
		if(value.length>0){
			var index = value.indexOf("/");
			if(index==-1||index==0||index==(value.length-1)){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'接近离去角的数据格式必须以\'/\'分隔成接近角和离去角！');			
				return false;
			}
		}
		obj = $(parent).find('#c60');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'前悬后悬字段长度为50！');			
			return false;
		}
		value = obj.val();
		if(value.length>0){
			var index = value.indexOf("/");
			if(index==-1||index==0||index==(value.length-1)){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'前悬后悬的数据格式必须以\'/\'分隔成前悬和后悬！');			
				return false;
			}
		}
//		obj = $(parent).find('#c61');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'驾驶室字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c62');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'传动型式字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c63');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最高车速字段长度为50！');			
			return false;
		}
//		obj = $(parent).find('#c64');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'底企型1字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c65');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'底企型2字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c66');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'底企型3字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c67');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'底企型4字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c68');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'其它字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c69');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'制动前字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c70');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'制动后字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c71');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'制操前字段长度为50！');			
//			return false;
//		}
//		obj = $(parent).find('#c72');
//		if(!checkLength(obj,0,50)){
//			obj.addClass('ui-state-error');
//			updateTips($(parent).find('#validateTips'),'制操后字段长度为50！');			
//			return false;
//		}
		obj = $(parent).find('#c73');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'起动方式字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c86');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'排放水平字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c88');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'油耗字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c89');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轴荷字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c79');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轮数字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c77');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'产品商标字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c80');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'联系人字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#c76');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'法人代表字段长度为50！');			
			return false;
		}
		obj = $(parent).find('#zdjgl');//2012-8-24 lmc
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最大净功率长度为50！');			
			return false;
		}
		
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#c1').val(jsonObj.id.c1);
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		dialogObj.find('#flag').val(jsonObj.flag);
		dialogObj.find('#c2').val(jsonObj.c2);
		dialogObj.find('#c3').val(jsonObj.c3);
		dialogObj.find('#c4').val(jsonObj.c4);
		dialogObj.find('#c27').val(jsonObj.c27);
		dialogObj.find('#c28').val(jsonObj.c28);
		dialogObj.find('#c29').val(jsonObj.c29);
		dialogObj.find('#c30').val(jsonObj.c30);
//		dialogObj.find('#c31').val(jsonObj.c31);
//		dialogObj.find('#c32').val(jsonObj.c32);
//		dialogObj.find('#c33').val(jsonObj.c33);
//		dialogObj.find('#c34').val(jsonObj.c34);
		dialogObj.find('#c5').val(jsonObj.c5);
		dialogObj.find('#c6').val(jsonObj.c6);
		dialogObj.find('#c7').val(jsonObj.c7);
		dialogObj.find('#c8').val(jsonObj.c8);
		dialogObj.find('#c9').val(jsonObj.c9);
		dialogObj.find('#c10').val(jsonObj.c10);
		dialogObj.find('#c11').val(jsonObj.c11);
//		dialogObj.find('#c18').val(jsonObj.c18);
		dialogObj.find('#c19').val(jsonObj.c19);
//		dialogObj.find('#c20').val(jsonObj.c20);
//		dialogObj.find('#c25').val(jsonObj.c25);
		dialogObj.find('#c26').val(jsonObj.c26);
		dialogObj.find('#c37').val(jsonObj.c37);
		dialogObj.find('#c38').val(jsonObj.c38);
		dialogObj.find('#c39').val(jsonObj.c39);
		dialogObj.find('#c40').val(jsonObj.c40);		
		dialogObj.find('#c41').val(jsonObj.c41);
		//alert(jsonObj.c41);
		
		dialogObj.find('#c42').val(jsonObj.c42);
//		dialogObj.find('#c43').val(jsonObj.c43);
//		dialogObj.find('#c44').val(jsonObj.c44);
//		dialogObj.find('#c45').val(jsonObj.c45);
		dialogObj.find('#c46').val(jsonObj.c46);
		dialogObj.find('#c47').val(jsonObj.c47);
//		dialogObj.find('#c48').val(jsonObj.c48);
		dialogObj.find('#c49').val(jsonObj.c49);
		dialogObj.find('#c50').val(jsonObj.c50);
		dialogObj.find('#c51').val(jsonObj.c51);
//		dialogObj.find('#c52').val(jsonObj.c52);
		dialogObj.find('#c53').val(jsonObj.c53);
//		dialogObj.find('#c54').val(jsonObj.c54);
//		dialogObj.find('#c55').val(jsonObj.c55);
//		dialogObj.find('#c56').val(jsonObj.c56);
		dialogObj.find('#c57').val(jsonObj.c57);
//		dialogObj.find('#c58').val(jsonObj.c58);
		dialogObj.find('#c59').val(jsonObj.c59);
		dialogObj.find('#c60').val(jsonObj.c60);
//		dialogObj.find('#c61').val(jsonObj.c61);
		dialogObj.find('#c62').val(jsonObj.c62);
		dialogObj.find('#c63').val(jsonObj.c63);
//		dialogObj.find('#c64').val(jsonObj.c64);
//		dialogObj.find('#c65').val(jsonObj.c65);
//		dialogObj.find('#c66').val(jsonObj.c66);
//		dialogObj.find('#c67').val(jsonObj.c67);
//		dialogObj.find('#c68').val(jsonObj.c68);
//		dialogObj.find('#c69').val(jsonObj.c69);
//		dialogObj.find('#c70').val(jsonObj.c70);
//		dialogObj.find('#c71').val(jsonObj.c71);
//		dialogObj.find('#c72').val(jsonObj.c72);
		dialogObj.find('#c73').val(jsonObj.c73);
		dialogObj.find('#c86').val(jsonObj.c86);
		dialogObj.find('#c88').val(jsonObj.c88);
		dialogObj.find('#c89').val(jsonObj.c89);
		dialogObj.find('#c79').val(jsonObj.c79);
		dialogObj.find('#c77').val(jsonObj.c77);
		dialogObj.find('#c80').val(jsonObj.c80);
		dialogObj.find('#c76').val(jsonObj.c76);
		dialogObj.find('#zdjgl').val(jsonObj.zdjgl);//lmc
			
		dialogObj.find('#state').val(jsonObj.state);
		//2018-05-03
		dialogObj.find('#c96').val(jsonObj.c96);
		dialogObj.find('#c97').val(jsonObj.c97);
		dialogObj.find('#c98').val(jsonObj.c98);
		dialogObj.find('#c99').val(jsonObj.c99);
		dialogObj.find('#c100').val(jsonObj.c100);
		
		//2021-05-20
		dialogObj.find('#mpflag').val(jsonObj.mpflag);
		//2023-12-11
		dialogObj.find('#c101').val(jsonObj.c101);
	}
	
	function clear(dialogObj){
		dialogObj.find('#c1').val("");
		dialogObj.find('#flag').val("");
		dialogObj.find('#c2').val("");
		dialogObj.find('#c3').val("");
		dialogObj.find('#c4').val("");
		dialogObj.find('#c27').val("");
		dialogObj.find('#c28').val("");
		dialogObj.find('#c29').val("");
		dialogObj.find('#c30').val("");
//		dialogObj.find('#c31').val("");
//		dialogObj.find('#c32').val("");
//		dialogObj.find('#c33').val("");
//		dialogObj.find('#c34').val("");
		dialogObj.find('#c5').val("");
		dialogObj.find('#c6').val("");
		dialogObj.find('#c7').val("");
		dialogObj.find('#c8').val("");
		dialogObj.find('#c9').val("");
		dialogObj.find('#c10').val("");
		dialogObj.find('#c11').val("");
//		dialogObj.find('#c18').val("");
		dialogObj.find('#c19').val("");
//		dialogObj.find('#c20').val("");
//		dialogObj.find('#c25').val("");
		dialogObj.find('#c26').val("");
		dialogObj.find('#c37').val("");
		dialogObj.find('#c38').val("");
		dialogObj.find('#c39').val("");
		dialogObj.find('#c40').val("");
		dialogObj.find('#c41').val("");
		dialogObj.find('#c42').val("");
//		dialogObj.find('#c43').val("");
//		dialogObj.find('#c44').val("");
//		dialogObj.find('#c45').val("");
		dialogObj.find('#c46').val("");
		dialogObj.find('#c47').val("");
//		dialogObj.find('#c48').val("");
		dialogObj.find('#c49').val("");
		dialogObj.find('#c50').val("");
		dialogObj.find('#c51').val("");
//		dialogObj.find('#c52').val("");
		dialogObj.find('#c53').val("");
//		dialogObj.find('#c54').val("");
//		dialogObj.find('#c55').val("");
//		dialogObj.find('#c56').val("");
		dialogObj.find('#c57').val("");
//		dialogObj.find('#c58').val("");
		dialogObj.find('#c59').val("");
		dialogObj.find('#c60').val("");
//		dialogObj.find('#c61').val("");
		dialogObj.find('#c62').val("");
		dialogObj.find('#c63').val("");
//		dialogObj.find('#c64').val("");
//		dialogObj.find('#c65').val("");
//		dialogObj.find('#c66').val("");
//		dialogObj.find('#c67').val("");
//		dialogObj.find('#c68').val("");
//		dialogObj.find('#c69').val("");
//		dialogObj.find('#c70').val("");
//		dialogObj.find('#c71').val("");
//		dialogObj.find('#c72').val("");
		dialogObj.find('#c73').val("");
		dialogObj.find('#c86').val("");
		dialogObj.find('#c88').val("");
		dialogObj.find('#c89').val("");
		dialogObj.find('#c79').val("");
		dialogObj.find('#c77').val("");
		dialogObj.find('#c80').val("");
		dialogObj.find('#c76').val("");
		dialogObj.find('#vercode').val("");
		dialogObj.find('#state').val("");
		//2018-05-03
		dialogObj.find('#c96').val("");
		dialogObj.find('#c97').val("");
		dialogObj.find('#c98').val("");
		dialogObj.find('#c99').val("");
		dialogObj.find('#c100').val("");
		
		//2021-05-20
		dialogObj.find('#mpflag').val("");
		//2023-12-11
		dialogObj.find('#c101').val("");
		

		dialogObj.find('#c1').attr('readonly',false);
		dialogObj.find('#flag').attr('readonly',false);
		dialogObj.find('#c2').attr('readonly',false);
		dialogObj.find('#c3').attr('readonly',false);
		dialogObj.find('#c4').attr('readonly',false);
		dialogObj.find('#c27').attr('readonly',false);
		dialogObj.find('#c28').attr('readonly',false);
		dialogObj.find('#c29').attr('readonly',false);
		dialogObj.find('#c30').attr('readonly',false);
//		dialogObj.find('#c31').attr('readonly',false);
//		dialogObj.find('#c32').attr('readonly',false);
//		dialogObj.find('#c33').attr('readonly',false);
//		dialogObj.find('#c34').attr('readonly',false);
		dialogObj.find('#c5').attr('readonly',false);
		dialogObj.find('#c6').attr('readonly',false);
		dialogObj.find('#c7').attr('readonly',false);
		dialogObj.find('#c8').attr('readonly',false);
		dialogObj.find('#c9').attr('readonly',false);
		dialogObj.find('#c10').attr('readonly',false);
		dialogObj.find('#c11').attr('readonly',false);
//		dialogObj.find('#c18').attr('readonly',false);
		dialogObj.find('#c19').attr('readonly',false);
//		dialogObj.find('#c20').attr('readonly',false);
//		dialogObj.find('#c25').attr('readonly',false);
		dialogObj.find('#c26').attr('readonly',false);
		dialogObj.find('#c37').attr('readonly',false);
		dialogObj.find('#c38').attr('readonly',false);
		dialogObj.find('#c39').attr('readonly',false);
		dialogObj.find('#c40').attr('readonly',false);
		dialogObj.find('#c41').attr('readonly',false);
		dialogObj.find('#c42').attr('readonly',false);
//		dialogObj.find('#c43').attr('readonly',false);
//		dialogObj.find('#c44').attr('readonly',false);
//		dialogObj.find('#c45').attr('readonly',false);
		dialogObj.find('#c46').attr('readonly',false);
		dialogObj.find('#c47').attr('readonly',false);
//		dialogObj.find('#c48').attr('readonly',false);
		dialogObj.find('#c49').attr('readonly',false);
		dialogObj.find('#c50').attr('readonly',false);
		dialogObj.find('#c51').attr('readonly',false);
//		dialogObj.find('#c52').attr('readonly',false);
		dialogObj.find('#c53').attr('readonly',false);
//		dialogObj.find('#c54').attr('readonly',false);
//		dialogObj.find('#c55').attr('readonly',false);
//		dialogObj.find('#c56').attr('readonly',false);
		dialogObj.find('#c57').attr('readonly',false);
//		dialogObj.find('#c58').attr('readonly',false);
		dialogObj.find('#c59').attr('readonly',false);
		dialogObj.find('#c60').attr('readonly',false);
//		dialogObj.find('#c61').attr('readonly',false);
		dialogObj.find('#c62').attr('readonly',false);
		dialogObj.find('#c63').attr('readonly',false);
//		dialogObj.find('#c64').attr('readonly',false);
//		dialogObj.find('#c65').attr('readonly',false);
//		dialogObj.find('#c66').attr('readonly',false);
//		dialogObj.find('#c67').attr('readonly',false);
//		dialogObj.find('#c68').attr('readonly',false);
//		dialogObj.find('#c69').attr('readonly',false);
//		dialogObj.find('#c70').attr('readonly',false);
//		dialogObj.find('#c71').attr('readonly',false);
//		dialogObj.find('#c72').attr('readonly',false);
		dialogObj.find('#c73').attr('readonly',false);
		dialogObj.find('#c86').attr('readonly',false);
		dialogObj.find('#c88').attr('readonly',false);
		dialogObj.find('#c89').attr('readonly',false);
		dialogObj.find('#c79').attr('readonly',false);
		dialogObj.find('#c77').attr('readonly',false);
		dialogObj.find('#c80').attr('readonly',false);
		dialogObj.find('#c76').attr('readonly',false);
		dialogObj.find('#zdjgl').attr('readonly',false);
		
		//2018-05-03
		//dialogObj.find('#c96').attr('readonly',false);
		//dialogObj.find('#c97').attr('readonly',false);
		//dialogObj.find('#c98').attr('readonly',false);
		//dialogObj.find('#c99').attr('readonly',false);
		//dialogObj.find('#c100').attr('readonly',false);
		
		dialogObj.find('#mpflag').attr('readonly',false);
		dialogObj.find('#c101').attr('readonly',false);
		
		type = null;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var qc1 = $('#qc1').val();
			var qstate = $('#qstate').val();
			location.href="publicNoticeCarModelManager.action?currentPage="+$('#jump').val()+"&qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&menuid="+menuid;   
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qc1 = $('#qc1').val();
				var qstate = $('#qstate').val();
				
				location.href=$(this).attr('value')+"&qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&menuid="+menuid;
		 });
	  });
	
});

$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

             //给这行添加class值为over，并且当鼠标一出该行时执行函数

             $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="5"><p>产品型号:<input type="text" id="qc1" name='qc1' class="text ui-widget-content " size="10" <s:if test="#request.qc1!=null"> value="<s:property value="#request.qc1" />"</s:if> />	
				  状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr><td width="80%"></td>
			  <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
  			  <td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
			   <td width="60" align="right"><button id="published" class="ui-button ui-state-default ui-corner-all">待发布</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="5%">选择</th> 
			    <th width="10%">产品型号</th>
			    <th width="7%">识别码</th>
			    <th width="10%">产品名称</th>
				<th width="13%">发动机型号</th>		
				<th width="7%">状态</th>
				<th width="10%">版本</th>
				<th width="7%">创建人</th>
				<th width="11%">创建时间</th>
				<th width="11%">生效时间</th>
				<th width="11%">发布时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.publicNoticePageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.c1" />,<s:property value="id.vercode" />' ></td>
			  		<td><s:property value="id.c1" /></td>
			  		<td><s:property value="flag" /></td>
			  		<td><s:property value="c2" /></td>
   		      		<td><s:property value="c8" /></td>				
			  		<td>
			  			<s:if test="state==0">未生效</s:if>
			  			<s:elseif test="state==1">生效</s:elseif>
			  			<s:elseif test="state==12">待发布</s:elseif>
			  			<s:elseif test="state==2">已发布</s:elseif>
			  			<s:elseif test="state==9">历史</s:elseif>
			  		</td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="creator" /></td>		
			  		<td><s:property value="time" /></td>	
			  		<td><s:property value="activetime" /></td>	
			  		<td><s:property value="publishtime" /></td>	
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.c1" />,<s:property value="id.vercode" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.publicNoticePage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="publicNoticeCarModelManager.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="publicNoticeCarModelManager.action?currentPage=<s:property value="#request.publicNoticePage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage>=#request.publicNoticePage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="publicNoticeCarModelManager.action?currentPage=<s:property value="#request.publicNoticePage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="publicNoticeCarModelManager.action?currentPage=<s:property value="#request.publicNoticePage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.publicNoticePage.currentPage" />/总页数 <s:property value="#request.publicNoticePage.maxPage" /> 总记录数 <s:property value="#request.publicNoticePage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  <td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              <td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="public_notice_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table id="one" width="100%">
	    	<tr>
	    		<td width="9%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="8" />&nbsp;
					<button name="imp" id="imp" class="ui-button ui-state-default ui-corner-all" style="position:static">导入</td>
				<td width="9%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td width="9%"><label><P>产品名称</label></td>
				<td width="17%"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td width="8%"><label><P>中文品牌</label></td>
				<td width="17%"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>
				<td width="7%"><label><P>英文品牌</label></td>
				<td width="16%"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>企业名称</label></td>
				<td><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>企业地址</label></td>
				<td><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>注册地址</label></td>
				<td><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="14" /></td>			
			</tr>
			<tr>
				<td><label><P>生产地址</label></td>
				<td><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="14" /></td>	
				<td><label><P>识别代号</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>前轮距</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>后轮距</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>
				<td><label><P>发动机</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>发动机企业</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>发动机排量</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>发动机功率</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>
				<td><label><P>底盘类别</label></td>
				<td><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>批次</label></td>
				<td><input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>长</label></td>
				<td><input type="text" id="c37" name="c37" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>宽</label></td>
				<td><input type="text" id="c38"  name="c38" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>		
				<td><label><P>高</label></td>
				<td><input type="text" id="c39" name="c39" class="text ui-widget-content ui-corner-all" size="14" /></td>	
				<td><label><P>燃料种类</label></td>
				<td><input type="text" id="c40" name="c40" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>依据标准</label></td>
				<td><s:select name="c41" list="#request.emissionStaMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" cssStyle="font-size:8pt;"></s:select></td>
				<td><label><P>转向形式</label></td>
				<td><input type="text" id="c42"  name="c42" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>		
				<td><label><P>轴数</label></td>
				<td><input type="text" id="c46"  name="c46" class="text ui-widget-content ui-corner-all" size="14" /></td>	
				<td><label><P>轴距</label></td>
				<td><input type="text" id="c47" name="c47" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>轮胎数</label></td>
				<td><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>轮胎规格</label></td>
				<td><input type="text" id="c50"  name="c50" class="text ui-widget-content ui-corner-all" size="14" /></td>				
			</tr>
			<tr>		
				<td><label><P>总质量</label></td>
				<td><input type="text" id="c51" name="c51" class="text ui-widget-content ui-corner-all" size="14" /></td>	
				<td width="9%"><label><P>整备质量</label></td>
				<td width="17%"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>额定载客</label></td>
				<td><input type="text" id="c57" name="c57" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>接近离去角</label></td>
				<td><input type="text" id="c59" name="c59" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>
			<tr>	
				<td><label><P>前悬后悬</label></td>
				<td><input type="text" id="c60" name="c60" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>传动型式</label></td>
				<td><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>最高车速</label></td>
				<td><input type="text" id="c63" name="c63" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>起动方式</label></td>
				<td><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="14" /></td>				
			</tr>
			<tr>		
				<td><label><P>排放水平</label></td>
				<td><input type="text" id="c86"  name="c86" class="text ui-widget-content ui-corner-all" size="14" /></td>	
				<td><label><P>油耗</label></td>
				<td><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>轴荷</label></td>
				<td><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>轮数</label></td>
				<td><input type="text" id="c79" name="c79" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>	
			<tr>			
				<td><label><P>产品商标</label></td>
				<td><input type="text" id="c77" name="c77" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>联系人</label></td>
				<td><input type="text" id="c80" name="c80" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>法人代表</label></td>
				<td><input type="text" id="c76"  name="c76" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>最大净功率</label></td>
				<td><input type="text" id="zdjgl"  name="zdjgl" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>	
			<tr>			
				<td><label><P>商品名</label></td>
				<td><input type="text" id="c96" name="c96" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动电机型号</label></td>
				<td><input type="text" id="c97" name="c97" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动电机峰值功率</label></td>
				<td><input type="text" id="c98"  name="c98" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>动力电池额定电压</label></td>
				<td><input type="text" id="c99"  name="c99" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>动力电池额定容量</label></td>
				<td ><input type="text" id="c100" name="c100" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>铭牌识别码</label></td>
				<td ><input type="text" id="mpflag" name="mpflag" class="text ui-widget-content ui-corner-all" size="14" /></td>
				<td><label><P>驱动方式</label></td>
				<td ><input type="text" id="c101" name="c101" class="text ui-widget-content ui-corner-all" size="14" /></td>
			</tr>			
		</Table>
		
		
		<input type='hidden' id='vercode' name='vercode'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		<input type='hidden' id='currentPage' name='currentPage' />
		<input type='hidden' id='qc1' name='qc1' />
		<input type='hidden' id='qstate' name='qstate' />
		</form>
	</fieldset>
</div>

<div id="public_notice_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table id="one" width="100%">
	    		<tr>
	    		<td width="9%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>产品名称</label></td>
				<td width="17%"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>中文品牌</label></td>
				<td width="17%"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td width="7%"><label><P>英文品牌</label></td>
				<td width="16%"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业名称</label></td>
				<td><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业地址</label></td>
				<td><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>注册地址</label></td>
				<td><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>生产地址</label></td>
				<td><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>识别代号</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>前轮距</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>后轮距</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>发动机</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机企业</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机排量</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机功率</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>底盘类别</label></td>
				<td><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>批次</label></td>
				<td><input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>长</label></td>
				<td><input type="text" id="c37" name="c37" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>宽</label></td>
				<td><input type="text" id="c38"  name="c38" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>高</label></td>
				<td><input type="text" id="c39" name="c39" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>燃料种类</label></td>
				<td><input type="text" id="c40" name="c40" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>依据标准</label></td>
				<td><input type="text" id="c41" name="c41" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>转向形式</label></td>
				<td><input type="text" id="c42"  name="c42" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>		
				<td><label><P>轴数</label></td>
				<td><input type="text" id="c46"  name="c46" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>轴距</label></td>
				<td><input type="text" id="c47" name="c47" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎数</label></td>
				<td><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎规格</label></td>
				<td><input type="text" id="c50"  name="c50" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>总质量</label></td>
				<td><input type="text" id="c51" name="c51" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>			
				<td width="9%"><label><P>整备质量</label></td>
				<td width="17%"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>额定载客</label></td>
				<td><input type="text" id="c57" name="c57" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>接近离去角</label></td>
				<td><input type="text" id="c59" name="c59" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>前悬后悬</label></td>
				<td><input type="text" id="c60" name="c60" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>传动型式</label></td>
				<td><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>最高车速</label></td>
				<td><input type="text" id="c63" name="c63" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>起动方式</label></td>
				<td><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>排放水平</label></td>
				<td><input type="text" id="c86"  name="c86" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>油耗</label></td>
				<td><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轴荷</label></td>
				<td><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮数</label></td>
				<td><input type="text" id="c79" name="c79" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>	
			<tr>	
				<td><label><P>产品商标</label></td>
				<td><input type="text" id="c77" name="c77" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>联系人</label></td>
				<td><input type="text" id="c80" name="c80" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>法人代表</label></td>
				<td><input type="text" id="c76"  name="c76" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>最大净功率</label></td>
				<td><input type="text" id="zdjgl"  name="zdjgl" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>商品名</label></td>
				<td><input type="text" id="c96" name="c96" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动电机型号</label></td>
				<td><input type="text" id="c97" name="c97" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动电机峰值功率</label></td>
				<td><input type="text" id="c98"  name="c98" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>动力电池额定电压</label></td>
				<td><input type="text" id="c99"  name="c99" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>动力电池额定容量</label></td>
				<td ><input type="text" id="c100" name="c100" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>铭牌识别码</label></td>
				<td ><input type="text" id="mpflag" name="mpflag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>驱动方式</label></td>
				<td ><input type="text" id="c101" name="c101" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				
			</tr>							
		</Table>
		
		
		<input type='hidden' id='vercode' name='vercode'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="upload_dialog" title="文件上传窗口" style="display:none">
	<p id="validateTips"></p>
		<div id="fileQueue">
			<input type="file" name="uploadFile" id="uploadFile" />
			<a href="javascript:$('#uploadFile').uploadifyUpload();">上传</>
		</div>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='c1' name='c1'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	<input type='hidden' id='currentPage' name='currentPage' />
	<input type='hidden' id='qc1' name='qc1' />
	<input type='hidden' id='qstate' name='qstate' />
	</form>
</div>

<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div id="confirm_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='c1' name='c1'>
</div>

<div id="pubilc_notice_dialog" title="操作窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
  	公告车型型号:<select id='pmodelList' name='pmodelList'><option value="">请选择...</option></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

</div>

</body>
</html>