package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model;

/**
 * SysGasuploadLog generated by MyEclipse Persistence Tools
 */

public class SysGasuploadLog implements java.io.Serializable {

	// Fields

	private Long id;

	private String loginname;

	private String ipaddress;

	private String descript;

	private String depdatetime;

	private String menuid;

	private String state;

	private String datadate;

	// Constructors

	/** default constructor */
	public SysGasuploadLog() {
	}

	/** minimal constructor */
	public SysGasuploadLog(Long id) {
		this.id = id;
	}

	/** full constructor */
	public SysGasuploadLog(Long id, String loginname, String ipaddress,
			String descript, String depdatetime, String menuid, String state,
			String datadate) {
		this.id = id;
		this.loginname = loginname;
		this.ipaddress = ipaddress;
		this.descript = descript;
		this.depdatetime = depdatetime;
		this.menuid = menuid;
		this.state = state;
		this.datadate = datadate;
	}

	// Property accessors

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getLoginname() {
		return this.loginname;
	}

	public void setLoginname(String loginname) {
		this.loginname = loginname;
	}

	public String getIpaddress() {
		return this.ipaddress;
	}

	public void setIpaddress(String ipaddress) {
		this.ipaddress = ipaddress;
	}

	public String getDescript() {
		return this.descript;
	}

	public void setDescript(String descript) {
		this.descript = descript;
	}

	public String getDepdatetime() {
		return this.depdatetime;
	}

	public void setDepdatetime(String depdatetime) {
		this.depdatetime = depdatetime;
	}

	public String getMenuid() {
		return this.menuid;
	}

	public void setMenuid(String menuid) {
		this.menuid = menuid;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getDatadate() {
		return this.datadate;
	}

	public void setDatadate(String datadate) {
		this.datadate = datadate;
	}

}