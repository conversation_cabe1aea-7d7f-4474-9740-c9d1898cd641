<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#query").click(function(){
	
		var carmodel = $('#carmodel').val();
		var cver1 = $('#cver1').val();
		var gver1 = $('#gver1').val();
		var zver1 = $('#zver1').val();
		var ever1 = $('#ever1').val();
		var qfactory = $('#qfactory').val();
		if(carmodel==""&&cver1==""&&gver1==""&&zver1==""&&ever1==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="modelver.action?carmodel="+encodeURI(encodeURI(carmodel))
			    +"&ever1="+encodeURI(encodeURI(ever1))                         
				+"&cver1="+encodeURI(encodeURI(cver1))+"&gver1="+encodeURI(encodeURI(gver1))+"&zver1="+encodeURI(encodeURI(zver1))+"&qfactory="+qfactory+"&menuid="+menuid;
		}
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
          url: 'business/modelver!modelverInfo.action',		           
          data: {'carmodel':id}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	        	var dialogObj = $('#modelver_display_dialog');
				setDialogValue(dialogObj,carObj);
				//$('input').attr("readonly", true);
				dialogObj.find('input').attr("readonly", true);
				dialogObj.data('title.dialog', '查看车辆版本信息').dialog('open');
				
	        }
	    });

		return false;
	}

	function setDialogValue(dialogObj,jsonObj){
		//dialogObj.find('#carmodel').val(jsonObj.carmodel);
		dialogObj.find('#carmodel').val(jsonObj.id.carmodel);//20160627
		
		dialogObj.find('#gver1').val(jsonObj.gver1);
		//dialogObj.find('#gver2').val(jsonObj.gver2);
		//dialogObj.find('#gswtime').val(jsonObj.gswtime);
		dialogObj.find('#gvalitime').val(jsonObj.gvalitime);
		dialogObj.find('#gvaliuser').val(jsonObj.gvaliuser);
		//dialogObj.find('#gstate').val(jsonObj.gstate==1?"有效":jsonObj.gstate);

		dialogObj.find('#cver1').val(jsonObj.cver1);
		//dialogObj.find('#cver2').val(jsonObj.cver2);
		//dialogObj.find('#cswtime').val(jsonObj.cswtime);
		dialogObj.find('#cvalitime').val(jsonObj.cvalitime);
		dialogObj.find('#cvaliuser').val(jsonObj.cvaliuser);
		//dialogObj.find('#cstate').val(jsonObj.cstate==1?"有效":jsonObj.cstate);
		
		dialogObj.find('#pver1').val(jsonObj.pver1);
		//dialogObj.find('#cver2').val(jsonObj.cver2);
		//dialogObj.find('#cswtime').val(jsonObj.cswtime);
		dialogObj.find('#pvalitime').val(jsonObj.pvalitime);
		dialogObj.find('#pvaliuser').val(jsonObj.pvaliuser);
		//dialogObj.find('#cstate').val(jsonObj.cstate==1?"有效":jsonObj.cstate);

		
		dialogObj.find('#ever1').val(jsonObj.ever1);
		dialogObj.find('#evalitime').val(jsonObj.evalitime);
		dialogObj.find('#evaliuser').val(jsonObj.evaliuser);
		
		dialogObj.find('#zver1').val(jsonObj.zver1);
		//dialogObj.find('#zver2').val(jsonObj.zver2);
		//dialogObj.find('#zswtime').val(jsonObj.zswtime);
		dialogObj.find('#zvalitime').val(jsonObj.zvalitime);
		dialogObj.find('#zvaliuser').val(jsonObj.zvaliuser);
		//dialogObj.find('#zstate').val(jsonObj.zstate==1?"有效":jsonObj.zstate);
		
		if(jsonObj.photos.length>0){
			for(var i=0;i<jsonObj.photos.length;i++){
				$("#pvercode").append("<option value='"+jsonObj.photos[i].id.vercode+"'>"+jsonObj.photos[i].id.filename+"</option>");   
			}
			$("#pver").val(jsonObj.photos[0].id.vercode);
		}
		//20160625
		dialogObj.find('#factory').val(jsonObj.id.factory);
	}
	
	$("#pvercode").change(function(){
		$("#pver").val($(this).val());
	});
	
	$('#export').click(function() {
		var carmodel = $('#carmodel').val();
		var cver1 = $('#cver1').val();
		var gver1 = $('#gver1').val();
		var zver1 = $('#zver1').val();
		var qfactory = $('#qfactory').val();
		location.href="modelver!exportModelver.action?carmodel="+encodeURI(encodeURI(carmodel))+"&cver1="+encodeURI(encodeURI(cver1))
		+"&gver1="+encodeURI(encodeURI(gver1))+"&zver1="+encodeURI(encodeURI(zver1))+"&qfactory="+qfactory;    
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

	$("#modelver_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 930,height: 400,modal: true,
		buttons: {
			'关闭': function() {
				$(this).dialog('close');
			}
		},
		close: function() {
			$(this).find('input').attr('value','');
			$(this).find('#pvercode  option').remove();
		}
	});
	
	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var carmodel = $('#carmodel').val();
			var cver1 = $('#cver1').val();
			var gver1 = $('#gver1').val();
			var zver1 = $('#zver1').val();
			var qfactory = $('#qfactory').val();
			location.href="modelver.action?currentPage="+$('#jump').val()+"&carmodel="+encodeURI(encodeURI(carmodel))+"&cver1="+encodeURI(encodeURI(cver1))
				+"&gver1="+encodeURI(encodeURI(gver1))+"&zver1="+encodeURI(encodeURI(zver1))+"&qfactory="+qfactory+"&menuid="+menuid;   
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var carmodel = $('#carmodel').val();
				var cver1 = $('#cver1').val();
				var gver1 = $('#gver1').val();
				var zver1 = $('#zver1').val();
				var qfactory = $('#qfactory').val();
				location.href=$(this).attr('value')+"&carmodel="+encodeURI(encodeURI(carmodel))+"&cver1="+encodeURI(encodeURI(cver1))
					+"&gver1="+encodeURI(encodeURI(gver1))+"&zver1="+encodeURI(encodeURI(zver1))+"&qfactory="+qfactory+"&menuid="+menuid;
		 });
	});

	
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

          //给这行添加class值为over，并且当鼠标一出该行时执行函数

          $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="3"><p>
					生产车型:<input type="text" id="carmodel" name='carmodel' class="text ui-widget-content " size="10" <s:if test="#request.carmodel!=null"> value="<s:property value="#request.carmodel" />"</s:if> />	
				 	COC版本号:<input type="text" id="cver1" name='cver1' class="text ui-widget-content " size="10" <s:if test="#request.cver1!=null"> value="<s:property value="#request.cver1" />"</s:if> />
				 	燃油标签版本号:<input type="text" id="gver1" name='gver1' class="text ui-widget-content " size="10" <s:if test="#request.gver1!=null"> value="<s:property value="#request.gver1" />"</s:if> />
				 	召回版本号:<input type="text" id="zver1" name='zver1' class="text ui-widget-content " size="10" <s:if test="#request.zver1!=null"> value="<s:property value="#request.zver1" />"</s:if> /> 
  			    	环保版本号:<input type="text" id="ever1" name='ever1' class="text ui-widget-content " size="10" <s:if test="#request.ever1!=null"> value="<s:property value="#request.ever1" />"</s:if> />
  			    	工厂:<s:select name="qfactory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.qfactory"></s:select>
  			    </td>
				
			</tr>
			<tr>
				<td width="90%"></td>
			  <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			  <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			  
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="8%">生产车型</th>
                <th width="8%">工厂</th>
			    <th width="10%">COC版本号</th>
			    <th width="10%">燃油标签版本号</th>	
				<th width="10%">召回版本号</th>
				<th width="10%">环保版本号</th>
				<th width="6%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.carVerInfoPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><s:property value="id.carmodel" /></td>
			  		<td><s:property value="id.factory" /></td>
			  		<td><s:property value="cver1" /></td>
   		      		<td><s:property value="gver1" /></td>
   		      		<td><s:property value="zver1" /></td>
   		      		<td><s:property value="ever1" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.carmodel" />,<s:property value="id.factory" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.carVerInfoPage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="modelver.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.carVerInfoPage.currentPage==#request.carVerInfoPage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="modelver.action?currentPage=<s:property value="#request.carVerInfoPage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.carVerInfoPage.currentPage>=#request.carVerInfoPage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="modelver.action?currentPage=<s:property value="#request.carVerInfoPage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.carVerInfoPage.currentPage==#request.carVerInfoPage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="modelver.action?currentPage=<s:property value="#request.carVerInfoPage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.carVerInfoPage.currentPage" />/总页数 <s:property value="#request.carVerInfoPage.maxPage" /> 总记录数 <s:property value="#request.carVerInfoPage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="modelver_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" >
	  	<table id="one" width="100%" border="0">
	  		<tr>
	  			<td><label><P>生产车型</label></td>
				<td><input type="text" id="carmodel" name="carmodel" class="text ui-widget-content ui-corner-all" size="12" /></td>
	  		</tr>
	  		<tr>
	  			<td><label><P>COC版本号</label></td>
				<td><input type="text" id="cver1" name="cver1" class="text ui-widget-content ui-corner-all" size="12" /></td>
				<td><label><P>COC生效时间</label></td>
				<td><input type="text" id="cvalitime" name="cvalitime" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>COC生效人</label></td>
				<td><input type="text" id="cvaliuser" name="cvaliuser" class="text ui-widget-content ui-corner-all" size="12" /></td>
			</tr>
			<tr>
	  			<td><label><P>车型照片版本号</label></td>
				<td><input type="text" id="pver1" name="pver1" class="text ui-widget-content ui-corner-all" size="12" /></td>
				<td><label><P>车型照片生效时间</label></td>
				<td><input type="text" id="pvalitime" name="pvalitime" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车型照片生效人</label></td>
				<td><input type="text" id="pvaliuser" name="pvaliuser" class="text ui-widget-content ui-corner-all" size="12" /></td>
			</tr>
	  		<tr>
	  			<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="gver1" name="gver1" class="text ui-widget-content ui-corner-all" size="12" /></td>
	  			<td><label><P>燃油标签生效时间</label></td>
				<td><input type="text" id="gvalitime" name="gvalitime" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>燃油标签生效人</label></td>
				<td><input type="text" id="gvaliuser" name="gvaliuser" class="text ui-widget-content ui-corner-all" size="12" /></td>		
			</tr>
	  		<tr>
	  			<td><label><P>召回版本号</label></td>
				<td><input type="text" id="zver1" name="zver1" class="text ui-widget-content ui-corner-all" size="12" /></td>
				<td><label><P>召回生效时间</label></td>
				<td><input type="text" id="zvalitime" name="zvalitime" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  			<td><label><P>召回生效人</label></td>
				<td><input type="text" id="zvaliuser" name="zvaliuser" class="text ui-widget-content ui-corner-all" size="12" /></td>
	  		</tr>
	  		<tr>
	  			<td><label><P>环保版本号</label></td>
				<td><input type="text" id="ever1" name="ever1" class=text ui-widget-content ui-corner-all" size="12" /></td>
				<td><label><P>环保生效时间</label></td>
				<td><input type="text" id="evalitime" name="evalitime" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>环保生效人</label></td>
				<td><input type="text" id="evaliuser" name="evaliuser" class="text ui-widget-content ui-corner-all" size="12" /></td>
			</tr>
	  		<tr>
	  			<td><label><P>车型照片版本号</label></td>
	  			<td><select id='pvercode'></select> </td>
	  			<td><label><P>工厂</label></td>
	  			<td ><s:select name="factory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" ></s:select></td>
				<td colspan='3'><input type="text" id="pver" name="pver" class="text ui-widget-content ui-corner-all" size="12" /> </td>
	  		</tr>
		</Table>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>