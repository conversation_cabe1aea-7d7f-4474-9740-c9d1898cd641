package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

/**
 * InterfaceLogId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class InterfaceDataId implements java.io.Serializable {

	// Fields

	private String tradeCode;
	private String reqSerialNo;
	private String createTime;

	// Constructors

	/** default constructor */
	public InterfaceDataId() {
	}

	/** full constructor */
	public InterfaceDataId(String tradeCode, String reqSerialNo,
			String createTime) {
		this.tradeCode = tradeCode;
		this.reqSerialNo = reqSerialNo;
		this.createTime = createTime;
	}

	public String getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getTradeCode() {
		return this.tradeCode;
	}

	public void setTradeCode(String tradeCode) {
		this.tradeCode = tradeCode;
	}

	public String getReqSerialNo() {
		return this.reqSerialNo;
	}

	public void setReqSerialNo(String reqSerialNo) {
		this.reqSerialNo = reqSerialNo;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof InterfaceDataId))
			return false;
		InterfaceDataId castOther = (InterfaceDataId) other;

		return ((this.getTradeCode() == castOther.getTradeCode()) || (this
				.getTradeCode() != null
				&& castOther.getTradeCode() != null && this.getTradeCode()
				.equals(castOther.getTradeCode())))
				&& ((this.getReqSerialNo() == castOther.getReqSerialNo()) || (this
						.getReqSerialNo() != null
						&& castOther.getReqSerialNo() != null && this
						.getReqSerialNo().equals(castOther.getReqSerialNo())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getTradeCode() == null ? 0 : this.getTradeCode().hashCode());
		result = 37
				* result
				+ (getReqSerialNo() == null ? 0 : this.getReqSerialNo()
						.hashCode());
		return result;
	}

}