package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CarPublicModeId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarPublicModelId implements java.io.Serializable {

	// Fields

	private String c1;
	private String vercode;

	// Constructors

	/** default constructor */
	public CarPublicModelId() {
	}

	/** full constructor */
	public CarPublicModelId(String c1, String vercode) {
		this.c1 = c1;
		this.vercode = vercode;
	}

	// Property accessors

	public String getC1() {
		return this.c1;
	}

	public void setC1(String c1) {
		this.c1 = c1;
	}

	public String getVercode() {
		return this.vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof CarPublicModelId))
			return false;
		CarPublicModelId castOther = (CarPublicModelId) other;

		return ((this.getC1() == castOther.getC1()) || (this.getC1() != null
				&& castOther.getC1() != null && this.getC1().equals(
				castOther.getC1())))
				&& ((this.getVercode() == castOther.getVercode()) || (this
						.getVercode() != null
						&& castOther.getVercode() != null && this.getVercode()
						.equals(castOther.getVercode())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result + (getC1() == null ? 0 : this.getC1().hashCode());
		result = 37 * result
				+ (getVercode() == null ? 0 : this.getVercode().hashCode());
		return result;
	}

}