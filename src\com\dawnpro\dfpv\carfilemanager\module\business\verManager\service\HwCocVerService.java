package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service;

import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcs;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcsId;

public interface HwCocVerService {

	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
	
	/**
	 *
	 * @param obj
	 */
	public void addCocVer(HwBaseCOCcs obj)throws DataAccessException;
	public void addCocVer(HwBaseCOCcs[] objs)throws DataAccessException;
	
	/**
	 *
	 * @param obj
	 */
	public void updateCocVer(HwBaseCOCcs obj)throws DataAccessException;
	public void updateCocVer(HwBaseCOCcs[] objs)throws DataAccessException;
	
	
	/**
	 *
	 * @param id
	 */
	public void deleteCocVer(HwBaseCOCcs[] id)throws DataAccessException;
	
	/**
	 *
	 * @param id
	 * @return
	 */
	public HwBaseCOCcs loadCocVer(HwBaseCOCcsId id)throws DataAccessException;
	
	/**
	 *
	 * @param id
	 * @return
	 */
	public HwBaseCOCcs findMaxCocVerBySccx(String c1)throws DataAccessException;
	
	/**
	 *
	 * @param hql
	 * @param params
	 * @return
	 */
	public List<HwBaseCOCcs> findCocVer(Object[] params)throws DataAccessException;
	
	public List<HwBaseCOCcs> findCocVer(String hql)throws DataAccessException;
}
