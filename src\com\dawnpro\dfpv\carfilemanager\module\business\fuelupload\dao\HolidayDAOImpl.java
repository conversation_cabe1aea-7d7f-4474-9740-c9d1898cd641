package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Holiday;

public class HolidayDAOImpl extends GenericHibernateDAOImpl<Holiday> implements HolidayDAO {

	public void addHoliday(Holiday obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCarColor Method Error:",e);
		}

	}

	public List<Holiday> findHoliday(String sql) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<Holiday> findHoliday(String sql, Object[] params) {
		// TODO Auto-generated method stub
		return null;
	}

	public void updateHoliday(Holiday obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCarColor Method Error:",e);
		}

	}

}
