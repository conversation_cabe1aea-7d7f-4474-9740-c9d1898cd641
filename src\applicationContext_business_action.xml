<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="autodetect" xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	<bean id="certificateManagerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.action.CertificateManagerAction" lazy-init="true" scope="request" >
		<property name="certificateService">
			<ref bean="certificateService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
		<property name="carPublicModelIconService">
			<ref bean="carPublicModelIconService" />
		</property>
	</bean>
	
	<bean id="publicNoticeCarModelManagerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.PublicNoticeCarModelManagerAction" lazy-init="true" scope="request" >
		<property name="publicNoticeCarModelService">
			<ref bean="publicNoticeCarModelService" />
		</property>
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
	</bean>
	
	<bean id="carRecallAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.CarRecallAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="carRecallService" />
		</property>
		<property name="modelverService">
			<ref bean="modelverService" />
		</property>
	</bean>
	
	<bean id="carRecallQueryAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.CarRecallQueryAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="carRecallService" />
		</property>
	</bean>
	
	<bean id="interfaceLogAction" class="com.dawnpro.dfpv.carfilemanager.module.business.share.action.InterfaceLogAction" lazy-init="true" scope="request">
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>
	</bean>
	
	<bean id="carInfoAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.CarInfoAction" lazy-init="true" scope="request" >
		<property name="service">
			<ref bean="carInfoService" />
		</property>
		<property name="versionService">
			<ref bean="modelverService" />
		</property>
		<property name="gasVerService">
			<ref bean="gasVerService" />
		</property>
		<property name="cocVerService">
			<ref bean="cocVerService" />
		</property>
		<property name="hwCocVerService">
			<ref bean="hwCocVerService" />
		</property>
		<property name="carRecallService">
			<ref bean="carRecallService" />
		</property>
		<property name="cocPhotoService">
			<ref bean="cocPhotoService" />
		</property>
		<property name="logService">
			<ref bean="sysLogService" />
		</property>
		<property name="certificateSupplementInfoService">
			<ref bean="certificateSupplementInfoService" />
		</property>
		<property name="proenvService">
			<ref bean="proenvService" />
		</property>
	</bean>
	
	
	<bean id="carVerCodeAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.CarVerCodeManage" lazy-init="true" scope="request" >
		<property name="carvercodeservice">
			<ref bean="carvercodeservice" />
		</property>
	</bean>
	
	<bean id="cocCancelAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.COCCancelAction" lazy-init="true" scope="request" >
		<property name="certificateService">
			<ref bean="certificateService" />
		</property>
	</bean>
	
	<bean id="gasVerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.action.GasVerAction" lazy-init="true" scope="request" >
		<property name="gasVerService">
			<ref bean="gasVerService" />
		</property>
		<property name="modelverService">
			<ref bean="modelverService" />
		</property>
		<!-- 2012-12-01 modify -->
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>		
		
		<property name="mqSnedService">
			<ref bean="mqSnedService" />
		</property>
		
	</bean>	
	
	<bean id="cocVerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.action.CocVerAction" lazy-init="true" scope="request" >
		<property name="cocVerService">
			<ref bean="cocVerService" />
		</property>
		<property name="modelverService">
			<ref bean="modelverService" />
		</property>
		<!-- 2012-12-01 modify -->
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>			
	</bean>
	
	<bean id="hwcocVerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.action.HwCocVerAction" lazy-init="true" scope="request" >
		<property name="hwCocVerService">
			<ref bean="hwCocVerService" />
		</property>
		<property name="modelverService">
			<ref bean="modelverService" />
		</property>
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>			
	</bean>
	
	<bean id="proenvAction" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.action.ProenvAction" lazy-init="true" scope="request" >
		<property name="proenvService">
			<ref bean="proenvService" />
		</property>
		<property name="modelverService">
			<ref bean="modelverService" />
		</property>
		<!-- 2012-12-01 modify -->
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>			
	</bean>
	
	
	<bean id="modelverAction" class="com.dawnpro.dfpv.carfilemanager.module.business.modelver.action.ModelverAction" lazy-init="true" scope="request" >
		<property name="modelverService">
			<ref bean="modelverService" />
		</property>
		<property name="cocPhotoService">
			<ref bean="cocPhotoService" />
		</property>
	</bean>
	
	<bean id="sendCarModelAction" class="com.dawnpro.dfpv.carfilemanager.module.business.share.action.SendCarModelAction" lazy-init="true" scope="request" >
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>
		<property name="productionCarModelService">
			<ref bean="productionCarModelService" />
		</property>
		<property name="publicNoticeCarModelService">
			<ref bean="publicNoticeCarModelService" />
		</property>
		<property name="mqSnedService">
			<ref bean="mqSnedService" />
		</property>
	</bean>
	
	<bean id="productionCarModelManagerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.ProductionCarModelManagerAction" lazy-init="true" scope="request" >
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>
		<property name="productionCarModelService">
			<ref bean="productionCarModelService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
	</bean>
	
	<bean id="cocPhotoManagerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.action.COCPhotoManagerAction" lazy-init="true" scope="request" >
		<property name="cocPhotoService">
			<ref bean="cocPhotoService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
		<property name="logService">
			<ref bean="sysLogService" />
		</property>
	</bean>
	
	<bean id="carColorAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.CarColorAction" lazy-init="true" scope="request" >
		<property name="carColorService">
			<ref bean="carColorService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
	</bean>
	
	
	<bean id="lqzNeutralAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.LqzNeutralAction" lazy-init="true" scope="request" >
		<property name="lqzNeutralService">
			<ref bean="lqzNeutralService" />
		</property>
	</bean>
	
	<bean id="cocModelTypeAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.COCModelTypeAction" lazy-init="true" scope="request" >
		<property name="cocModelTypeService">
			<ref bean="cocModelTypeService" />
		</property>
		<property name="gasPrinterLogService">
			<ref bean="gasPrinterLogService" />
		</property>
	</bean>	
	
	<bean id="yearCodeAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.YearCodeAction" lazy-init="true" scope="request" >
		<property name="yearCodeService">
			<ref bean="yearCodeService" />
		</property>
	</bean>			
	
	
	
	<bean id="typicalityNeutralAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.TypicalityNeutralAction" lazy-init="true" scope="request" >
		<property name="typicalityNeutralService">
			<ref bean="typicalityNeutralService" />
		</property>
	</bean>	
	<bean id="typicalityNeutralPhotoAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.TypicalityNeutralPhotoAction" lazy-init="true" scope="request" >
		<property name="typicalityNeutralPhotoService">
			<ref bean="typicalityNeutralPhotoService" />
		</property>
	</bean>	
	<!-- fuelupload start-->
	<bean id="fuelUploadAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.FuelUploadAction" lazy-init="true" scope="request" >
		<property name="fuelUploadService">
			<ref bean="fuelUploadService" />
		</property>
	</bean>	
	<bean id="uploadUserAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadUserAction" lazy-init="true" scope="request" >
		<property name="uploadUserService">
			<ref bean="uploadUserService" />
		</property>
	</bean>	
	<bean id="holidayAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.HolidayAction" lazy-init="true" scope="request" >
		<property name="holidayService">
			<ref bean="holidayService" />
		</property>
	</bean>	
	<bean id="filterModelAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.FilterModelAction" lazy-init="true" scope="request" >
		<property name="filterModelService">
			<ref bean="filterModelService" />
		</property>
	</bean>	
	<bean id="uploadStateManagerAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadStateManagerAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
	</bean>	
	<bean id="uploadStateModifyAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadStateModifyAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
	</bean>	
	<bean id="uploadStateMonitorAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadStateMonitorAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
	</bean>	
	<bean id="carPublicModelIconAction" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.action.CarPublicModelIconAction" lazy-init="true" scope="request" >
		<property name="carPublicModelIconService">
			<ref bean="carPublicModelIconService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
	</bean>	
	<bean id="uploadStateQueryAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadStateQueryAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
	</bean>	
	
	<bean id="UploadScjcQueryAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadScjcQueryAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
		
	</bean>		
	
	<bean id="UploadWqbfQueryAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.UploadWqbfQueryAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
		<property name="scjcNeutralService">
			<ref bean="scjcNeutralService" />
		</property>
	</bean>	
	
	<bean id="COCQueryAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.COCQueryAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
	</bean>	
	<!-- 2018-05-16 -->
	<bean id="cocUploadStateModifyAction" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action.CocUploadStateModifyAction" lazy-init="true" scope="request" >
		<property name="uploadStateManagerService">
			<ref bean="uploadStateManagerService" />
		</property>
	</bean>	
	<!-- fuelupload end -->	
	<bean id="carModelSelectAction" class="com.dawnpro.dfpv.carfilemanager.module.business.share.action.CarModelSelectAction" lazy-init="true" scope="request" >
		<property name="productionCarModelService">
			<ref bean="productionCarModelService" />
		</property>
		<property name="publicNoticeCarModelService">
			<ref bean="publicNoticeCarModelService" />
		</property>
		<property name="interfaceLogService">
			<ref bean="interfaceLogService" />
		</property>
	</bean>	
	
	<!--  
	<bean id="interfaceWorker" class="com.dawnpro.dfpv.carfilemanager.module.business.share.factory.InterfaceWorker" >
		<property name="interfaceDataProcessService">
			<ref bean="interfaceDataProcessService"/>
		</property> 
	</bean>
	-->
	<!-- 
	<bean id="worker" class="com.dawnpro.dfpv.carfilemanager.module.business.share.action.InterfaceWorkAction" lazy-init="true" scope="request" >
		<property name="interfaceWorker">
			<ref bean="interfaceWorker"/>
		</property> 
	</bean>
	 -->
	 
	 <bean id="certificateExportAction" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.action.CertificateExportAction" lazy-init="true" scope="request" >
		<property name="certificateServiceImpl">
			<ref bean="certificateService"/>
		</property> 
	</bean>
	 
	 
</beans>
