<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole" table="sys_role" lazy="false">
        <id name="roleid" type="java.lang.String">
            <column name="roleid" length="36" />
            <generator class="uuid.hex" />
        </id>
        <property name="name" type="java.lang.String">
            <column name="name" length="30" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="remark" length="100" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="creator" length="20"  />
        </property>
        <property name="time" type="java.lang.String">
            <column name="time" length="19"  />
        </property>
    </class>
</hibernate-mapping>
