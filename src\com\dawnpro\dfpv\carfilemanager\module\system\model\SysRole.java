package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * SysRole entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SysRole implements java.io.Serializable {

	// Fields

	private String roleid;
	private String name;
	private String remark;
	private String creator;
	private String time;

	// Constructors

	/** default constructor */
	public SysRole() {
	}

	/** minimal constructor */
	public SysRole(String roleid, String creator, String time) {
		this.roleid = roleid;
		this.creator = creator;
		this.time = time;
	}

	/** full constructor */
	public SysRole(String roleid, String name, String remark,
			String creator, String time) {
		this.roleid = roleid;
		this.name = name;
		this.remark = remark;
		this.creator = creator;
		this.time = time;
	}

	// Property accessors

	public String getRoleid() {
		return roleid;
	}

	public void setRoleid(String roleid) {
		this.roleid = roleid;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

}