package com.dawnpro.dfpv.carfilemanager.module.business.share.model;
// Generated 2018-9-26 15:56:55 by Hibernate Tools 3.6.0.Final

/**
 * InterfaceMsgLog generated by hbm2java
 */
public class InterfaceMsgLog implements java.io.Serializable {

	private String id;
	private String tradeCode;
	private String reqSerialNo;
	private String tradeTime;
	private String requesterId;
	private String state;
	private String type;
	private String exceTime;
	private String createTime;
	private String errInfo;
	private String path;
	private String batchno;

	public InterfaceMsgLog() {
	}

	public InterfaceMsgLog(String id) {
		this.id = id;
	}

	public InterfaceMsgLog(String id, String tradeCode, String reqSerialNo, String tradeTime, String requesterId,
			String state, String type, String exceTime, String createTime, String errInfo, String path,
			String batchno) {
		this.id = id;
		this.tradeCode = tradeCode;
		this.reqSerialNo = reqSerialNo;
		this.tradeTime = tradeTime;
		this.requesterId = requesterId;
		this.state = state;
		this.type = type;
		this.exceTime = exceTime;
		this.createTime = createTime;
		this.errInfo = errInfo;
		this.path = path;
		this.batchno = batchno;
	}

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTradeCode() {
		return this.tradeCode;
	}

	public void setTradeCode(String tradeCode) {
		this.tradeCode = tradeCode;
	}

	public String getReqSerialNo() {
		return this.reqSerialNo;
	}

	public void setReqSerialNo(String reqSerialNo) {
		this.reqSerialNo = reqSerialNo;
	}

	public String getTradeTime() {
		return this.tradeTime;
	}

	public void setTradeTime(String tradeTime) {
		this.tradeTime = tradeTime;
	}

	public String getRequesterId() {
		return this.requesterId;
	}

	public void setRequesterId(String requesterId) {
		this.requesterId = requesterId;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getExceTime() {
		return this.exceTime;
	}

	public void setExceTime(String exceTime) {
		this.exceTime = exceTime;
	}

	public String getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getErrInfo() {
		return this.errInfo;
	}

	public void setErrInfo(String errInfo) {
		this.errInfo = errInfo;
	}

	public String getPath() {
		return this.path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getBatchno() {
		return this.batchno;
	}

	public void setBatchno(String batchno) {
		this.batchno = batchno;
	}

}
