/**
 * 
 */
package com.dawnpro.dfpv.carfilemanager.module.system.action;

import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysMenuService;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysOperateService;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysRoleService;

/**
 * <AUTHOR>
 *
 */
public class SysRoleAction extends BaseSupportAction{
	private static final long serialVersionUID = -6371005813002910434L;
	private Logger logger = Logger.getLogger(SysRoleAction.class.getName());
	private String currentPage = null;
	private String roleid;
	private String name;
	private String remark;
	private SysRoleService service=null;
	private SysMenuService sysMenuService = null;
	private SysOperateService sysOperateService = null;
	
	public void setSysOperateService(SysOperateService sysOperateService) {
		this.sysOperateService = sysOperateService;
	}
	
	public void setSysMenuService(SysMenuService sysMenuService) {
		this.sysMenuService = sysMenuService;
	}
	
	public void setService(SysRoleService service) {
		this.service = service;
	}
	
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}

	public void setRoleid(String roleid) {
		this.roleid = roleid;
	}
	public void setName(String name) {
		this.name = name;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String execute(){
		try{
			SysUser sysuser = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", sysuser.getRoleid());
			
			Page page = new Page();
			if(this.currentPage!=null&&!this.currentPage.trim().equals("")){
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			}else{
				page.setCurrentPage(1);
			}
			this.getServletRequest().setAttribute("page", page);
			List results = this.service.pagination(page);
			this.getServletRequest().setAttribute("pageData", results);
			
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute(SYSTEM_USER_CURRENT_MENUS, this.sysMenuService.curUserSystemMenu(user));
			this.getServletRequest().setAttribute(SYSTEM_USER_CURRENT_OPERATES, this.sysOperateService.allOperateList());
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return SUCCESS;
	}

	public String roleInfo(){
		try{
			SysRole gg = this.service.loadRole(roleid);
			JSONObject jsonObject = JSONObject.fromObject(gg);
			//String tmp=new String(jsonObject.toString().getBytes("UTF-8"),"ISO-8859-1");
			this.setJson(jsonObject.toString());
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		
		return JSON;
	}

	public String addRole(){
		try{
			SysRole gg = new SysRole();
			
			gg.setName(name);
			gg.setRemark(remark);
			
			SysUser user =(SysUser)getSession().get(SYSTEM_USER);
			gg.setCreator(user.getLoginname());
			gg.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));
			
			this.service.addRole(gg);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}

	public String updateRole(){
		try{
			SysRole gg = new SysRole();
			
			gg.setRoleid(roleid);
			gg.setName(name);
			gg.setRemark(remark);			

			SysUser user =(SysUser)getSession().get(SYSTEM_USER);
			gg.setCreator(user.getLoginname());
			gg.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));

			this.service.updateRole(gg);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}

	public String deleteRole(){
		try{
			String tmp = this.roleid.trim();
			String[] roleides = tmp.split("&");
			if(roleides!=null && roleides.length>0){
				SysRole[] gg = new SysRole[roleides.length];
				for(int i=0;i<roleides.length;i++){
					gg[i] = new SysRole();
					gg[i].setRoleid(roleides[i]);
				}
				this.service.deleteRole(gg);
			}
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}
	
	public String isRoleExist(){
		boolean isExist = true;
		try{
			isExist = this.service.isRoleExist(name);
			this.setJson(String.valueOf(isExist));
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return JSON;
	}
	
}
