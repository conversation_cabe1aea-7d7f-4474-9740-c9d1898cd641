<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser" table="sys_user" >
        <id name="loginname" type="java.lang.String">
            <column name="loginname" length="20" />
            <generator class="assigned" />
        </id>
        <property name="password" type="java.lang.String">
            <column name="password" length="20"  />
        </property>
        <property name="username" type="java.lang.String">
            <column name="username" length="10" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="state" length="1"  />
        </property>
        <property name="roleid" type="java.lang.String">
            <column name="roleid" length="36"  />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="remark" length="100" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="creator" length="20"  />
        </property>
        <property name="time" type="java.lang.String">
            <column name="time" length="19"  />
        </property>
    </class>
</hibernate-mapping>
