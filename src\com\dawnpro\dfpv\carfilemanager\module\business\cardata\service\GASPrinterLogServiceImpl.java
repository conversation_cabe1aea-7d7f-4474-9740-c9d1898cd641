package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.GASPrinterLogDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.GASPrinterLog;



public class GASPrinterLogServiceImpl implements GASPrinterLogService {
	private GASPrinterLogDAO gasPrinterLogDAO = null;

	private PaginationService paginationService = null;

	
	public GASPrinterLogDAO getGasPrinterLogDAO() {
		return gasPrinterLogDAO;
	}

	public void setGasPrinterLogDAO(GASPrinterLogDAO gasPrinterLogDAO) {
		this.gasPrinterLogDAO = gasPrinterLogDAO;
	}

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	
	
	public void addGASPrinterLog(GASPrinterLog obj)
			throws DataAccessException {
		try{
			this.gasPrinterLogDAO.addGASPrinterLog(obj);
		}catch (DataAccessException e) {
			throw new SystemException("GASPrinterLog Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("GASPrinterLog Method Error:", e);
		}
	}

}
