package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;


import java.util.Date;

/**
 * Lpzinfo entity. <AUTHOR> Persistence Tools
 */

public class GASPrinterLog implements java.io.Serializable {

	// Fields
	private String id;
	private String printpoint;				
	private String printer;			
	
	private String creator;
	private Date createdate;

	// Constructors

	/** default constructor */
	public GASPrinterLog() {
	}
	
	

	public GASPrinterLog(String id, String printpoint, String printer, String creator, Date createdate) {
		super();
		this.id = id;
		this.printpoint = printpoint;
		this.printer = printer;
		this.creator = creator;
		this.createdate = createdate;
	}



	/** minimal constructor */

	

	// Property accessors

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	

	public String getPrintpoint() {
		return printpoint;
	}



	public void setPrintpoint(String printpoint) {
		this.printpoint = printpoint;
	}



	public String getPrinter() {
		return printer;
	}



	public void setPrinter(String printer) {
		this.printer = printer;
	}



	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreatedate() {
		return createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}


}