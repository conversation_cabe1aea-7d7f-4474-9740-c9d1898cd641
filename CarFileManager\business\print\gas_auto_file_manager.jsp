<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
	p {font-family:"宋体";font-size: 10pt;font-weight:bold;}
	input.text { width:12; padding: .4em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align: center; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript">	
$(function() {
	$("#dialog").dialog({
		autoOpen: false,
		bgiframe: false,
		height: 400,
		weight:40,
		modal: false
	});

	$('#vin').bind('keyup',function(event) {  
     	if(event.keyCode==13){
     		window.document.gas.printGASBatch($("#printPage")[0].value,"");  
   		}   
     	//if($.trim($('#vin')[0].value).length==17){
   			//window.document.gas.printGAS($("#vin")[0].value);
   	   	//}
    });   
	$("#search").click(function() {
			window.document.gas.printGASBatch($("#printPage")[0].value,""); 
		});

	$("#import").click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('print/gas_auto_file_upload.jsp',winName,params);
	});
	
	var count=$("#printPage option").length;
	for(var i=0;i<count;i++){           
		if($("#printPage").get(0).options[i].text == 30){  
			$("#printPage").get(0).options[i].selected = true;  

			break;  
	    }  
	} 

	
				
}
);

function showInfo(info){
	$("#info").text(info);
}	
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<!-- 
<p>VIN:<input type="text" id="vin" class="text ui-widget-content ui-corner-all"/>&nbsp;&nbsp;<button id="create-user" class="ui-button ui-state-default ui-corner-all">查询</button></p>
 -->
<div align="center"> 
	<jsp:plugin name="gas" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet.class" codebase="." archive = "../applet/com.dawnpro.dfpv.carfilemanager.print.applet.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/Qrcode_encoder.jar" 
	iepluginurl="http://***********:8080/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="200" width="800">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="tmp"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
<div align="center">
<table width="623" height="63">
	<tr>
		<td width="620" align="left"><p id="info" style="font-family:arial;font-size: 10pt; "></p></td>
		<td></td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
	<td width="280" height="19" align="left"  style="font-family:arial;font-size: 24pt; ">每次打印页数:</td>
	<td width="25" align="left">
		<select name="printPage" id="printPage">
			<s:bean name="org.apache.struts2.util.Counter" id="counter" >
  				<s:param name="first" value="1" />
   				<s:param name="last" value="50" />
   				<s:iterator>
     				<option value="<s:property/>"><s:property/></option>
   				</s:iterator>
			</s:bean>
         </select>
	</td>
	<td width="403" align="left"><button id="search" class="ui-button ui-state-default ui-corner-all">批量打印</button>&nbsp;&nbsp;<button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
		<td>
		</td>
	</tr>
	<tr>
		<td width="403"></font></td>
	</tr>
</table>
<div align="center"><font color="red" size='4'>提示:批量打印时请预留打印纸的最后2张，不然会导致下次装纸打印时数据上下偏移。</div>
</div>
</body>
</html>