package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

import java.util.Date;

/**
 * CarPublicModelIcon generated by MyEclipse Persistence Tools
 */

public class CarPublicModelIcon implements java.io.Serializable {

	// Fields

	private String pmodel;

	private String printicon;

	private String state;

	private String creator;

	private Date createdate;

	private Date updatedate;

	// Constructors

	/** default constructor */
	public CarPublicModelIcon() {
	}

	/** minimal constructor */
	public CarPublicModelIcon(String pmodel) {
		this.pmodel = pmodel;
	}

	/** full constructor */
	public CarPublicModelIcon(String pmodel, String printicon, String state,
			String creator, Date createdate, Date updatedate) {
		this.pmodel = pmodel;
		this.printicon = printicon;
		this.state = state;
		this.creator = creator;
		this.createdate = createdate;
		this.updatedate = updatedate;
	}

	// Property accessors

	public String getPmodel() {
		return this.pmodel;
	}

	public void setPmodel(String pmodel) {
		this.pmodel = pmodel;
	}

	public String getPrinticon() {
		return this.printicon;
	}

	public void setPrinticon(String printicon) {
		this.printicon = printicon;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreatedate() {
		return this.createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}

	public Date getUpdatedate() {
		return this.updatedate;
	}

	public void setUpdatedate(Date updatedate) {
		this.updatedate = updatedate;
	}

}