<%@ page language="java" contentType="text/html; charset=UTF-8" import="java.util.List,com.dawnpro.dfpv.carfilemanager.module.system.model.SysMenu,com.dawnpro.dfpv.carfilemanager.module.system.model.SysOperate"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link type="text/css" href="js/jquery/treetable/css/jquery.treeTable.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		
		div#sys-menu {  width: 100%; margin: 0px; }
		div#sys-menu table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#sys-menu table td, div#sys-menu table th { border: 1px solid #eee; padding: .6em 18px; text-align: center; font-size: 10pt; }
		div#sys-menu table td, div#sys-menu table tr { border: 1px solid #eee; padding: .3em 18px; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/treetable/jquery.treeTable.js"></script>
<script type="text/javascript" src="js/jquery/treetable/jquery.ui.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#role_dialog").dialog({bgiframe: true,autoOpen: false,width: 380,height: 280,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#role_dialog');
					allFields = $([]).add(parent.find('#name')).add(parent.find('#remark')).add(parent.find('#roleid'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#role_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					if(type=="add"){
						jQuery.ajax({
				            url: 'system/sysRole!isRoleExist.action',
				            data: 'name='+$(this).find('#name').val(),
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#role_dialog');
					            if(json2Bean(data).json=="true"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'角色名:['+dialog.find('#name').val()+']已经存在不能新增！');		
								}else{
									dialog.find('#createForm')[0].action="system/sysRole!addRole.action";
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						$(this).find('#createForm')[0].action="system/sysRole!updateRole.action";
						$(this).find('#createForm')[0].submit();
					}
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
		}
		});

	$("#role_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 380,height: 280,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
		}
		});
	
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				if(type=="delete"){					
					formObj[0].action = "system/sysRole!deleteRole.action";
					formObj[0].submit();
				}
			}
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#create").click(function() {
		type = "add";
		$('#role_dialog').data('title.dialog', '新增角色').dialog('open');	
	});

    $("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			jQuery.ajax({
	            url: 'system/sysRole!roleInfo.action',		           
	            data: 'roleid='+id, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#role_dialog');
	            	setDialogValue(dialogObj,carObj);

	       	    	dialogObj.data('title.dialog', '修改角色').dialog('open');
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				if(id==""){
					id = this.value;
					info = "角色名称:"+ $('#users-contain').find('#'+this.value).val();
				}else{
					id = id+"&"+ this.value; 
					info = info+"&"+"角色名称:"+ $('#users-contain').find('#'+this.value).val();
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#roleid').val(id);
   	   	}
	
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
	        url: 'system/sysRole!roleInfo.action',		           
	        data: 'roleid='+id, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#role_display_dialog');
	        	
				setDialogValue(dialogObj,carObj);
								
	       	   	dialogObj.dialog('open');
	        }
	    });

		return false;
	}
	
	function validate(parent){
		var obj = $(parent).find('#name');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'角色名称字段不能为空，最大长度为30！');			
			return false;
		}
		obj = $(parent).find('#remark');
		if(!checkLength(obj,0,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'角色说明字段最大长度为100！');			
			return false;
		}
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#name').val(jsonObj.name);
		dialogObj.find('#remark').val(jsonObj.remark);
		dialogObj.find('#roleid').val(jsonObj.roleid);
	}
	
	function clear(dialogObj){
		dialogObj.find('#name').val("");
		dialogObj.find('#remark').val("");

		dialogObj.find('#name').attr('readonly',false);
		dialogObj.find('#remark').attr('readonly',false);
		
		type = null;
	}

	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			location.href="sysRole.action?currentPage="+$('#jump').val()+"&menuid="+menuid;;   
   		}   
   		
    });

	$("#permission").click(function() {
		var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
		
   		if(index==1){
   			var dialogObj = $('#permission_dialog');
	        dialogObj.find('#roleid').val(id);
	    	dialogObj.dialog('open');
   	   		
   			jQuery.ajax({
	            url: 'system/sysPermission!menuToRolePermission.action',
		        data: 'roleid='+id,
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                alert("调用远程服务出错！");
	            },
	            success: function(data) {		
		            var content = json2Bean(data).json;
	 	            var jsonObj = eval("("+content.toString()+")"); 
	 	    	  	
	 	       		var checkedObj = dialogObj.find('#sys-menu').find("#menuid"); 
	 				checkedObj.each(function(){
	 					for(var i=0;i<jsonObj.length;i++){
			 				
							if($(this).val()==jsonObj[i].id.menuid){
								$(this).attr("checked",true);

								break;
							}
			 			}
	 					
	 				});

	 				var checkedObj = dialogObj.find('#sys-menu').find("#operateid"); 
	 				var tmp = null;
	 				var operates = null;
	 				var isBreak = false;
	 				checkedObj.each(function(){
		 				for(var i=0;i<jsonObj.length;i++){
	 						if(jsonObj[i].operatePermissions.length>0){
			 					operates = jsonObj[i].operatePermissions;
			 					tmp = $(this).val().split("@");
			 					for(var j=0;j<operates.length;j++){
			 						if(tmp[0]==operates[j].id.menuId&&tmp[1]==operates[j].id.operateId){
										$(this).attr("checked",true);
										isBreak = true;
									}else{
										//alert(tmp[0]+","+operates[j].id.menuId+","+tmp[1]+","+operates[j].id.operateId);
									}
				 				}
				 			}
							if(isBreak==true){
								isBreak = false;

								
							}
			 			}
	 					
	 				});
	            }
	        });

   			
   			
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要授权的角色！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能授权一个角色！');
	   		messageObj.dialog('open');
   	   	 }
		
	});
	
	$("#permission_dialog").dialog({bgiframe: true,autoOpen: false,width: 680,height: 480,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确认': function() {
				//var menuid = "";
				//var operateid = "";
				//var checkboxObj;
				//$("#sysMenu").find('tbody').find('tr').each(function(i){
					//checkboxObj =  $(this).find("[name='menuid']");
					
					//if(checkboxObj.attr("checked")==true){
						//menuid+=checkboxObj.attr("value")+"&";
				     //}
					
					//$(this).find("[name='operateid']").each(function(i){
						//if($(this).attr("checked")==true){
							//operateid+=$(this).attr("value")+"&";
					    //}
					//});
					
				    
				//});
				
				var dlgButton = $('.ui-dialog-buttonpane button');//	
				dlgButton.attr('disabled', 'disabled');
				dlgButton.addClass('ui-state-disabled');
				
				var formObj = $(this).find('#permissionForm');
				//formObj.find('#menuid').val(menuid);
				//formObj.find('#operateid').val(operateid);
				//alert(formObj.find('#menuid').val());
				//alert(formObj.find('#operateid').val());
				
				formObj[0].action="system/sysPermission!permission.action";
				formObj[0].submit();
				
			}
			
		},
		close: function() {
			
		}
		});
	
});

$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

          //给这行添加class值为over，并且当鼠标一出该行时执行函数

          $(this).removeClass("over");})    //移除该行的class


});

function selectChild(index,allChildSize){
	var checked = 'false';
	$("#sysMenu").find('tbody').find('tr').each(function(i){
		if(i==index){
			checked = $(this).find("[name='menuid']").attr("checked");
	      }
	     else if(i>index&&i<=(allChildSize+index)){
	    	 $(this).find("[name='menuid']").attr("checked",checked);
	     }
	});
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
   <table width="100%">
  			<tr>
  				<td width="85%"></td>
			   <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  
  			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			   <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			  
			 
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header">
                <th width="5%">选择</th> 
			    <th width="12%">类型名</th>
			    <th width="60%">类型编码</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="name"/>' ></td>
			  		<td><s:property value="name" /></td>
			  		<td>
			  			<s:property value="type" />
			  			<input type='hidden' id='<s:property value="name" />' name='<s:property value="name" />' value="<s:property value="type"/>"/>
			  		</td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="name"/>">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   </td>
   </tr>
</table>
</div>

<div id="role_dialog">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr><td>
			<label><P>类型名称*:</label>
			</td><td>
			<input type="text" id="name" name="name" class="text ui-widget-content ui-corner-all"/>
			</td></tr><tr><td>		
			<label><P>类型代码*:</label>
			</td><td>
			<input type="text" id="type" name="type" class="text ui-widget-content ui-corner-all" />
			</td></tr>
		</Table>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="role_display_dialog" title="查看窗口">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr><td>
			<label><P>类型名称*:</label>
			</td><td>
			<input type="text" id="name" name="name" class="text ui-widget-content ui-corner-all" readonly="true"/>
			</td></tr><tr><td>		
			<label><P>类型代码*:</label>
			</td><td>
			<input type="text" id="type" name="type" class="text ui-widget-content ui-corner-all"  readonly="true"/>
			</td></tr>
		</Table>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='name' name='name'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

</body>
</html>