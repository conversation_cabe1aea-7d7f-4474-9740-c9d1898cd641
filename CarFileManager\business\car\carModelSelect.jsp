<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String callFunName = request.getParameter("callFunName");
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<title>车型选择</title>
</head>
<body>
<table width="100%" cellpadding="0" cellspacing="0">
  <tr>
    <td>车型选择</td>
    <td align="right"> 
    		<button id="btn_qd" class="ui-button ui-state-default ui-corner-all">确定</button>  
    		<button id="btn_qx" class="ui-button ui-state-default ui-corner-all">取消</button>  
		</td>
  </tr>
</table>
<hr size="2" color="#FF9933">
<table>
	<tr>
		<td width="100%">
			<div id="treeboxbox_tree" style="width:446; height:300;background-color:#f5f5f5;border :1px solid Silver; overflow:auto; "/>
		</td>
	</tr>
</table>
<table width="100%" cellpadding="0" cellspacing="0">
  <tr>
    <td></td>
    <td align="right"> 
    		<button id="btn_qd1" class="ui-button ui-state-default ui-corner-all">确定</button>  
    		<button id="btn_qx1" class="ui-button ui-state-default ui-corner-all">取消</button>  
		</td>
  </tr>
</table>
<script language="javascript">
	var callFunName='<%=callFunName==null?"":callFunName%>';
	$("#btn_qd").click(function() {  
		window.returnValue=getAllLeaftValue();  
		//eval("window.opener."+callFunName+"('"+getAllLeaftValue()+"');");
		window.close();  	  		
	});	
	$("#btn_qx").click(function() {  
		window.close();  	  		
	});	
	$("#btn_qd1").click(function() {  
		window.returnValue=getAllLeaftValue();  
		//eval("window.opener."+callFunName+"('"+getAllLeaftValue()+"');");
		window.close();  	  		
	});	
	$("#btn_qx1").click(function() {  
		window.close();  	  		
	});			
	function doLog(str){			
		//var log = document.getElementById("logarea");
		//log.innerHTML = log.innerHTML+str+"</br>"
		//log.scrollTop = log.scrollHeight;
	}
	function getAllLeaftValue(){
		var valuesTemp=tree.getAllChecked();
		if(valuesTemp!=null&&valuesTemp!=""){
			var values=valuesTemp.split(",");
			var result="";
			for(var i=0;i<values.length;i++){
				var value=values[i];
				if(value.indexOf("##_p_")!=0){
					if(result!="")
						result+=",";
					result+=value.split(".")[1];
				}
			}
			return result;
		}
		return "";
	}
	function tonclick(id){			
		doLog("Item "+tree.getItemText(id)+" was selected");			
	};
	function tondblclick(id){			
		doLog("Item "+tree.getItemText(id)+" was doubleclicked");			
	};			
	function tondrag(id,id2){			
		return confirm("Do you want to move node "+tree.getItemText(id)+" to item "+tree.getItemText(id2)+"?");			
	};
	function tonopen(id,mode){			
		return confirm("Do you want to "+(mode>0?"close":"open")+" node "+tree.getItemText(id)+"?");			
	};
	
	function toncheck(id,state){			
		doLog("Item "+tree.getItemText(id)+" was " +((state)?"checked":"unchecked"));		
	};		

	tree=new dhtmlXTreeObject("treeboxbox_tree","100%","100%",0);
	tree.setImagePath("<%=path%>/images/csh_yellowbooks/");
	tree.enableCheckBoxes(1);
	tree.enableDragAndDrop(1);
	
	tree.enableThreeStateCheckboxes(true);
	tree.setOnDblClickHandler(tondblclick);
	tree.setDragHandler(tondrag);
	tree.loadXML('business/carModelSelect!getCarModel.action?type=<%=request.getAttribute("type")%>');			
</script>