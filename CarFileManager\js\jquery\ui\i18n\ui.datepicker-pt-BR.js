/* Brazilian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['pt-BR'] = {
		closeText: 'Fechar',
		prevText: '&#x3c;Anterior',
		nextText: 'Pr&oacute;ximo&#x3e;',
		currentText: 'Ho<PERSON>',
		monthNames: ['Janeiro','Fevereiro','Mar&ccedil;o','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Agosto','Setembro','Outubro','Novembro','Dezemb<PERSON>'],
		monthNamesShort: ['Jan','Fev','Mar','Abr','<PERSON>','Jun',
		'Jul','Ago','Set','Out','Nov','Dez'],
		dayNames: ['<PERSON>','Segunda-feira','Ter&ccedil;a-feira','<PERSON>ua<PERSON>-feira','<PERSON><PERSON><PERSON>-feira','<PERSON><PERSON>-feira','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','Sab'],
		dayNamesMin: ['Dom','Seg','Ter','Qua','Qui','Sex','Sab'],
		dateFormat: 'dd/mm/yy', firstDay: 0,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['pt-BR']);
});