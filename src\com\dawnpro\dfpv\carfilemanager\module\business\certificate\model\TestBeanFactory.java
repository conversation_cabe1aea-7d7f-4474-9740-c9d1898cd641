package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRAbstractBeanDataSourceProvider;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class TestBeanFactory extends JRAbstractBeanDataSourceProvider{
	public TestBeanFactory() {
		super(TestBean.class);
		// TODO Auto-generated constructor stub
	}

	public static Collection getBeans(){
		List  res = new ArrayList();
		TestBean bean = new TestBean();
		bean.setGroupdate("2009-2");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("co2");
		bean.setTjs("1470");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-2");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("zh");
		bean.setTjs("762");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-3");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("co2");
		bean.setTjs("7541");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-3");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("zh");
		bean.setTjs("962");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-4");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("co2");
		bean.setTjs("11549");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-4");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("zh");
		bean.setTjs("1162");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-5");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("co2");
		bean.setTjs("8549");
		res.add(bean);
		
		bean = new TestBean();
		bean.setGroupdate("2009-5");
		bean.setEnginetype("N6A 10FX3A PSA");
		bean.setTjlx("zh");
		bean.setTjs("2162");
		res.add(bean);
		return res;
	}

	public JRDataSource create(JasperReport arg0) throws JRException {
		// TODO Auto-generated method stub
		return new JRBeanCollectionDataSource(getBeans());
	}

	public void dispose(JRDataSource arg0) throws JRException {
		// TODO Auto-generated method stub
		
	}
}
