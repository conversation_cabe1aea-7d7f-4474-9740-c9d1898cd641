<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="system-action" extends="struts-base" namespace="/system">
		<action name="login" class="loginAction">
			<result name="exit">../login_faild.jsp</result>
		</action>

		<action name="sysPasswod" class="sysPasswordAction">
		</action>
		
		<action name="sysMenuNavigation" class="sysMenuNavigationAction">
			<result name="navigation">../sys_navigation_menu.jsp</result>
		</action>
		
		<action name="sysUser" class="sysUserAction">
			<result name="success">user_Manage.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">sysUser.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
		</action>
		
		<action name="sysRole" class="sysRoleAction">
			<result name="success">role_Manage.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">sysRole.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
		</action>
		
		<action name="sysLog" class="sysLogAction">
			<result name="success">system_log.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">sysLog.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
  			<result name="excel" type="stream">
            	<param name="contentType">application/vnd.ms-excel</param>   
                <param name="inputName">excelStream</param>            
               	<param name="contentDisposition">attachment;filename="${fileName}"</param>
                <param name="bufferSize">5096</param>
            </result>
		</action>
		
		<action name="sysOperate" class="sysOperateAction">
			<result name="success">operate_manage.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">sysOperate.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
		</action>
		
		<action name="sysPermission" class="sysPermissionAction">
			<result name="success" type="redirect-action">
 				<param name="actionName">sysRole.action</param>
  			</result>
		</action>
		
		<action name="sysDataDictionary" class="sysDataDictionaryAction">
			<result name="success">dataDictionary.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">sysDataDictionary.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
		</action>
		
		<action name="sysPrintType" class="sysPrintTypeAction">
			<result name="success">printPoint.jsp</result>
			<result name="view" type="redirect-action">
 				<param name="actionName">sysPrintType.action</param>
 				<param name="menuid">${menuid}</param>
  			</result>
		</action>
		
	</package>
</struts>