<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
		xmlns:context="http://www.springframework.org/schema/context" xmlns:jee="http://www.springframework.org/schema/jee"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	
	<bean id="mySessionFactory" class="org.springframework.orm.hibernate3.LocalSessionFactoryBean">
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.OracleDialect</prop>
				<prop key="hibernate.statement_cache.size">30</prop>
				<prop key="hibernate.cglib.use_reflection_optimizer">true</prop>
				<prop key="hibernate.show_sql">false</prop>
				<prop key="hibernate.proxool.xml">proxool.xml</prop>
				<prop key="hibernate.proxool.pool_alias">DBPool</prop>
				<prop key="hibernate.jdbc.batch_size">30</prop> 
			</props>
		</property>
		<property name="mappingResources">
			<list>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysUser.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysRole.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysMenu.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysOperate.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysPermission.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysOperatePermission.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SystemConfigParams.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/SysLog.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/DataDictionary.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/system/model/DataDictionaryType.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CarPublicModel.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CarProductionModel.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CarRecall.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CarInfoVoid.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CarRecodInfo.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/Modelver.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CocPhoto.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/TypicalityNeutral.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/Lpzinfo.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/COCModelType.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/YearCode.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/GASPrinterLog.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/TypicalityNeutralPhoto.hbm.xml</value>
			    <value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/Vfl03.hbm.xml</value>
			    <value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/Vfl04.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/CarInfo.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/CarInfoAuto.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/Cartypetemplate.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/HwBaseCOCcs.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/Proenvironment.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/Fuellabeltemplate.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/CarPublicModelIcon.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/cardata/model/CarColorModel.hbm.xml</value>							
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/InterfaceData.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Mh01.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Ms05.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Bm04.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Bm05.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Bm07.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/CarColorMapping.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/HWCOCYearSeq.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/CertificateSupplementInfo.hbm.xml</value>
				<!-- fuelupload start-->
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/Uploaduser.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/certificate/model/Holiday.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/fuelupload/model/FilterModel.hbm.xml</value>
				
				
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Ts02.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Bv01.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Bv02.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Bs09.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Vfl01.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/share/model/Vfl02.hbm.xml</value>
				<value>com/dawnpro/dfpv/carfilemanager/module/business/fuelupload/model/SysGasuploadLog.hbm.xml</value>
				<!-- fuelupload end -->	
			</list>
		</property>
	</bean>
	
	<bean id="transactionManager" class="org.springframework.orm.hibernate3.HibernateTransactionManager">
		<property name="sessionFactory">
			<ref local="mySessionFactory" />
		</property>
		 <!-- 嵌套事物 --> 
        <property name="nestedTransactionAllowed" value="true"/> 
	</bean>
	
	<tx:advice id="txAdvice">   
    	<tx:attributes>
        	<tx:method name="add*" propagation="REQUIRED"/>
        	<!-- 嵌套事务  -->
        	<tx:method name="addBatchData" propagation="NESTED"/>      
        	<tx:method name="addLog" propagation="NESTED"/>    
        	
        	<tx:method name="update*" propagation="REQUIRED"/>   
        	<tx:method name="delete*" propagation="REQUIRED"/>  
        	<tx:method name="get*" read-only="true" />  
        	<tx:method name="load*" read-only="true" />
        	<tx:method name="find*" read-only="true" />       
        	<tx:method name="login" read-only="true" />
        	<tx:method name="pagination*" read-only="true" />  
    	</tx:attributes>   
	</tx:advice>  
	
	<aop:config proxy-target-class="true" >
		<aop:pointcut id="defaultServiceOperation" expression="execution(* com.dawnpro.dfpv.carfilemanager..*DAO*.*(..))"/>
        <aop:advisor pointcut-ref="defaultServiceOperation" advice-ref="txAdvice"/>        
	</aop:config>
	
</beans>
