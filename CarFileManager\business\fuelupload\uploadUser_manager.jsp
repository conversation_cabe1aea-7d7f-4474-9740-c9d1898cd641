<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
         div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
		.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">
$(function(){
	$("#ok").click(function() {
		var parent = $('#public_pw_dialog');
		if(!checkLengthWithoutSpace($('#username'),1,20)){
			updateTips($(parent).find('#validateTips'),'用户名不能为空且长度不能超过20!');
		}else if(!checkLengthWithoutSpace($('#password'),1,20)){
			updateTips($(parent).find('#validateTips'),'密码不能为空且长度不能超过20!');
		}else if($("input[name='flag']:checked").val() == '3' && !checkLengthWithoutSpace($('#qyjmxx'),1,20)){
			updateTips($(parent).find('#validateTips'),'KEY不能为空且长度不能超过20!');
		}else{
			var dialog = $("#confirm_dialog");
			dialog.find('#message').text("确定保存吗？");	
			dialog.data('title.dialog', '确认').dialog('open');
		}
		
	});

	$("#message_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'确定': function() {
				$(this).dialog('close');
			}
		}	
	});
	
	
	$("#confirm_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},		
			'确定': function() {
				var qyjmxx = '';
				if($("input[name='flag']:checked").val()=='3'){
					qyjmxx = $('#qyjmxx').val();
				}
				jQuery.ajax({
					url: 'business/uploadUser!saveUploadUser.action',
					data:{'username' : $('#username').val(), 'password' : $('#password').val(),'qyjmxx' : qyjmxx,'flag':$("input[name='flag']:checked").val()}, 
			        type: 'POST',
			        dataType:'json', 
		            beforeSend: function() {
	   				
		            },
		            error: function(request) {
		            	
		            },
		            success: function(data) {
		            	var content = data.json;
		            	var msg = '';
		            	var msgDialog=$("#message_dialog");
		            	if(content=="true"){
				   	   		//updateTips($(parent).find('#validateTips'),'修改成功!');
				   	   		msg = "修改成功!";
				   	   		
		            	}else{
				   	   		//updateTips($(parent).find('#validateTips'),'修改失败!'+content);
				   	   		msg = "修改失败!";
		            	}
		            	msgDialog.find('#message').text(msg);	
		            	msgDialog.data('title.dialog', '消息').dialog('open');	
		            	
		            }
		        });
		        $(this).dialog('close');
			}
		}
	});
	
});

function loadUploadUserInfo(obj){
	//获取当前类型的账号信息，燃油显示三项，国环显示两项
	jQuery.ajax({
		url: 'business/uploadUser!findUploadUser.action', 	           
		data:{'flag' : $(obj).val()}, 
        type: 'POST',
        dataType:'json', 
           beforeSend: function() {	
           },
           error: function(request) {
           },
           success: function(data) {
           	var content = data.json;
           	var users = eval("("+content.toString()+")");
           	$("#username").val(users.username);	
           	$("#password").val(users.password);
           	
           	if(	$("input[name='flag']:checked").val()=='3'){
           		$("#keytr").show();
           		$("#qyjmxx").val(users.qyjmxx);
           		
           		$("#public_pw_dialog table tr:eq(1)").show();
           		$("#public_pw_dialog table tr:eq(3)").show();
           	}
           	else if($("input[name='flag']:checked").val()=='4'){
           		$("#public_pw_dialog table tr:eq(1)").hide();
           		$("#keytr").hide();
           	}else{
           		$("#keytr").hide();
           		
           		$("#public_pw_dialog table tr:eq(1)").show();
           	}
           	
           }
       });	
}

</script>
<title></title>
</head>
<body>
<div id="public_pw_dialog">
	<p id="validateTips" align="center"></p>
	<fieldset>
		
			<table width="380" border="0" align="center" cellpadding="0" cellspacing="0">
			  	<tr>
					<td align="right"><label>用户类别：</label></td>
					<td><s:radio theme="simple" onclick="loadUploadUserInfo(this);" list="#{'3':'燃油','2':'国环','4':'证书'}" name="flag" value="#request.uploaduser.flag" cssClass="text ui-widget-content ui-corner-all" /></td>
				</tr>
			  	<tr>
					<td align="right"><label>用 户 名：</label></td>
					<td><input type="text" id="username" name="username" class="text ui-widget-content ui-corner-all" size="18" <s:if test="#request.uploaduser.username!=null"> value="<s:property value="#request.uploaduser.username" />"</s:if>/></td>
				</tr>
				<tr>
					<td align="right"><label> 密 码：</label></td>
					<td><input type="text" id="password" name="password" class="text ui-widget-content ui-corner-all" size="18" <s:if test="#request.uploaduser.password!=null"> value="<s:property value="#request.uploaduser.password" />"</s:if>/></td>
				</tr>
				<s:if test="#request.uploaduser.flag=='3'.toString()">
				<tr id="keytr">
					<td align="right"><label>KEY值：</label></td>
					<td><input type="text" id="qyjmxx" name="qyjmxx" class="text ui-widget-content ui-corner-all" size="18" <s:if test="#request.uploaduser.qyjmxx!=null"> value="<s:property value="#request.uploaduser.qyjmxx" />"</s:if>/></td>
				</tr>
				</s:if>
			  	<tr><td colspan="2"><div align="center"><button id="ok" class="ui-button ui-state-default ui-corner-all">确定</button></div></td></tr>
			</table>
				
	</fieldset>
</div>
<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='password' name='password'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>
<div id="confirm_dialog" title="确认窗口" style="display:none">
	
	<p id="message" align="center" style="font: bold;"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	
</div>
<div id="message_dialog" title="消息窗口" style="display:none">
	
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>	
	
</div>
</body>
</html>