package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;

public interface SysRoleDAO {
	
	public void addRole(SysRole role) throws DataAccessException;
	
	public void deleteRole(SysRole[] role) throws DataAccessException;
	
	public void updateRole(SysRole role) throws DataAccessException;
	
	public List<SysRole> findRole(String sql, String[] params) throws DataAccessException;
	
	public List<SysRole> findRole(String sql) throws DataAccessException;
	
	public SysRole loadRoleObj(String roleId) throws DataAccessException;
	
	public List<SysRole> findAllRole() throws DataAccessException;
}
