<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="HWCOC_A4_CDD_LC_02"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Landscape"
		 pageWidth="842"
		 pageHeight="595"
		 columnWidth="802"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.zoom" value="1.5" />
	<property name="ireport.x" value="20" />
	<property name="ireport.y" value="0" />
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />


	<field name="barCodeImageUrl" class="java.lang.String">
		<fieldDescription><![CDATA[barCodeImageUrl]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="year" class="java.lang.String">
		<fieldDescription><![CDATA[year]]></fieldDescription>
	</field>
	<field name="yearseq" class="java.lang.String">
		<fieldDescription><![CDATA[yearseq]]></fieldDescription>
	</field>
	<field name="color" class="java.lang.String">
		<fieldDescription><![CDATA[color]]></fieldDescription>
	</field>
	<field name="imageInputStream" class="java.io.InputStream">
		<fieldDescription><![CDATA[imageInputStream]]></fieldDescription>
	</field>
	<field name="prodDate" class="java.lang.String">
		<fieldDescription><![CDATA[prodDate]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="c1" class="java.lang.String">
		<fieldDescription><![CDATA[c1]]></fieldDescription>
	</field>
	<field name="c2" class="java.lang.String">
		<fieldDescription><![CDATA[c2]]></fieldDescription>
	</field>
	<field name="c3" class="java.lang.String">
		<fieldDescription><![CDATA[c3]]></fieldDescription>
	</field>
	<field name="c4" class="java.lang.String">
		<fieldDescription><![CDATA[c4]]></fieldDescription>
	</field>
	<field name="c5" class="java.lang.String">
		<fieldDescription><![CDATA[c5]]></fieldDescription>
	</field>
	<field name="c6" class="java.lang.String">
		<fieldDescription><![CDATA[c6]]></fieldDescription>
	</field>
	<field name="c7" class="java.lang.String">
		<fieldDescription><![CDATA[c7]]></fieldDescription>
	</field>
	<field name="c8" class="java.lang.String">
		<fieldDescription><![CDATA[c8]]></fieldDescription>
	</field>
	<field name="c9" class="java.lang.String">
		<fieldDescription><![CDATA[c9]]></fieldDescription>
	</field>
	<field name="c10" class="java.lang.String">
		<fieldDescription><![CDATA[c10]]></fieldDescription>
	</field>
	<field name="c11" class="java.lang.String">
		<fieldDescription><![CDATA[c11]]></fieldDescription>
	</field>
	<field name="c12" class="java.lang.String">
		<fieldDescription><![CDATA[c12]]></fieldDescription>
	</field>
	<field name="c13" class="java.lang.String">
		<fieldDescription><![CDATA[c13]]></fieldDescription>
	</field>
	<field name="c14" class="java.lang.String">
		<fieldDescription><![CDATA[c14]]></fieldDescription>
	</field>
	<field name="c15" class="java.lang.String">
		<fieldDescription><![CDATA[c15]]></fieldDescription>
	</field>
	<field name="c16" class="java.lang.String">
		<fieldDescription><![CDATA[c16]]></fieldDescription>
	</field>
	<field name="c17" class="java.lang.String">
		<fieldDescription><![CDATA[c17]]></fieldDescription>
	</field>
	<field name="c18" class="java.lang.String">
		<fieldDescription><![CDATA[c18]]></fieldDescription>
	</field>
	<field name="c19" class="java.lang.String">
		<fieldDescription><![CDATA[c19]]></fieldDescription>
	</field>
	<field name="c20" class="java.lang.String">
		<fieldDescription><![CDATA[c20]]></fieldDescription>
	</field>
	<field name="c21" class="java.lang.String">
		<fieldDescription><![CDATA[c21]]></fieldDescription>
	</field>
	<field name="c22" class="java.lang.String">
		<fieldDescription><![CDATA[c22]]></fieldDescription>
	</field>
	<field name="c23" class="java.lang.String">
		<fieldDescription><![CDATA[c23]]></fieldDescription>
	</field>
	<field name="c24" class="java.lang.String">
		<fieldDescription><![CDATA[c24]]></fieldDescription>
	</field>
	<field name="c25" class="java.lang.String">
		<fieldDescription><![CDATA[c25]]></fieldDescription>
	</field>
	<field name="c26" class="java.lang.String">
		<fieldDescription><![CDATA[c26]]></fieldDescription>
	</field>
	<field name="c27" class="java.lang.String">
		<fieldDescription><![CDATA[c27]]></fieldDescription>
	</field>
	<field name="c28" class="java.lang.String">
		<fieldDescription><![CDATA[c28]]></fieldDescription>
	</field>
	<field name="c29" class="java.lang.String">
		<fieldDescription><![CDATA[c29]]></fieldDescription>
	</field>
	<field name="c30" class="java.lang.String">
		<fieldDescription><![CDATA[c30]]></fieldDescription>
	</field>
	<field name="c31" class="java.lang.String">
		<fieldDescription><![CDATA[c31]]></fieldDescription>
	</field>
	<field name="c32" class="java.lang.String">
		<fieldDescription><![CDATA[c32]]></fieldDescription>
	</field>
	<field name="c33" class="java.lang.String">
		<fieldDescription><![CDATA[c33]]></fieldDescription>
	</field>
	<field name="c34" class="java.lang.String">
		<fieldDescription><![CDATA[c34]]></fieldDescription>
	</field>
	<field name="c35" class="java.lang.String">
		<fieldDescription><![CDATA[c35]]></fieldDescription>
	</field>
	<field name="c36" class="java.lang.String">
		<fieldDescription><![CDATA[c36]]></fieldDescription>
	</field>
	<field name="c37" class="java.lang.String">
		<fieldDescription><![CDATA[c37]]></fieldDescription>
	</field>
	<field name="c38" class="java.lang.String">
		<fieldDescription><![CDATA[c38]]></fieldDescription>
	</field>
	<field name="c39" class="java.lang.String">
		<fieldDescription><![CDATA[c39]]></fieldDescription>
	</field>
	<field name="c40" class="java.lang.String">
		<fieldDescription><![CDATA[c40]]></fieldDescription>
	</field>
	<field name="c41" class="java.lang.String">
		<fieldDescription><![CDATA[c41]]></fieldDescription>
	</field>
	<field name="c42" class="java.lang.String">
		<fieldDescription><![CDATA[c42]]></fieldDescription>
	</field>
	<field name="c43" class="java.lang.String">
		<fieldDescription><![CDATA[c43]]></fieldDescription>
	</field>
	<field name="c44" class="java.lang.String">
		<fieldDescription><![CDATA[c44]]></fieldDescription>
	</field>
	<field name="c45" class="java.lang.String">
		<fieldDescription><![CDATA[c45]]></fieldDescription>
	</field>
	<field name="c46" class="java.lang.String">
		<fieldDescription><![CDATA[c46]]></fieldDescription>
	</field>
	<field name="c47" class="java.lang.String">
		<fieldDescription><![CDATA[c47]]></fieldDescription>
	</field>
	<field name="c48" class="java.lang.String">
		<fieldDescription><![CDATA[c48]]></fieldDescription>
	</field>
	<field name="c49" class="java.lang.String">
		<fieldDescription><![CDATA[c49]]></fieldDescription>
	</field>
	<field name="c50" class="java.lang.String">
		<fieldDescription><![CDATA[c50]]></fieldDescription>
	</field>
	<field name="c51" class="java.lang.String">
		<fieldDescription><![CDATA[c51]]></fieldDescription>
	</field>
	<field name="c52" class="java.lang.String">
		<fieldDescription><![CDATA[c52]]></fieldDescription>
	</field>
	<field name="c53" class="java.lang.String">
		<fieldDescription><![CDATA[c53]]></fieldDescription>
	</field>
	<field name="c54" class="java.lang.String">
		<fieldDescription><![CDATA[c54]]></fieldDescription>
	</field>
	<field name="c55" class="java.lang.String">
		<fieldDescription><![CDATA[c55]]></fieldDescription>
	</field>
	<field name="c56" class="java.lang.String">
		<fieldDescription><![CDATA[c56]]></fieldDescription>
	</field>
	<field name="c57" class="java.lang.String">
		<fieldDescription><![CDATA[c57]]></fieldDescription>
	</field>
	<field name="c58" class="java.lang.String">
		<fieldDescription><![CDATA[c58]]></fieldDescription>
	</field>
	<field name="c59" class="java.lang.String">
		<fieldDescription><![CDATA[c59]]></fieldDescription>
	</field>
	<field name="c60" class="java.lang.String">
		<fieldDescription><![CDATA[c60]]></fieldDescription>
	</field>
	<field name="c61" class="java.lang.String">
		<fieldDescription><![CDATA[c61]]></fieldDescription>
	</field>
	<field name="c62" class="java.lang.String">
		<fieldDescription><![CDATA[c62]]></fieldDescription>
	</field>
	<field name="c63" class="java.lang.String">
		<fieldDescription><![CDATA[c63]]></fieldDescription>
	</field>
	<field name="c64" class="java.lang.String">
		<fieldDescription><![CDATA[c64]]></fieldDescription>
	</field>
	<field name="c65" class="java.lang.String">
		<fieldDescription><![CDATA[c65]]></fieldDescription>
	</field>
	<field name="c66" class="java.lang.String">
		<fieldDescription><![CDATA[c66]]></fieldDescription>
	</field>
	<field name="c67" class="java.lang.String">
		<fieldDescription><![CDATA[c67]]></fieldDescription>
	</field>
	<field name="c68" class="java.lang.String">
		<fieldDescription><![CDATA[c68]]></fieldDescription>
	</field>
	<field name="c69" class="java.lang.String">
		<fieldDescription><![CDATA[c69]]></fieldDescription>
	</field>
	<field name="c70" class="java.lang.String">
		<fieldDescription><![CDATA[c70]]></fieldDescription>
	</field>
	<field name="c71" class="java.lang.String">
		<fieldDescription><![CDATA[c71]]></fieldDescription>
	</field>
	<field name="c72" class="java.lang.String">
		<fieldDescription><![CDATA[c72]]></fieldDescription>
	</field>
	<field name="c73" class="java.lang.String">
		<fieldDescription><![CDATA[c73]]></fieldDescription>
	</field>
	<field name="c74" class="java.lang.String">
		<fieldDescription><![CDATA[c74]]></fieldDescription>
	</field>
	<field name="c75" class="java.lang.String">
		<fieldDescription><![CDATA[c75]]></fieldDescription>
	</field>
	<field name="c76" class="java.lang.String">
		<fieldDescription><![CDATA[c76]]></fieldDescription>
	</field>
	<field name="c77" class="java.lang.String">
		<fieldDescription><![CDATA[c77]]></fieldDescription>
	</field>
	<field name="c78" class="java.lang.String">
		<fieldDescription><![CDATA[c78]]></fieldDescription>
	</field>
	<field name="c79" class="java.lang.String">
		<fieldDescription><![CDATA[c79]]></fieldDescription>
	</field>
	<field name="c80" class="java.lang.String">
		<fieldDescription><![CDATA[c80]]></fieldDescription>
	</field>
	<field name="c81" class="java.lang.String">
		<fieldDescription><![CDATA[c81]]></fieldDescription>
	</field>
	<field name="c82" class="java.lang.String">
		<fieldDescription><![CDATA[c82]]></fieldDescription>
	</field>
	<field name="c83" class="java.lang.String">
		<fieldDescription><![CDATA[c83]]></fieldDescription>
	</field>
	<field name="c84" class="java.lang.String">
		<fieldDescription><![CDATA[c84]]></fieldDescription>
	</field>
	<field name="c85" class="java.lang.String">
		<fieldDescription><![CDATA[c85]]></fieldDescription>
	</field>
	<field name="c86" class="java.lang.String">
		<fieldDescription><![CDATA[c86]]></fieldDescription>
	</field>
	<field name="c87" class="java.lang.String">
		<fieldDescription><![CDATA[c87]]></fieldDescription>
	</field>
	<field name="c88" class="java.lang.String">
		<fieldDescription><![CDATA[c88]]></fieldDescription>
	</field>
	<field name="c89" class="java.lang.String">
		<fieldDescription><![CDATA[c89]]></fieldDescription>
	</field>
	<field name="c90" class="java.lang.String">
		<fieldDescription><![CDATA[c90]]></fieldDescription>
	</field>
	<field name="c91" class="java.lang.String">
		<fieldDescription><![CDATA[c91]]></fieldDescription>
	</field>
	<field name="c92" class="java.lang.String">
		<fieldDescription><![CDATA[c92]]></fieldDescription>
	</field>
	<field name="c93" class="java.lang.String">
		<fieldDescription><![CDATA[c93]]></fieldDescription>
	</field>
	<field name="c94" class="java.lang.String">
		<fieldDescription><![CDATA[c94]]></fieldDescription>
	</field>
	<field name="c95" class="java.lang.String">
		<fieldDescription><![CDATA[c95]]></fieldDescription>
	</field>
	<field name="c96" class="java.lang.String">
		<fieldDescription><![CDATA[c96]]></fieldDescription>
	</field>
	<field name="c97" class="java.lang.String">
		<fieldDescription><![CDATA[c97]]></fieldDescription>
	</field>
	<field name="c98" class="java.lang.String">
		<fieldDescription><![CDATA[c98]]></fieldDescription>
	</field>
	<field name="c99" class="java.lang.String">
		<fieldDescription><![CDATA[c99]]></fieldDescription>
	</field>
	<field name="c100" class="java.lang.String">
		<fieldDescription><![CDATA[c100]]></fieldDescription>
	</field>
	<field name="c101" class="java.lang.String">
		<fieldDescription><![CDATA[c101]]></fieldDescription>
	</field>
	<field name="c102" class="java.lang.String">
		<fieldDescription><![CDATA[c102]]></fieldDescription>
	</field>
	<field name="c103" class="java.lang.String">
		<fieldDescription><![CDATA[c103]]></fieldDescription>
	</field>
	<field name="c104" class="java.lang.String">
		<fieldDescription><![CDATA[c104]]></fieldDescription>
	</field>
	<field name="c105" class="java.lang.String">
		<fieldDescription><![CDATA[c105]]></fieldDescription>
	</field>
	<field name="c106" class="java.lang.String">
		<fieldDescription><![CDATA[c106]]></fieldDescription>
	</field>
	<field name="c107" class="java.lang.String">
		<fieldDescription><![CDATA[c107]]></fieldDescription>
	</field>
	<field name="c108" class="java.lang.String">
		<fieldDescription><![CDATA[c108]]></fieldDescription>
	</field>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="555"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="25"
						y="19"
						width="39"
						height="14"
						key="staticText-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 25.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="19"
						width="166"
						height="14"
						key="staticText-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Engine capacity
)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="19"
						width="15"
						height="14"
						key="staticText-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="19"
						width="164"
						height="14"
						key="textField-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c49}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="33"
						width="39"
						height="14"
						key="staticText-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 26.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="33"
						width="166"
						height="14"
						key="staticText-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Fuel]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="33"
						width="15"
						height="14"
						key="staticText-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="33"
						width="164"
						height="14"
						key="textField-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c50}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="47"
						width="39"
						height="14"
						key="staticText-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 27.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="47"
						width="345"
						height="14"
						key="staticText-12"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Maximum power]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="61"
						width="39"
						height="14"
						key="staticText-14"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 27.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="61"
						width="166"
						height="14"
						key="staticText-15"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Maximum net power]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="61"
						width="15"
						height="14"
						key="staticText-16"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="61"
						width="164"
						height="14"
						key="textField-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c52}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="75"
						width="39"
						height="14"
						key="staticText-17"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 27.3.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="75"
						width="166"
						height="14"
						key="staticText-18"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Maximum net power]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="75"
						width="15"
						height="14"
						key="staticText-19"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="75"
						width="164"
						height="14"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c53}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="89"
						width="39"
						height="14"
						key="staticText-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 27.4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="89"
						width="166"
						height="14"
						key="staticText-21"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Maximum 30 minutes power]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="89"
						width="15"
						height="14"
						key="staticText-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="89"
						width="164"
						height="14"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c54}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="103"
						width="39"
						height="14"
						key="staticText-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 28.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="103"
						width="166"
						height="14"
						key="staticText-24"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Gearbox (type)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="103"
						width="15"
						height="14"
						key="staticText-25"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="103"
						width="164"
						height="14"
						key="textField-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c55}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="117"
						width="39"
						height="24"
						key="staticText-26"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 28.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="117"
						width="166"
						height="24"
						key="staticText-27"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Gearbox ratios (to complete for vehicles with manual shift transmissions)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="117"
						width="15"
						height="24"
						key="staticText-28"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="117"
						width="164"
						height="24"
						key="textField-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c56}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="141"
						width="39"
						height="14"
						key="staticText-29"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 28.1.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="141"
						width="166"
						height="14"
						key="staticText-30"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Final drive ratio (if applicable)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="141"
						width="15"
						height="14"
						key="staticText-31"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="141"
						width="164"
						height="14"
						key="textField-12"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c57}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="155"
						width="39"
						height="24"
						key="staticText-32"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 28.1.2. ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="155"
						width="166"
						height="24"
						key="staticText-33"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Final drive ratios (to complete if and where applicable)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="155"
						width="15"
						height="24"
						key="staticText-34"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="155"
						width="164"
						height="24"
						key="textField-13"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c58}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="200"
						width="39"
						height="14"
						key="staticText-35"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 29.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="200"
						width="166"
						height="14"
						key="staticText-36"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[   Maximum speed]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="200"
						width="15"
						height="14"
						key="staticText-37"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="200"
						width="164"
						height="14"
						key="textField-14"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c59}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="250"
						width="39"
						height="38"
						key="staticText-38"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 35.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="250"
						width="345"
						height="12"
						key="staticText-39"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Fitted tyre/wheel combination/energy efficiency class of rolling resistance coefficients (RRC) and tyre category used for CO₂ determination (if applicable):]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="309"
						width="39"
						height="24"
						key="staticText-53"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 36.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="309"
						width="166"
						height="24"
						key="staticText-54"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Trailer brake connections mechanical/electric/pneumatic/hydraulic]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="309"
						width="15"
						height="24"
						key="staticText-55"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="309"
						width="164"
						height="24"
						key="textField-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c62}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="491"
						width="39"
						height="14"
						key="staticText-62"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 46.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="491"
						width="345"
						height="14"
						key="staticText-63"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Sound level]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="505"
						width="39"
						height="14"
						key="staticText-65"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="505"
						width="166"
						height="14"
						key="staticText-66"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  — Stationary N/A dB(A) at engine speed]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="505"
						width="15"
						height="14"
						key="staticText-67"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="505"
						width="164"
						height="14"
						key="textField-24"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c69}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="186"
						width="384"
						height="14"
						key="staticText-149"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Maximum speed]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="236"
						width="39"
						height="14"
						key="staticText-150"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 30.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="236"
						width="166"
						height="14"
						key="staticText-151"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[   Axle(s) track]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="236"
						width="15"
						height="14"
						key="staticText-152"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="236"
						width="164"
						height="14"
						key="textField-53"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c60}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="222"
						width="384"
						height="14"
						key="staticText-153"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Axles and suspension]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="64"
						y="274"
						width="345"
						height="14"
						key="textField-54"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c61}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="295"
						width="384"
						height="14"
						key="staticText-154"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Brakes]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="341"
						width="384"
						height="14"
						key="staticText-155"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Bodywork]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="355"
						width="39"
						height="14"
						key="staticText-156"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 38.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="262"
						width="139"
						height="12"
						key="staticText-157"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ (RRC) and tyre category used for CO]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="355"
						width="15"
						height="14"
						key="staticText-158"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="355"
						width="164"
						height="14"
						key="textField-55"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c63}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="369"
						width="39"
						height="14"
						key="staticText-159"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 40.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="369"
						width="166"
						height="14"
						key="staticText-160"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Color of vehicle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="369"
						width="15"
						height="14"
						key="staticText-161"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="369"
						width="164"
						height="14"
						key="textField-56"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{color}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="383"
						width="39"
						height="14"
						key="staticText-162"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 41.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="383"
						width="166"
						height="14"
						key="staticText-163"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Number and configuration of doors]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="383"
						width="15"
						height="14"
						key="staticText-164"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="383"
						width="164"
						height="14"
						key="textField-57"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c65}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="421"
						width="39"
						height="24"
						key="staticText-171"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 42.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="421"
						width="166"
						height="24"
						key="staticText-172"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Seat(s) designated for use only when the vehicle is stationary]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="421"
						width="15"
						height="24"
						key="staticText-173"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="421"
						width="164"
						height="24"
						key="textField-60"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c67}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="397"
						width="39"
						height="24"
						key="staticText-192"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 42.
 ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="397"
						width="166"
						height="24"
						key="staticText-193"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Number of seating positions (including the driver)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="397"
						width="15"
						height="24"
						key="staticText-194"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="397"
						width="164"
						height="24"
						key="textField-67"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c66}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="445"
						width="39"
						height="24"
						key="staticText-195"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 42.3.
 ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="445"
						width="166"
						height="24"
						key="staticText-196"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Number of wheelchair user accessible position]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="445"
						width="15"
						height="24"
						key="staticText-197"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="445"
						width="164"
						height="24"
						key="textField-68"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c68}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="477"
						width="384"
						height="14"
						key="staticText-198"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Environmental performances]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="25"
						y="519"
						width="39"
						height="14"
						key="staticText-199"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="519"
						width="166"
						height="14"
						key="staticText-200"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  — Drive-by]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="230"
						y="519"
						width="15"
						height="14"
						key="staticText-201"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="245"
						y="519"
						width="164"
						height="14"
						key="textField-69"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c70}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="19"
						width="39"
						height="12"
						key="staticText-202"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47. ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="19"
						width="166"
						height="12"
						key="staticText-203"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Exhaust emission level

)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="19"
						width="15"
						height="12"
						key="staticText-204"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="19"
						width="154"
						height="12"
						key="textField-70"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c71}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="31"
						width="39"
						height="12"
						key="staticText-205"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="31"
						width="143"
						height="12"
						key="staticText-206"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Parameters for emission testing of V]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="409"
						y="43"
						width="39"
						height="12"
						key="staticText-208"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="43"
						width="166"
						height="12"
						key="staticText-209"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Test mass, kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="43"
						width="15"
						height="12"
						key="staticText-210"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="43"
						width="154"
						height="12"
						key="textField-72"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c72}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="55"
						width="39"
						height="12"
						key="staticText-211"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.2.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="55"
						width="166"
						height="12"
						key="staticText-212"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Frontal area, m²]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="55"
						width="15"
						height="12"
						key="staticText-213"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="55"
						width="154"
						height="12"
						key="textField-73"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c73}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="67"
						width="39"
						height="24"
						key="staticText-214"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.2.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="67"
						width="166"
						height="24"
						key="staticText-215"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Projected frontal area of air entrance of the front grille (if applicable), cm²]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="67"
						width="15"
						height="24"
						key="staticText-216"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="67"
						width="154"
						height="24"
						key="textField-74"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c74}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="91"
						width="39"
						height="12"
						key="staticText-217"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.3. ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="91"
						width="166"
						height="12"
						key="staticText-218"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Road load coefficients]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="409"
						y="103"
						width="39"
						height="12"
						key="staticText-220"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.3.0.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="103"
						width="166"
						height="12"
						key="staticText-221"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  f0, N]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="103"
						width="15"
						height="12"
						key="staticText-222"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="103"
						width="154"
						height="12"
						key="textField-76"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c75}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="115"
						width="39"
						height="12"
						key="staticText-223"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.3.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="115"
						width="166"
						height="12"
						key="staticText-224"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  f1, N/(km/h)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="115"
						width="15"
						height="12"
						key="staticText-225"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="115"
						width="154"
						height="12"
						key="textField-77"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c76}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="127"
						width="39"
						height="12"
						key="staticText-226"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.1.3.2.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="127"
						width="166"
						height="12"
						key="staticText-227"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  f2, N/(km/h)²]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="127"
						width="15"
						height="12"
						key="staticText-228"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="127"
						width="154"
						height="12"
						key="textField-78"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c77}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="139"
						width="39"
						height="12"
						key="staticText-229"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.2.
 ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="139"
						width="166"
						height="12"
						key="staticText-230"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Driving cycle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="409"
						y="151"
						width="39"
						height="12"
						key="staticText-232"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.2.1.
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="151"
						width="166"
						height="12"
						key="staticText-233"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Driving Cycle class(1/2/3a/3b)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="151"
						width="15"
						height="12"
						key="staticText-234"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="151"
						width="154"
						height="12"
						key="textField-80"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c78}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="163"
						width="39"
						height="12"
						key="staticText-235"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.2.2. 
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="163"
						width="86"
						height="12"
						key="staticText-236"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Downscaling factor (f 
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="163"
						width="15"
						height="12"
						key="staticText-237"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="163"
						width="154"
						height="12"
						key="textField-81"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c79}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="175"
						width="39"
						height="12"
						key="staticText-238"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 47.2.3. 
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="175"
						width="166"
						height="12"
						key="staticText-239"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Capped speed( yes/no)
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="175"
						width="15"
						height="12"
						key="staticText-240"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="175"
						width="154"
						height="12"
						key="textField-82"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c80}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="187"
						width="39"
						height="12"
						key="staticText-241"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 48.
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="187"
						width="166"
						height="12"
						key="staticText-242"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Exhaust emissions
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="614"
						y="187"
						width="15"
						height="12"
						key="staticText-243"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="629"
						y="187"
						width="154"
						height="34"
						key="textField-83"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c81}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="221"
						width="39"
						height="12"
						key="staticText-244"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 49.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="467"
						y="221"
						width="315"
						height="12"
						key="staticText-245"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[emissions/fuel consumption/electric energy consumption
 of the front grille (if applicable), cm2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="409"
						y="233"
						width="39"
						height="12"
						key="staticText-247"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 1. 
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="233"
						width="334"
						height="12"
						key="staticText-248"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  All powertrains, except OVC hybrid electric (if applicable)
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="415"
						y="245"
						width="80"
						height="14"
						key="staticText-249"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  WLTP values]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="495"
						y="245"
						width="14"
						height="14"
						key="staticText-250"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ CO]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="580"
						y="245"
						width="85"
						height="14"
						key="staticText-251"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ Fuel consumption]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="665"
						y="245"
						width="98"
						height="14"
						key="staticText-252"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ Electric Consumption (EC   )]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="415"
						y="259"
						width="80"
						height="14"
						key="staticText-253"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Low]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="495"
						y="259"
						width="85"
						height="14"
						key="textField-84"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c82}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="580"
						y="259"
						width="85"
						height="14"
						key="textField-85"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c87}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="665"
						y="259"
						width="118"
						height="14"
						key="textField-86"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c92}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="415"
						y="273"
						width="80"
						height="14"
						key="staticText-254"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Medium]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="495"
						y="273"
						width="85"
						height="14"
						key="textField-87"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c83}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="580"
						y="273"
						width="85"
						height="14"
						key="textField-88"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c88}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="665"
						y="273"
						width="118"
						height="14"
						key="textField-89"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c93}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="415"
						y="287"
						width="80"
						height="14"
						key="staticText-255"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  High
]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="495"
						y="287"
						width="85"
						height="14"
						key="textField-90"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c84}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="580"
						y="287"
						width="85"
						height="14"
						key="textField-91"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c89}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="665"
						y="287"
						width="118"
						height="14"
						key="textField-92"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c94}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="415"
						y="301"
						width="80"
						height="14"
						key="staticText-256"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Extra High
]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="495"
						y="301"
						width="85"
						height="14"
						key="textField-93"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c85}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="580"
						y="301"
						width="85"
						height="14"
						key="textField-94"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c90}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="665"
						y="301"
						width="118"
						height="14"
						key="textField-95"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c95}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="415"
						y="315"
						width="80"
						height="14"
						key="staticText-257"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Combined
]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="495"
						y="315"
						width="85"
						height="14"
						key="textField-96"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c86}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="580"
						y="315"
						width="85"
						height="14"
						key="textField-97"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c91}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="665"
						y="315"
						width="118"
						height="14"
						key="textField-98"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c96}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="329"
						width="39"
						height="12"
						key="staticText-258"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 2. 
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="329"
						width="334"
						height="12"
						key="staticText-259"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Electric range of pure electric vehicles (if applicable)
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="341"
						width="201"
						height="12"
						key="staticText-261"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Electric range
]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="649"
						y="341"
						width="133"
						height="12"
						key="textField-99"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c98}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="448"
						y="353"
						width="201"
						height="12"
						key="staticText-264"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Electric range city
]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="649"
						y="353"
						width="133"
						height="12"
						key="textField-100"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c99}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="365"
						width="39"
						height="12"
						key="staticText-266"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 3.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="365"
						width="186"
						height="12"
						key="staticText-267"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Vehicle fitted with eco-innovation(s)(yes/no) ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="634"
						y="365"
						width="15"
						height="12"
						key="staticText-268"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="649"
						y="365"
						width="133"
						height="12"
						key="textField-101"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c100}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="377"
						width="39"
						height="12"
						key="staticText-269"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="377"
						width="186"
						height="12"
						key="staticText-270"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  OVC hybrid electric vehicles (if applicable)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="634"
						y="377"
						width="15"
						height="12"
						key="staticText-271"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="649"
						y="377"
						width="133"
						height="12"
						key="textField-102"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c101}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="389"
						width="39"
						height="24"
						key="staticText-272"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 5.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="389"
						width="186"
						height="24"
						key="staticText-273"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Electric range of OVC hybrid electric vehicles (if applicable)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="634"
						y="389"
						width="15"
						height="24"
						key="staticText-274"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ :]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="649"
						y="389"
						width="133"
						height="24"
						key="textField-103"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c102}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="413"
						width="373"
						height="14"
						key="staticText-275"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Miscellaneous]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="409"
						y="427"
						width="25"
						height="34"
						key="staticText-276"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 51.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="434"
						y="427"
						width="306"
						height="34"
						key="staticText-277"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  For special purpose vehicles: designation in accordance with point 5 of Part A of Annex I to Regulation (EU) 2018/858 of the European Parliament and of the Council:]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="409"
						y="521"
						width="25"
						height="12"
						key="staticText-281"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 56.
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="434"
						y="521"
						width="276"
						height="12"
						key="staticText-282"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Vehicle certified in accordance with UN Regulation No 156(yes/no):

]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="710"
						y="521"
						width="72"
						height="12"
						key="textField-105"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c108}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="509"
						width="25"
						height="12"
						key="staticText-284"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 55.
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="434"
						y="509"
						width="276"
						height="12"
						key="staticText-285"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Vehicle certified in accordance with UN Regulation No 155(yes/no):

]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="710"
						y="509"
						width="72"
						height="12"
						key="textField-106"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c107}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="485"
						width="25"
						height="24"
						key="staticText-287"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 54.]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="710"
						y="485"
						width="72"
						height="24"
						key="textField-107"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c106}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="461"
						width="25"
						height="12"
						key="staticText-289"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 52.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="434"
						y="461"
						width="49"
						height="12"
						key="staticText-290"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Remarks:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="483"
						y="461"
						width="299"
						height="12"
						key="textField-108"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c104}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="409"
						y="473"
						width="149"
						height="12"
						key="staticText-292"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Additional tyre/wheel combinations:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="558"
						y="473"
						width="225"
						height="12"
						key="textField-109"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c105}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="744"
						y="427"
						width="40"
						height="34"
						key="textField-110"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center" rotation="None" lineSpacing="Single">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c103}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="434"
						y="485"
						width="276"
						height="24"
						key="staticText-293"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Vehicle fitted with(TPMS/ELKS/AEBS/ESS/AIF/ISA/DDAW/ADDW
/EDR/DAM/ADS/eCall):]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="591"
						y="34"
						width="64"
						height="11"
						key="staticText-294"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="6" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ind]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="534"
						y="166"
						width="13"
						height="11"
						key="staticText-295"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="6" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[dsc]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="547"
						y="163"
						width="54"
						height="12"
						key="staticText-297"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[)
]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="64"
						y="355"
						width="166"
						height="14"
						key="staticText-298"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Code for bodywork]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="208"
						y="262"
						width="123"
						height="12"
						key="staticText-299"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ determination (if applicable):]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="203"
						y="265"
						width="5"
						height="9"
						key="staticText-300"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" lineSpacing="Single">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="6" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="514"
						y="245"
						width="66"
						height="14"
						key="staticText-301"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[emissions]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="509"
						y="245"
						width="5"
						height="14"
						key="staticText-302"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" lineSpacing="1_1_2">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="6" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="448"
						y="221"
						width="14"
						height="12"
						key="staticText-303"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ CO]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="462"
						y="221"
						width="5"
						height="12"
						key="staticText-304"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" lineSpacing="1_1_2">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="6" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="772"
						y="245"
						width="11"
						height="14"
						key="staticText-305"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="763"
						y="245"
						width="9"
						height="14"
						key="staticText-306"/>
					<box>					<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.25" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" lineSpacing="1_1_2">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="6" isBold="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[AC]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
