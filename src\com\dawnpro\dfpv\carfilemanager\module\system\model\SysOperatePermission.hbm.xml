<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.SysOperatePermission" table="SYS_OPERATE_PERMISSION">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.system.model.SysOperatePermissionId">
            <key-property name="menuId" type="java.lang.String">
                <column name="MENU_ID" length="36" />
            </key-property>
            <key-property name="operateId" type="java.lang.String">
                <column name="OPERATE_ID" length="36" />
            </key-property>
            <key-property name="roleid" type="java.lang.String">
                <column name="ROLEID" length="36" />
            </key-property>
        </composite-id>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
    </class>
</hibernate-mapping>
