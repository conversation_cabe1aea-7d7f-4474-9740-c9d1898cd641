package com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhoto;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhotoId;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bm07;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.COCPhotoDAO;

public class COCPhotoDAOImpl extends GenericHibernateDAOImpl<CocPhoto>  implements COCPhotoDAO {

	public void addPhoto(CocPhoto photo) {
		try{
			this.add(photo);
		}catch(Exception e){
			throw new DataAccessException("addPhoto Method Error:",e);
		}
	}
	
	public void addPhoto(CocPhoto[] photo) {
		try{
			this.addBatch(photo);
		}catch(Exception e){
			throw new DataAccessException("addPhoto Method Error:",e);
		}
	}
	
	public void deletePhoto(CocPhoto photo) {
		try{
			this.delete(photo);
		}catch(Exception e){
			throw new DataAccessException("deletePhoto Method Error:",e);
		}
	}
	

	public void deletePhoto(CocPhoto[] photo) {
		try{
			this.deleteBatch(photo);
		}catch(Exception e){
			throw new DataAccessException("deletePhoto Method Error:",e);
		}
	}
	
	public void updatePhoto(String oldfilename,CocPhoto photo) {
		try{
			this.updateOrDeleteToHSQL("update CocPhoto c SET c.id.filename=?,c.path=?,c.creator=?,c.time=? where c.id.model=? and c.id.filename=? and c.id.vercode=?",new Object[]{photo.getId().getFilename(),photo.getPath(),photo.getCreator(),photo.getTime(),photo.getId().getModel(),oldfilename,photo.getId().getVercode()});
		}catch(Exception e){
			throw new DataAccessException("updatePhoto Method Error:",e);
		}
	}

	public List<CocPhoto> findPhoto(String sql,Object[] params) {
		try{
			return this.find(sql,params);
		}catch(Exception e){
			throw new DataAccessException("findPhoto Method Error:",e);
		}
	}

	public CocPhoto loadCarModelPhotoObj(CocPhotoId id) {
		try{
			return (CocPhoto) this.load(CocPhoto.class, id);
		}catch(Exception e){
			throw new DataAccessException("loadCarModelPhotoObj Method Error:",e);
		}
	}

	public void updateEffectCarModelPhoto(Object[] value) {
		try{
			String sql = "update CocPhoto g set g.state='9' where g.id.model=? and g.id.filename=? and g.state='1'";
			this.updateOrDeleteToHSQL(sql,new Object[]{value[2],value[3]});
			
			sql = "update CocPhoto g set g.state=1,g.time=?,g.effecttime=? where g.id.model=? and g.id.filename=? and g.id.vercode=?";
			this.updateOrDeleteToHSQL(sql,value);
		}catch(Exception e){
			throw new DataAccessException("effectCarModelPhoto Method Error:",e);
		}
	}

	public List findCarModelMaterialno(String sql, Object[] params) {
		try{
			return this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findCarModelMaterialno Method Error:",e);
		}
	}

	public void updatePhoto(CocPhoto[] photo) {
		try{
			 this.updateBatch(photo);
		}catch(Exception e){
			throw new DataAccessException("updatePhoto Method Error:",e);
		}
	}

}
