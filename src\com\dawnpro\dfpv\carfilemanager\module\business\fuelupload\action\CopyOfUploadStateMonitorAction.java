package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.UploadStateManagerService;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

public class CopyOfUploadStateMonitorAction extends BaseSupportAction{
	private Logger logger = Logger.getLogger(CopyOfUploadStateMonitorAction.class.getName());
	private UploadStateManagerService uploadStateManagerService = null;
	private String currentPage = null;
	private String uploadType;
	private String uploadState;
	private String beginDate;
	private String endDate;
	private String slcx;
	private String pmodel;
	private String vin;
	private String sLoginName=null;
	private String sDescript=null;
	
	
	public String getsLoginName() {
		return sLoginName;
	}
	public void setsLoginName(String sLoginName) {
		this.sLoginName = sLoginName;
	}
	public String getsDescript() {
		return sDescript;
	}
	public void setsDescript(String sDescript) {
		this.sDescript = sDescript;
	}
	public String getVin() {
		return vin;
	}
	public void setVin(String vin) {
		this.vin = vin;
	}
	public String getUploadState() {
		return uploadState;
	}
	public void setUploadState(String uploadState) {
		this.uploadState = uploadState;
	}
	public UploadStateManagerService getUploadStateManagerService() {
		return uploadStateManagerService;
	}
	public void setUploadStateManagerService(
			UploadStateManagerService uploadStateManagerService) {
		this.uploadStateManagerService = uploadStateManagerService;
	}
	public String getUploadType() {
		return uploadType;
	}
	public void setUploadType(String uploadType) {
		this.uploadType = uploadType;
	}
	public String getCurrentPage() {
		return currentPage;
	}
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}
	public String getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getPmodel() {
		return pmodel;
	}
	public void setPmodel(String pmodel) {
		this.pmodel = pmodel;
	}
	public String getSlcx() {
		return slcx;
	}
	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}
	
	public String execute(){
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			
			Page page = new Page();
			List results = null;
			
			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			this.sLoginName = sLoginName==null?"":sLoginName;
			this.sDescript = sDescript==null?"":sDescript;
			this.sLoginName = java.net.URLDecoder.decode(this.sLoginName, "UTF-8");
			this.sDescript = java.net.URLDecoder.decode(this.sDescript, "UTF-8");	
			params.put("beginDate", this.getBeginDate());
			params.put("endDate", this.getEndDate());
			params.put("sLoginName", this.getsLoginName());
			params.put("sDescript", this.getsDescript());

				results = uploadStateManagerService.findSysGasuploadlog(page, params);

			if(results == null)
				results = new ArrayList();
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("uploadStateMonitorPageData", results);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}

	public String findUploadGasLog(){
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			
			Page page = new Page();
			List results = null;
			
			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			
			results = uploadStateManagerService.findInterfaceStateList(page, params);
			if(results == null)
				results = new ArrayList();
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("interfaceStatePageData", results);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
	
	public String gasuploadlog(){
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			
			Page page = new Page();
			List results = null;
			
			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			this.sLoginName = sLoginName==null?"":sLoginName;
			this.sDescript = sDescript==null?"":sDescript;
			this.sLoginName = java.net.URLDecoder.decode(this.sLoginName, "UTF-8");
			this.sDescript = java.net.URLDecoder.decode(this.sDescript, "UTF-8");	
			params.put("beginDate", this.getBeginDate());
			params.put("endDate", this.getEndDate());
			params.put("sLoginName", this.getsLoginName());
			params.put("sDescript", this.getsDescript());

				results = uploadStateManagerService.findSysGasuploadlog(page, params);

			if(results == null)
				results = new ArrayList();
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("uploadStateMonitorPageData", results);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
	
	
}
