<?xml version="1.0" encoding="UTF-8"?>   
<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN" "web-jsptaglibrary_1_2.dtd" >   
<taglib>
	<tlib-version>1.0</tlib-version>
	<jsp-version>1.2</jsp-version>
	<short-name>CustomJSTL</short-name>
	<uri>/jsp-customTags</uri>
	<display-name>CustomJSTL</display-name>
	<description>
		<![CDATA[Display CustomJSTL information.]]>
	</description>
	<tag>
		<name>tree</name>
		<tag-class>com.dawnpro.dfpv.carfilemanager.common.custom.tag.tree.DTreeTag</tag-class>
		<body-content>JSP</body-content>
		<display-name>TreeTag</display-name>
		<description>
			<![CDATA[树型标签类.]]>
		</description>
			<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[控件的标识符]]>
			</description>
		</attribute>
		<attribute>
			<name>value</name>
			<required>tree</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				<![CDATA[bean对象]]>
			</description>
		</attribute>
		<attribute>
			<name>nameAttribute</name>
			<required>tree</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[名称属性]]>
			</description>
		</attribute>
		<attribute>
			<name>childMethodName</name>
			<required>true</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[获得子节点的方法名]]>
			</description>
		</attribute>
		<attribute>
			<name>pathAttribute</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[路径属性]]>
			</description>
		</attribute>
		<attribute>
			<name>targetAttribute</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[目标属性,对应iframe]]>
			</description>
		</attribute>
		<attribute>
			<name>expand</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<description>
				<![CDATA[默认展开,true:全部展开,false:不展开]]>
			</description>
		</attribute>
	</tag>
	
	<tag>
		<name>grid</name>
		<tag-class>com.dawnpro.dfpv.carfilemanager.common.custom.tag.grid.GridTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<name>dataKey</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>
		<attribute>
			<name>columnFields</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
		</attribute>
	</tag>
</taglib> 