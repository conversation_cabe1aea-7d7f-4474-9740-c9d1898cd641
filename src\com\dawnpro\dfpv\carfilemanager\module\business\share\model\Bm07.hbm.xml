<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bm07" table="BM_07" >
       <id name="sccxbm" type="java.lang.String">
            <column name="SCCXBM" length="50" />
            <generator class="assigned" />
        </id>
        <property name="cpx" type="java.lang.String">
            <column name="CPX" length="100" />
        </property>
 		<property name="cpxmc" type="java.lang.String">
            <column name="CPXMC" length="300" />
        </property>
        <property name="ptbm" type="java.lang.String">
            <column name="PTBM" length="20" />
        </property>
        <property name="xlbm" type="java.lang.String">
            <column name="XLBM" length="20" />
        </property>
        <property name="xlmc" type="java.lang.String">
            <column name="XLMC" length="100" />
        </property>
        <property name="cxbm" type="java.lang.String">
            <column name="CXBM" length="20" />
        </property>
        <property name="cxmc" type="java.lang.String">
            <column name="CXMC" length="100" />
        </property>
        <property name="cxdh" type="java.lang.String">
            <column name="CXDH" length="15" />
        </property>
        <property name="ggh" type="java.lang.String">
            <column name="GGH" length="20" />
        </property>
        <property name="cpms" type="java.lang.String">
            <column name="CPMS" length="100" />
        </property>
        <property name="cpbm" type="java.lang.String">
            <column name="CPBM" length="20" />
        </property>
        <property name="cpmc" type="java.lang.String">
            <column name="CPMC" length="50" />
        </property>
        <property name="sccxms" type="java.lang.String">
            <column name="SCCXMS" length="100" />
        </property>
        <property name="psbm" type="java.lang.String">
            <column name="PSBM" length="20" />
        </property>
        <property name="yqbm" type="java.lang.String">
            <column name="YQBM" length="10" />
        </property>
        <property name="fsrq" type="java.lang.String">
            <column name="FSRQ" length="8" />
        </property>
        <property name="fssj" type="java.lang.String">
            <column name="FSSJ" length="6" />
        </property>
    </class>
</hibernate-mapping>
