package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

import java.util.List;

/**
 * Modelver entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class Modelver implements java.io.Serializable {

	// Fields

	private ModelverId id;
	private String gver1;
	private String gver2;
	private String gswtime;
	private String gvalitime;
	private String gvaliuser;
	private String gstate;
	private String cver1;
	private String cver2;
	private String cswtime;
	private String cvalitime;
	private String cvaliuser;
	private String cstate;
	private String zver1;
	private String zver2;
	private String zswtime;
	private String zvalitime;
	private String zvaliuser;
	private String zstate;
	private String pver1;
	private String pver2;
	private String pswtime;
	private String pvalitime;
	private String pvaliuser;
	private String pstate;
	private List<CocPhoto>  photos = null;
	


	private String ever1;
	private String ever2;
	private String eswtime;
	private String evalitime;
	private String evaliuser;
	private String estate;

	/** default constructor */
	public Modelver() {
	}

	/** minimal constructor */
	public Modelver(ModelverId id) {
		this.id = id;
	}

	/** full constructor */
	public Modelver(ModelverId id, String gver1, String gver2,
			String gswtime, String gvalitime, String gvaliuser, String gstate,
			String cver1, String cver2, String cswtime, String cvalitime,
			String cvaliuser, String cstate, String zver1, String zver2,
			String zswtime, String zvalitime, String zvaliuser, String zstate,String pver1,String pver2,
			String pswtime,String pvalitime,String pvaliuser,String pstate) {
		this.id = id;
		this.gver1 = gver1;
		this.gver2 = gver2;
		this.gswtime = gswtime;
		this.gvalitime = gvalitime;
		this.gvaliuser = gvaliuser;
		this.gstate = gstate;
		this.cver1 = cver1;
		this.cver2 = cver2;
		this.cswtime = cswtime;
		this.cvalitime = cvalitime;
		this.cvaliuser = cvaliuser;
		this.cstate = cstate;
		this.zver1 = zver1;
		this.zver2 = zver2;
		this.zswtime = zswtime;
		this.zvalitime = zvalitime;
		this.zvaliuser = zvaliuser;
		this.zstate = zstate;
		this.pver1 = pver1;
		this.pver2 = pver2;
		this.pswtime = pswtime;
		this.pvalitime = pvalitime;
		this.pvaliuser = pvaliuser;
		this.pstate = pstate;
	}

	// Property accessors

	public ModelverId getId() {
		return id;
	}

	public void setId(ModelverId id) {
		this.id = id;
	}

	public String getGver1() {
		return this.gver1;
	}

	public void setGver1(String gver1) {
		this.gver1 = gver1;
	}

	public String getGver2() {
		return this.gver2;
	}

	public void setGver2(String gver2) {
		this.gver2 = gver2;
	}

	public String getGswtime() {
		return this.gswtime;
	}

	public void setGswtime(String gswtime) {
		this.gswtime = gswtime;
	}

	public String getGvalitime() {
		return this.gvalitime;
	}

	public void setGvalitime(String gvalitime) {
		this.gvalitime = gvalitime;
	}

	public String getGvaliuser() {
		return this.gvaliuser;
	}

	public void setGvaliuser(String gvaliuser) {
		this.gvaliuser = gvaliuser;
	}

	public String getGstate() {
		return this.gstate;
	}

	public void setGstate(String gstate) {
		this.gstate = gstate;
	}

	public String getCver1() {
		return this.cver1;
	}

	public void setCver1(String cver1) {
		this.cver1 = cver1;
	}

	public String getCver2() {
		return this.cver2;
	}

	public void setCver2(String cver2) {
		this.cver2 = cver2;
	}

	public String getCswtime() {
		return this.cswtime;
	}

	public void setCswtime(String cswtime) {
		this.cswtime = cswtime;
	}

	public String getCvalitime() {
		return this.cvalitime;
	}

	public void setCvalitime(String cvalitime) {
		this.cvalitime = cvalitime;
	}

	public String getCvaliuser() {
		return this.cvaliuser;
	}

	public void setCvaliuser(String cvaliuser) {
		this.cvaliuser = cvaliuser;
	}

	public String getCstate() {
		return this.cstate;
	}

	public void setCstate(String cstate) {
		this.cstate = cstate;
	}

	public String getZver1() {
		return this.zver1;
	}

	public void setZver1(String zver1) {
		this.zver1 = zver1;
	}

	public String getZver2() {
		return this.zver2;
	}

	public void setZver2(String zver2) {
		this.zver2 = zver2;
	}

	public String getZswtime() {
		return this.zswtime;
	}

	public void setZswtime(String zswtime) {
		this.zswtime = zswtime;
	}

	public String getZvalitime() {
		return this.zvalitime;
	}

	public void setZvalitime(String zvalitime) {
		this.zvalitime = zvalitime;
	}

	public String getZvaliuser() {
		return this.zvaliuser;
	}

	public void setZvaliuser(String zvaliuser) {
		this.zvaliuser = zvaliuser;
	}

	public String getZstate() {
		return this.zstate;
	}

	public void setZstate(String zstate) {
		this.zstate = zstate;
	}
	
	public String getPver1() {
		return pver1;
	}

	public void setPver1(String pver1) {
		this.pver1 = pver1;
	}

	public String getPver2() {
		return pver2;
	}

	public void setPver2(String pver2) {
		this.pver2 = pver2;
	}

	public String getPswtime() {
		return pswtime;
	}

	public void setPswtime(String pswtime) {
		this.pswtime = pswtime;
	}

	public String getPvalitime() {
		return pvalitime;
	}

	public void setPvalitime(String pvalitime) {
		this.pvalitime = pvalitime;
	}

	public String getPvaliuser() {
		return pvaliuser;
	}

	public void setPvaliuser(String pvaliuser) {
		this.pvaliuser = pvaliuser;
	}

	public String getPstate() {
		return pstate;
	}

	public void setPstate(String pstate) {
		this.pstate = pstate;
	}
	
	public List<CocPhoto> getPhotos() {
		return photos;
	}

	public void setPhotos(List<CocPhoto> photos) {
		this.photos = photos;
	}
	public String getEver1() {
		return ever1;
	}

	public void setEver1(String ever1) {
		this.ever1 = ever1;
	}

	public String getEver2() {
		return ever2;
	}

	public void setEver2(String ever2) {
		this.ever2 = ever2;
	}

	public String getEswtime() {
		return eswtime;
	}

	public void setEswtime(String eswtime) {
		this.eswtime = eswtime;
	}

	public String getEvalitime() {
		return evalitime;
	}

	public void setEvalitime(String evalitime) {
		this.evalitime = evalitime;
	}

	public String getEvaliuser() {
		return evaliuser;
	}

	public void setEvaliuser(String evaliuser) {
		this.evaliuser = evaliuser;
	}

	public String getEstate() {
		return estate;
	}

	public void setEstate(String estate) {
		this.estate = estate;
	}
}