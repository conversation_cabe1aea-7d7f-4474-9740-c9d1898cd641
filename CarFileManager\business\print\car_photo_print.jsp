<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
	p {font-family:"宋体";font-size: 10pt;font-weight:bold;}
	input.text { width:12; padding: .4em; }
	input.text1 { width:12; padding: .4em; background:#bcd4ec;}
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align: center; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/jquery-cookie.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	$("#dialog").dialog({
		autoOpen: false,
		bgiframe: false,
		height: 400,
		weight:40,
		modal: false
	});

	$('#vin').bind('keyup',function(event) {  
     	if(event.keyCode==13){
     		if(vin.gblen()==17||vin.gblen()==8){
    			window.document.photo.printCarPhoto(vin); 
    		}else{
    			alert("VIN长度不和要求，必须输入完整的17位或后8位！");
    		}
   		}   
     	//if($.trim($('#vin')[0].value).length==17){
   			//window.document.gas.printGAS($("#vin")[0].value);
   	   	//}
    });   
    
	$("#search").click(function() {
		var vin = $("#vin")[0].value;
		if(vin.gblen()==17||vin.gblen()==8){
			window.document.photo.printCarPhoto(vin); 
		}else{
			alert("VIN长度不和要求，必须输入完整的17位或后8位！");
		}		
	});
				
}
);

function vinFocus(){
	$("#vin")[0].focus(); 
	$("#vin").addClass("text ui-widget-content ui-corner-all");
}

function showInfo(model,color,materialNo){
	$("#model")[0].value = model;
	$("#color")[0].value = color;
	$("#materialNo")[0].value = materialNo;
	
	$("#vin")[0].select();
}

function readPrinterCookie(){
	var cookie = new Array(2);
	cookie[0] = $.cookie("cocprinter");
	cookie[1] = $.cookie("photoprinter");
	
	return cookie;
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<!-- 
<p>VIN:<input type="text" id="vin" class="text ui-widget-content ui-corner-all"/>&nbsp;&nbsp;<button id="create-user" class="ui-button ui-state-default ui-corner-all">查询</button></p>
 -->
<div align="center"> 
	<jsp:plugin name="photo" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet.class" codebase="." archive = "../../applet/com.dawnpro.dfpv.carfilemanager.print.applet.jar,../../applet/jasperreports-3.6.1.jar,../../applet/commons-logging-1.1.1.jar,../../applet/commons-collections-3.2.jar,../../applet/commons-digester-1.7.jar,../../applet/Qrcode_encoder.jar" 
	iepluginurl="http://********/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="120" width="800">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="photo"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
<div align="center">
<table width="820" height="63" border="0">
	<tr>
	<td width="200" height="19" align="left" style="font-family:arial;font-size: 24pt; " >VIN:</td>
	<td colspan="3" align="left">
	<table cellpadding="0" cellspacing="0">
		<tr>
			<td><input id="vin" type="text" width="100" class="text ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; " /></td>
			<td>&nbsp;&nbsp;<button id="search" class="ui-button ui-state-default ui-corner-all">查询</button></td>
		</tr>
	</table>
	</tr>
	<tr>
		<td height="18" align="left" style="font-family:arial;font-size: 24pt; ">车型:</td>
		<td width="122"><input id="model" type="text" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt; width:200px;" readOnly></td>
		<td width="160" align="left" style="font-family:arial;font-size: 24pt; ">颜色:</td>
		<td width="127"> <input id="color" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt; width:200px;" readOnly/></td>
	</tr>
	
	<tr>
		<td height="18" align="left" style="font-family:arial;font-size: 24pt; ">物料号:</td>
		<td colspan="3" align="left"><input id="materialNo" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-size: 24pt;" readOnly/></td>
	</tr>
</table>
</div>
</body>
</html>