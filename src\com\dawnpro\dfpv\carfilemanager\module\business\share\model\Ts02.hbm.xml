<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Ts02" table="TS_02">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Ts02Id">
            <key-property name="matchBillCode" type="java.lang.String">
                <column name="MATCH_BILL_CODE" length="10" />
            </key-property>
            <key-property name="matchBillItem" type="java.lang.String">
                <column name="MATCH_BILL_ITEM" length="4" />
            </key-property>
        </composite-id>
        <property name="vin" type="java.lang.String">
            <column name="VIN" length="18" />
        </property>
        <property name="prepareEndTime" type="java.lang.String">
            <column name="PREPARE_END_TIME" length="14" />
        </property>
    </class>
</hibernate-mapping>
