<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary" table="DATA_DICTIONARY">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryId">
            <key-property name="name" type="java.lang.String">
                <column name="NAME" length="30" />
            </key-property>
            <key-property name="value" type="java.lang.String">
                <column name="VALUE" length="20" />
            </key-property>
            <key-property name="type" type="java.lang.String">
                <column name="TYPE" length="20" />
            </key-property>
        </composite-id>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
    </class>
</hibernate-mapping>
