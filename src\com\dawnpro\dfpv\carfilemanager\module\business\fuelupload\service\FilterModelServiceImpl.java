package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao.FilterModelDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FilterModel;

public class FilterModelServiceImpl implements FilterModelService {
	
	private FilterModelDAO filterModelDAO = null;
	private PaginationService paginationService = null;		

	public FilterModelDAO getFilterModelDAO() {
		return filterModelDAO;
	}

	public void setFilterModelDAO(FilterModelDAO filterModelDAO) {
		this.filterModelDAO = filterModelDAO;
	}

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public void addFilterModel(FilterModel obj) throws DataAccessException {
		try{
			this.filterModelDAO.addFilterModel(obj);
		}catch (DataAccessException e) {
			throw new SystemException("addFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("addFilterModel Method Error:", e);
		}
	}

	public void deleteFilterModel(Serializable id) throws DataAccessException {
		try{
			this.filterModelDAO.deleteFilterModel(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("deleteFilterModel Method Error:", e);
		}

	}

	public void deleteFilterModel(Serializable[] id) throws DataAccessException {
		try{
			this.filterModelDAO.deleteFilterModel(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("deleteFilterModel Method Error:", e);
		}
	}

	public FilterModel loadFilterModel(Serializable id)
			throws DataAccessException {
		FilterModel filterModel = null;
		try{
			filterModel = this.filterModelDAO.loadFilterModel(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("deleteFilterModel Method Error:", e);
		}
		return filterModel;
	}

	public List<?> pagination(Page page, Map<String, String> params)
			throws DataAccessException {
		List results = null;
		try{
			String slcx = params.get("slcx") == null ? "" : params.get("slcx");
			StringBuffer sub = new StringBuffer(30);
			StringBuffer countBuf = new StringBuffer(30);
			StringBuffer sqlBuf = new StringBuffer(30);
			
			countBuf.append("select count(*) from FilterModel g ");
			sqlBuf.append("from FilterModel g ");
				
			sub.append(" where 1 = 1 ");
			if(!slcx.equals("")){
				sub.append(" and g.slcx like '%").append(slcx).append("%' ");
			}

			countBuf.append(sub);
			sqlBuf.append(sub);
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countBuf.toString());
			results = this.paginationService.pagination(sqlBuf.toString());
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}

	public void updateFilterModel(FilterModel obj) throws DataAccessException {
		try{
			this.filterModelDAO.updateFilterModel(obj);
		}catch (DataAccessException e) {
			throw new SystemException("updateFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("updateFilterModel Method Error:", e);
		}

	}

	public List<FilterModel> findFilterModel(String slcx) throws DataAccessException {
		List<FilterModel> list = new ArrayList<FilterModel>();
		try{
			Object[] params = new Object[1];
			params[0] = slcx;
			list = this.filterModelDAO.findFilterModel(" from FilterModel t where t.slcx=?", params);
		}catch (DataAccessException e) {
			throw new SystemException("updateFilterModel Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("updateFilterModel Method Error:", e);
		}
		return list;
	}

}
