package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * DataDictionaryType entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class DataDictionaryType implements java.io.Serializable {

	// Fields

	private String name;
	private String type;
	private String creator;
	private String time;

	// Constructors

	/** default constructor */
	public DataDictionaryType() {
	}

	/** minimal constructor */
	public DataDictionaryType(String name) {
		this.name = name;
	}

	/** full constructor */
	public DataDictionaryType(String name, String type, String creator,
			String time) {
		this.name = name;
		this.type = type;
		this.creator = creator;
		this.time = time;
	}

	// Property accessors

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

}