<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.system.model.SysMenu" table="sys_menu">
        <id name="menuid" type="java.lang.String">
            <column name="menuid" length="36" />
            <generator class="assigned" />
        </id>
        <property name="name" type="java.lang.String">
            <column name="name" length="30" />
        </property>
        <property name="parentid" type="java.lang.String">
            <column name="parentid" length="36" />
        </property>
        <property name="path" type="java.lang.String">
            <column name="path" length="100" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="remark" length="100" />
        </property>
         <property name="orderflag" type="java.lang.Integer">
            <column name="orderflag" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="creator" length="20"  />
        </property>
        <property name="time" type="java.lang.String">
            <column name="time" length="19"  />
        </property>
    </class>
</hibernate-mapping>
