package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.HWCOCYearSeqDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HWCOCYearSeq;



public class HWCOCYearSeqServiceImpl implements HWCOCYearSeqService {
	private HWCOCYearSeqDAO hwCocYearSeqDAO = null;

	private PaginationService paginationService = null;

	
	public HWCOCYearSeqDAO getHwCocYearSeqDAO() {
		return hwCocYearSeqDAO;
	}

	public void setHwCocYearSeqDAO(HWCOCYearSeqDAO hwCOCYearSeqDAO) {
		this.hwCocYearSeqDAO = hwCOCYearSeqDAO;
	}

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	
	
	public void addHWCOCYearSeq(HWCOCYearSeq obj)
			throws DataAccessException {
		try{
			this.hwCocYearSeqDAO.addHWCOCYearSeq(obj);
		}catch (DataAccessException e) {
			throw new SystemException("GASPrinterLog Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("GASPrinterLog Method Error:", e);
		}
	}

}
