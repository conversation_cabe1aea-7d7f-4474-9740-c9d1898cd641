package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

// default package

/**
 * Vfl03 entity. <AUTHOR> Persistence Tools
 */

public class Vfl03 implements java.io.Serializable {

	// Fields

	private String vin;
	private String rh;
	private String et;
	private String ap;
	private String testtype;
	private String testno;
	private String testdate;
	private String epass;
	private Double vrhc;
	private Double vlhc;
	private Double vrco;
	private Double vlco;
	private Double vrnox;
	private Double vlnox;
	private String serialno;
	private String uuid;
	private String cratedate;
	private String analymanuf;
	private String analyname;
	private String analymodel;
	private String analydate;
	private String dynomodel;
	private String dynomanuf;
	private String finalresult;
	private Double secvrhc;
	private Double secvlhc;
	private Double secvrco;
	private Double secvlco;
	private Double secvrnox;
	private Double secvlnox;

	// Constructors

	/** default constructor */
	public Vfl03() {
	}

	/** minimal constructor */
	public Vfl03(String vin) {
		this.vin = vin;
	}

	/** full constructor */
	public Vfl03(String vin, String rh, String et, String ap, String testtype,
			String testno, String testdate, String epass, Double vrhc,
			Double vlhc, Double vrco, Double vlco, Double vrnox, Double vlnox,
			String serialno, String uuid, String cratedate, String analymanuf,
			String analyname, String analymodel, String analydate,
			String dynomodel, String dynomanuf, String finalresult,
			Double secvrhc, Double secvlhc, Double secvrco, Double secvlco,
			Double secvrnox, Double secvlnox) {
		this.vin = vin;
		this.rh = rh;
		this.et = et;
		this.ap = ap;
		this.testtype = testtype;
		this.testno = testno;
		this.testdate = testdate;
		this.epass = epass;
		this.vrhc = vrhc;
		this.vlhc = vlhc;
		this.vrco = vrco;
		this.vlco = vlco;
		this.vrnox = vrnox;
		this.vlnox = vlnox;
		this.serialno = serialno;
		this.uuid = uuid;
		this.cratedate = cratedate;
		this.analymanuf = analymanuf;
		this.analyname = analyname;
		this.analymodel = analymodel;
		this.analydate = analydate;
		this.dynomodel = dynomodel;
		this.dynomanuf = dynomanuf;
		this.finalresult = finalresult;
		this.secvrhc = secvrhc;
		this.secvlhc = secvlhc;
		this.secvrco = secvrco;
		this.secvlco = secvlco;
		this.secvrnox = secvrnox;
		this.secvlnox = secvlnox;
	}

	// Property accessors

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getRh() {
		return this.rh;
	}

	public void setRh(String rh) {
		this.rh = rh;
	}

	public String getEt() {
		return this.et;
	}

	public void setEt(String et) {
		this.et = et;
	}

	public String getAp() {
		return this.ap;
	}

	public void setAp(String ap) {
		this.ap = ap;
	}

	public String getTesttype() {
		return this.testtype;
	}

	public void setTesttype(String testtype) {
		this.testtype = testtype;
	}

	public String getTestno() {
		return this.testno;
	}

	public void setTestno(String testno) {
		this.testno = testno;
	}

	public String getTestdate() {
		return this.testdate;
	}

	public void setTestdate(String testdate) {
		this.testdate = testdate;
	}

	public String getEpass() {
		return this.epass;
	}

	public void setEpass(String epass) {
		this.epass = epass;
	}

	public Double getVrhc() {
		return this.vrhc;
	}

	public void setVrhc(Double vrhc) {
		this.vrhc = vrhc;
	}

	public Double getVlhc() {
		return this.vlhc;
	}

	public void setVlhc(Double vlhc) {
		this.vlhc = vlhc;
	}

	public Double getVrco() {
		return this.vrco;
	}

	public void setVrco(Double vrco) {
		this.vrco = vrco;
	}

	public Double getVlco() {
		return this.vlco;
	}

	public void setVlco(Double vlco) {
		this.vlco = vlco;
	}

	public Double getVrnox() {
		return this.vrnox;
	}

	public void setVrnox(Double vrnox) {
		this.vrnox = vrnox;
	}

	public Double getVlnox() {
		return this.vlnox;
	}

	public void setVlnox(Double vlnox) {
		this.vlnox = vlnox;
	}

	public String getSerialno() {
		return this.serialno;
	}

	public void setSerialno(String serialno) {
		this.serialno = serialno;
	}

	public String getUuid() {
		return this.uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getCratedate() {
		return this.cratedate;
	}

	public void setCratedate(String cratedate) {
		this.cratedate = cratedate;
	}

	public String getAnalymanuf() {
		return this.analymanuf;
	}

	public void setAnalymanuf(String analymanuf) {
		this.analymanuf = analymanuf;
	}

	public String getAnalyname() {
		return this.analyname;
	}

	public void setAnalyname(String analyname) {
		this.analyname = analyname;
	}

	public String getAnalymodel() {
		return this.analymodel;
	}

	public void setAnalymodel(String analymodel) {
		this.analymodel = analymodel;
	}

	public String getAnalydate() {
		return this.analydate;
	}

	public void setAnalydate(String analydate) {
		this.analydate = analydate;
	}

	public String getDynomodel() {
		return this.dynomodel;
	}

	public void setDynomodel(String dynomodel) {
		this.dynomodel = dynomodel;
	}

	public String getDynomanuf() {
		return this.dynomanuf;
	}

	public void setDynomanuf(String dynomanuf) {
		this.dynomanuf = dynomanuf;
	}

	public String getFinalresult() {
		return this.finalresult;
	}

	public void setFinalresult(String finalresult) {
		this.finalresult = finalresult;
	}

	public Double getSecvrhc() {
		return this.secvrhc;
	}

	public void setSecvrhc(Double secvrhc) {
		this.secvrhc = secvrhc;
	}

	public Double getSecvlhc() {
		return this.secvlhc;
	}

	public void setSecvlhc(Double secvlhc) {
		this.secvlhc = secvlhc;
	}

	public Double getSecvrco() {
		return this.secvrco;
	}

	public void setSecvrco(Double secvrco) {
		this.secvrco = secvrco;
	}

	public Double getSecvlco() {
		return this.secvlco;
	}

	public void setSecvlco(Double secvlco) {
		this.secvlco = secvlco;
	}

	public Double getSecvrnox() {
		return this.secvrnox;
	}

	public void setSecvrnox(Double secvrnox) {
		this.secvrnox = secvrnox;
	}

	public Double getSecvlnox() {
		return this.secvlnox;
	}

	public void setSecvlnox(Double secvlnox) {
		this.secvlnox = secvlnox;
	}

}