package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysDataDictionaryDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysDataDictionaryTypeDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysRoleDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;

public class SysDataDictionaryTypeServiceImpl implements SysDataDictionaryTypeService{
	private Logger logger = Logger.getLogger(SysDataDictionaryTypeServiceImpl.class.getName());
	private SysDataDictionaryTypeDAO dao;
	private SysDataDictionaryDAO dataDictionaryDao;
	public void setDao(SysDataDictionaryTypeDAO dao) {
		this.dao = dao;
	}

	public void setDataDictionaryDao(SysDataDictionaryDAO dataDictionaryDao) {
		this.dataDictionaryDao = dataDictionaryDao;
	}

	public void addDataDictionaryType(DataDictionaryType data)
			throws DataAccessException {
		try{
			this.dao.addDataDictionaryType(data);
		}catch(DataAccessException e){
			logger.error("addDataDictionaryType Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("addDataDictionaryType Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public void deleteDataDictionaryType(DataDictionaryType[] data)
			throws DataAccessException {
		try{
			this.dao.deleteDataDictionaryType(data);
			for(DataDictionaryType typeObjectTemp:data){
				DataDictionaryType typeObject=this.dao.loadDataDictionaryTypeObj(typeObjectTemp.getName());
				String hql="from DataDictionary g where g.id.type='"+typeObject.getType()+"'";
				List<DataDictionary> dataDictionary=this.dataDictionaryDao.findDataDictionary(hql);
				if(dataDictionary!=null && dataDictionary.size()>0){
					for(DataDictionary element:dataDictionary){
						this.dataDictionaryDao.deleteDataDictionary(element);
					}
				}
			}
		}catch(DataAccessException e){
			logger.error("deleteDataDictionaryType Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("deleteDataDictionaryType Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public List<DataDictionaryType> findDataDictionaryType(String sql)
			throws DataAccessException {
		try{
			return this.dao.findDataDictionaryType(sql);
		}catch(DataAccessException e){
			logger.error("findDataDictionaryType Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("findDataDictionaryType Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public boolean isDataDictionaryTypeExist(String name)
			throws DataAccessException {
		DataDictionaryType temp=loadDataDictionaryType(name);
		if(temp!=null)
			return true;
		return false;
	}

	public DataDictionaryType loadDataDictionaryType(String id)
			throws DataAccessException {
		try{
			return this.dao.loadDataDictionaryTypeObj(id);
		}catch(DataAccessException e){
			logger.error("loadDataDictionaryType Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("loadDataDictionaryType Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public void updateDataDictionaryType(DataDictionaryType data)
			throws DataAccessException {
		try{
			this.dao.updateDataDictionaryType(data);
		}catch(DataAccessException e){
			logger.error("updateDataDictionaryType Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("updateDataDictionaryType Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}
}
