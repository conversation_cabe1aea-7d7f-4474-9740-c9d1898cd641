<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Vfl04" table="VFL_04" schema="VFILE">
        <id name="vin" type="java.lang.String">
            <column name="VIN" length="20" />
            <generator class="assigned" />
        </id>
        <property name="vehiclemodel" type="java.lang.String">
            <column name="VEHICLEMODEL" length="20">
                <comment>车辆的公告车型</comment>
            </column>
        </property>
        <property name="obd" type="java.lang.String">
            <column name="OBD" length="2">
                <comment>型式检验时的OBD要求,</comment>
            </column>
        </property>
        <property name="odo" type="java.lang.Double">
            <column name="ODO" precision="8" scale="1">
                <comment>车辆行驶里程</comment>
            </column>
        </property>
        <property name="checker" type="java.lang.String">
            <column name="CHECKER" length="50">
                <comment>检验员</comment>
            </column>
        </property>
        <property name="testno" type="java.lang.String">
            <column name="TESTNO" length="18">
                <comment>检测报告编号</comment>
            </column>
        </property>
        <property name="ecmmodulename" type="java.lang.String">
            <column name="ECMMODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="ecmmoduleid" type="java.lang.String">
            <column name="ECMMODULEID" length="11">
                <comment>控制单元模块ID</comment>
            </column>
        </property>
        <property name="ecmcalid" type="java.lang.String">
            <column name="ECMCALID" length="20">
                <comment>CALID</comment>
            </column>
        </property>
        <property name="ecmcvn" type="java.lang.String">
            <column name="ECMCVN" length="20">
                <comment>CVN</comment>
            </column>
        </property>
        <property name="tcmmodulename" type="java.lang.String">
            <column name="TCMMODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="tcmmoduleid" type="java.lang.String">
            <column name="TCMMODULEID" length="11">
                <comment>控制单元模块ID</comment>
            </column>
        </property>
        <property name="tcmcalid" type="java.lang.String">
            <column name="TCMCALID" length="20">
                <comment>CALID</comment>
            </column>
        </property>
        <property name="tcmcvn" type="java.lang.String">
            <column name="TCMCVN" length="20">
                <comment>CVN</comment>
            </column>
        </property>
        <property name="ecm2modulename" type="java.lang.String">
            <column name="ECM2MODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="ecm2moduleid" type="java.lang.String">
            <column name="ECM2MODULEID" length="11">
                <comment>控制单元模块ID</comment>
            </column>
        </property>
        <property name="ecm2calid" type="java.lang.String">
            <column name="ECM2CALID" length="20">
                <comment>CALID</comment>
            </column>
        </property>
        <property name="ecm2cvn" type="java.lang.String">
            <column name="ECM2CVN" length="20">
                <comment>CVN</comment>
            </column>
        </property>
        <property name="dmcmmodulename" type="java.lang.String">
            <column name="DMCMMODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="dmcmmoduleid" type="java.lang.String">
            <column name="DMCMMODULEID" length="11">
                <comment>控制单元模块ID</comment>
            </column>
        </property>
        <property name="dmcmcalid" type="java.lang.String">
            <column name="DMCMCALID" length="20">
                <comment>CALID</comment>
            </column>
        </property>
        <property name="dmcmcvn" type="java.lang.String">
            <column name="DMCMCVN" length="20">
                <comment>CVN</comment>
            </column>
        </property>
        <property name="scrmodulename" type="java.lang.String">
            <column name="SCRMODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="scrmoduleid" type="java.lang.String">
            <column name="SCRMODULEID" length="11">
                <comment>控制单元模块ID</comment>
            </column>
        </property>
        <property name="scrcalid" type="java.lang.String">
            <column name="SCRCALID" length="20">
                <comment>CALID</comment>
            </column>
        </property>
        <property name="scrcvn" type="java.lang.String">
            <column name="SCRCVN" length="20">
                <comment>CVN</comment>
            </column>
        </property>
        <property name="hvbecmmodulename" type="java.lang.String">
            <column name="HVBECMMODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="hvbecmmoduleid" type="java.lang.String">
            <column name="HVBECMMODULEID" length="11">
                <comment>控制单元模块ID</comment>
            </column>
        </property>
        <property name="hvbecmcalid" type="java.lang.String">
            <column name="HVBECMCALID" length="20">
                <comment>CALID</comment>
            </column>
        </property>
        <property name="hvbecmcvn" type="java.lang.String">
            <column name="HVBECMCVN" length="20">
                <comment>CVN</comment>
            </column>
        </property>
        <property name="bcmmodulename" type="java.lang.String">
            <column name="BCMMODULENAME" length="100">
                <comment>控制单元模块名称</comment>
            </column>
        </property>
        <property name="serialno" type="java.lang.String">
            <column name="SERIALNO" length="50">
                <comment>唯一流水号</comment>
            </column>
        </property>
        <property name="uuid" type="java.lang.String">
            <column name="UUID" length="50" />
        </property>
        <property name="cratedate" type="java.lang.String">
            <column name="CRATEDATE" length="50">
                <comment>创建时间</comment>
            </column>
        </property>
        <property name="bcmmoduleid" type="java.lang.String">
            <column name="BCMMODULEID" length="11" />
        </property>
        <property name="bcmcalid" type="java.lang.String">
            <column name="BCMCALID" length="20" />
        </property>
        <property name="bcmcvn" type="java.lang.String">
            <column name="BCMCVN" length="20" />
        </property>
        <property name="othmodulename" type="java.lang.String">
            <column name="OTHMODULENAME" length="100" />
        </property>
        <property name="othmoduleid" type="java.lang.String">
            <column name="OTHMODULEID" length="11" />
        </property>
        <property name="othcalid" type="java.lang.String">
            <column name="OTHCALID" length="20" />
        </property>
        <property name="othcvn" type="java.lang.String">
            <column name="OTHCVN" length="20" />
        </property>
        <property name="apass" type="java.lang.String">
            <column name="APASS" length="1" />
        </property>
        <property name="ocommunchk" type="java.lang.String">
            <column name="OCOMMUNCHK" length="1" />
        </property>
        <property name="opass" type="java.lang.String">
            <column name="OPASS" length="1" />
        </property>
        <property name="testdate" type="java.lang.String">
            <column name="TESTDATE" length="50" />
        </property>
        <property name="ctest" type="java.lang.String">
            <column name="CTEST" length="200">
                <comment>委托的检测机构</comment>
            </column>
        </property>
        <property name="ctestlocation" type="java.lang.String">
            <column name="CTESTLOCATION" length="200">
                <comment>检测地点</comment>
            </column>
        </property>
    </class>
</hibernate-mapping>
