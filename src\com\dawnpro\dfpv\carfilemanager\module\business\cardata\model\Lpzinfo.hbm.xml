<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Lpzinfo" table="LPZINFO" lazy="false">
       
       <id name="id" type="java.lang.String">
            <column name="ID" length="50" />
            <generator class="assigned" />
        </id>
        
        <property name="minzl" type="java.lang.String">
            <column name="MINZL" length="10">
            </column>
        </property>
        
        <property name="maxzl" type="java.lang.String">
            <column name="MAXZL" length="10">
            </column>
        </property>
        
        <property name="lpz1" type="java.lang.String">
            <column name="LPZ1" length="10">
                <comment>领跑值1</comment>
            </column>
        </property>
        
        <property name="lpz2" type="java.lang.String">
            <column name="LPZ2" length="10">
                <comment>领跑值2</comment>
            </column>
        </property>
        
        <property name="xs" type="java.lang.String">
            <column name="XS" length="10" />
        </property>
        <property name="engineType" type="java.lang.String">
            <column name="ENGINE_TYPE" length="20" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20">
                <comment>创建人</comment>
            </column>
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7">
                <comment>时间</comment>
            </column>
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="cyear" type="java.lang.String">
            <column name="CYEAR" length="10" />
        </property>
    </class>
</hibernate-mapping>
