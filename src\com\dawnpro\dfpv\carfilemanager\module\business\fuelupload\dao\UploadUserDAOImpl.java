package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Uploaduser;

public class UploadUserDAOImpl extends GenericHibernateDAOImpl<Uploaduser> implements UploadUserDAO {

	public void addUploadUser(Uploaduser obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addUploadUser Method Error:",e);
		}
	}

	public List<Uploaduser> findUploadUser(String sql) {
		List<Uploaduser> list = null;
		try{
			list = this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findFilterModel Method Error:",e);
		}
		return list;
	}

	public void updateUploadUser(Uploaduser obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateUploadUser Method Error:",e);
		}
	}

	public void deleteUploadUser(Uploaduser obj) {
		try{
			this.delete(obj);
		}catch(Exception e){
			throw new DataAccessException("deleteUploadUser Method Error:",e);
		}
	}

}
