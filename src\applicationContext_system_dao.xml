<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="autodetect" xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
				
	<bean id="paginationDAO" class="com.dawnpro.dfpv.carfilemanager.common.pagination.dao.PaginationDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
			
	<bean id="sPaginationDAO" class="com.dawnpro.dfpv.carfilemanager.common.pagination.dao.PaginationDAOImpl" lazy-init="true" scope="singleton">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysUserDao" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysUserDAOImpl" lazy-init="true" scope="request" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysMenuDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysMenuDAOImpl" lazy-init="true" scope="request" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sSysMenuDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysMenuDAOImpl" lazy-init="true" scope="singleton" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="systemConfigParamsDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SystemConfigParamsDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysRoleDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysRoleDAOImpl" lazy-init="true" scope="request" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysLogDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysLogDAOImpl" lazy-init="true" scope="singleton" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sSysOperateDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysOperateDAOImpl" lazy-init="true" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysOperateDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysOperateDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysPermissionDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysPermissionDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysDataDictionaryDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysDataDictionaryDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="sysDataDictionaryTypeDAO" class="com.dawnpro.dfpv.carfilemanager.module.system.dao.SysDataDictionaryTypeDAOImpl" lazy-init="true" scope="request">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
</beans>
