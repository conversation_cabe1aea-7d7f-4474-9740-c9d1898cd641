<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FilterModel" table="FILTER_MODEL" lazy="false">
        <id name="id" type="java.lang.String">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="slcx" type="java.lang.String">
            <column name="SLCX" length="20" unique="true" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="10" />
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7" />
        </property>
        <property name="mark" type="java.lang.String">
            <column name="MARK" length="100" />
        </property>
    </class>
</hibernate-mapping>
