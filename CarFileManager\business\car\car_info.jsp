<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var tgvercode = null;
	var tmodel = null;
	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	//$("#export1").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				//if(operatePerrmission[i].flag.indexOf("export1")!=-1){
					//$("#export1").attr("disabled", false);
				//}
            }
        }
    });
	
	$("#public_notice_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 270,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#public_notice_dialog');
					allFields = $([]).add(parent.find('#vin')).add(parent.find('#engineNo')).add(parent.find('#model'))
					.add(parent.find('#engineType')).add(parent.find('#color')).add(parent.find('#prodDate'))
					.add(parent.find('#cocNum')).add(parent.find('#cocColor')).add(parent.find('#materialNo'))
					.add(parent.find('#gvercode')).add(parent.find('#cvercode')).add(parent.find('#zvercode'))
					.add(parent.find('#evercode')).add(parent.find('#pvercode'))
					.add(parent.find('#motorNo')).add(parent.find('#motorType'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#public_notice_dialog')==true){
					
					//var dlgButton = parent.find('.ui-dialog-buttonpane button');//
					var dlgButton = $('#public_notice_dialog').find('.ui-dialog-buttonpane button');//
					dlgButton.attr('disabled', 'disabled');
			    	dlgButton.addClass('ui-state-disabled');
					if(type=="add"){
						jQuery.ajax({
				            url: 'business/carInfo!islegalCarModel.action',
					        data: 'vin='+$(this).find('#vin').val()+'&model='+$(this).find('#model').val()+'&gvercode='+$(this).find('#gvercode').val()+'&cvercode='+$(this).find('#cvercode').val()+'&zvercode='+$(this).find('#zvercode').val()+'&evercode='+$(this).find('#evercode').val()+'&pvercode='+$(this).find('#pvercode').val()+'&factory='+$(this).find('#factory').val()+'&motorNo='+$(this).find('#motorNo').val()+'&motorType='+$(this).find('#motorType').val(),
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#public_notice_dialog');
					            if(json2Bean(data).json=="true"){
					            	var vin= $('#qvin').val();
									var startDate= $('#startDate').val();
									var endDate= $('#endDate').val();
									var cocStartDate= $('#cocStartDate').val();
									var cocEndDate= $('#cocEndDate').val();
									var currentPage=$('#currentPage_temp').val();
					            	dialog.find('#createForm')[0].action="business/carInfo!addCarModel.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}else{
									var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),json2Bean(data).json);	
								}
				            }
				        });
					}else if(type=="update"){
						//alert("uupdate");
						var isChange = "0"
					if(tgvercode != $(this).find('#gvercode').val()||tmodel !=$(this).find('#model').val()){
						isChange = "1";
					}
						jQuery.ajax({
				            url: 'business/carInfo!islegalVersion.action',
					        data: 'model='+$(this).find('#model').val()+'&gvercode='+$(this).find('#gvercode').val()+'&cvercode='+$(this).find('#cvercode').val()+'&zvercode='+$(this).find('#zvercode').val()+'&evercode='+$(this).find('#evercode').val()+'&pvercode='+$(this).find('#pvercode').val()+'&isChange='+isChange+'&vin='+$(this).find('#vin').val()+'&factory='+$(this).find('#factory').val(),
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#public_notice_dialog');
					            var str = json2Bean(data).json;
					            var tstate ="F";
					            if(str=="true"||str=="isExist"){
					            	var vin= $('#qvin').val();
									var startDate= $('#startDate').val();
									var endDate= $('#endDate').val();
									var cocStartDate= $('#cocStartDate').val();
									var cocEndDate= $('#cocEndDate').val();
									var currentPage=$('#currentPage_temp').val();
									if(str =="isExist"){
									if(confirm("是否要对该上传的数据进行修改上传")){
										tstate = "T";
										var dialogObjx = $('#test_dialog');
										dialogObjx.data('title.dialog', '修改').dialog('open');
									}
										else{
											tstate = "F";
						            dialog.find('#createForm')[0].action="business/carInfo!updateCarModel.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage+"&tstate="+tstate;
						            dialog.find('#createForm')[0].submit();	
										}
										}
						            if(str =="true"){
						            	tstate = "F";
						            dialog.find('#createForm')[0].action="business/carInfo!updateCarModel.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage+"&tstate="+tstate;
						            dialog.find('#createForm')[0].submit();	
						            }

								}else{
									var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),json2Bean(data).json);	
								}
				            }
				        });
					}
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			$('#public_notice_dialog').find('#vin').attr('readonly',false);
		}
		});

	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 250,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
			$('#public_notice_dialog').find('#vin').attr('readonly',false);
		}
		});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var vin= $('#qvin').val();
				var startDate= $('#startDate').val();
				var endDate= $('#endDate').val();
				var cocStartDate= $('#cocStartDate').val();
				var cocEndDate= $('#cocEndDate').val();
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/carInfo!deleteCarModels.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/carInfo!updateCarModelEffect.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="cancel"){
					formObj[0].action = "business/carInfo!cancelCar.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
					formObj[0].submit();
				}
			}
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
    
	$("#test_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 300,
		height: 200,
		modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				clear($('#public_notice_dialog'));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
				$('#public_notice_dialog').dialog('close');
			},
			'保存': function() {
			var modreason = $('#reason').val();
							$('#modreason').val(modreason);
			            	var vin= $('#qvin').val();
							var startDate= $('#startDate').val();
							var endDate= $('#endDate').val();
							var cocStartDate= $('#cocStartDate').val();
							var cocEndDate= $('#cocEndDate').val();
							var currentPage=$('#currentPage_temp').val();
							var dialog = $('#public_notice_dialog');
				            dialog.find('#createForm')[0].action="business/carInfo!updateCarModel.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage+"&tstate=T";
				            dialog.find('#createForm')[0].submit();	

			}
			
		}
	});
    	
	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
		if(index==1){
	       	var params = "vin="+id;
	       	jQuery.ajax({
	            url: 'business/carInfo!carModelInfo.action',		           
	            data: params,
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	  				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#public_notice_dialog');
	            	type = "add";
					setDialogValue(dialogObj,model);
					dialogObj.find('#vin').val('');
					dialogObj.find('#cocNum').val('');
					dialogObj.find('#gvercode').val('');
					dialogObj.find('#cvercode').val('');
					dialogObj.find('#zvercode').val('');										
					dialogObj.find('#pvercode').val('');
					document.getElementById("factory").options[0].selected=true
					dialogObj.data('title.dialog', '新增车辆证书').dialog('open');
	            }
	        });  			 	    	
  	   	}else if(index>1){
			messageObj = $('#message_dialog');
  	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
   			messageObj.dialog('open');
  	   	 }else{
			type = "add";
			document.getElementById("factory").options[0].selected=true
			$('#public_notice_dialog').data('title.dialog', '新增车辆证书').dialog('open');
  	   	 }      				
	});

    $("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			jQuery.ajax({
	            url: 'business/carInfo!carModelInfo.action',		           
	            data: 'vin='+id, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_notice_dialog');
					if(carObj.state==null || carObj.state=="" || carObj.state=="0" || carObj.state=="1"){
						setDialogValue(dialogObj,carObj);
						tgvercode  = carObj.gvercode;
						tmodel = carObj.model;
		       	    	dialogObj.find('#vin').attr('readonly',true);
		       	    	dialogObj.data('title.dialog', '修改车辆证书').dialog('open');
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[维护]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				if(id==""){
					id = this.value;
					info = "vin:"+this.value;
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"vin:"+this.value;
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#vin').val(id);
   	   	}
	
	});


	$("#cancel").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				if(id==""){
					id = this.value;
					info = "vin:"+this.value;
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"vin:"+this.value;
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要作废的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能作废一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		type = "cancel";
   	   		jQuery.ajax({
            	url: 'business/carInfo!carModelInfo.action',		           
            	data: 'vin='+id, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
            		var dialogObj = $('#public_notice_dialog');
					if(carObj.state==null || carObj.state=="" || carObj.state=="0" || carObj.state=="1"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为作废 状态！ 共'+index+'条数据');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#vin').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[维护]的数据！');
			   	   		messageObj.dialog('open');
					}
            }
        });

   	   		
   	   	}
	});
	
	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				if(id==""){
					id = this.value;
					info = "vin:"+this.value;
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"vin:"+this.value;
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		type = "effect";
   	   		jQuery.ajax({
            	url: 'business/carInfo!carModelInfo.action',		           
            	data: 'vin='+id, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
            		var dialogObj = $('#public_notice_dialog');
					if(carObj.state==null || carObj.state=="" || carObj.state=="0" || carObj.state=="1"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共'+index+'条数据');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#vin').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[维护]的数据！');
			   	   		messageObj.dialog('open');
					}
            }
        });

   	   		
   	   	}
	});

	$("#query").click(function(){
		var vin = $('#qvin').val();
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		var cocStartDate = $('#cocStartDate').val();
		var cocEndDate = $('#cocEndDate').val();
		var model = $('#model').val();
		
		if(vin==""&&startDate==""&&endDate==""&&cocStartDate==""&&cocEndDate==""&&model==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入查询条件！');
			messageObj.dialog("open");
		}else{
			var obj = $('#startDate');
			if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#endDate');
			if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#cocStartDate');
			if(cocStartDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			obj = $('#cocEndDate');
			if(cocEndDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			location.href="carInfo.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&model="+encodeURI(encodeURI(model))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&menuid="+menuid;
		}
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
	        url: 'business/carInfo!carModelInfo.action',		           
	        data: 'vin='+id, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#public_notice_display_dialog');
	        	
				setDialogValue(dialogObj,carObj);
								
	       	   	dialogObj.dialog('open');
	        }
	    });

		return false;
	}

	$('#template').click(function() {
		location.href="business/carInfo!download.action";  
		
	});

	$('#import').click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>business/car/car_info_upload.jsp',winName,params); 
	});

	$("#export_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'导出': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var vin= $('#qvin').val();
				var startDate= $('#startDate').val();
				var endDate= $('#endDate').val();
				var cocStartDate= $('#cocStartDate').val();
				var cocEndDate= $('#cocEndDate').val();
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/carInfo!deleteCarModels.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/carInfo!updateCarModelEffect.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="cancel"){
					formObj[0].action = "business/carInfo!cancelCar.action?startDate="+startDate+"&endDate="+endDate+"&qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&currentPage="+currentPage;
					formObj[0].submit();
				}
			}
		}
	});
	
	$('#export1').click(function() {
		var cocStartDate = $('#cocStartDate').val();
		var cocEndDate = $('#cocEndDate').val();

		if(cocStartDate=="" && cocEndDate==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入导出条件--COC证书打印日期！');
			messageObj.dialog("open");
		}else{
			location.href="business/carInfo!exportCocBdData.action?cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate;
		}
	});
	
	$('#export').click(function() {
		var vin = $('#qvin').val();
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		var cocStartDate = $('#cocStartDate').val();
		var cocEndDate = $('#cocEndDate').val();
		var model = $('#model').val();
		if(model==""&&vin==""&&startDate==""&&endDate==""&&cocStartDate==""&&cocEndDate==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入导出条件！');
			messageObj.dialog("open");
		}else{
			location.href="business/carInfo!exportData.action?qvin="+encodeURI(encodeURI(vin))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&startDate="+startDate+"&endDate="+endDate+"&model="+model;
		}
	});
	
	function validate(parent){
		var obj = $(parent).find('#vin');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'VIN编码字段不能为空,最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#engineNo');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机号字段最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#model');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产车型最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#engineType');
		if(!checkLength(obj,0,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机型号最大长度为30！');			
			return false;
		}
		obj = $(parent).find('#color');
		if(!checkLength(obj,0,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'颜色最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#prodDate');
		if(!checkLength(obj,0,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产日期最大长度为10！');			
			return false;
		}
		obj = $(parent).find('#cocNum');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'COC编码最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#cocColor');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'COC颜色最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#materialNo');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型物料号最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#motorNo');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'驱动电机号最大长度为20！');			
			return false;
		}
		obj = $(parent).find('#motorType');
		if(!checkLength(obj,0,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'驱动电机类型最大长度为20！');			
			return false;
		}
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#vin').val(jsonObj.vin);
		dialogObj.find('#engineNo').val(jsonObj.engineNo);
		dialogObj.find('#model').val(jsonObj.model);
		dialogObj.find('#engineType').val(jsonObj.engineType);
		dialogObj.find('#color').val(jsonObj.color);
		dialogObj.find('#prodDate').val(jsonObj.prodDate);
		dialogObj.find('#cocNum').val(jsonObj.cocNum);
		dialogObj.find('#cocColor').val(jsonObj.cocColor);
		dialogObj.find('#remark').val(jsonObj.remark);
		dialogObj.find('#state').val(jsonObj.state);
		dialogObj.find('#materialNo').val(jsonObj.materialNo);
		dialogObj.find('#gvercode').val(jsonObj.gvercode);
		dialogObj.find('#cvercode').val(jsonObj.cvercode);
		dialogObj.find('#zvercode').val(jsonObj.zvercode);
		dialogObj.find('#pvercode').val(jsonObj.pvercode);
		dialogObj.find('#evercode').val(jsonObj.evercode);
		//增加驱动电机号和类型 20181219
		dialogObj.find('#motorNo').val(jsonObj.motorNo);
		dialogObj.find('#motorType').val(jsonObj.motorType);
		
		var selectObj = dialogObj.find("#factory"); 
		$(selectObj).children().each(function(){ 
			if ($(this).val()==jsonObj.factory){
				$(this).attr("selected","true"); 
			} 
		});
	}
	
	function clear(dialogObj){
		dialogObj.find('#vin').val("");
		dialogObj.find('#engineNo').val("");
		dialogObj.find('#model').val("");
		dialogObj.find('#engineType').val("");
		dialogObj.find('#color').val("");
		dialogObj.find('#prodDate').val("");
		dialogObj.find('#cocNum').val("");
		dialogObj.find('#cocColor').val("");
		dialogObj.find('#remark').val("");
		dialogObj.find('#state').val("");
		dialogObj.find('#materialNo').val("");
		dialogObj.find('#gvercode').val("");
		dialogObj.find('#cvercode').val("");
		dialogObj.find('#zvercode').val("");
		dialogObj.find('#pvercode').val("");
		dialogObj.find('#evercode').val("");
		//增加驱动电机号和类型 20181219
		dialogObj.find('#motorNo').val("");
		dialogObj.find('#motorType').val("");
		
		dialogObj.find('#vin').attr('readonly',false);
		dialogObj.find('#engineNo').attr('readonly',false);
		dialogObj.find('#model').attr('readonly',false);
		dialogObj.find('#engineType').attr('readonly',false);
		dialogObj.find('#color').attr('readonly',false);
		dialogObj.find('#prodDate').attr('readonly',false);
		dialogObj.find('#cocNum').attr('readonly',false);
		dialogObj.find('#materialNo').attr('readonly',false);
		dialogObj.find('#cocColor').attr('readonly',false);
		dialogObj.find('#gvercode').attr('readonly',false);
		dialogObj.find('#cvercode').attr('readonly',false);
		dialogObj.find('#zvercode').attr('readonly',false);
		dialogObj.find('#pvercode').attr('readonly',false);
		dialogObj.find('#evercode').attr('readonly',false);
		//增加驱动电机号和类型 20181219
		dialogObj.find('#motorNo').attr('readonly',false);
		dialogObj.find('#motorType').attr('readonly',false);
		type = null;
	}

	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var vin = $('#qvin').val();
			var startDate = $('#startDate').val();
			var endDate = $('#endDate').val();
			var cocStartDate = $('#cocStartDate').val();
			var cocEndDate = $('#cocEndDate').val();
			var model = $('#model').val();
			location.href="carInfo.action?currentPage="+$('#jump').val()+"&qvin="+encodeURI(encodeURI(vin))+"&model="+encodeURI(encodeURI(model))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&startDate="+startDate+"&endDate="+endDate+"&menuid="+menuid;   
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var vin = $('#qvin').val();
				var startDate = $('#startDate').val();
				var endDate = $('#endDate').val();
				var cocStartDate = $('#cocStartDate').val();
				var cocEndDate = $('#cocEndDate').val();
				var model = $('#model').val();
				location.href=$(this).attr('value')+"&qvin="+encodeURI(encodeURI(vin))+"&model="+encodeURI(encodeURI(model))+"&cocStartDate="+cocStartDate+"&cocEndDate="+cocEndDate+"&startDate="+startDate+"&endDate="+endDate+"&menuid="+menuid;
		 });
	  });
    
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#startDate").datepicker($.datepicker.regional['zh']);
	$("#endDate").datepicker($.datepicker.regional['zh']);

	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#cocStartDate").datepicker($.datepicker.regional['zh']);
	$("#cocEndDate").datepicker($.datepicker.regional['zh']);
    
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

           //给这行添加class值为over，并且当鼠标一出该行时执行函数

           $(this).removeClass("over");})    //移除该行的class


});

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="6"><p>VIN:<input type="text" id="qvin" name="qvin" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.qvin!=null"> value="<s:property value="#request.qvin" />"</s:if>/>  		
				生产车型:<input type="text" id=model name="model" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.model!=null"> value="<s:property value="#request.model" />"</s:if>/>  	    
				过点点位：<s:select name="uploadType" list="#{'1':'Z02','2':'Z12'}"  listKey="key" listValue="value" headerKey="" headerValue="全部" theme="simple"></s:select>				
				版本绑定：<s:select name="uploadState" list="#{'T':'已挂','F':'未挂'}"  listKey="key" listValue="value" headerKey="" headerValue="全部" theme="simple" ></s:select>
				 <p>生产日期:<input type="text" id="startDate" class="text ui-widget-content " size="10" <s:if test="#request.startDate!=null"> value="<s:property value="#request.startDate" />" </s:if>/>-<input type="text" id="endDate" class="text ui-widget-content"  size="10" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />" </s:if>/>
				COC证书打印日期:<input type="text" id="cocStartDate" class="text ui-widget-content " size="10" <s:if test="#request.cocStartDate!=null"> value="<s:property value="#request.cocStartDate" />" </s:if>/>-<input type="text" id="cocEndDate" class="text ui-widget-content"  size="10" <s:if test="#request.cocEndDate!=null"> value="<s:property value="#request.cocEndDate" />" </s:if>/>
				</td>
			</tr>
			
			<tr><td width="80%"></td>
			  <td width="7%" align="right"></td>
			  <td width="7%" align="right"></td>
			  <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			  <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
  			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			   <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			   <td width="60" align="right"><button id="export1" class="ui-button ui-state-default ui-corner-all">COC补打导出</button></td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="5%">选择</th> 
			    <th width="14%">VIN</th>
			    <th width="14%">COC流水号</th>
			    <th width="8%">生产车型</th>
				<th width="6%">颜色</th>
				<th width="12%">发动机号</th>
				<th width="10%">发动机类型</th>
				<th width="10%">生产日期</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="cocNum" /></td>
			  		<td><s:property value="model" /></td>	
			  		<td><s:property value="color" /></td>
			  		<td><s:property value="engineNo" /></td>	
			  		<td><s:property value="engineType" /></td>	
			  		<td><s:property value="prodDate" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="vin" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carInfo.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carInfo.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carInfo.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="carInfo.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value='<s:property value="#request.page.currentPage"/>'/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  <td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              <td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="public_notice_dialog">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>VIN编码</label></td>
				<td><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>发动机号</label></td>
				<td><input type="text" id="engineNo" name="engineNo" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>实例车型</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>发动机型号</label></td>
				<td><input type="text" id="engineType" name="engineType" class="text ui-widget-content ui-corner-all" size="15" /></td>
			</tr>
			<tr>	
				<td><label><P>颜色</label></td>
				<td>
					<input type="text" id="color" name="color" class="text ui-widget-content ui-corner-all" size="15"/>
				</td>
				<td><label><P>生产日期</label></td>
				<td><input type="text" id="prodDate" name="prodDate" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>COC编码</label></td>
				<td><input type="text" id="cocNum" name="cocNum" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>COC颜色</label></td>
				<td>
					<input type="text" id="cocColor" name="cocColor" class="text ui-widget-content ui-corner-all" size="15"/>
				</td>
			</tr>
			
			<tr>
				<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="gvercode" name="gvercode" class="text ui-widget-content ui-corner-all" size="15"  /></td>
				<td><label><P>COC版本号</label></td>
				<td><input type="text" id="cvercode" name="cvercode" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td><label><P>召回版本号</label></td>
				<td><input type="text" id="zvercode" name="zvercode" class="text ui-widget-content ui-corner-all" size="15"  /></td>
				<td><label><P>环保版本号</label></td>
				<td><input type="text" id="evercode" name="evercode" class="text ui-widget-content ui-corner-all" size="15" /></td>							
				
			</tr>
			<tr>
				<td><label><P>车型物料号</label></td>
				<td><input type="text" id="materialNo" name="materialNo" class="text ui-widget-content ui-corner-all" size="15" /></td>							
				<td><label><P>照片版本</label></td>
				<td><input type="text" id="pvercode" name="pvercode" class="text ui-widget-content ui-corner-all" size="15" /></td>
				<td> 工厂类型:</td>
				<td>
					 <s:select name="factory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.factorytype"></s:select>
				</td>
				<td><label><P>驱动电机号</label></td>
				<td><input type="text" id="motorNo" name="motorNo" class="text ui-widget-content ui-corner-all" size="15" /></td>					
			</tr>
			<tr>
				<td><label><P>驱动电机类型</label></td>
				<td colspan="7"><input type="text" id="motorType" name="motorType" class="text ui-widget-content ui-corner-all" size="15" /></td>							
			</tr>			
		</Table>
		<input type='hidden' id='modreason' name='modreason'/>		
		<input type='hidden' id='time' name='time'/>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='remark' name='remark'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="public_notice_display_dialog" title="查看窗口" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>VIN编码</label></td>
				<td><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>发动机号</label></td>
				<td><input type="text" id="engineNo" name="engineNo" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>实例车型</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>发动机型号</label></td>
				<td><input type="text" id="engineType" name="engineType" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
			</tr>
			<tr>	
				<td><label><P>颜色</label></td>
				<td><input type="text" id="color" name="color" class="text ui-widget-content ui-corner-all" size="15" readonly="true"/></td>
				<td><label><P>生产日期</label></td>
				<td><input type="text" id="prodDate" name="prodDate" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC编码</label></td>
				<td><input type="text" id="cocNum" name="cocNum" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC颜色</label></td>
				<td>
				<input type="text" id="cocColor" name="cocColor" class="text ui-widget-content ui-corner-all" size="15" readonly="true" />
				</td>
			</tr>
			<tr>
				<td><label><P>燃油标签版本号</label></td>
				<td><input type="text" id="gvercode" name="gvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>COC版本号</label></td>
				<td><input type="text" id="cvercode" name="cvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>召回版本号</label></td>
				<td><input type="text" id="zvercode" name="zvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true" /></td>
				<td><label><P>环保版本号</label></td>
				<td><input type="text" id="evercode" name="evercode" class="text ui-widget-content ui-corner-all" size="15" /></td>		
			</tr>
			<tr>
				<td><label><P>车型物料号</label></td>
				<td><input type="text" id="materialNo" name="materialNo" class="text ui-widget-content ui-corner-all" size="15" readonly="true"/></td>							
				<td><label><P>照片版本</label></td>
				<td><input type="text" id="pvercode" name="pvercode" class="text ui-widget-content ui-corner-all" size="15" readonly="true"/></td>
				<td> 工厂类型:</td>
				<td>
					 <s:select name="factory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.factorytype"></s:select>
				</td>
				<td><label><P>驱动电机号</label></td>
				<td><input type="text" id="motorNo" name="motorNo" class="text ui-widget-content ui-corner-all" size="15" readonly="true"/></td>					
			</tr>
			<tr>
				<td><label><P>驱动电机类型</label></td>
				<td colspan="7"><input type="text" id="motorType" name="motorType" class="text ui-widget-content ui-corner-all" size="15" readonly="true"/></td>							
			</tr>			
		</Table>
		<input type='hidden' id='time' name='time'/>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='remark' name='remark'/>
		<input type='hidden' id='state' name='state'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message" style="word-wrap: break-word;"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='vin' name='vin'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="export_dialog" title="数据导出窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='vin' name='vin'>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div id="test_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td align="left"><label><P>原因:</label></td>
				<td><input type="text" id="reason" name="reason"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20"/>
					</td>
	    	</tr>			
		</Table>
		<input type='hidden' id='type' name='type'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>
</html>