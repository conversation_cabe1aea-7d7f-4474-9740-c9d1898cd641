package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarRecallDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecall;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecodInfo;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecodInfoId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate;

public class CarRecallServiceImpl implements CarRecallService{
	private CarRecallDAO dao = null;
	private PaginationService paginationService = null;
	
	public void setDao(CarRecallDAO dao) {
		this.dao = dao;
	}
	
	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	public void addCarModel(CarRecodInfo obj) {
		try{
			this.dao.addCarModel(obj);
		}catch(DataAccessException e){
			throw new SystemException("addCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCarModel Method Error:",e);
		}
	}
	
	public void updateCarModel(CarRecodInfo obj) {
		try{
			this.dao.updateCarModel(obj);
		}catch(DataAccessException e){
			throw new SystemException("updateCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCarModel Method Error:",e);
		}
	}
	
	public void deleteCarModel(CarRecodInfo obj) {
		try{
			this.dao.deleteCarModel(obj);
		}catch(DataAccessException e){
			throw new SystemException("deleteCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCarModel Method Error:",e);
		}
	}
	
	public void deleteCarModels(CarRecodInfo[] obj) throws DataAccessException {
		try{
			this.dao.deleteCarModels(obj);
		}catch(DataAccessException e){
			throw new SystemException("deleteCarModels Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCarModels Method Error:",e);
		}
	}
	
	public CarRecodInfo loadCarModelObj(CarRecodInfoId id) {
		CarRecodInfo gg = null;
		try{
			gg = this.dao.loadCarModelObj(id);
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}
		
		return gg;
	}
	
	public Object[] loadCarModelInfo(CarRecodInfoId id) {
		Object[] gg = null;
		try{
			String hql = "from CarRecodInfo g,CarInfo c where g.id.c1=c.model and g.id.vercode=c.zvercode" +
					" and g.id.c1='"+id.getC1()+"' and g.id.vercode='"+id.getVercode()+"'";
			gg = this.dao.findCarModelInfo(hql);
			
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}
		
		return gg;
	}
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException {
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id.c1) from CarRecodInfo g ";
				sql = "from CarRecodInfo g ";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub += " and g.id.c1 like '%"+params[0]+"%'";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub += " and g.state='"+params[1]+"'";
				}
				
				countSql = "select count(g.id.c1) from CarRecodInfo g where 1=1"+sub;
				sql = "from CarRecodInfo g where 1=1"+sub;
			}
			sql += " order by g.createdate desc,g.id.c1 desc,g.effectTime desc,g.id.vercode desc";
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}
	
	public List<?> paginationQuery(Page page,Object[] params) throws DataAccessException {
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id.c1) from CarRecodInfo g,CarInfo c where g.id.c1=c.model and g.id.vercode=c.zvercode";
				sql = "from CarRecodInfo g,CarInfo c where g.id.c1=c.model and g.id.vercode=c.zvercode order by c.vin,c.prodDate";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub += " and c.prodDate>='"+params[0]+"'";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub += " and c.prodDate<='"+params[1]+"'";
				}
				if(params[2]!=null&&!params[2].equals("")){
					sub += " and c.vin like '%"+params[2]+"%'";
				}
				if(params[3]!=null&&!params[3].equals("")){
					sub += " and c.engineType like '%"+params[3]+"%'";
				}
				if(params[4]!=null&&!params[4].equals("")){
					sub += " and g.model like '%"+params[4]+"%'";
				}
				
				countSql = "select count(g.id.c1) from CarRecodInfo g,CarInfo c where g.id.c1=c.model and g.id.vercode=c.zvercode"+sub;
				sql = "from CarRecodInfo g,CarInfo c where g.id.c1=c.model and g.id.vercode=c.zvercode"+sub+" order by c.vin,c.prodDate";
			}
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("paginationQuery Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("paginationQuery Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("paginationQuery Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("paginationQuery Method Error:",e);
		}
		return results;
	}
	
	public boolean isCarModelExist(CarRecodInfoId id) {
		try{
			List<CarRecodInfo> result = this.dao.findCarModel("from CarRecodInfo g where g.id.c1=? and g.id.vercode=?", new Object[]{id.getC1(),id.getVercode()});
			if(result!=null && result.size()>0){
				return true;
			}else{
				return false;
			}
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("isCarModelExist Method Error:",e1);
		}
	}	
	
	private CarRecodInfo findCarModelEffective(CarRecodInfoId id){
		CarRecodInfo gg = null;
		try{
			List<CarRecodInfo> result = this.dao.findCarModel("from CarRecodInfo g where g.id.c1=? and g.id.vercode=?", new Object[]{id.getC1(),id.getVercode()});
			if(result!=null&&result.size()>0){
				gg = result.get(0);
			}
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelEffective Method Error:",e1);
		}
		return gg;
	}
	
	public void updateCarModelEffect(CarRecodInfo gg) {
		try{
			this.dao.updateCarModel("update CarRecodInfo g set g.state=? where g.vin=? ",new Object[]{gg.getState(),gg.getId().getC1()});
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("updafteCarModelEffect Method Error:",e1);
		}
	}

	public List<CarRecodInfo> allCarModel(String hql) {
		try{
			List<CarRecodInfo> result = this.dao.findCarModel(hql);
			return result;
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("allCarModel Method Error:",e1);
		}
	}
	
	public CarRecodInfo findMaxCarMOdelBySccx(String c1) throws DataAccessException {
		CarRecodInfo gg = null;
		try{
			gg = this.dao.findMaxCarModelBySccx(c1);
		}catch(DataAccessException e){
			throw new SystemException("findMaxCarMOdelBySccx Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findMaxCarMOdelBySccx Method Error:",e);
		}
		return gg;
	}
}
