<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-2.5.xsd"
	default-lazy-init="false">

	<bean id="schedulerFactoryBean" lazy-init="false" autowire="no"   
          class="org.springframework.scheduling.quartz.SchedulerFactoryBean">  
        <property name="applicationContextSchedulerContextKey" value="applicationContextKey"/>  
        <property name="triggers">  
           <list>  
                <ref bean="doTime"/>  
            </list>  
        </property>  
    </bean>  
      
    <!-- 定义触发时间 -->  
        <bean id="doTime" class="org.springframework.scheduling.quartz.CronTriggerBean">  
            <property name="jobDetail">  
                <ref bean="a"/>  
            </property>  
            <!-- cron表达式  
            	每月1号       0 0 0 1 * ?
            	每10分钟一次0 0/10 * * * ?
             -->  
            <property name="cronExpression">  
                <value> 0 0 0 1 * ?</value>  
            </property>  
        </bean>  
  
    <!-- 指定时间工作的类 -->  
    <bean id="a" class="org.springframework.scheduling.quartz.JobDetailBean">  
         <property name="jobClass">  
           <value>com.quartz.SpringQtz</value>
         </property>  
  	     
    </bean>  
    
    <bean id="gasVerService1" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.GasVerServiceImpl"  >
		<property name="gasVerDAO">
			<ref bean="gasVerDao1" />
		</property>
		
		<property name="paginationService">
			<ref bean="paginationService1" />
		</property>
	</bean>
    
    
    <bean id="gasVerDao1" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.GasVerDAOImpl" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="paginationService1" class="com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationServiceImpl" >
		<property name="paginationDAO">
			<ref bean="paginationDAO1" />
		</property>
	</bean>
	
	<bean id="paginationDAO1" class="com.dawnpro.dfpv.carfilemanager.common.pagination.dao.PaginationDAOImpl" >
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
    
    <bean id="qtz" class="com.quartz.SpringQtz">
    	
    </bean>
</beans>