package com.dawnpro.dfpv.carfilemanager.module.business.share.model;
// Generated 2018-9-18 13:11:19 by Hibernate Tools 3.6.0.Final

import java.util.Date;

/**
 * InterfaceProcdureLog generated by hbm2java
 */
public class InterfaceProcdureLog implements java.io.Serializable {

	private String id;
	private String procno;
	private Date starttime;
	private Date endtime;
	private String errinfo;

	public InterfaceProcdureLog() {
	}

	public InterfaceProcdureLog(String id) {
		this.id = id;
	}

	public InterfaceProcdureLog(String id, String procno, Date starttime, Date endtime, String errinfo) {
		this.id = id;
		this.procno = procno;
		this.starttime = starttime;
		this.endtime = endtime;
		this.errinfo = errinfo;
	}

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getProcno() {
		return this.procno;
	}

	public void setProcno(String procno) {
		this.procno = procno;
	}

	public Date getStarttime() {
		return this.starttime;
	}

	public void setStarttime(Date starttime) {
		this.starttime = starttime;
	}

	public Date getEndtime() {
		return this.endtime;
	}

	public void setEndtime(Date endtime) {
		this.endtime = endtime;
	}

	public String getErrinfo() {
		return this.errinfo;
	}

	public void setErrinfo(String errinfo) {
		this.errinfo = errinfo;
	}

}
