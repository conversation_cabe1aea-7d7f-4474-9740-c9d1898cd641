<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;

	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#query").click(function(){
		var sd = $('#beginDate').val();
		var ed = $('#endDate').val();
		var sl = $('#sLoginName').val();
		var sdesc = $('#sDescript').val();
		if(sd==""&&ed==""&&sl==""&&sdesc==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			var obj = $('#beginDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			obj = $('#endDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			location.href="uploadStateMonitor!gasuploadlog.action?beginDate="+sd+"&endDate="+ed+"&sLoginName="+encodeURI(encodeURI(sl))+"&sDescript="+encodeURI(encodeURI(sdesc));
		}
	});
	
	$('#jump').bind('keyup',function(event) {  
		var sd = $('#beginDate').val();
		var ed = $('#endDate').val();
		var sl = $('#sLoginName').val();
		var sdesc = $('#sDescript').val();
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var obj = $('#beginDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			obj = $('#endDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			location.href="uploadStateMonitor!gasuploadlog.action?currentPage="+$('#jump').val()+"&beginDate="+sd+"&endDate="+ed+"&sLoginName="+encodeURI(encodeURI(sl))+"&sDescript="+encodeURI(encodeURI(sdesc));
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var obj = $('#beginDate');
				if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
					obj.focus();
					return ;
				}
				obj = $('#endDate');
				if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
					obj.focus();
					return ;
				}
			  	var sd = $('#beginDate').val();
				var ed = $('#endDate').val();
				var sl = $('#sLoginName').val();
				var sdesc = $('#sDescript').val();
				location.href=$(this).attr('value')+"&beginDate="+sd+"&endDate="+ed+"&sLoginName="+encodeURI(encodeURI(sl))+"&sDescript="+encodeURI(encodeURI(sdesc));
		 });
	});
    
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#beginDate").datepicker($.datepicker.regional['zh']);
	$("#endDate").datepicker($.datepicker.regional['zh']);
    
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

         //给这行添加class值为over，并且当鼠标一出该行时执行函数

         $(this).removeClass("over");})    //移除该行的class
});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
					<td width="98%" align="left"><p>日期:
				  <input type="text" id="beginDate" class="text ui-widget-content ui-corner-all" size="10" <s:if test="#request.beginDate!=null"> value="<s:property value="#request.beginDate" />"</s:if>/> 至 
				  <input type="text" id="endDate" class="text ui-widget-content ui-corner-all"  size="10"  <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />"</s:if>/>	
				  用户名：<input type="text" id="sLoginName" class="text ui-widget-content ui-corner-all"  size="10"  <s:if test="#request.sLoginName!=null"> value="<s:property value="#request.sLoginName" />"</s:if>/>
				  操作内容：<input type="text" id="sDescript" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.sDescript!=null"> value="<s:property value="#request.sDescript" />"</s:if>/>
  		
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="20%">用户</th> 
			    <th width="10%">网络地址</th>
			    <th width="50%">使用功能描述</th>
				<th width="20%">使用时间</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.uploadStateMonitorPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><s:property value="loginname"/></td>
			  		<td><s:property value="ipaddress" /></td>
			  		<td><s:property value="descript" /></td>
			  		<td><s:property value="depdatetime" /></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadStateMonitor!gasuploadlog.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadStateMonitor!gasuploadlog.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadStateMonitor!gasuploadlog.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadStateMonitor!gasuploadlog.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	         
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>