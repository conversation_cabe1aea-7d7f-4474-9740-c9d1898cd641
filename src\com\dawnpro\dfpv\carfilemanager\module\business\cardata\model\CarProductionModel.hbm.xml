<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarProductionModel" table="CAR_PRODUCTION_MODEL" lazy="false">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarProductionModelId">
            <key-property name="model" type="java.lang.String">
                <column name="MODEL" length="20" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="15" />
            </key-property>
        </composite-id>
        <property name="modelName" type="java.lang.String">
            <column name="MODEL_NAME" length="40" />
        </property>
        <property name="pmodelCode" type="java.lang.String">
            <column name="PMODEL_CODE" length="20" />
        </property>
        <property name="brandCode" type="java.lang.String">
            <column name="BRAND_CODE" length="20" />
        </property>
        <property name="brandName" type="java.lang.String">
            <column name="BRAND_NAME" length="40" />
        </property>
        <property name="decorationStaCode" type="java.lang.String">
            <column name="DECORATION_STA_CODE" length="20" />
        </property>
        <property name="decorationStaName" type="java.lang.String">
            <column name="DECORATION_STA_NAME" length="40" />
        </property>
        <property name="emissionStaCode" type="java.lang.String">
            <column name="EMISSION_STA_CODE" length="20" />
        </property>
        <property name="emissionStaName" type="java.lang.String">
            <column name="EMISSION_STA_NAME" length="40" />
        </property>
        <property name="engineTypeCode" type="java.lang.String">
            <column name="ENGINE_TYPE_CODE" length="20" />
        </property>
        <property name="engineTypeName" type="java.lang.String">
            <column name="ENGINE_TYPE_NAME" length="40" />
        </property>
        <property name="isSunRoof" type="java.lang.String">
            <column name="IS_SUN_ROOF" length="1" />
        </property>
        <property name="modelKindCode" type="java.lang.String">
            <column name="MODEL_KIND_CODE" length="20" />
        </property>
        <property name="modelKindName" type="java.lang.String">
            <column name="MODEL_KIND_NAME" length="40" />
        </property>
        <property name="modelTypeCode" type="java.lang.String">
            <column name="MODEL_TYPE_CODE" length="20" />
        </property>
        <property name="modelTypeName" type="java.lang.String">
            <column name="MODEL_TYPE_NAME" length="40" />
        </property>
        <property name="safetyRestraintCode" type="java.lang.String">
            <column name="SAFETY_RESTRAINT_CODE" length="20" />
        </property>
        <property name="safetyRestraintName" type="java.lang.String">
            <column name="SAFETY_RESTRAINT_NAME" length="40" />
        </property>
        <property name="transmissionCode" type="java.lang.String">
            <column name="TRANSMISSION_CODE" length="20" />
        </property>
        <property name="transmissionName" type="java.lang.String">
            <column name="TRANSMISSION_NAME" length="40" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="2" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="deliveryCapacity" type="java.lang.String">
            <column name="DELIVERY_CAPACITY" length="20" />
        </property>
        <property name="flag" type="java.lang.String">
            <column name="flag" length="4" />
        </property>
        <property name="mpflag" type="java.lang.String">
            <column name="mpflag" length="4" />
        </property>
        <property name="activetime" type="java.lang.String">
            <column name="ACTIVETIME" length="19" />
        </property>
        <property name="publishtime" type="java.lang.String">
            <column name="PUBLISHTIME" length="19" />
        </property>
    </class>
</hibernate-mapping>
