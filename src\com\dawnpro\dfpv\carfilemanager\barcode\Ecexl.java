package com.dawnpro.dfpv.carfilemanager.barcode;

import java.io.File;

import jxl.Sheet;
import jxl.Workbook;
import jxl.WorkbookSettings;

public class Ecexl {

	public static void main(String[] args){
		try{
			WorkbookSettings settings = new WorkbookSettings();
			
			settings.setEncoding("ISO-8859-1"); 
			Workbook book = Workbook.getWorkbook(new File("e:/z12.xls"));
			Sheet sheet = book.getSheet(0);
			int rownum = sheet.getRows();
			StringBuffer sql = new StringBuffer();
			sql.append("select * from car_info where vin not in(");
			for (int i = 0; i < rownum; i++){
				System.out.println("i:"+i);
				sql.append("'"+sheet.getCell(2,i).getContents()+"' , ");
			}
			sql.append(")");
			System.out.println(sql.toString());
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
}
