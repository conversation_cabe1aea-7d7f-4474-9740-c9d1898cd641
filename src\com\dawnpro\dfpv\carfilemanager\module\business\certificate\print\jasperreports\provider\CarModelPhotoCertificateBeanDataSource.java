package com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.jasperreports.provider;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRField;

public class CarModelPhotoCertificateBeanDataSource implements JRDataSource{
	
	private int index = 1;
	
	public Object getFieldValue(JRField arg0) throws JRException {
		// TODO Auto-generated method stub
		return null;
	}

	public boolean next() throws JRException {
		if(index==1){
			index++;
			return true;
		}
		
		return false;
	}

}
