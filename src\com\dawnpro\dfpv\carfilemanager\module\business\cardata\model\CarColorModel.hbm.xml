<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarColorModel" table="CAR_COLOR_MAPPING" lazy="false">
        <id name="code" type="java.lang.String">
            <column name="CODE" length="50" />
            <generator class="assigned" />
        </id>
        <property name="color" type="java.lang.String">
            <column name="COLOR" length="50" />
        </property>
        <property name="basecolor" type="java.lang.String">
            <column name="BASECOLOR" length="50" />
        </property>     
        <property name="encolor" type="java.lang.String">
            <column name="ENCOLOR" length="50" />
        </property>
    </class>
</hibernate-mapping>
