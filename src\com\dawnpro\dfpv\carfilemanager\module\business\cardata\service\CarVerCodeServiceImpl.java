package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.awt.Color;
import java.awt.Font;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.URL;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.struts2.ServletActionContext;

import com.dawnpro.dfpv.carfilemanager.base.action.Action;
import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarInfoDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Modelver;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProeHBBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Proenvironment;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProenvironmentId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.MarkImageFont;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.NewBarcodeUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.jasperreports.provider.HBCertificateBeanDataSource;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.jasperreports.provider.HBCertificateBeanDataSourcePDF;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.ProenvDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.ProenvService;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper.CarVerCodeMapper;

public class CarVerCodeServiceImpl  implements CarVerCodeService {
	
	private CarInfoDAO dao = null;
	public void setDao(CarInfoDAO dao) {
		this.dao = dao;
	}

	private PaginationService paginationService = null;
	

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public Modelver loadMODELVER(String model,String factory) throws DataAccessException {
		List list = null;
		Modelver m = new Modelver();
		try{
			String sql = "select GVER1,CVER1,ZVER1,PVER1 from Modelver where carmodel='"+model+"'and factory='"+factory+"'"; 
			list =  this.paginationService.doSql(sql);
			if(list.size()>0){
				Object[] obj = (Object[]) list.get(0);
				m.setGver1(obj[0]==null?"":obj[0].toString());
				m.setCver1(obj[1]==null?"":obj[1].toString());
				m.setZver1(obj[2]==null?"":obj[2].toString());
				m.setPver1(obj[3]==null?"":obj[3].toString());
			}
			
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}
		
		return m;
	}
	
	
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException{
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = " select count(vin) from Car_Info t where "
						+"	(t.time is not null and t.gvercode is null)"
						+"		or"
						+"		(t.date1 is not null and (t.cvercode is null or t.evercode is null or t.zvercode is null))"
						+"		or"
						+"		(t.prod_Date is not null and t.pvercode is null)"
						;
				
				sql = " select vin,model,prod_Date,gvercode,cvercode,zvercode,pvercode,evercode,date1,time from Car_Info t where "
						+"	(t.time is not null and t.gvercode is null)"
						+"		or"
						+"		(t.date1 is not null and (t.cvercode is null or t.evercode is null or t.zvercode is null))"
						+"		or"
						+"		(t.prod_Date is not null and t.pvercode is null)"
						;
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " and t.model like ('"+String.valueOf(params[0])+"%') ";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" and t.vin = '"+params[1]+"' ";
				}
				if(params[2]!=null&&!params[2].equals("")){
					sub = sub+" and t.factory = '"+params[2]+"' ";
				}
				
				countSql = " select count(vin) from Car_Info t where 1=1  and("
						+"	(t.time is not null and t.gvercode is null)"
						+"		or"
						+"		(t.date1 is not null and (t.cvercode is null or t.evercode is null or t.zvercode is null))"
						+"		or"
						+"		(t.prod_Date is not null and t.pvercode is null) )"
						+sub;
				
				sql = " select vin,model,prod_Date,gvercode,cvercode,zvercode,pvercode,evercode,date1,time from Car_Info t where 1=1 and( "
						+"	(t.time is not null and t.gvercode is null)"
						+"		or"
						+"		(t.date1 is not null and (t.cvercode is null or t.evercode is null or t.zvercode is null))"
						+"		or"
						+"		(t.prod_Date is not null and t.pvercode is null) )"
						+sub;
				
			}
			sql += " order by t.time desc";
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSumBySQL(countSql);
			results = this.paginationService.paginationBySQL(sql,new CarVerCodeMapper());
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		
		
		return results;
	}

	public CarInfo loadModelObj(String vin) throws DataAccessException {
		CarInfo gg = null;
		try{
			gg = this.dao.loadCarModelObj(vin);
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}
		
		return gg;
	}

	public void updateModel(CarInfo obj) {
		try{
				this.dao.updateCarModel(obj);
		}catch(DataAccessException e){
			throw new SystemException("updateCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCarModel Method Error:",e);
		}
	}

	public String loadPcode(String model) {
		List list = null;
		String pcode = "";
		try{
			String sql = "select vercode from COC_PHOTO t where  model='"+model+"' and state=1 "; 
			list =  this.paginationService.doSql(sql);
			if(list.size()>0){
				Object obj = (Object) list.get(0);
				pcode = obj==null?"":obj.toString();
			}
			
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}
		
		return pcode;
	}

	
	
}
