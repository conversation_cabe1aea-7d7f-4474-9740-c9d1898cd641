package com.dawnpro.dfpv.carfilemanager.module.system.action;

import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryTypeService;

public class SysDataDictionaryTypeAction extends BaseSupportAction{
	private static final long serialVersionUID = -6371005813002910434L;
	private Logger logger = Logger.getLogger(SysDataDictionaryTypeAction.class.getName());
	private SysDataDictionaryTypeService service=null;
	public void setService(SysDataDictionaryTypeService service) {
		this.service = service;
	}

	private String sname;
	public void setSname(String sname) {
		this.sname = sname;
	}

	private String name;
	private String type;
	public void setName(String name) {
		this.name = name;
	}
	public void setType(String type) {
		this.type = type;
	}
	

	public String execute(){
		try{
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			
			List results = null;
			
			if(this.sname==null||this.sname.equals("")){
				results = this.service.findDataDictionaryType("from DataDictionaryType");
			}else{
				if(sname!=null){
					sname = java.net.URLDecoder.decode(this.sname, "UTF-8");
				}
				results = this.service.findDataDictionaryType("from DataDictionaryType g where g.name like '"+sname+"'");
			}
			
			this.getServletRequest().setAttribute("sname",this.sname==null?"":this.sname.trim());
			this.getServletRequest().setAttribute("pageData", results);
			
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		
		return SUCCESS;
	}
	
	public String isModelExist(){
		boolean isExist = true;
		try{
			isExist = this.service.isDataDictionaryTypeExist(name);
			this.setJson(String.valueOf(isExist));
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return JSON;
	}

	public String modelInfo(){
		try{
			DataDictionaryType gg = this.service.loadDataDictionaryType(name);
			JSONObject jsonObject = JSONObject.fromObject(gg); 
			this.setJson(jsonObject.toString());
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return JSON;
	}
	
	public String addModel(){
		try{
			DataDictionaryType gg = new DataDictionaryType();
			gg.setType(this.type==null?"":this.type.trim());
			gg.setName(this.name==null?"":this.name.trim());
			
			SysUser user =(SysUser)getSession().get(SYSTEM_USER);
			gg.setCreator(user.getLoginname());
			gg.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));

			this.service.addDataDictionaryType(gg);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}

	public String updateModel(){
		try{
			DataDictionaryType gg = new DataDictionaryType();
			gg.setType(this.type==null?"":this.type.trim());
			gg.setName(this.name==null?"":this.name.trim());

			SysUser user =(SysUser)getSession().get(SYSTEM_USER);
			gg.setCreator(user.getLoginname());
			gg.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));

			this.service.updateDataDictionaryType(gg);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}

	public String deleteModels(){
		try{
			String tmp = this.name.trim();
			String[] nemes = tmp.split("&");
			if(nemes!=null && nemes.length>0){
				DataDictionaryType[] gg = new DataDictionaryType[nemes.length];
				for(int i=0;i<nemes.length;i++){
					gg[i] = new DataDictionaryType();
					gg[i].setName(nemes[i]);
				}
				this.service.deleteDataDictionaryType(gg);
			}
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}
	
}
