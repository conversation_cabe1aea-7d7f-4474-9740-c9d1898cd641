package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysLog;

public interface SysLogDAO {
	
	void addLog(SysLog data) throws DataAccessException;
	
	List<SysLog> findLog(String sql, String[] params) throws DataAccessException;
	
	List<SysLog> findLog(String sql) throws DataAccessException;
}
