package com.dawnpro.dfpv.carfilemanager.module.business.share.service.impl;

import java.io.Serializable;

import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceProcdureLog;
import com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceProcdureLogService;

public class InterfaceProcdureLogServiceImpl implements InterfaceProcdureLogService {

	public void addInterfaceProcdureLog(InterfaceProcdureLog prodlog) {
		// TODO Auto-generated method stub

	}

	public void updateInterfaceProcdureLog(InterfaceProcdureLog prodlog) {
		// TODO Auto-generated method stub

	}

	public InterfaceProcdureLog loadInterfaceProcdureLog(Serializable id) {
		// TODO Auto-generated method stub
		return null;
	}

}
