package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;



/**
 * ����:�û���ݷ��ʽӿ���
 * @author:�Ų�
 * @since: 2008-4-22
 * **/
public interface SysUserDA0 {

	/**
	 * ����:�����û�����
	 * @param	user:�û�����
	 * **/
	public void addUser(SysUser user) throws DataAccessException;
	
	/**
	 * ����:ɾ���û�����
	 * @param	user:�û�����
	 * **/
	public void deleteUser(SysUser[] user) throws DataAccessException;
	
	/**
	 * ����:�༭�û�����
	 * @param	user:�û�����
	 * **/
	public void updateUser(SysUser user) throws DataAccessException;
	
	/**
	 * ����:��ѯ�û�����
	 * @param	sql:	��ѯ���
	 * @param	params:	��ѯ����
	 * **/
	public List<SysUser> findUser(String sql, String[] params) throws DataAccessException;
	
	public List<SysUser> findUser(String sql) throws DataAccessException;
	
	public SysUser loadUserObj(String loginName) throws DataAccessException;
	
//	
//	/**
//	 * ����:��ѯ�û�����
//	 * @param	sql:	��ѯ���
//	 * @param	params:	��ѯ����
//	 * **/
//	public List<SysUser> findUser(String sql, RowMapper mapper) throws DataAccessException;
//	
//	/**
//	 * ����:��ѯ�û�����
//	 * @param	sql:	��ѯ���
//	 * @param	params:	��ѯ����
//	 * **/
//	public List<SysRole> findRole(String sql, RowMapper mapper) throws DataAccessException;
}
