package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcs;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcsId;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.CocVerDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.HwCocVerDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.HwCocVerService;

public class HwCocVerServiceImpl implements HwCocVerService {
	private HwCocVerDAO hwCocVerDAO = null;

	private PaginationService paginationService = null;
	

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public HwCocVerDAO getHwCocVerDAO() {
		return hwCocVerDAO;
	}

	public void setHwCocVerDAO(HwCocVerDAO hwCocVerDAO) {
		this.hwCocVerDAO = hwCocVerDAO;
	}

	public List<?> pagination(Page page,Object[] params) throws DataAccessException{
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id.slcx) from HwBaseCOCcs g";
				sql = "from HwBaseCOCcs g ";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " and g.id.slcx like ('"+String.valueOf(params[0])+"%') ";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" and g.state = '"+params[1]+"' ";
				}
				//20160610 lmc
				if(params[2]!=null&&!params[2].equals("")){
					sub = sub+" and g.factory = '"+params[2]+"' ";
				}
				
				countSql = "select count(g.id.slcx) from HwBaseCOCcs g where 1=1 "+sub;
				sql = "from HwBaseCOCcs g where 1=1 "+sub;
			}
			sql += " order by g.createdate desc,g.id.slcx desc,g.effecttime desc";
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		
		
		return results;
	}

	public void addCocVer(HwBaseCOCcs obj) throws DataAccessException {
		try{
			this.hwCocVerDAO.addCocVer(obj);
		}catch(DataAccessException e){
			throw new SystemException("addCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCocVer Method Error:",e);
		}
		
	}

	public void deleteCocVer(HwBaseCOCcs[] id) throws DataAccessException {
		try{
			this.hwCocVerDAO.deleteCocVer(id);
		}catch(DataAccessException e){
			throw new SystemException("deleteCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCocVer Method Error:",e);
		}
	}

	public HwBaseCOCcs loadCocVer(HwBaseCOCcsId id)
			throws DataAccessException {
		HwBaseCOCcs gg = null;
		try{
			gg = this.hwCocVerDAO.loadCocVer(id);
		}catch(DataAccessException e){
			throw new SystemException("loadCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCocVer Method Error:",e);
		}
		return gg;
	}

	public void updateCocVer(HwBaseCOCcs obj) throws DataAccessException {
		try{
			this.hwCocVerDAO.updateCocVer(obj);
		}catch(DataAccessException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}
	}

	public HwBaseCOCcs findMaxCocVerBySccx(String c1) throws DataAccessException {
		HwBaseCOCcs gg = null;
		try{
			gg = this.hwCocVerDAO.findMaxCocVerBySccx(c1);
		}catch(DataAccessException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}
		return gg;
	}

	public List<HwBaseCOCcs> findCocVer(Object[] params){
		List<HwBaseCOCcs> list = null;
		try{
			String hql = "from HwBaseCOCcs g where 1=1";
			String sub = "";
			if(params != null && params.length>0)
			{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " and g.id.slcx like ('"+String.valueOf(params[0])+"%') ";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" and g.state = '"+params[1]+"' ";
				}
			}
			sub = sub+" order by g.id.slcx desc,g.effecttime desc";
			list = this.hwCocVerDAO.findCocVer(hql+sub);
		}catch(DataAccessException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}
		return list;
	}

	public List<HwBaseCOCcs> findCocVer(String hql)throws DataAccessException {
		List<HwBaseCOCcs> list = null;
		try{
			list = this.hwCocVerDAO.findCocVer(hql);
		}catch(DataAccessException e){
			throw new SystemException("findCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findCocVer Method Error:",e);
		}
		return list;
	}

	public void addCocVer(HwBaseCOCcs[] objs) throws DataAccessException {
		try{
			this.hwCocVerDAO.addCocVer(objs);
		}catch(DataAccessException e){
			throw new SystemException("addCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCocVer Method Error:",e);
		}
	}

	public void updateCocVer(HwBaseCOCcs[] objs) throws DataAccessException {
		try{
			this.hwCocVerDAO.updateCocVer(objs);
		}catch(DataAccessException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}
	}
	
}
