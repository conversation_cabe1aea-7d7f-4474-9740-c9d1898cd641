package com.dawnpro.dfpv.carfilemanager.module.system.model;

import java.util.List;

/**
 * SysPermission entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SysPermission implements java.io.Serializable {

	// Fields

	private SysPermissionId id;
	private String creator;
	private String time;
	private String remark;
	private List<SysOperatePermission> operatePermissions = null;
	// Constructors

	/** default constructor */
	public SysPermission() {
	}

	/** minimal constructor */
	public SysPermission(SysPermissionId id) {
		this.id = id;
	}

	/** full constructor */
	public SysPermission(SysPermissionId id, String creator, String time,
			String remark) {
		this.id = id;
		this.creator = creator;
		this.time = time;
		this.remark = remark;
	}

	// Property accessors

	public SysPermissionId getId() {
		return this.id;
	}

	public void setId(SysPermissionId id) {
		this.id = id;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<SysOperatePermission> getOperatePermissions() {
		return operatePermissions;
	}

	public void setOperatePermissions(List<SysOperatePermission> operatePermissions) {
		this.operatePermissions = operatePermissions;
	}

}