<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Modelver" table="MODELVER">
        <!-- 
        <id name="carmodel" type="java.lang.String">
            <column name="CARMODEL" length="50" />
            <generator class="assigned" />
        </id> -->
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.ModelverId">
            <key-property name="carmodel" type="java.lang.String">
                <column name="CARMODEL" length="50" />
            </key-property>
            <key-property name="factory" type="java.lang.String">
                <column name="FACTORY" length="10" not-null="true" />
            </key-property>
        </composite-id>        
        <property name="gver1" type="java.lang.String">
            <column name="GVER1" length="15" />
        </property>
        <property name="gver2" type="java.lang.String">
            <column name="GVER2" length="15" />
        </property>
        <property name="gswtime" type="java.lang.String">
            <column name="GSWTIME" length="19" />
        </property>
        <property name="gvalitime" type="java.lang.String">
            <column name="GVALITIME" length="19" />
        </property>
        <property name="gvaliuser" type="java.lang.String">
            <column name="GVALIUSER" length="20" />
        </property>
        <property name="gstate" type="java.lang.String">
            <column name="GSTATE" length="1" />
        </property>
        <property name="cver1" type="java.lang.String">
            <column name="CVER1" length="15" />
        </property>
        <property name="cver2" type="java.lang.String">
            <column name="CVER2" length="15" />
        </property>
        <property name="cswtime" type="java.lang.String">
            <column name="CSWTIME" length="19" />
        </property>
        <property name="cvalitime" type="java.lang.String">
            <column name="CVALITIME" length="19" />
        </property>
        <property name="cvaliuser" type="java.lang.String">
            <column name="CVALIUSER" length="20" />
        </property>
        <property name="cstate" type="java.lang.String">
            <column name="CSTATE" length="1" />
        </property>
        <property name="zver1" type="java.lang.String">
            <column name="ZVER1" length="15" />
        </property>
        <property name="zver2" type="java.lang.String">
            <column name="ZVER2" length="15" />
        </property>
        <property name="zswtime" type="java.lang.String">
            <column name="ZSWTIME" length="19" />
        </property>
        <property name="zvalitime" type="java.lang.String">
            <column name="ZVALITIME" length="19" />
        </property>
        <property name="zvaliuser" type="java.lang.String">
            <column name="ZVALIUSER" length="20" />
        </property>
        <property name="zstate" type="java.lang.String">
            <column name="ZSTATE" length="1" />
        </property>
         <property name="pver1" type="java.lang.String">
            <column name="PVER1" length="15" />
        </property>
        <property name="pver2" type="java.lang.String">
            <column name="PVER2" length="15" />
        </property>
        <property name="pswtime" type="java.lang.String">
            <column name="PSWTIME" length="19" />
        </property>
        <property name="pvalitime" type="java.lang.String">
            <column name="PVALITIME" length="19" />
        </property>
        <property name="pvaliuser" type="java.lang.String">
            <column name="PVALIUSER" length="20" />
        </property>
        <property name="pstate" type="java.lang.String">
            <column name="PSTATE" length="1" />
        </property>
        
         <property name="ever1" type="java.lang.String">
            <column name="EVER1" length="15" />
        </property>
        <property name="ever2" type="java.lang.String">
            <column name="EVER2" length="15" />
        </property>
        <property name="eswtime" type="java.lang.String">
            <column name="ESWTIME" length="19" />
        </property>
        <property name="evalitime" type="java.lang.String">
            <column name="EVALITIME" length="19" />
        </property>
        <property name="evaliuser" type="java.lang.String">
            <column name="EVALIUSER" length="19" />
        </property>
        <property name="estate" type="java.lang.String">
            <column name="ESTATE" length="19" />
        </property>
        
        
    </class>
</hibernate-mapping>
