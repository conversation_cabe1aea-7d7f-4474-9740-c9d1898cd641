<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="c" uri="/jsp-customTags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/dtree/dtree.css" rel="stylesheet" />
<style type="text/css">
	p { /* Headers & Footer in Center & East panes */
		font-size:		9pt;
		background:		#EEF;
		border:			1px solid #BBB;
		border-width:	1 1 1px;
		padding:		3px 10px;
		margin:			12;
		width:80%
	}
	body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6; }
</style>
<SCRIPT type="text/javascript" src="js/dtree/dtree.js"></SCRIPT>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<c:tree value="${request.sys_user_current_menus}" nameAttribute="name" childMethodName="childs" pathAttribute="path" targetAttribute="mainFrame" expand="true"></c:tree>
</body>
</html>