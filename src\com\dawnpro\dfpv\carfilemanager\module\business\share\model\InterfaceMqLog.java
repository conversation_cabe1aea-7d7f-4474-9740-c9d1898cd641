package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

import java.util.List;



/**
 * InterfaceLog entity. <AUTHOR> Persistence Tools
 */

public class InterfaceMqLog  implements java.io.Serializable {
	
    // Fields    

     private String id;
     private String type;
     private String mqname;
     private String beginlen;
     private String endlen;
     private String allnum;
     private String successnum;
     private String failnum;
     private String state;


    // Constructors
	/** default constructor */
    public InterfaceMqLog() {
    
     }
    
    public InterfaceMqLog( String type, String mqname, String beginlen, String endlen,
			String allnum, String successnum, String failnum, String state) {
    	this.type = type;
		this.mqname = mqname;
		this.beginlen = beginlen;
		this.endlen = endlen;
		this.allnum = allnum;
		this.successnum = successnum;
		this.failnum = failnum;
		this.state = state;
	}


	public String getId() {
		return id;
	}


	public void setId(String id) {
		this.id = id;
	}


	public String getType() {
		return type;
	}


	public void setType(String type) {
		this.type = type;
	}


	public String getMqname() {
		return mqname;
	}


	public void setMqname(String mqname) {
		this.mqname = mqname;
	}


	public String getBeginlen() {
		return beginlen;
	}


	public void setBeginlen(String beginlen) {
		this.beginlen = beginlen;
	}


	public String getEndlen() {
		return endlen;
	}


	public void setEndlen(String endlen) {
		this.endlen = endlen;
	}


	public String getAllnum() {
		return allnum;
	}


	public void setAllnum(String allnum) {
		this.allnum = allnum;
	}


	public String getSuccessnum() {
		return successnum;
	}


	public void setSuccessnum(String successnum) {
		this.successnum = successnum;
	}


	public String getFailnum() {
		return failnum;
	}


	public void setFailnum(String failnum) {
		this.failnum = failnum;
	}


	public String getState() {
		return state;
	}


	public void setState(String state) {
		this.state = state;
	}
    	
}