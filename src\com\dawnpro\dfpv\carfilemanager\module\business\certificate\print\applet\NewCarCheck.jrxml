<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="COC" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="adtjc" class="java.lang.String">
		<fieldDescription><![CDATA[adtjc]]></fieldDescription>
	</field>
	<field name="analyDate" class="java.lang.String">
		<fieldDescription><![CDATA[analyDate]]></fieldDescription>
	</field>
	<field name="analyManuf" class="java.lang.String">
		<fieldDescription><![CDATA[analyManuf]]></fieldDescription>
	</field>
	<field name="analyModel" class="java.lang.String">
		<fieldDescription><![CDATA[analyModel]]></fieldDescription>
	</field>
	<field name="analyName" class="java.lang.String">
		<fieldDescription><![CDATA[analyName]]></fieldDescription>
	</field>
	<field name="ap" class="java.lang.String">
		<fieldDescription><![CDATA[ap]]></fieldDescription>
	</field>
	<field name="apass" class="java.lang.String">
		<fieldDescription><![CDATA[apass]]></fieldDescription>
	</field>
	<field name="bcmcalid" class="java.lang.String">
		<fieldDescription><![CDATA[bcmcalid]]></fieldDescription>
	</field>
	<field name="bcmcvn" class="java.lang.String">
		<fieldDescription><![CDATA[bcmcvn]]></fieldDescription>
	</field>
	<field name="bcmmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[bcmmoduleid]]></fieldDescription>
	</field>
	<field name="bsqxs" class="java.lang.String">
		<fieldDescription><![CDATA[bsqxs]]></fieldDescription>
	</field>
	<field name="checker" class="java.lang.String">
		<fieldDescription><![CDATA[checker]]></fieldDescription>
	</field>
	<field name="chzhq" class="java.lang.String">
		<fieldDescription><![CDATA[chzhq]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="clzzname" class="java.lang.String">
		<fieldDescription><![CDATA[clzzname]]></fieldDescription>
	</field>
	<field name="cnzl" class="java.lang.String">
		<fieldDescription><![CDATA[cnzl]]></fieldDescription>
	</field>
	<field name="ctest" class="java.lang.String">
		<fieldDescription><![CDATA[ctest]]></fieldDescription>
	</field>
	<field name="ctestlocation" class="java.lang.String">
		<fieldDescription><![CDATA[ctestlocation]]></fieldDescription>
	</field>
	<field name="dcrl" class="java.lang.String">
		<fieldDescription><![CDATA[dcrl]]></fieldDescription>
	</field>
	<field name="ddjxh" class="java.lang.String">
		<fieldDescription><![CDATA[ddjxh]]></fieldDescription>
	</field>
	<field name="dmcmcalid" class="java.lang.String">
		<fieldDescription><![CDATA[dmcmcalid]]></fieldDescription>
	</field>
	<field name="dmcmcvn" class="java.lang.String">
		<fieldDescription><![CDATA[dmcmcvn]]></fieldDescription>
	</field>
	<field name="dmcmmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[dmcmmoduleid]]></fieldDescription>
	</field>
	<field name="dynoManuf" class="java.lang.String">
		<fieldDescription><![CDATA[dynoManuf]]></fieldDescription>
	</field>
	<field name="dynoModel" class="java.lang.String">
		<fieldDescription><![CDATA[dynoModel]]></fieldDescription>
	</field>
	<field name="ecm2calid" class="java.lang.String">
		<fieldDescription><![CDATA[ecm2calid]]></fieldDescription>
	</field>
	<field name="ecm2cvn" class="java.lang.String">
		<fieldDescription><![CDATA[ecm2cvn]]></fieldDescription>
	</field>
	<field name="ecm2moduleid" class="java.lang.String">
		<fieldDescription><![CDATA[ecm2moduleid]]></fieldDescription>
	</field>
	<field name="ecmcalid" class="java.lang.String">
		<fieldDescription><![CDATA[ecmcalid]]></fieldDescription>
	</field>
	<field name="ecmcvn" class="java.lang.String">
		<fieldDescription><![CDATA[ecmcvn]]></fieldDescription>
	</field>
	<field name="ecmmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[ecmmoduleid]]></fieldDescription>
	</field>
	<field name="engineno" class="java.lang.String">
		<fieldDescription><![CDATA[engineno]]></fieldDescription>
	</field>
	<field name="epass" class="java.lang.String">
		<fieldDescription><![CDATA[epass]]></fieldDescription>
	</field>
	<field name="et" class="java.lang.String">
		<fieldDescription><![CDATA[et]]></fieldDescription>
	</field>
	<field name="fdjcp" class="java.lang.String">
		<fieldDescription><![CDATA[fdjcp]]></fieldDescription>
	</field>
	<field name="fdjgc" class="java.lang.String">
		<fieldDescription><![CDATA[fdjgc]]></fieldDescription>
	</field>
	<field name="fdjpl" class="java.lang.String">
		<fieldDescription><![CDATA[fdjpl]]></fieldDescription>
	</field>
	<field name="fdjscdz" class="java.lang.String">
		<fieldDescription><![CDATA[fdjscdz]]></fieldDescription>
	</field>
	<field name="fdjxh" class="java.lang.String">
		<fieldDescription><![CDATA[fdjxh]]></fieldDescription>
	</field>
	<field name="finalresult" class="java.lang.String">
		<fieldDescription><![CDATA[finalresult]]></fieldDescription>
	</field>
	<field name="hbsb" class="java.lang.String">
		<fieldDescription><![CDATA[hbsb]]></fieldDescription>
	</field>
	<field name="hvbecmcalid" class="java.lang.String">
		<fieldDescription><![CDATA[hvbecmcalid]]></fieldDescription>
	</field>
	<field name="hvbecmcvn" class="java.lang.String">
		<fieldDescription><![CDATA[hvbecmcvn]]></fieldDescription>
	</field>
	<field name="hvbecmmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[hvbecmmoduleid]]></fieldDescription>
	</field>
	<field name="jcuploadflg" class="java.lang.String">
		<fieldDescription><![CDATA[jcuploadflg]]></fieldDescription>
	</field>
	<field name="jzzl" class="java.lang.String">
		<fieldDescription><![CDATA[jzzl]]></fieldDescription>
	</field>
	<field name="obd" class="java.lang.String">
		<fieldDescription><![CDATA[obd]]></fieldDescription>
	</field>
	<field name="obdplcae" class="java.lang.String">
		<fieldDescription><![CDATA[obdplcae]]></fieldDescription>
	</field>
	<field name="odo" class="java.lang.String">
		<fieldDescription><![CDATA[odo]]></fieldDescription>
	</field>
	<field name="opass" class="java.lang.String">
		<fieldDescription><![CDATA[opass]]></fieldDescription>
	</field>
	<field name="otestdate" class="java.lang.String">
		<fieldDescription><![CDATA[otestdate]]></fieldDescription>
	</field>
	<field name="othcalid" class="java.lang.String">
		<fieldDescription><![CDATA[othcalid]]></fieldDescription>
	</field>
	<field name="othcvn" class="java.lang.String">
		<fieldDescription><![CDATA[othcvn]]></fieldDescription>
	</field>
	<field name="othmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[othmoduleid]]></fieldDescription>
	</field>
	<field name="pfbz" class="java.lang.String">
		<fieldDescription><![CDATA[pfbz]]></fieldDescription>
	</field>
	<field name="pmodelcode" class="java.lang.String">
		<fieldDescription><![CDATA[pmodelcode]]></fieldDescription>
	</field>
	<field name="proddate" class="java.lang.String">
		<fieldDescription><![CDATA[proddate]]></fieldDescription>
	</field>
	<field name="qgs" class="java.lang.String">
		<fieldDescription><![CDATA[qgs]]></fieldDescription>
	</field>
	<field name="rh" class="java.lang.String">
		<fieldDescription><![CDATA[rh]]></fieldDescription>
	</field>
	<field name="rygjfs" class="java.lang.String">
		<fieldDescription><![CDATA[rygjfs]]></fieldDescription>
	</field>
	<field name="scgdz" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="scrcalid" class="java.lang.String">
		<fieldDescription><![CDATA[scrcalid]]></fieldDescription>
	</field>
	<field name="scrcvn" class="java.lang.String">
		<fieldDescription><![CDATA[scrcvn]]></fieldDescription>
	</field>
	<field name="scrmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[scrmoduleid]]></fieldDescription>
	</field>
	<field name="secvlco" class="java.lang.String">
		<fieldDescription><![CDATA[secvlco]]></fieldDescription>
	</field>
	<field name="secvlhc" class="java.lang.String">
		<fieldDescription><![CDATA[secvlhc]]></fieldDescription>
	</field>
	<field name="secvlnox" class="java.lang.String">
		<fieldDescription><![CDATA[secvlnox]]></fieldDescription>
	</field>
	<field name="secvrco" class="java.lang.String">
		<fieldDescription><![CDATA[secvrco]]></fieldDescription>
	</field>
	<field name="secvrhc" class="java.lang.String">
		<fieldDescription><![CDATA[secvrhc]]></fieldDescription>
	</field>
	<field name="secvrnox" class="java.lang.String">
		<fieldDescription><![CDATA[secvrnox]]></fieldDescription>
	</field>
	<field name="tcmcalid" class="java.lang.String">
		<fieldDescription><![CDATA[tcmcalid]]></fieldDescription>
	</field>
	<field name="tcmcvn" class="java.lang.String">
		<fieldDescription><![CDATA[tcmcvn]]></fieldDescription>
	</field>
	<field name="tcmmoduleid" class="java.lang.String">
		<fieldDescription><![CDATA[tcmmoduleid]]></fieldDescription>
	</field>
	<field name="testDate" class="java.lang.String">
		<fieldDescription><![CDATA[testDate]]></fieldDescription>
	</field>
	<field name="testNo" class="java.lang.String">
		<fieldDescription><![CDATA[testNo]]></fieldDescription>
	</field>
	<field name="testtype" class="java.lang.String">
		<fieldDescription><![CDATA[testtype]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="vlco" class="java.lang.String">
		<fieldDescription><![CDATA[vlco]]></fieldDescription>
	</field>
	<field name="vlhc" class="java.lang.String">
		<fieldDescription><![CDATA[vlhc]]></fieldDescription>
	</field>
	<field name="vlnox" class="java.lang.String">
		<fieldDescription><![CDATA[vlnox]]></fieldDescription>
	</field>
	<field name="vrco" class="java.lang.String">
		<fieldDescription><![CDATA[vrco]]></fieldDescription>
	</field>
	<field name="vrhc" class="java.lang.String">
		<fieldDescription><![CDATA[vrhc]]></fieldDescription>
	</field>
	<field name="vrnox" class="java.lang.String">
		<fieldDescription><![CDATA[vrnox]]></fieldDescription>
	</field>
	<field name="xxgkhao" class="java.lang.String">
		<fieldDescription><![CDATA[xxgkhao]]></fieldDescription>
	</field>
	<field name="zdzzl" class="java.lang.String">
		<fieldDescription><![CDATA[zdzzl]]></fieldDescription>
	</field>
	<field name="ocommunchk" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="3" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="786" splitType="Stretch">
			<textField>
				<reportElement x="262" y="557" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vlhc}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="65" width="80" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[报告编号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="101" y="65" width="189" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{testNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="101" y="89" width="189" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pmodelcode}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="89" width="80" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆型号]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="101" y="101" width="189" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{clzzname}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="101" width="80" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆生产企业]]></text>
			</staticText>
			<textField>
				<reportElement x="385" y="89" width="170" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vin}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="89" width="95" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆识别代号（VIN)]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="137" width="80" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机型号]]></text>
			</staticText>
			<textField>
				<reportElement x="101" y="137" width="189" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fdjxh}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="385" y="137" width="170" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{engineno}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="137" width="95" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机号码]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="101" width="95" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆排放阶段]]></text>
			</staticText>
			<textField>
				<reportElement x="385" y="101" width="170" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{pfbz}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="101" y="113" width="189" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bsqxs}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="113" width="80" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[变速器型式]]></text>
			</staticText>
			<textField>
				<reportElement x="385" y="113" width="170" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{chzhq}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="113" width="95" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[催化转化器型号]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="125" width="95" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[最大总设计质量（kg)]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="125" width="80" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[基准质量（kg)]]></text>
			</staticText>
			<textField>
				<reportElement x="101" y="125" width="189" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{jzzl}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="385" y="125" width="170" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{zdzzl}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="101" y="149" width="189" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fdjgc}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="149" width="95" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机排量（L)]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="149" width="80" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机生产企业]]></text>
			</staticText>
			<textField>
				<reportElement x="385" y="149" width="170" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fdjpl}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="161" width="95" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[燃油供给方式]]></text>
			</staticText>
			<textField>
				<reportElement x="385" y="161" width="170" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{rygjfs}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="101" y="161" width="189" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{qgs}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="161" width="80" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[气缸数]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="173" width="80" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[电动机型号]]></text>
			</staticText>
			<textField>
				<reportElement x="101" y="173" width="189" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ddjxh}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="173" width="95" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[储能装置型号]]></text>
			</staticText>
			<textField>
				<reportElement x="385" y="173" width="170" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cnzl}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="101" y="185" width="189" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dcrl}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="385" y="185" width="170" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{obdplcae}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="185" width="80" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[电池容量]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="185" width="95" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[OBD接口位置]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="197" width="534" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[1.2 外观检验]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="209" width="534" height="36"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA["        "+本车实车污染控制装置与环保随车清单信息一致]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="305" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机控制单元ECM]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="293" width="111" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[OBD通讯是否正常]]></text>
			</staticText>
			<textField>
				<reportElement x="280" y="305" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ecmcalid}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="101" y="317" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[变速箱控制单元TCM]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="413" width="534" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[1.4 污染物排放测试]]></text>
			</staticText>
			<textField>
				<reportElement x="280" y="377" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bcmcalid}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="317" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tcmcalid}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="280" y="389" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{othcalid}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="425" width="534" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[环境参数]]></text>
			</staticText>
			<textField>
				<reportElement x="280" y="329" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ecm2calid}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="101" y="329" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发动机控制单元ECM(附属控制)]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="341" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[电驱动控制单元DMCM]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="353" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[选择性催化还原SCR]]></text>
			</staticText>
			<textField>
				<reportElement x="280" y="341" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dmcmcalid}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="101" y="365" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[高压电池控制单元HV-BECM]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="401" width="72" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[检验员：]]></text>
			</staticText>
			<textField>
				<reportElement x="462" y="401" width="78" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{checker}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="353" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{scrcalid}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="365" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{hvbecmcalid}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="101" y="377" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车身稳定控制模块BCM]]></text>
			</staticText>
			<staticText>
				<reportElement x="101" y="389" width="139" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[其他控制单元]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="305" width="80" height="96"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID/CVN信息]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="533" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[实测值(燃料1)]]></text>
			</staticText>
			<textField>
				<reportElement x="262" y="545" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{secvrhc}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="192" y="521" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="545" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[实测值(燃料2)]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="533" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vrco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="262" y="533" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vrhc}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="521" width="171" height="48"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA["  "+简易瞬态工况法]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="545" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{secvrco}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="262" y="521" width="84" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[HC(g/km)]]></text>
			</staticText>
			<staticText>
				<reportElement x="173" y="37" width="225" height="20"/>
				<textElement textAlignment="Center">
					<font fontName="楷体_GB2312" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[新车下线污染物排放检测报告]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="385" y="65" width="170" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{otestdate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="65" width="95" height="12"/>
				<textElement textAlignment="Right">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[检验日期：]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="77" width="534" height="12"/>
				<textElement>
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[1.1 基本信息]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="245" width="534" height="36"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[公司（公司盖章）：+ "    "]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="281" width="534" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[1.3 OBD检查]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="305" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="317" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="329" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="341" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="353" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="365" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="377" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="240" y="389" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CAL ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="389" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="377" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="365" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="341" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="353" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="317" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="305" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<staticText>
				<reportElement x="390" y="329" width="40" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CVN]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="430" y="389" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{othcvn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="317" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{tcmcalid}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="377" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bcmcvn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="365" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{hvbecmcvn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="329" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ecm2cvn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="341" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dmcmcvn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="353" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{scrcvn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="305" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ecmcvn}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="401" width="111" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[OBD检查结果]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="446" y="437" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ap}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="374" y="437" width="72" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[相对湿度（%）]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="437" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[大气压（kPa）]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="262" y="437" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{et}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="437" width="101" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[环境温度（℃）]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="122" y="437" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{rh}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="374" y="461" width="72" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[分析仪检定日期]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="461" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[分析仪名称]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="461" width="101" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[分析仪生产企业]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="122" y="461" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{analyManuf}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="449" width="534" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[分析仪/测功机参数]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="262" y="461" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{analyName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="446" y="461" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dynoModel}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="192" y="473" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[底盘测功机型号]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="262" y="473" width="110" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dynoModel}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="122" y="473" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dynoManuf}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="21" y="473" width="101" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[底盘测功机生产企业]]></text>
			</staticText>
			<staticText>
				<reportElement x="346" y="521" width="104" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO(g/km)]]></text>
			</staticText>
			<staticText>
				<reportElement x="262" y="485" width="136" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO(g/km)]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="509" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[企业限值]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="485" width="171" height="36"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA["  " + 瞬态工况法]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="497" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[实测值]]></text>
			</staticText>
			<textField>
				<reportElement x="398" y="509" width="157" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="262" y="509" width="136" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="192" y="485" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="398" y="497" width="157" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="262" y="497" width="136" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="398" y="485" width="157" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[HC+NOx(g/km)]]></text>
			</staticText>
			<textField>
				<reportElement x="450" y="545" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{secvrnox}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="533" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vrnox}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="450" y="521" width="104" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NOx(g/km)]]></text>
			</staticText>
			<staticText>
				<reportElement x="262" y="569" width="84" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NO(10 )]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="593" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[企业限值]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="569" width="171" height="36"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA["  "+稳态工况法]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="581" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[实测值]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="593" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="262" y="593" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="450" y="593" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="192" y="569" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="450" y="581" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="346" y="581" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="450" y="569" width="104" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NO(10 )]]></text>
			</staticText>
			<textField>
				<reportElement x="262" y="581" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="346" y="569" width="104" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO(%)]]></text>
			</staticText>
			<staticText>
				<reportElement x="450" y="605" width="104" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[高怠速]]></text>
			</staticText>
			<textField>
				<reportElement x="450" y="629" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="346" y="605" width="104" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[低怠速]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="629" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[实测值]]></text>
			</staticText>
			<textField>
				<reportElement x="262" y="629" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="192" y="605" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="262" y="605" width="84" height="24"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[过量空气系数
（λ）]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="605" width="171" height="48"/>
				<textElement verticalAlignment="Middle">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA["  "+稳态工况法]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="617" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="629" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="450" y="641" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="192" y="641" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[企业限值]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="641" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="262" y="641" width="84" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="462" y="653" width="78" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{checker}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="390" y="653" width="72" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[检验员：]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="653" width="171" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[污染物排放结果]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="665" width="533" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[注：1）“检验日期”为8位数，年份（4位）+月份（2位）+日期（2位）；]]></text>
			</staticText>
			<staticText>
				<reportElement x="21" y="677" width="533" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA["  " + 2） 基本信息中“电动机型号”/“储能装置型号”/“电池容量”仅适用混合动力车辆；]]></text>
			</staticText>
			<staticText>
				<reportElement x="510" y="569" width="10" height="9"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[-6]]></text>
			</staticText>
			<textField>
				<reportElement x="398" y="641" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="398" y="629" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="502" y="629" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<textField>
				<reportElement x="502" y="641" width="52" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="312" y="569" width="10" height="9"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[-6]]></text>
			</staticText>
			<staticText>
				<reportElement x="242" y="653" width="20" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[合格]]></text>
			</staticText>
			<staticText>
				<reportElement x="398" y="617" width="52" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NO(10 )]]></text>
			</staticText>
			<staticText>
				<reportElement x="346" y="617" width="52" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO(%)]]></text>
			</staticText>
			<staticText>
				<reportElement x="502" y="617" width="52" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NO(10 )]]></text>
			</staticText>
			<staticText>
				<reportElement x="450" y="617" width="52" height="12"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO(%)]]></text>
			</staticText>
			<staticText>
				<reportElement x="432" y="617" width="10" height="9"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[-6]]></text>
			</staticText>
			<staticText>
				<reportElement x="536" y="617" width="10" height="9"/>
				<textElement>
					<font size="5"/>
				</textElement>
				<text><![CDATA[-6]]></text>
			</staticText>
			<staticText>
				<reportElement x="312" y="653" width="34" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[不合格]]></text>
			</staticText>
			<rectangle>
				<reportElement x="29" y="501" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="jystw" x="29" y="543" width="6" height="6" backcolor="#FFFFFF">
					<printWhenExpression><![CDATA[(($F{testtype}.equals("3") ) ? new Boolean(true) : new Boolean(false))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement key="jystb" x="29" y="543" width="6" height="6" backcolor="#000000"/>
			</rectangle>
			<rectangle>
				<reportElement x="29" y="586" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement x="29" y="627" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<staticText>
				<reportElement x="262" y="401" width="34" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[不合格]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="401" width="20" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[合格]]></text>
			</staticText>
			<rectangle>
				<reportElement key="pfnw" x="297" y="657" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="pfnb" mode="Opaque" x="297" y="657" width="6" height="6" backcolor="#000000">
					<printWhenExpression><![CDATA[(($F{epass}.equals("0") ) ? new Boolean(true) : new Boolean(false))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="262" y="293" width="18" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[否]]></text>
			</staticText>
			<staticText>
				<reportElement x="192" y="293" width="20" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[是]]></text>
			</staticText>
			<rectangle>
				<reportElement key="obd1nw" x="247" y="297" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="obd1nb" x="247" y="297" width="6" height="6" backcolor="#000000">
					<printWhenExpression><![CDATA[(($F{opass}.equals("0") ) ? new Boolean(true) : new Boolean(false))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="173" y="17" width="225" height="20"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="14" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[东风汽车集团有限公司]]></text>
			</staticText>
			<textField>
				<reportElement x="450" y="557" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vlnox}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="192" y="557" width="70" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[企业限值]]></text>
			</staticText>
			<textField>
				<reportElement x="346" y="557" width="104" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="10" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vlco}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement key="pfyw" mode="Opaque" x="227" y="657" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="pfyb" mode="Opaque" x="227" y="657" width="6" height="6" backcolor="#000000">
					<printWhenExpression><![CDATA[(($F{epass}.equals("0") ) ? new Boolean(false) : new Boolean(true))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement key="obd1yw" x="177" y="297" width="6" height="6" forecolor="#000000" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="obd1yb" x="177" y="297" width="6" height="6" backcolor="#000000">
					<printWhenExpression><![CDATA[(($F{opass}.equals("0") ) ? new Boolean(false) : new Boolean(true))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement key="obd2yw" x="177" y="405" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="obd2yb" x="177" y="405" width="6" height="6" backcolor="#000000">
					<printWhenExpression><![CDATA[(($F{ocommunchk}.equals("0") ) ? new Boolean(false) : new Boolean(true))]]></printWhenExpression>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement key="obd2nw" x="247" y="405" width="6" height="6" backcolor="#FFFFFF"/>
			</rectangle>
			<rectangle>
				<reportElement key="obd2nb" x="247" y="405" width="6" height="6" backcolor="#000000">
					<printWhenExpression><![CDATA[(($F{ocommunchk}.equals("0") ) ? new Boolean(true) : new Boolean(false))]]></printWhenExpression>
				</reportElement>
			</rectangle>
		</band>
	</detail>
	<columnFooter>
		<band height="5" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
