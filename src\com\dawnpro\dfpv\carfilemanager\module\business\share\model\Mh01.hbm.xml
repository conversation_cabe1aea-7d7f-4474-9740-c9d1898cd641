<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Mh01" table="MH_01">
        <id name="vin" type="java.lang.String">
            <column name="VIN" length="20" />
            <generator class="assigned" />
        </id>
        <property name="engineNo" type="java.lang.String">
            <column name="ENGINE_NO" length="20" />
        </property>
        <property name="model" type="java.lang.String">
            <column name="MODEL" length="20" />
        </property>
        <property name="engineType" type="java.lang.String">
            <column name="ENGINE_TYPE" length="20" />
        </property>
        <property name="color" type="java.lang.String">
            <column name="COLOR" length="50" />
        </property>
        <property name="prodDate" type="java.lang.String">
            <column name="PROD_DATE" length="10" />
        </property>
        <property name="reqSerialNo" type="java.lang.String">
                <column name="REQ_SERIAL_NO" length="54" />
        </property>
        
        <!-- 2018 12 17 -->     
         <property name="motorNo" type="java.lang.String">
            <column name="MOTOR_NO" length="20" />
        </property>
        
         <property name="motorType" type="java.lang.String">
            <column name="MOTOR_TYPE" length="20" />
        </property>          
    </class>
</hibernate-mapping>
