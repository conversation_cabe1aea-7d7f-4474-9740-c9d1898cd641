package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

/**
 * Ts02Id generated by MyEclipse Persistence Tools
 */

public class Ts02Id implements java.io.Serializable {

	// Fields

	private String matchBillCode;

	private String matchBillItem;

	// Constructors

	/** default constructor */
	public Ts02Id() {
	}

	/** full constructor */
	public Ts02Id(String matchBillCode, String matchBillItem) {
		this.matchBillCode = matchBillCode;
		this.matchBillItem = matchBillItem;
	}

	// Property accessors

	public String getMatchBillCode() {
		return this.matchBillCode;
	}

	public void setMatchBillCode(String matchBillCode) {
		this.matchBillCode = matchBillCode;
	}

	public String getMatchBillItem() {
		return this.matchBillItem;
	}

	public void setMatchBillItem(String matchBillItem) {
		this.matchBillItem = matchBillItem;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof Ts02Id))
			return false;
		Ts02Id castOther = (Ts02Id) other;

		return ((this.getMatchBillCode() == castOther.getMatchBillCode()) || (this
				.getMatchBillCode() != null
				&& castOther.getMatchBillCode() != null && this
				.getMatchBillCode().equals(castOther.getMatchBillCode())))
				&& ((this.getMatchBillItem() == castOther.getMatchBillItem()) || (this
						.getMatchBillItem() != null
						&& castOther.getMatchBillItem() != null && this
						.getMatchBillItem()
						.equals(castOther.getMatchBillItem())));
	}

	public int hashCode() {
		int result = 17;

		result = 37
				* result
				+ (getMatchBillCode() == null ? 0 : this.getMatchBillCode()
						.hashCode());
		result = 37
				* result
				+ (getMatchBillItem() == null ? 0 : this.getMatchBillItem()
						.hashCode());
		return result;
	}

}