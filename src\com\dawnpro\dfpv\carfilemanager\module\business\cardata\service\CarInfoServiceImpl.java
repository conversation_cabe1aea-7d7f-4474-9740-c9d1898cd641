package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarInfoDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.CarInfoVoidDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarInfoVoid;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhoto;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Modelver;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;
import com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceLogDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.COCPhotoService;

public class CarInfoServiceImpl implements CarInfoService{
	private CarInfoDAO dao = null;
	private CarInfoVoidDAO carInfoVoidDao=null;
	private InterfaceLogDAO interfaceLogDAO = null;
	private PaginationService paginationService = null;
	private COCPhotoService cocPhotoService = null;

	public void setDao(CarInfoDAO dao) {
		this.dao = dao;
	}
	
	public void setCarInfoVoidDao(CarInfoVoidDAO carInfoVoidDao) {
		this.carInfoVoidDao = carInfoVoidDao;
	}
	
	public void setInterfaceLogDAO(InterfaceLogDAO interfaceLogDAO) {
		this.interfaceLogDAO = interfaceLogDAO;
	}
	
	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	public void setCocPhotoService(COCPhotoService cocPhotoService) {
		this.cocPhotoService = cocPhotoService;
	}
	
	public void addCarModel(CarInfo obj) {
		try{
			this.dao.addCarModel(obj);
		}catch(DataAccessException e){
			throw new SystemException("addCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCarModel Method Error:",e);
		}
	}
	
	public void updateCarModel(CarInfo obj) {
		try{
			CarInfo gg = this.loadCarModelObj(obj.getVin());
			String type=gg.getState();
			if(type==null || type=="" || type.equals("0") || type.equals("1")){
				this.dao.updateCarModel(obj);
			}
		}catch(DataAccessException e){
			throw new SystemException("updateCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCarModel Method Error:",e);
		}
	}
	
	public void deleteCarModel(CarInfo obj) {
		try{
			this.dao.deleteCarModel(obj);
		}catch(DataAccessException e){
			throw new SystemException("deleteCarModel Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCarModel Method Error:",e);
		}
	}
	
	public void deleteCarModels(CarInfo[] obj) throws DataAccessException {
		try{
			this.dao.deleteCarModels(obj);
		}catch(DataAccessException e){
			throw new SystemException("deleteCarModels Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCarModels Method Error:",e);
		}
	}
	
	public CarInfo loadCarModelObj(String vin) {
		CarInfo gg = null;
		try{
			gg = this.dao.loadCarModelObj(vin);
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelObj Method Error:",e);
		}
		
		return gg;
	}
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException {
		List results = null;
		try{
			StringBuffer sub = new StringBuffer(30);
			String tmp = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.vin) from CarInfo g order by g.prodDate desc";
				sql = "from CarInfo g order by g.prodDate desc";
			}else{
				
				sub.append(" where ");
				if(params[0]!=null&&!params[0].equals("")){
					String[] vin = ((String)params[0]).split(",");
					StringBuffer buf = new StringBuffer();
					sub.append("(");
					for(int i = 0; i < vin.length; i++){						
						buf.append(" g.vin like '%"+vin[i].trim()+"' or ");						
					}
					sub.append(buf.substring(0, buf.length()-3));					
					sub.append(" )and ");
//					sub.append(" g.vin like '"+String.valueOf(params[0]).trim()+"' and ");
				}
				if(params[5]!=null&&!"".equals(params[5])){
					sub.append(" g.model like '%"+params[5]+"%' and ");
				}
//				if(params[1]!=null&&params[1].equals("true")){
//					sub.append(" g.cocNum is not null and ");
//				}
				if(params[1]!=null&&!params[1].equals("")&&params[2]!=null&&!params[2].equals("")){
					sub.append("  to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') >= to_date('"+String.valueOf(params[1]).trim()+"','yyyy-mm-dd') and to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') <= to_date('"+String.valueOf(params[2]).trim()+"','yyyy-mm-dd') and ");
				}else if(params[1]!=null&&!params[1].equals("")&&(params[2]==null||params[2].equals(""))){
					sub.append(" to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') >= to_date('"+String.valueOf(params[1]).trim()+"','yyyy-mm-dd') and ");
				}else if((params[1]==null||params[1].equals(""))&&(params[2]!=null&&!params[2].equals(""))){
					sub.append(" to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') <= to_date('"+String.valueOf(params[2]).trim()+"','yyyy-mm-dd') and ");
				}
				
				if(params[3]!=null&&!params[3].equals("")&&params[4]!=null&&!params[4].equals("")){
					sub.append("  to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd hh24:mi:ss') >= to_date('"+String.valueOf(params[3]).trim()+"','yyyy-mm-dd') and to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd hh24:mi:ss') <= to_date('"+String.valueOf(params[4]).trim()+"','yyyy-mm-dd') and ");
				}else if(params[3]!=null&&!params[3].equals("")&&(params[4]==null||params[4].equals(""))){
					sub.append(" to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd') >= to_date('"+String.valueOf(params[3]).trim()+"','yyyy-mm-dd') and ");
				}else if((params[3]==null||params[3].equals(""))&&(params[4]!=null&&!params[4].equals(""))){
					sub.append(" to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd') <= to_date('"+String.valueOf(params[4]).trim()+"','yyyy-mm-dd') and ");
				}
				
				//use for test
				//sub.append(" g.evercode is not null and ");
				
				tmp = sub.substring(0,sub.lastIndexOf("and"));
				
				countSql = "select count(g.vin) from CarInfo g "+tmp;
				sql = "from CarInfo g "+tmp+" order by g.time,g.vin desc";
			}
			//System.out.println("sql:"+sql);
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			//this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}
	
	public boolean isCarModelExist(String vin) {
		try{
			List<CarInfo> result = this.dao.findCarModel("from CarInfo g where g.vin=?", new Object[]{vin});
			if(result!=null && result.size()>0){
				return true;
			}else{
				return false;
			}
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("isCarModelExist Method Error:",e1);
		}
	}	
	
	public CarInfo findCarModelEffective(String vin){
		CarInfo gg = null;
		try{
			List<CarInfo> result = this.dao.findCarModel("from CarInfo g where g.vin=? and g.state=2", new Object[]{vin});
			if(result!=null&&result.size()>0){
				gg = result.get(0);
			}
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelEffective Method Error:",e1);
		}
		return gg;
	}
	
	public void updafteCarModelState(CarInfo gg) {
		try{
			this.dao.updateCarModel("update CarInfo g set g.state=? where g.vin=? ",new Object[]{gg.getState(),gg.getVin()});
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("updafteCarModelState Method Error:",e1);
		}
	}
	
	public void cancelCarModel(String vin){
		CarInfo car=this.loadCarModelObj(vin);
		CarInfoVoid cancelCar=new CarInfoVoid();
		
		cancelCar.setCocColor(car.getCocColor());
		cancelCar.setCocman(car.getCocman());
		//cancelCar.setCocNum(car.getCocNum());
		cancelCar.setColor(car.getColor());
		cancelCar.setCreator(car.getCreator());
		cancelCar.setCvercode(car.getCvercode());
		cancelCar.setEngineNo(car.getEngineNo());
		cancelCar.setEngineType(car.getEngineType());
		cancelCar.setGasman(car.getGasman());
		cancelCar.setGvercode(car.getGvercode());
		cancelCar.setModel(car.getModel());
		cancelCar.setProdDate(car.getProdDate());
		cancelCar.setRemark(car.getRemark());
		cancelCar.setState("9");
		cancelCar.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));
		//cancelCar.setVin(car.getVin());
		
		carInfoVoidDao.addCarModel(cancelCar);
		this.deleteCarModel(car);
	}

	public List<CarInfo> allCarModel() throws DataAccessException {
		try{
			List<CarInfo> result = this.dao.findCarModel("from CarInfo g");
			return result;
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("isCarModelExist Method Error:",e1);
		}
	}
	
	public List<CarInfo> findCarModel(Object[] params) throws DataAccessException {
		List<CarInfo> result = null;
		try{
			StringBuffer sub = new StringBuffer(30);
			String tmp = "";
			String countSql = null;
			String sql = null;
			sub.append(" where ");
			if(params[0]!=null&&!params[0].equals("")){
				String[] vin = ((String)params[0]).split(",");
				StringBuffer buf = new StringBuffer();
				sub.append("(");
				for(int i = 0; i < vin.length; i++){						
					buf.append(" g.vin like '%"+vin[i].trim()+"' or ");						
				}
				sub.append(buf.substring(0, buf.length()-3));					
				sub.append(" )and ");
			}
//			if(params[1]!=null&&params[1].equals("true")){
//				sub.append(" g.cocNum is not null and ");
//			}
			if(params[1]!=null&&!params[1].equals("")&&params[2]!=null&&!params[2].equals("")){
				sub.append("  to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') >= to_date('"+String.valueOf(params[1]).trim()+"','yyyy-mm-dd') and to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') <= to_date('"+String.valueOf(params[2]).trim()+"','yyyy-mm-dd') and ");
			}else if(params[1]!=null&&!params[1].equals("")&&(params[2]==null||params[2].equals(""))){
				sub.append(" to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') = to_date('"+String.valueOf(params[1]).trim()+"','yyyy-mm-dd') and ");
			}else if((params[1]==null||params[1].equals(""))&&(params[2]!=null&&!params[2].equals(""))){
				sub.append(" to_date(substr(g.prodDate,0,10),'yyyy-MM-dd') = to_date('"+String.valueOf(params[2]).trim()+"','yyyy-mm-dd') and ");
			}
			
			if(params[3]!=null&&!params[3].equals("")&&params[4]!=null&&!params[4].equals("")){
				sub.append("  to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd hh24:mi:ss') >= to_date('"+String.valueOf(params[3]).trim()+"','yyyy-mm-dd') and to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd hh24:mi:ss') <= to_date('"+String.valueOf(params[4]).trim()+"','yyyy-mm-dd') and ");
			}else if(params[3]!=null&&!params[3].equals("")&&(params[4]==null||params[4].equals(""))){
				sub.append(" to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd') = to_date('"+String.valueOf(params[3]).trim()+"','yyyy-mm-dd') and ");
			}else if((params[3]==null||params[3].equals(""))&&(params[4]!=null&&!params[4].equals(""))){
				sub.append(" to_date(substr(g.cocPrintTime,0,10),'yyyy-MM-dd') = to_date('"+String.valueOf(params[4]).trim()+"','yyyy-mm-dd') and ");
			}
			if(params[5]!=null&&!"".equals(params[5])){
				sub.append(" g.model like '%"+params[5]+"%' and ");
			}
			//System.out.println("sub:"+sub);
			tmp = sub.substring(0,sub.lastIndexOf("and"));
			
			sql = "from CarInfo g "+tmp+" order by g.cocNum,g.vin desc";
			result = this.dao.findCarModel(sql);
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("findCarModel Method Error:",e1);
		}
		
		return result;
	}

	public void updateModelVersion(CarInfo[] carInfos){
		List<CarInfo> result = null;
		List<?> versions = null;
		CarInfo cafInfo = null;
		Modelver version = null;
		CocPhoto photo = null;
		try{
			StringBuffer sql = new StringBuffer();
			sql.append("from Modelver v where ");
			for(int i=0;i<carInfos.length;i++){
				sql.append("v.id.carmodel ='"+carInfos[i].getModel()+"'");
				if(i<carInfos.length-1){
					sql.append(" or ");
				}
			}
			versions = this.interfaceLogDAO.findData(sql.toString());
			int size = versions.size();		
			if(versions!=null&&versions.size()>0){
				for(int i=0;i<carInfos.length;i++){
					cafInfo = (CarInfo)carInfos[i];
					for(int j=0;j<size;j++){
						version = (Modelver)versions.get(j);
//						if(cafInfo.getModel().equals(version.getId().getCarmodel().trim())
//								&& cafInfo.getFactory().equals(version.getId().getFactory().trim())){//20160611
//								
//								cafInfo.setGvercode(version.getGver1());
//								
//								cafInfo.setCvercode(version.getCver1());
//								
//								cafInfo.setZvercode(version.getZver1());
//
//							break;
//						}
						if(cafInfo.getModel().equals(version.getId().getCarmodel().trim())){//20170406
							
							cafInfo.setGvercode(version.getGver1());
							cafInfo.setZvercode(version.getZver1());
							cafInfo.setEvercode(version.getEver1());
							if(cafInfo.getFactory().equals(version.getId().getFactory().trim())){
								cafInfo.setCvercode(version.getCver1());
							}
							break;
						}
					}
				}	
			}
			
			List<CocPhoto> photovercodeList = this.cocPhotoService.allEffectCarModelPhoto();
			if(photovercodeList!=null){
				size = photovercodeList.size();
				for(int i=0;i<carInfos.length;i++){
					for(int j=0;j<size;j++){
						photo = photovercodeList.get(j);
//						System.out.println("aa:"+carInfos[i].getMaterialNo().substring(10,12)+" e1:"+carInfos[i].getModel().equals(photo.getId().getModel())+" e2:"+carInfos[i].getMaterialNo().substring(10,12).equals(photo.getId().getFilename().substring(10,12)));
//						System.out.println("d1:"+carInfos[i].getMaterialNo().substring(10,12)+" d2:"+photo.getId().getFilename().substring(10,12));
//						
//						if(carInfos[i].getModel().equals(photo.getId().getModel())
//								&&carInfos[i].getMaterialNo().substring(10,12).equals(photo.getId().getFilename().substring(10,12))){
//							
//							carInfos[i].setPvercode(photo.getId().getVercode());
//							
//							break;
//						}
						if(carInfos[i].getModel().equals(photo.getId().getModel())
								&&(photo.getId().getFilename().indexOf(carInfos[i].getMaterialNo()) != -1)){//20170406
							
							carInfos[i].setPvercode(photo.getId().getVercode());
							
							break;
						}
					}
				}
			}

			
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("findCarModel Method Error:",e1);
		}
	}
	
	public boolean isStateT(String vin) throws DataAccessException{
		List<CarInfo> result = null;
		try{
			StringBuffer sql = new StringBuffer();
			sql.append("from CarInfo v where v.vin='"+vin+"'");
			result = this.interfaceLogDAO.findData(sql.toString());
			CarInfo gg = result.get(0);
			if("T".equals(gg.getFuelupload())||"T".equals(gg.getFuelmodified())||"T".equals(gg.getFueldelay())){
				return true;
				}
			else{
				return false;
				}
		}
		catch(DataAccessException e){
			throw e;
		}
	}

	public List<CarInfo> findCarCustom(String sql) throws DataAccessException {
		try{			
			List<CarInfo> result = this.dao.findCarModel(sql);
			return result;
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("isCarModelExist Method Error:",e1);
		}
	}	
}
