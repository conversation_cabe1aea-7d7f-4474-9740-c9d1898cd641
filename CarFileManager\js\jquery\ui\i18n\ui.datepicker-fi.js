/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>� (<EMAIL>). */
jQuery(function($){
    $.datepicker.regional['fi'] = {
		closeText: '<PERSON><PERSON>',
		prevText: '&laquo;<PERSON><PERSON><PERSON>',
		nextText: '<PERSON><PERSON><PERSON>&raquo;',
		currentText: 'T&auml;n&auml;&auml;n',
        monthNames: ['<PERSON><PERSON><PERSON><PERSON>','He<PERSON>ikuu','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>kuu','Toukokuu','Kes&auml;kuu',
        'Hein&auml;kuu','<PERSON>oku<PERSON>','<PERSON><PERSON><PERSON>ku<PERSON>','<PERSON>akuu','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
        monthNamesShort: ['<PERSON><PERSON>','He<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>uko','Kes&auml;',
        'Hein&auml;','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','Ma','T<PERSON>','<PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>'],
		dayNames: ['<PERSON>nu<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','Tiistai','Keskiviikko','Torstai','Perjantai','Lauantai'],
		dayNamesMin: ['Su','Ma','Ti','Ke','To','Pe','La'],
        dateFormat: 'dd.mm.yy', firstDay: 1,
		isRTL: false};
    $.datepicker.setDefaults($.datepicker.regional['fi']);
});
