/**
 * 
 */
package com.dawnpro.dfpv.carfilemanager.module.business.share.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDefine;

/**
 * <AUTHOR>
 *
 */
public interface InterfaceDefineDAO {
	
	public void addInterfaceDefine(InterfaceDefine def);
	
	public void deleteInterfaceDefine(InterfaceDefine def);
	
	public void updateInterfaceDefine(InterfaceDefine def);
	
	public Object loadInterfaceDefine(Class cla, String id);
	
	public List findData(String sql, Object[] params);

}
