<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bm05" table="BM_05" lazy="false">
        <id name="id" type="java.lang.Long">
            <column name="ID" precision="8" scale="0" />
             <generator class="sequence">
              <param name="sequence">B_BM_05</param>
         	</generator>
        </id>
        <property name="pmodel" type="java.lang.String">
            <column name="PMODEL_CODE" length="20" />
        </property>
        <property name="modelName" type="java.lang.String">
            <column name="MODEL_NAME" length="40" />
        </property>
        <property name="pmodelBrand" type="java.lang.String">
            <column name="PMODEL_BRAND" length="40" />
        </property>
        <property name="pmodelFuelType" type="java.lang.String">
            <column name="PMODEL_FUEL_TYPE" length="20" />
        </property>
        <property name="pmodelOutlineLength" type="java.lang.String">
            <column name="PMODEL_OUTLINE_LENGTH" length="10" />
        </property>
        <property name="pmodelOutlineWidth" type="java.lang.String">
            <column name="PMODEL_OUTLINE_WIDTH" length="10" />
        </property>
        <property name="pmodelOutlineHeight" type="java.lang.String">
            <column name="PMODEL_OUTLINE_HEIGHT" length="10" />
        </property>
        <property name="pmodelAxleNum" type="java.lang.String">
            <column name="PMODEL_AXLE_NUM" length="10" />
        </property>
        <property name="pmodelAxleDistance" type="java.lang.String">
            <column name="PMODEL_AXLE_DISTANCE" length="10" />
        </property>
        <property name="pmodelTypeNum" type="java.lang.String">
            <column name="PMODEL_TYPE_NUM" length="10" />
        </property>
        <property name="pmodelWheeltrackFront" type="java.lang.String">
            <column name="PMODEL_WHEELTRACK_FRONT" length="10" />
        </property>
        <property name="pmodelWheeltrackBehind" type="java.lang.String">
            <column name="PMODEL_WHEELTRACK_BEHIND" length="10" />
        </property>
        <property name="pmodelDriveMode" type="java.lang.String">
            <column name="PMODEL_DRIVE_MODE" length="20" />
        </property>
        <property name="pmodelTotalMass" type="java.lang.String">
            <column name="PMODEL_TOTAL_MASS" length="10" />
        </property>
        <property name="pmodelShippingMass" type="java.lang.String">
            <column name="PMODEL_SHIPPING_MASS" length="10" />
        </property>
        <property name="pmodelSeatingCapacity" type="java.lang.String">
            <column name="PMODEL_SEATING_CAPACITY" length="10" />
        </property>
        <property name="pmodelEngineType" type="java.lang.String">
            <column name="PMODEL_ENGINE_TYPE" length="40" />
        </property>
        <property name="pmodelEngineDisp" type="java.lang.String">
            <column name="PMODEL_ENGINE_DISP" length="40" />
        </property>
        <property name="pmodelRatedPower" type="java.lang.String">
            <column name="PMODEL_RATED_POWER" length="40" />
        </property>
        <property name="pmodelMadeCountry" type="java.lang.String">
            <column name="PMODEL_MADE_COUNTRY" length="40" />
        </property>
        <property name="pmodelDesc" type="java.lang.String">
            <column name="PMODEL_DESC" length="128" />
        </property>
        <property name="command" type="java.lang.String">
            <column name="COMMAND" length="1" />
        </property>
        <property name="reqSerialNo" type="java.lang.String">
            <column name="REQ_SERIAL_NO" length="54" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="flag" type="java.lang.String">
            <column name="flag" length="4" />
        </property>
        <property name="zdjgl" type="java.lang.String">
            <column name="ZDJGL" length="50" />
        </property>        
        <property name="goodsName" type="java.lang.String">
            <column name="GOODS_NAME" length="50" />
        </property>        
        <property name="driveMotorModel" type="java.lang.String">
            <column name="DRIVE_MOTOR_MODEL" length="50" />
        </property>        
        <property name="driveMotorPeakPower" type="java.lang.String">
            <column name="DRIVE_MOTOR_PEAKPOWER" length="50" />
        </property>        
        <property name="powerBatteryRateVoltage" type="java.lang.String">
            <column name="POWER_BATTERY_RATEVOLTAGE" length="50" />
        </property>        
        <property name="powerBatteryRatedCapacity" type="java.lang.String">
            <column name="POWE_RBATTERY_RATEDCAPACITY" length="50" />
        </property>
        <!-- 20180815 -->        
        <property name="vinpre8" type="java.lang.String">
            <column name="VINPRE8" length="50" />
        </property>
        
        <property name="mpflag" type="java.lang.String">
            <column name="MPFLAG" length="4" />
        </property>
        
        <property name="axleLoad" type="java.lang.String">
            <column name="AXLE_LOAD" length="20" />
        </property>  
        
        <property name="state" type="java.lang.String">
            <column name="STATE" length="4" />
        </property> 
        
        <property name="sendTime" type="java.lang.String">
            <column name="SEND_TIME" length="19" />
        </property> 
        <property name="vercode" type="java.lang.String">
            <column name="VERCODE" length="15" />
        </property> 
            
    </class>
</hibernate-mapping>
