package com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl;

import java.io.Serializable;
import java.util.List;

import org.hibernate.ObjectNotFoundException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuellabeltemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.GasVerDAO;

public class GasVerDAOImpl extends GenericHibernateDAOImpl<Fuellabeltemplate> implements GasVerDAO {


	public void addGasVer(Fuellabeltemplate obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addGasVer Method Error:",e);
		}

	}

	public void deleteGasVer(Serializable id) {
		try{
			this.delete(this.loadGasVer(id));
		}catch(Exception e){
			throw new DataAccessException("deleteGasVer Method Error:",e);
		}
	}

	public void deleteGasVer(Fuellabeltemplate[] id) {
		try{
			if (id != null){
				//for(int i =0 ; i < id.length; i++){
				//	deleteGasVer(id[i]);
				//}
				this.deleteBatch(id);
			}
		}catch(Exception e){
			throw new DataAccessException("deleteGasVer Method Error:",e);
		}

	}

	public List<Fuellabeltemplate> findGasVer(String hql) {
		List<Fuellabeltemplate> list = null;
		try{
			list = this.find(hql);
		}catch(Exception e){
			throw new DataAccessException("findGasVer Method Error:",e);
		}
		return list;
	}

	public List<Fuellabeltemplate> findGasVer(String hql, Object[] params) {
		List<Fuellabeltemplate> list = null;
		try{
			list = this.find(hql, params);
		}catch(Exception e){
			throw new DataAccessException("findGasVer Method Error:",e);
		}
		return list;
	}

	public Fuellabeltemplate loadGasVer(Serializable id) {
		Fuellabeltemplate obj = null;
		try{
			obj = (Fuellabeltemplate)this.load(Fuellabeltemplate.class, id);
		}catch(ObjectNotFoundException e){
			
		}catch(Exception e){
			//throw new DataAccessException("loadGasVer Method Error:",e);
		}
		return obj;
	}

	public void updateGasVer(Fuellabeltemplate obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateGasVer Method Error:",e);
		}
	}

	
	public Fuellabeltemplate findMaxGasVerBySccx(String c1) {
		Fuellabeltemplate obj = null;
		try{
			List<Fuellabeltemplate> results = null;
			String sql = "from Fuellabeltemplate t where t.id.slcx=? order by substr(t.id.vercode,length(t.id.vercode)-1,2) desc";
			Object[] params = new Object[]{c1};
			results = this.find(sql,params);
			if(results != null && results.size()>0)
				obj = results.get(0);
		}catch(Exception e){
			throw new DataAccessException("findMaxGasVerBySccx Method Error:",e);
		}
		return obj;
	}

	public void addGasVer(Fuellabeltemplate[] obj) {
		try{
			this.addBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("addGasVer Method Error:",e);
		}
	}

	public void updateGasVer(Fuellabeltemplate[] obj) {
		try{
			this.updateBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("updateGasVer Method Error:",e);
		}
	}
}
