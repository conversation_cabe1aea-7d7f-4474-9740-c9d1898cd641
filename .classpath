<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="con" path="com.genuitec.eclipse.j2eedt.core.J2EE14_CONTAINER"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/Qrcode_encoder.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/antlr-2.7.6.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/asm-2.2.3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/asm-commons-2.2.3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/asm-util-2.2.3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/aspectjrt.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/aspectjweaver.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/avalon-framework-4.2.0.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/barcode4j.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/cglib-nodep-2.1_3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/classes12.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/com.ibm.mq.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/com.ibm.mqjms.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-beanutils.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-collections-3.2.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-compress-1.1.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-fileupload-1.2.1.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-io-1.4.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-lang-2.4.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/connector.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/dhbcore.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/dom4j-1.6.1.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/ehcache-1.3.0.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/ezmorph-1.0.4.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/freemarker-2.3.8.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/geronimo-j2ee-management_1.0_spec-1.0.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/geronimo-jms_1.1_spec-1.0.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/groovy-all-1.5.5.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/hibernate3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/jasperreports-3.6.1.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/jaxen-1.1-beta-7.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/jcommon-1.0.13.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/json-lib-2.2.1-jdk15.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/jsonplugin-0.32.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/jta.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/jxl.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/log4j-1.2.14.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/metadata-extractor-2.3.1.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/ognl-2.6.11.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/plugin.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/proxool-0.9.0RC3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/rt.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/servlet-api.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-aop.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-beans.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-context.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-core.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-hibernate3.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-jdbc.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-tx.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring-web.jar" sourcepath="F:/study/source/spring-framework-3.2/spring-context/src">
		<attributes>
			<attribute name="source_encoding" value="UTF-8"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/spring.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/struts2-core-********.jar" sourcepath="F:/study/source/struts-2.5.20-src.zip">
		<attributes>
			<attribute name="source_encoding" value="UTF-8"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/struts2-spring-plugin-********.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/xwork-2.0.4.jar"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/quartz-2.0.2.jar"/>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v7.0"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-digester.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/commons-logging-1.1.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/iText-2.1.0.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/iTextAsian.jar"/>
	<classpathentry kind="lib" path="CarFileManager/WEB-INF/lib/quartz-1.6.5.jar"/>
	<classpathentry kind="output" path="CarFileManager/WEB-INF/classes"/>
</classpath>
