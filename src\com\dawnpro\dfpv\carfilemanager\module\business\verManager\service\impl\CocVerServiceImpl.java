package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.CocVerDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.CocVerService;

public class CocVerServiceImpl implements CocVerService {
	private CocVerDAO cocVerDAO = null;

	private PaginationService paginationService = null;
	

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public CocVerDAO getCocVerDAO() {
		return cocVerDAO;
	}

	public void setCocVerDAO(CocVerDAO cocVerDAO) {
		this.cocVerDAO = cocVerDAO;
	}
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException{
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id.c1) from Cartypetemplate g";
				sql = "from Cartypetemplate g ";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " and g.id.c1 like ('"+String.valueOf(params[0])+"%') ";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" and g.state = '"+params[1]+"' ";
				}
				//20160610 lmc
				if(params[2]!=null&&!params[2].equals("")){
					sub = sub+" and g.factory = '"+params[2]+"' ";
				}
				
				countSql = "select count(g.id.c1) from Cartypetemplate g where 1=1 "+sub;
				sql = "from Cartypetemplate g where 1=1 "+sub;
			}
			sql += " order by g.createdate desc,g.id.c1 desc,g.effecttime desc";
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		
		
		return results;
	}

	public void addCocVer(Cartypetemplate obj) throws DataAccessException {
		try{
			this.cocVerDAO.addCocVer(obj);
		}catch(DataAccessException e){
			throw new SystemException("addCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCocVer Method Error:",e);
		}
		
	}

	public void deleteCocVer(Cartypetemplate[] id) throws DataAccessException {
		try{
			this.cocVerDAO.deleteCocVer(id);
		}catch(DataAccessException e){
			throw new SystemException("deleteCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deleteCocVer Method Error:",e);
		}
	}

	public Cartypetemplate loadCocVer(CartypetemplateId id)
			throws DataAccessException {
		Cartypetemplate gg = null;
		try{
			gg = this.cocVerDAO.loadCocVer(id);
		}catch(DataAccessException e){
			throw new SystemException("loadCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCocVer Method Error:",e);
		}
		return gg;
	}

	public void updateCocVer(Cartypetemplate obj) throws DataAccessException {
		try{
			this.cocVerDAO.updateCocVer(obj);
		}catch(DataAccessException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}
	}

	public Cartypetemplate findMaxCocVerBySccx(String c1) throws DataAccessException {
		Cartypetemplate gg = null;
		try{
			gg = this.cocVerDAO.findMaxCocVerBySccx(c1);
		}catch(DataAccessException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}
		return gg;
	}

	public List<Cartypetemplate> findCocVer(Object[] params){
		List<Cartypetemplate> list = null;
		try{
			String hql = "from Cartypetemplate g where 1=1";
			String sub = "";
			if(params != null && params.length>0)
			{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " and g.id.c1 like ('"+String.valueOf(params[0])+"%') ";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" and g.state = '"+params[1]+"' ";
				}
			}
			sub = sub+" order by g.id.c1 desc,g.effecttime desc";
			list = this.cocVerDAO.findCocVer(hql+sub);
		}catch(DataAccessException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findMaxCocVerBySccx Method Error:",e);
		}
		return list;
	}

	public List<Cartypetemplate> findCocVer(String hql)throws DataAccessException {
		List<Cartypetemplate> list = null;
		try{
			list = this.cocVerDAO.findCocVer(hql);
		}catch(DataAccessException e){
			throw new SystemException("findCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("findCocVer Method Error:",e);
		}
		return list;
	}

	public void addCocVer(Cartypetemplate[] objs) throws DataAccessException {
		try{
			this.cocVerDAO.addCocVer(objs);
		}catch(DataAccessException e){
			throw new SystemException("addCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addCocVer Method Error:",e);
		}
	}

	public void updateCocVer(Cartypetemplate[] objs) throws DataAccessException {
		try{
			this.cocVerDAO.updateCocVer(objs);
		}catch(DataAccessException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updateCocVer Method Error:",e);
		}
	}
	
}
