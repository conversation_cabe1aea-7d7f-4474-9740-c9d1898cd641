<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#export").attr("disabled", true);
	
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });

	$("#query").click(function(){
		var qdxcx = $('#qdxcx').val();
		location.href="typicalityNeutral.action?qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
	});
	
	$("#create").click(function() {      	  		
   		type = "add";
   	 	$('#typicality_neutral_dialog').data('title.dialog', '新增').dialog('open');
 				
	});
	$('#export').click(function() {
		var qdxcx = $('#qdxcx').val();
		location.href="business/typicalityNeutral!exportTypicalityCarModel.action?qdxcx="+encodeURI(encodeURI(qdxcx));
	});
	
	$("#modelSelect").click(function() {      	  		
   		/*var sReturn=window.showModalDialog('<%=path%>/business/carModelSelect.action?callFunName=test&rnd='+Math.random(),'aa','resizable=yes,scrollbars=yes,toolbar=no,menubar=no,fullscreen=no,status=no');
   		
   		if(sReturn!=null){
	   		if(sReturn.indexOf(",")!=-1){
	   			alert("只能选择一个车型！");
	   			return;
	   		}   		
   			var obj=$("#dxcx", document.forms['createForm']);
   			obj.val(sReturn);
   		}*/
   		createTree();
   		var dialogObj = $('#tree_dialog');
   		dialogObj.find("#flg").val('1');
   		dialogObj.data('title.dialog', '车型选择').dialog('open');				
	});
	
	$("#modelSelect1").click(function() {      	  		
   		/*var sReturn=window.showModalDialog('<%=path%>/business/carModelSelect.action?callFunName=test&rnd='+Math.random(),'aa','resizable=yes,scrollbars=yes,toolbar=no,menubar=no,fullscreen=no,status=no');
   		
   		if(sReturn!=null){  		
   			var obj=$("#zxcx", document.forms['createForm']);
   			obj.val(sReturn);
   		}*/
   		createTree();
   		var dialogObj = $('#tree_dialog');
   		dialogObj.find("#flg").val('2');
   		dialogObj.data('title.dialog', '车型选择').dialog('open');   						
	});
	
	function selectCarModel(data, flg){
		if(data!=null){
			if(flg=='1'){
		   		if(data.indexOf(",")!=-1){
		   	   	 	messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:只能选择一个车型！');
		   	   		messageObj.dialog('open');
		   			return false;
		   		}		
	   			var obj=$("#dxcx", document.forms['createForm']);
	   			obj.val(data);		
			}else if(flg=='2'){	   		  		
	   			var obj=$("#zxcx", document.forms['createForm']);
	   			obj.val(data);
	   		}
   		}
   		return true;		
	}	
	
	$("#tree_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 400,
		height: 500,
		modal: true,
		buttons: {		
			'取消': function() {				
				$(this).dialog('close');
			},
			'确定': function() {		
				if(selectCarModel(getAllLeaftValue() ,$(this).find("#flg").val())){		
					$(this).dialog('close');
				}
			}			
		},
		close: function() {
			$(this).find("#treeboxbox_tree").text('');			
		}				
	});	

	$("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
       	var color = "";
       	var zxcx = "";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
   		if(index==1){
   			type = "update";   			
   			jQuery.ajax({
	            url: 'business/typicalityNeutral!getTypicalityNeutral.action',		           
	            data: {'id' : id}, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                alert("系统错误，请与管理员联系！");
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#typicality_neutral_dialog');
					setDialogValue(dialogObj,carObj);
	       	    	dialogObj.data('title.dialog', '修改').dialog('open');
					
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});

	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
       	var state="";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
         		index++;					
				if(id==""){
					id = this.value;
					info = this.parentElement.parentElement.children[1].innerText;
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+this.parentElement.parentElement.children[1].innerText;
				}				
             }
      	});
		
   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
 			type = "delete";
  	   		messageObj = $('#operate_dialog');
  	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
  	   		messageObj.dialog('open');
  	   		messageObj.find('#ids').val(id);
   	   		
   	   	}
	
	});
	
	
	$("#typicality_neutral_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 500,
		height: 400,
		modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#typicality_neutral_dialog');
					allFields = $([]).add(parent.find('#dxcx')).add(parent.find('#zxcx'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#typicality_neutral_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					if(type=="add"){
						jQuery.ajax({
				            url: 'business/typicalityNeutral!isTypicalityNeutralExist.action',
					        data: {'dxcx' : $(this).find('#dxcx').val()},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#typicality_neutral_dialog');
					            if(json2Bean(data).json=="true"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'典型车型:['+dialog.find('#dxcx').val()+'] 已经存在不能新增！');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].action="business/typicalityNeutral!addTypicalityNeutral.action?currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						var currentPage=$('#currentPage_temp').val();
						$(this).find('#createForm')[0].action="<%=path%>/business/typicalityNeutral!updateTypicalityNeutral.action?currentPage="+currentPage;
						$(this).find('#createForm')[0].submit();
					}
				}
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			clear($(this));
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			type = null;
			//$('#typicality_neutral_dialog').find('#vin').attr('readonly',false);
		}
	});

	$("#car_color_mapping_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 500,height: 400,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
		}
	});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				var parent = $('#typicality_neutral_dialog');
				updateTips($(parent).find('#validateTips'),'');
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/typicalityNeutral!deleteTypicalityNeutral.action?currentPage="+currentPage;
					formObj[0].submit();
				}
			}
		},
		close: function() {
			var parent = $('#typicality_neutral_dialog');
			updateTips($(parent).find('#validateTips'),'');
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
    function display(id,title){
		jQuery.ajax({
	        url: 'business/typicalityNeutral!getTypicalityNeutral.action',		           
	        data: {'id' : id}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	            var dialogObj = $('#car_color_mapping_display_dialog');
				setDialogValue(dialogObj,carObj);
				dialogObj.data('title.dialog', title).dialog('open');
	        }
	    });

		return false;
	}
	
	$(".display").each(function(i){
		  $(this).click(function() {
			var id=  $(this).attr('value');	  	
			display(id,"查看窗口");
		 });
	  });	

    function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#id').val(jsonObj.id);
		dialogObj.find('#dxcx').val(jsonObj.dxcx);
		dialogObj.find('#zxcx').val(jsonObj.zxcx);
	}
    
	function clear(dialogObj){		
		dialogObj.find('#id').val("");
		dialogObj.find('#dxcx').val("");
		dialogObj.find('#zxcx').val("");
	}

	function validate(parent){
		//return true;
		var obj = $(parent).find('#dxcx');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'典型车型必填,并且长度不能超过20！');
			obj.focus();			
			return false;
		}
		obj = $(parent).find('#zxcx');
		if(!checkLength(obj,1,512)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'中性车型必填，并且长度不能超过512！');
			obj.focus();			
			return false;
		}
		
		if($(parent).find('#zxcx').val().indexOf($(parent).find('#dxcx').val())!=-1){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'典型车型不能包含在中性车型里面！');
			return false;
		}
		
		return true;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var qdxcx = $('#qdxcx').val();
			location.href="typicalityNeutral.action?currentPage="+$('#jump').val()+"&qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qdxcx = $('#qdxcx').val();
				location.href=$(this).attr('value')+"&qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
		 });
	  });
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan=""><p>时间:<input type="checkbox" id="cx" name="cx" class="checkbox ui-widget-content ui-corner-all"  size="18"  />
				车型:<input type="checkbox" id="qdxcx" name="qdxcx" class="text ui-widget-content ui-corner-all"  size="18"  />
				发动机:<input type="checkbox" id="fdj" name="qdxcx" class="text ui-widget-content ui-corner-all"  size="18"  />
				</td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>统计</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="6%">典型车型</th>
			    <th width="7%">中性车型</th>
			    <th width="7%">创建人</th>
			    <th width="7%">创建时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.typicalityNeutralPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id" />' ></td>
			  		<td><s:property value="dxcx" /></td>
			  		<td><s:property value="zxcx" /></td>			  		
			  		<td><s:property value="creator" /></td>	
			  		<td><s:date  name="createdate" format="yyyy-MM-dd hh:mm:ss" /></td>			  		
			  		<td><a class='display' onclick="return false;" href='#' value='<s:property value="id" />'>查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="typicalityNeutral.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="typicalityNeutral.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="typicalityNeutral.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="typicalityNeutral.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<!--<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  
			  	<td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              
			  	<td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
				-->
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="typicality_neutral_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>典型车型:</label></td>
				<td><input type="text" id="dxcx" name="dxcx"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20"/>&nbsp;
				<button name="modelSelect" id="modelSelect" class="ui-button ui-state-default ui-corner-all" style="position:static">选择</button>
					</td>
	    	</tr>
	    	<tr>
				<td><label><P>中性车型:</label></td>
				<td>
					<input type="text" id="zxcx" name="zxcx" class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
					<button name="modelSelect1" id="modelSelect1" class="ui-button ui-state-default ui-corner-all" style="position:static" >选择</button>					
				</td>
			</tr>			
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="car_color_mapping_display_dialog" title="查看窗口" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>典型车型:</label></td>
				<td><input type="text" id="dxcx" name="dxcx" class="text ui-widget-content ui-corner-all" size="20" maxlength="20"/>		
					</td>
	    	</tr>
	    	<tr>
				<td><label><P>中性车型:</label></td>
				<td>
					<input type="text" id="zxcx" name="zxcx" class="text ui-widget-content ui-corner-all" size="35"  />					
				</td>
			</tr>			
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="tree_dialog" title="车型选择" style="display:none">
	<form id="operateForm" method='post'>
	<table>
		<tr>
			<td width="100%">
				<div id="treeboxbox_tree" style="width:446; height:300;background-color:#f5f5f5;border :1px solid Silver; overflow:auto; "/>
			</td>
		</tr>
		<input type="hidden" name="flg" id="flg"/>		
	</table>	
	</form>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<script type="text/javascript">
function showInfo(c1,zxcx,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}
	function doLog(str){			
		//var log = document.getElementById("logarea");
		//log.innerHTML = log.innerHTML+str+"</br>"
		//log.scrollTop = log.scrollHeight;
	}
	var tree;
	function getAllLeaftValue(){
		var valuesTemp=tree.getAllChecked();
		if(valuesTemp!=null&&valuesTemp!=""){
			var values=valuesTemp.split(",");
			var result="";
			for(var i=0;i<values.length;i++){
				var value=values[i];
				if(value.indexOf("##_p_")!=0){
					if(result!="")
						result+=",";
					result+=value.split(".")[1];
				}
			}
			return result;
		}
		return "";
	}
	function tonclick(id){			
		doLog("Item "+tree.getItemText(id)+" was selected");			
	};
	function tondblclick(id){			
		doLog("Item "+tree.getItemText(id)+" was doubleclicked");			
	};			
	function tondrag(id,id2){			
		return confirm("Do you want to move node "+tree.getItemText(id)+" to item "+tree.getItemText(id2)+"?");			
	};
	function tonopen(id,mode){			
		return confirm("Do you want to "+(mode>0?"close":"open")+" node "+tree.getItemText(id)+"?");			
	};
	
	function toncheck(id,state){			
		doLog("Item "+tree.getItemText(id)+" was " +((state)?"checked":"unchecked"));		
	};		
	function createTree(){
		tree=new dhtmlXTreeObject("treeboxbox_tree","100%","100%",0);
		tree.setImagePath("<%=path%>/images/csh_yellowbooks/");
		tree.enableCheckBoxes(1);
		tree.enableDragAndDrop(1);
		
		tree.enableThreeStateCheckboxes(true);
		tree.setOnDblClickHandler(tondblclick);
		tree.setDragHandler(tondrag);
		tree.loadXML('business/carModelSelect!getCarModel.action?type=<%=request.getAttribute("type")%>');		
	}	
</script>
</body>
</html>