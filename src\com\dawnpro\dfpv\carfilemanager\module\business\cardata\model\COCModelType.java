package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;


import java.util.Date;

/**
 * Lpzinfo entity. <AUTHOR> Persistence Tools
 */

public class COCModelType implements java.io.Serializable {

	// Fields
	private String id;
	private String model;				
	private String modelType;			
	private String engineNum;	
	private String motorNum;

	private String cocPrintType;

	private String creator;
	private Date createdate;
	private String remark;

	// Constructors

	/** default constructor */
	public COCModelType() {
	}
	
	

	public COCModelType(String id, String model, String modelType, String engineNum, String motorNum,
			String cocPrintType, String creator, Date createdate, String remark) {
		super();
		this.id = id;
		this.model = model;
		this.modelType = modelType;
		this.engineNum = engineNum;
		this.motorNum = motorNum;
		this.cocPrintType = cocPrintType;
		this.creator = creator;
		this.createdate = createdate;
		this.remark = remark;
	}



	/** minimal constructor */

	

	// Property accessors

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getModelType() {
		return modelType;
	}

	public void setModelType(String modelType) {
		this.modelType = modelType;
	}

	public String getEngineNum() {
		return engineNum;
	}

	public void setEngineNum(String engineNum) {
		this.engineNum = engineNum;
	}

	public String getMotorNum() {
		return motorNum;
	}

	public void setMotorNum(String motorNum) {
		this.motorNum = motorNum;
	}

	public String getCocPrintType() {
		return cocPrintType;
	}

	public void setCocPrintType(String cocPrintType) {
		this.cocPrintType = cocPrintType;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreatedate() {
		return createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	

}