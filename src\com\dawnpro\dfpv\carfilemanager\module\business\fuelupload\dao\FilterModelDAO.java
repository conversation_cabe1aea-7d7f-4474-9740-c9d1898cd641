package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao;

import java.io.Serializable;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FilterModel;

public interface FilterModelDAO {
	public void addFilterModel(FilterModel obj);
	public void updateFilterModel(FilterModel obj);
	public void deleteFilterModel(Serializable id);
	public void deleteFilterModel(Serializable[] id);
	public List<FilterModel> findFilterModel(String sql);
	public List<FilterModel> findFilterModel(String sql, Object[] params);
	public FilterModel loadFilterModel(Serializable id);
}
