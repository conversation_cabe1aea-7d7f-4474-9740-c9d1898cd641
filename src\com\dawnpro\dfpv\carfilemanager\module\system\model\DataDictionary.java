package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * DataDictionary entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class DataDictionary implements java.io.Serializable {

	// Fields

	private DataDictionaryId id;
	private String creator;
	private String time;

	// Constructors

	/** default constructor */
	public DataDictionary() {
	}

	/** minimal constructor */
	public DataDictionary(DataDictionaryId id) {
		this.id = id;
	}

	/** full constructor */
	public DataDictionary(DataDictionaryId id, String creator, String time) {
		this.id = id;
		this.creator = creator;
		this.time = time;
	}

	// Property accessors

	public DataDictionaryId getId() {
		return this.id;
	}

	public void setId(DataDictionaryId id) {
		this.id = id;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

}