<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceData" table="INTERFACE_LOG" >
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDataId">
            <key-property name="tradeCode" type="java.lang.String">
                <column name="TRADE_CODE" length="6" />
            </key-property>
            <key-property name="reqSerialNo" type="java.lang.String">
                <column name="REQ_SERIAL_NO" length="54" />
            </key-property>
            <key-property name="createTime" type="java.lang.String">
            	<column name="CREATE_TIME" length="23" />
        	</key-property>
        </composite-id>
        <property name="tradeTime" type="java.lang.String">
            <column name="TRADE_TIME" length="23" />
        </property>
        <property name="requesterId" type="java.lang.String">
            <column name="REQUESTER_ID" length="6" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="4" />
        </property>
        <property name="type" type="java.lang.String">
            <column name="TYPE" length="4" />
        </property>
        <property name="errInfo" type="java.lang.String">
            <column name="ERR_INFO" length="1000" />
        </property>
        <property name="path" type="java.lang.String">
            <column name="path" length="1000" />
        </property>
    </class>
</hibernate-mapping>
