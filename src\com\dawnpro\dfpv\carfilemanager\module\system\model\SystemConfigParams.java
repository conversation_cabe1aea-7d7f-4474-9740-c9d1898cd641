package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * SystemConfig entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SystemConfigParams implements java.io.Serializable {

	// Fields
	private Integer id;
	private String name;
	private String value;
	private String min;
	private String max;
	private String dicname;
	private String year;
	private String state;
	
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}
	
	public String getYear() {
		if(this.name!=null && !"".equals(this.name)){
			return this.name.split("_")[2];
		}else{
			return null;
		}
	}

	public void setYear(String year) {
		this.year = year;
	}
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getDicname() {
		return dicname;
	}

	public void setDicname(String dicname) {
		this.dicname = dicname;
	}

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}

	public String getMax() {
		return max;
	}

	public void setMax(String max) {
		this.max = max;
	}

	/** default constructor */
	public SystemConfigParams() {
	}

	/** minimal constructor */
	public SystemConfigParams(String name) {
		this.name = name;
	}

	/** full constructor */
	public SystemConfigParams(String name, String value, String min, String max,String dicname,String state) {
		this.name = name;
		this.value = value;
		this.min = min;
		this.max = max;
		this.dicname = dicname;
		this.state = state;
	}

	// Property accessors

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getValue() {
		return this.value;
	}

	public void setValue(String value) {
		this.value = value;
	}

}