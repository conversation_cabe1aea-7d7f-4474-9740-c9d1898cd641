package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Uploaduser;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.UploadUserService;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

public class UploadUserAction extends BaseSupportAction{
	private Logger logger = Logger.getLogger(UploadUserAction.class.getName());
	private UploadUserService uploadUserService;
	private String username;
	private String password;
	private String qyjmxx;
	private String flag;

	public UploadUserService getUploadUserService() {
		return uploadUserService;
	}

	public void setUploadUserService(UploadUserService uploadUserService) {
		this.uploadUserService = uploadUserService;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getQyjmxx() {
		return qyjmxx;
	}

	public void setQyjmxx(String qyjmxx) {
		this.qyjmxx = qyjmxx;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String loadUploadUser(){
		
		Uploaduser uploaduser = null;
		try{
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			String usertype = flag == null ? "3" : flag; 
			uploaduser = uploadUserService.loadUploadUser(usertype);
			if(uploaduser == null){
				uploaduser = new Uploaduser();
			}
			this.getServletRequest().setAttribute("username", username);
			this.getServletRequest().setAttribute("uploaduser", uploaduser);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "success";
	}
	
	public String findUploadUser(){
		
		Uploaduser uploaduser = null;
		try{
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			String usertype = flag == null ? "3" : flag; 
			uploaduser = uploadUserService.loadUploadUser(usertype);
			if(uploaduser == null){
				uploaduser = new Uploaduser();
			}
			JSONObject jsonObject = JSONObject.fromObject(uploaduser);
			this.setJson(jsonObject.toString());	
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}
	
	public String saveUploadUser(){
		
		Uploaduser uploaduser = new Uploaduser();;
		try{
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			
			uploaduser.setUsername(this.username);
			uploaduser.setPassword(this.password);
			uploaduser.setQyjmxx(this.qyjmxx);
			uploaduser.setFlag(this.flag);
			uploadUserService.addOrUpdateUploadUser(uploaduser);
			this.setJson(String.valueOf(true));
		}catch (DataAccessException e) {
			logger.error("rewriteUploaduser DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("rewriteUploaduser SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("rewriteUploaduser Runtime Error:", e);
			throw new SystemException("rewriteUploaduser Runtime Error:", e);
		}
		return JSON;
	}
	
	
}
