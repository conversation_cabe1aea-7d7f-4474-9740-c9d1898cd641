package com.dawnpro.dfpv.carfilemanager.barcode;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Date;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class Test2 {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		try{
			ZipFile zipFile = new ZipFile("d:/H30.zip");
			File dir = new File("d:/TEST");
//			if(dir.exists()==false){
//				dir.mkdirs();
//			}
			Enumeration emu = zipFile.entries();
			ZipEntry entry = null;
			BufferedInputStream bis = null;
			
			while(emu.hasMoreElements()){
				entry = (ZipEntry)emu.nextElement();
				System.out.println(entry.getName());
			
				if (entry.isDirectory()){
					new File(dir.getPath()+"/"+entry.getName()).mkdirs();
					
					continue;
				}
				
				
				
				bis = new BufferedInputStream(zipFile.getInputStream(entry));
				File file = new File(dir.getPath()+"/"+entry.getName());
				
				File parent = file.getParentFile();
				if(parent != null && (!parent.exists())){
					parent.mkdirs();
				}
				
				FileOutputStream fos = new FileOutputStream(file);
				BufferedOutputStream bos = new BufferedOutputStream(fos,512);
				int count;
				byte data[] = new byte[512];
				while ((count = bis.read(data, 0, 512)) != -1){
					bos.write(data, 0, count);
				}
				bos.flush();
				bos.close();
				bis.close();
		}
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	

}
