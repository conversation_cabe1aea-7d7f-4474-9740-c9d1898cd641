<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecall" table="CAR_RECALL" >
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecallId">
            <key-property name="vin" type="java.lang.String">
                <column name="VIN" length="20" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="15" />
            </key-property>
        </composite-id>
        <property name="engineNo" type="java.lang.String">
            <column name="ENGINE_NO" length="20" />
        </property>
        <property name="model" type="java.lang.String">
            <column name="MODEL" length="50" />
        </property>
        <property name="engineType" type="java.lang.String">
            <column name="ENGINE_TYPE" length="20" />
        </property>
        <property name="color" type="java.lang.String">
            <column name="COLOR" length="50" />
        </property>
        <property name="prodDate" type="java.lang.String">
            <column name="PROD_DATE" length="10" />
        </property>
        <property name="c1" type="java.lang.String">
            <column name="C1" length="20" />
        </property>
        <property name="c2" type="java.lang.String">
            <column name="C2" length="20" />
        </property>
        <property name="c3" type="java.lang.String">
            <column name="C3" length="20" />
        </property>
        <property name="c4" type="java.lang.String">
            <column name="C4" length="40" />
        </property>
        <property name="c5" type="java.lang.String">
            <column name="C5" length="40" />
        </property>
        <property name="c6" type="java.lang.String">
            <column name="C6" length="60" />
        </property>
        <property name="c7" type="java.lang.String">
            <column name="C7" length="10" />
        </property>
        <property name="c8" type="java.lang.String">
            <column name="C8" length="10" />
        </property>
        <property name="c9" type="java.lang.String">
            <column name="C9" length="10" />
        </property>
        <property name="c10" type="java.lang.String">
            <column name="C10" length="10" />
        </property>
        <property name="c11" type="java.lang.String">
            <column name="C11" length="10" />
        </property>
        <property name="c12" type="java.lang.String">
            <column name="C12" length="10" />
        </property>
        <property name="c13" type="java.lang.String">
            <column name="C13" length="20" />
        </property>
        <property name="c14" type="java.lang.String">
            <column name="C14" length="30" />
        </property>
        <property name="c15" type="java.lang.String">
            <column name="C15" length="20" />
        </property>
        <property name="c16" type="java.lang.String">
            <column name="C16" length="60" />
        </property>
        <property name="c17" type="java.lang.String">
            <column name="C17" length="40" />
        </property>
        <property name="c18" type="java.lang.String">
            <column name="C18" length="20" />
        </property>
        <property name="c19" type="java.lang.String">
            <column name="C19" length="20" />
        </property>
        <property name="c20" type="java.lang.String">
            <column name="C20" length="20" />
        </property>
        <property name="c21" type="java.lang.String">
            <column name="C21" length="20" />
        </property>
        <property name="c22" type="java.lang.String">
            <column name="C22" length="60" />
        </property>
        <property name="d23" type="java.lang.String">
            <column name="D23" length="60" />
        </property>
        <property name="c24" type="java.lang.String">
            <column name="C24" length="60" />
        </property>
        <property name="c25" type="java.lang.String">
            <column name="C25" length="60" />
        </property>
        <property name="c26" type="java.lang.String">
            <column name="C26" length="20" />
        </property>
        <property name="c27" type="java.lang.String">
            <column name="C27" length="20" />
        </property>
        <property name="c28" type="java.lang.String">
            <column name="C28" length="20" />
        </property>
        <property name="c29" type="java.lang.String">
            <column name="C29" length="20" />
        </property>
        <property name="c30" type="java.lang.String">
            <column name="C30" length="20" />
        </property>
        <property name="c31" type="java.lang.String">
            <column name="C31" length="20" />
        </property>
        <property name="c32" type="java.lang.String">
            <column name="C32" length="20" />
        </property>
        <property name="c33" type="java.lang.String">
            <column name="C33" length="20" />
        </property>
        <property name="c34" type="java.lang.String">
            <column name="C34" length="20" />
        </property>
        <property name="c35" type="java.lang.String">
            <column name="C35" length="20" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="effectTime" type="java.util.Date">
            <column name="EFFECT_TIME" length="7" />
        </property>
    </class>
</hibernate-mapping>
