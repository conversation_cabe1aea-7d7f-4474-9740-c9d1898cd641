<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#export").attr("disabled", true);
	$("#export1").attr("disabled", true);
	$("#export2").attr("disabled", true);
	
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
					$("#export1").attr("disabled", false);
					$("#export2").attr("disabled", false);
				}
            }
        }
    });

	$("#query").click(function(){
		var dateType = $('input:radio[name=dateType]:checked').val();
		location.href="fuelUpload.action?menuid="+menuid+"&dateType="+dateType;
	});

	
	$('#export').click(function() {
		var dateType = $('input:radio[name=dateType]:checked').val();
		location.href="business/fuelUpload!exportLineImage.action?menuid="+menuid+"&dateType="+dateType+"&type=1";
	});
	
	
	$('#export1').click(function() {
		var dateType = $('input:radio[name=dateType]:checked').val();
		location.href="business/fuelUpload!exportLineImage.action?menuid="+menuid+"&dateType="+dateType+"&type=2";
	});
	
	
	$('#export2').click(function() {
		var dateType = $('input:radio[name=dateType]:checked').val();
		location.href="business/fuelUpload!exportReportExcle.action?menuid="+menuid+"&dateType="+dateType;
	});

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var qdxcx = $('#qdxcx').val();
			location.href="fuelUpload.action?currentPage="+$('#jump').val()+"&qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qdxcx = $('#qdxcx').val();
				location.href=$(this).attr('value')+"&qdxcx="+encodeURI(encodeURI(qdxcx))+"&menuid="+menuid;
		 });
	  });
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class


});


</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan=""><p>时间:<input type="radio" id="dateType" name="dateType" value="1" class="radio ui-widget-content ui-corner-all"  <s:if test='#request.dateType=="1"'> checked="checked" </s:if> />年
				<input type="radio" id="dateType" name="dateType" value="2" class="radio ui-widget-content ui-corner-all" <s:if test='#request.dateType=="2"'> checked="checked" </s:if>  />季度
				<input type="radio" id="dateType" name="dateType" value="3" class="radio ui-widget-content ui-corner-all"  <s:if test='#request.dateType=="3"'> checked="checked" </s:if> />月
				</td>
				<td align="right" colspan='3'></td>
			</tr>
			
			<tr>
			   <td width="80%"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="60" align="right"></td>
  			   <td width="60" align="right"></td>
			   <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			   <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">综合工况导出</button></td>
			   <td width="60" align="right"><button id="export1" class="ui-button ui-state-default ui-corner-all">CO2排放导出</button></td>
			   <td width="60" align="right"><button id="export2" class="ui-button ui-state-default ui-corner-all">EXCLE导出</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">序号</th> 
			    <th width="6%">时间</th>
			    <th width="6%">车辆数</th>
			    <th width="7%">综合工况平均值</th>
			    <th width="7%">CO2平均值</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.fueluploadPageData" status="obj"  > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><s:property value="#obj.count" /></td>
			  		<td><s:property value="time" /></td>
			  		<td><s:property value="count" /></td>			  		
			  		<td><s:property value="avg_zhgk" /></td>	
			  		<td><s:property value="avg_zhcopl" /></td>	
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="fuelUpload.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="fuelUpload.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="fuelUpload.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="fuelUpload.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<!--<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  
			  	<td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              
			  	<td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
				-->
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="fuelUploadReport_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>典型车型:</label></td>
				<td><input type="text" id="dxcx" name="dxcx"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20"/>&nbsp;
				<button name="modelSelect" id="modelSelect" class="ui-button ui-state-default ui-corner-all" style="position:static">选择</button>
					</td>
	    	</tr>
	    	<tr>
				<td><label><P>中性车型:</label></td>
				<td>
					<input type="text" id="zxcx" name="zxcx" class="text ui-widget-content ui-corner-all" size="35"  />&nbsp;
					<button name="modelSelect1" id="modelSelect1" class="ui-button ui-state-default ui-corner-all" style="position:static" >选择</button>					
				</td>
			</tr>			
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="car_color_mapping_display_dialog" title="查看窗口" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td><label><P>典型车型:</label></td>
				<td><input type="text" id="dxcx" name="dxcx" class="text ui-widget-content ui-corner-all" size="20" maxlength="20"/>		
					</td>
	    	</tr>
	    	<tr>
				<td><label><P>中性车型:</label></td>
				<td>
					<input type="text" id="zxcx" name="zxcx" class="text ui-widget-content ui-corner-all" size="35"  />					
				</td>
			</tr>			
		</Table>
		<input type='hidden' id='id' name='id'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="tree_dialog" title="车型选择" style="display:none">
	<form id="operateForm" method='post'>
	<table>
		<tr>
			<td width="100%">
				<div id="treeboxbox_tree" style="width:446; height:300;background-color:#f5f5f5;border :1px solid Silver; overflow:auto; "/>
			</td>
		</tr>
		<input type="hidden" name="flg" id="flg"/>		
	</table>	
	</form>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<script type="text/javascript">
function showInfo(c1,zxcx,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}
	function doLog(str){			
		//var log = document.getElementById("logarea");
		//log.innerHTML = log.innerHTML+str+"</br>"
		//log.scrollTop = log.scrollHeight;
	}
	var tree;
	function getAllLeaftValue(){
		var valuesTemp=tree.getAllChecked();
		if(valuesTemp!=null&&valuesTemp!=""){
			var values=valuesTemp.split(",");
			var result="";
			for(var i=0;i<values.length;i++){
				var value=values[i];
				if(value.indexOf("##_p_")!=0){
					if(result!="")
						result+=",";
					result+=value.split(".")[1];
				}
			}
			return result;
		}
		return "";
	}
	function tonclick(id){			
		doLog("Item "+tree.getItemText(id)+" was selected");			
	};
	function tondblclick(id){			
		doLog("Item "+tree.getItemText(id)+" was doubleclicked");			
	};			
	function tondrag(id,id2){			
		return confirm("Do you want to move node "+tree.getItemText(id)+" to item "+tree.getItemText(id2)+"?");			
	};
	function tonopen(id,mode){			
		return confirm("Do you want to "+(mode>0?"close":"open")+" node "+tree.getItemText(id)+"?");			
	};
	
	function toncheck(id,state){			
		doLog("Item "+tree.getItemText(id)+" was " +((state)?"checked":"unchecked"));		
	};		
	function createTree(){
		tree=new dhtmlXTreeObject("treeboxbox_tree","100%","100%",0);
		tree.setImagePath("<%=path%>/images/csh_yellowbooks/");
		tree.enableCheckBoxes(1);
		tree.enableDragAndDrop(1);
		
		tree.enableThreeStateCheckboxes(true);
		tree.setOnDblClickHandler(tondblclick);
		tree.setDragHandler(tondrag);
		tree.loadXML('business/carModelSelect!getCarModel.action?type=<%=request.getAttribute("type")%>');		
	}	
</script>
</body>
</html>