package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jxl.Workbook;
import jxl.read.biff.BiffException;
import jxl.write.Alignment;
import jxl.write.Border;
import jxl.write.BorderLineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Lpzinfo;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.Vfl03;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.ScjcNeutralService;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelScjcUploadBean;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FuelUploadBean;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.UploadStateManagerService;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.util.OperateExcel;

public class UploadWqbfQueryAction extends BaseSupportAction{
	private Logger logger = Logger.getLogger(UploadWqbfQueryAction.class.getName());
	private UploadStateManagerService uploadStateManagerService = null;
	private ScjcNeutralService scjcNeutralService = null;
	private String currentPage = null;
	private String uploadType;
	private String uploadState;
	private String beginDate;
	private String endDate;
	private String csbeginDate;
	private String csendDate;
	private String slcx;
	private String pmodel;
	private String pvin;
	private String vin;
    private String ids;
	
	private String fileName = null;
	private InputStream excelStream = null;
	
	private String cocyears;
	private String halfyear;
	
	public String getCocyears() {
		return cocyears;
	}
	public void setCocyears(String cocyears) {
		this.cocyears = cocyears;
	}
	public String getHalfyear() {
		return halfyear;
	}
	public void setHalfyear(String halfyear) {
		this.halfyear = halfyear;
	}
	public String getVin() {
		return vin;
	}
	public void setVin(String vin) {
		this.vin = vin;
	}
	public String getUploadState() {
		return uploadState;
	}
	public void setUploadState(String uploadState) {
		this.uploadState = uploadState;
	}
	public UploadStateManagerService getUploadStateManagerService() {
		return uploadStateManagerService;
	}
	public void setUploadStateManagerService(
			UploadStateManagerService uploadStateManagerService) {
		this.uploadStateManagerService = uploadStateManagerService;
	}
	

	public ScjcNeutralService getScjcNeutralService() {
		return scjcNeutralService;
	}
	public void setScjcNeutralService(ScjcNeutralService scjcNeutralService) {
		this.scjcNeutralService = scjcNeutralService;
	}
	public String getUploadType() {
		return uploadType;
	}
	public void setUploadType(String uploadType) {
		this.uploadType = uploadType;
	}
	public String getCurrentPage() {
		return currentPage;
	}
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}
	public String getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getCsbeginDate() {
		return csbeginDate;
	}
	public void setCsbeginDate(String csbeginDate) {
		this.csbeginDate = csbeginDate;
	}
	public String getCsendDate() {
		return csendDate;
	}
	public void setCsendDate(String csendDate) {
		this.csendDate = csendDate;
	}
	public String getPmodel() {
		return pmodel;
	}
	public void setPmodel(String pmodel) {
		this.pmodel = pmodel;
	}
	public String getSlcx() {
		return slcx;
	}
	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}
	
	
	
	public String getPvin() {
		return pvin;
	}
	public void setPvin(String pvin) {
		this.pvin = pvin;
	}
	public InputStream getExcelStream() {
		return excelStream;
	}
	public void setExcelStream(InputStream excelStream) {
		this.excelStream = excelStream;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public String execute(){
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());

			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
						
			params.put("vin", pvin);
			params.put("pmodel", pmodel);
			
			params.put("beginDate", beginDate);
			params.put("endDate", endDate);
			params.put("csbeginDate", csbeginDate);
			params.put("csendDate", csendDate);
			params.put("uploadType", uploadType);
			params.put("uploadState", uploadState);
			results = uploadStateManagerService.paginationWqbf(page, params);
			if(results == null)
				results = new ArrayList();
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("uploadWqbfQueryPageData", results);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
	
	public String isTypicalityNeutralExist(){
		try{
			String isExist = this.scjcNeutralService.isTypicalityNeutralExist(vin);
			this.setJson(String.valueOf(isExist));			
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
    }
	
	public String isVfl04(){
		try{
			String isExist = this.uploadStateManagerService.isVfl04(vin);
			this.setJson(String.valueOf(isExist));			
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
    }
	
	public String addTypicalityNeutral(){
		String id = java.util.UUID.randomUUID().toString();
		Vfl03  vfl03 = new Vfl03();
		vfl03.setVin(vin);
		
		vfl03.setRh(rh);
		vfl03.setEt(et);
		vfl03.setAp(ap);
		vfl03.setTesttype("3");
		vfl03.setTestno(testNo);
		vfl03.setTestdate(testDate);
		vfl03.setEpass("1");
		vfl03.setUuid(id);
		vfl03.setFinalresult("1");
		
		vfl03.setVrhc(Double.valueOf(vrhc));
		vfl03.setVlhc(Double.valueOf(vlhc));
		vfl03.setVrco(Double.valueOf(vrco));
		vfl03.setVlco(Double.valueOf(vlco));
		vfl03.setVrnox(Double.valueOf(vrnox));
		vfl03.setVlnox(Double.valueOf(vlnox));
		vfl03.setAnalymanuf(analyManuf);
		vfl03.setAnalyname(analyName);
		vfl03.setAnalymodel(analyModel);
		vfl03.setAnalydate(analyDate);
		vfl03.setDynomodel(dynoModel);
		vfl03.setDynomanuf(dynoManuf);
		vfl03.setCratedate(CalendarUtil.getDefaultCurrentLocatlTime());
		vfl03.setSecvrhc(Double.valueOf(secvrhc));
		vfl03.setSecvlhc(Double.valueOf(secvlhc));
		vfl03.setSecvrco(Double.valueOf(secvrco));
		vfl03.setSecvlco(Double.valueOf(secvlco));
		vfl03.setSecvrnox(Double.valueOf(secvrnox));
		vfl03.setSecvlnox(Double.valueOf(secvlnox));
		
		try{
			this.scjcNeutralService.addTypicalityNeutral(vfl03);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String updateTypicalityNeutral(){
		try{
		Vfl03  vfl03 = this.scjcNeutralService.loadTypicalityNeutral(vin);
		//vfl03.setVin(vin);
		vfl03.setRh(rh);
		vfl03.setEt(et);
		vfl03.setAp(ap);
		vfl03.setTestno(testNo);
		vfl03.setTestdate(testDate);
		
		vfl03.setVrhc(Double.valueOf(vrhc));
		vfl03.setVlhc(Double.valueOf(vlhc));
		vfl03.setVrco(Double.valueOf(vrco));
		vfl03.setVlco(Double.valueOf(vlco));
		vfl03.setVrnox(Double.valueOf(vrnox));
		vfl03.setVlnox(Double.valueOf(vlnox));
		vfl03.setAnalymanuf(analyManuf);
		vfl03.setAnalyname(analyName);
		vfl03.setAnalymodel(analyModel);
		vfl03.setAnalydate(analyDate);
		vfl03.setDynomodel(dynoModel);
		vfl03.setDynomanuf(dynoManuf);
		
		vfl03.setSecvrhc(Double.valueOf(secvrhc));
		vfl03.setSecvlhc(Double.valueOf(secvlhc));
		vfl03.setSecvrco(Double.valueOf(secvrco));
		vfl03.setSecvlco(Double.valueOf(secvlco));
		vfl03.setSecvrnox(Double.valueOf(secvrnox));
		vfl03.setSecvlnox(Double.valueOf(secvlnox));
		
			this.scjcNeutralService.updateTypicalityNeutral(vfl03);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String deleteTypicalityNeutral(){
		String[] id = ids.split("&");
		try{
			this.scjcNeutralService.deleteTypicalityNeutral(id);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	
	public String getTypicalityNeutral(){
		try{
			
			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
						
			params.put("vin", vin);
			params.put("pmodel", "");
			
			params.put("beginDate", "");
			params.put("endDate", "");
			params.put("csbeginDate", "");
			params.put("csendDate", "");
			params.put("uploadType", "");
			params.put("uploadState", "");
			results = uploadStateManagerService.paginationWqbf(page, params);
			FuelScjcUploadBean obj1 = null;
			obj1 =(FuelScjcUploadBean)results.get(0);
			JSONObject jsonObject = JSONObject.fromObject(obj1);
			if(obj1 != null)
				//this.setJson(jsonObject.toString());
				this.getSession().put(JSON_DATA, jsonObject.toString());	
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}
	
	
	public String updateState(){
		String ids = this.getServletRequest().getParameter("ids");
		String type = this.getServletRequest().getParameter("type");
		String reason = this.getServletRequest().getParameter("reason");
		try{
			String[] vins = ids.split(",");
			this.uploadStateManagerService.updateUploadState(vins, type, reason);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	

	
	public String uploaDisplay(){
		String vin = this.getServletRequest().getParameter("vin");
		try{
			//List isSuccess = uploadStateManagerService.isSuccessState(vin);
			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
						
			params.put("vin", vin);
			params.put("pmodel", pmodel);
			
			params.put("beginDate", beginDate);
			params.put("endDate", endDate);
			params.put("csbeginDate", csbeginDate);
			params.put("csendDate", csendDate);
			params.put("uploadType", uploadType);
			params.put("uploadState", uploadState);
			results = uploadStateManagerService.paginationWqbf(page, params);
			FuelScjcUploadBean obj1 = null;
			obj1 =(FuelScjcUploadBean)results.get(0);
			JSONObject jsonObject = JSONObject.fromObject(obj1);
			if(obj1 != null)
				//this.setJson(jsonObject.toString());
				this.getSession().put(JSON_DATA, jsonObject.toString());					
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}	
	

	
	
		private String rh;
		private String et;
		private String ap;
		private String testDate;
		private String testNo;
		private String epass;
		private String vrhc;
		private String vlhc;
		private String vrco;
		private String vlco;
		private String vrnox;
		private String vlnox;
		private String serialno;
		private String uuid;
		private String cratedate;
		private String analyManuf;
		private String analyName;
		private String analyModel;
		private String analyDate;
		private String dynoModel;
		private String dynoManuf;
		private String finalresult;
		private String secvrhc;
		private String secvlhc;
		private String secvrco;
		private String secvlco;
		private String secvrnox;
		private String secvlnox;

		public Logger getLogger() {
			return logger;
		}
		public void setLogger(Logger logger) {
			this.logger = logger;
		}
		public String getRh() {
			return rh;
		}
		public void setRh(String rh) {
			this.rh = rh;
		}
		public String getEt() {
			return et;
		}
		public void setEt(String et) {
			this.et = et;
		}
		public String getAp() {
			return ap;
		}
		public void setAp(String ap) {
			this.ap = ap;
		}
	
		public String getEpass() {
			return epass;
		}
		public void setEpass(String epass) {
			this.epass = epass;
		}
		public String getVrhc() {
			return vrhc;
		}
		public void setVrhc(String vrhc) {
			this.vrhc = vrhc;
		}
		public String getVlhc() {
			return vlhc;
		}
		public void setVlhc(String vlhc) {
			this.vlhc = vlhc;
		}
		public String getVrco() {
			return vrco;
		}
		public void setVrco(String vrco) {
			this.vrco = vrco;
		}
		public String getVlco() {
			return vlco;
		}
		public void setVlco(String vlco) {
			this.vlco = vlco;
		}
		public String getVrnox() {
			return vrnox;
		}
		public void setVrnox(String vrnox) {
			this.vrnox = vrnox;
		}
		public String getVlnox() {
			return vlnox;
		}
		public void setVlnox(String vlnox) {
			this.vlnox = vlnox;
		}
		public String getSerialno() {
			return serialno;
		}
		public void setSerialno(String serialno) {
			this.serialno = serialno;
		}
		public String getUuid() {
			return uuid;
		}
		public void setUuid(String uuid) {
			this.uuid = uuid;
		}
		public String getCratedate() {
			return cratedate;
		}
		public void setCratedate(String cratedate) {
			this.cratedate = cratedate;
		}
		
		public String getTestDate() {
			return testDate;
		}
		public void setTestDate(String testDate) {
			this.testDate = testDate;
		}
		public String getTestNo() {
			return testNo;
		}
		public void setTestNo(String testNo) {
			this.testNo = testNo;
		}
		public String getAnalyManuf() {
			return analyManuf;
		}
		public void setAnalyManuf(String analyManuf) {
			this.analyManuf = analyManuf;
		}
		public String getAnalyName() {
			return analyName;
		}
		public void setAnalyName(String analyName) {
			this.analyName = analyName;
		}
		public String getAnalyModel() {
			return analyModel;
		}
		public void setAnalyModel(String analyModel) {
			this.analyModel = analyModel;
		}
		public String getAnalyDate() {
			return analyDate;
		}
		public void setAnalyDate(String analyDate) {
			this.analyDate = analyDate;
		}
		public String getDynoModel() {
			return dynoModel;
		}
		public void setDynoModel(String dynoModel) {
			this.dynoModel = dynoModel;
		}
		public String getDynoManuf() {
			return dynoManuf;
		}
		public void setDynoManuf(String dynoManuf) {
			this.dynoManuf = dynoManuf;
		}
		public String getFinalresult() {
			return finalresult;
		}
		public void setFinalresult(String finalresult) {
			this.finalresult = finalresult;
		}
		public String getSecvrhc() {
			return secvrhc;
		}
		public void setSecvrhc(String secvrhc) {
			this.secvrhc = secvrhc;
		}
		public String getSecvlhc() {
			return secvlhc;
		}
		public void setSecvlhc(String secvlhc) {
			this.secvlhc = secvlhc;
		}
		public String getSecvrco() {
			return secvrco;
		}
		public void setSecvrco(String secvrco) {
			this.secvrco = secvrco;
		}
		public String getSecvlco() {
			return secvlco;
		}
		public void setSecvlco(String secvlco) {
			this.secvlco = secvlco;
		}
		public String getSecvrnox() {
			return secvrnox;
		}
		public void setSecvrnox(String secvrnox) {
			this.secvrnox = secvrnox;
		}
		public String getSecvlnox() {
			return secvlnox;
		}
		public void setSecvlnox(String secvlnox) {
			this.secvlnox = secvlnox;
		}
		public String getIds() {
			return ids;
		}
		public void setIds(String ids) {
			this.ids = ids;
		}
		
		
}
