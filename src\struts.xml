<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 2.0//EN" "http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<!-- 开启动态方法调用 -->
	<constant name="struts.enable.DynamicMethodInvocation" value="true"/>
	<constant name="struts.locale" value="zh_CN"/>
	<constant name="struts.i18n.encoding" value="UTF-8"/>
	<constant name="struts.multipart.saveDir"  value="upload"/> 
	<constant name="struts.multipart.maxSize" value="209715200" /> 
	
	<!-- 
	
	 国际化消息资源文件
	<constant name="struts.custom.i18n.resources" value="globalMessages"/>
	-->
	
	<!-- Spring接管Action -->
	<constant name="org.apache.struts2.spring.StrutsSpringObjectFactory" value="=spring"/>   
	
	<package name="struts-base" extends="struts-default">
    	<!-- 验证用户登录拦截机   -->
		<interceptors> 
           <interceptor name ="security" class ="securityInterceptor" />
            <!-- 定义拦截栈 -->
            <interceptor-stack name="appStack"> 
				<interceptor-ref name="defaultStack"/> 
				<interceptor-ref name="security"/>
				<interceptor-ref name ="timer" />
			</interceptor-stack> 
        </interceptors> 
        
        <!-- 设置所有Action自动调用的拦截器堆栈 -->
        <default-interceptor-ref name="appStack"/>
        
         <!-- 全局资源 -->
		<global-results>
			<result name="login">../login_faild.jsp</result>
			<result name="error">../index.jsp</result>
			<result name="json" type="redirect-action">
 				<param name="actionName">../common/handlerJSON.action</param>
 				<param name="json">${json}</param>
  			</result>
  			<result name="JSONONE" >/dataone.jsp</result>
		</global-results>
		
		<!-- 全局异常映射 -->
		<global-exception-mappings>   
           <exception-mapping result="error" exception="com.dawnpro.dfpv.carfilemanager.base.SystemException"/>
        </global-exception-mappings>
    </package>
	
	<!-- JSON Action -->
	<package name="common-json-action" extends="json-default" namespace="/common"> 
		<action name="handlerJSON" class="com.dawnpro.dfpv.carfilemanager.common.action.JSONMessageAction">		
			<result type="json"/>
		</action> 
	</package>
	
	<!-- 导入系统管理配置文件 -->
	<include file="struts-systemManage.xml"/>
	<include file="struts-businessManage.xml"/>
	
</struts>