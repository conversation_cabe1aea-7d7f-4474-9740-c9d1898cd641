package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * FuellabeltemplateId entity. <AUTHOR> Persistence Tools
 */

public class FuellabeltemplateId implements java.io.Serializable {

	// Fields

	private String slcx;
	private String vercode;

	// Constructors

	/** default constructor */
	public FuellabeltemplateId() {
	}

	/** full constructor */
	public FuellabeltemplateId(String slcx, String vercode) {
		this.slcx = slcx;
		this.vercode = vercode;
	}

	// Property accessors

	public String getSlcx() {
		return this.slcx;
	}

	public void setSlcx(String slcx) {
		this.slcx = slcx;
	}

	public String getVercode() {
		return this.vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof FuellabeltemplateId))
			return false;
		FuellabeltemplateId castOther = (FuellabeltemplateId) other;

		return ((this.getSlcx() == castOther.getSlcx()) || (this.getSlcx() != null
				&& castOther.getSlcx() != null && this.getSlcx().equals(
				castOther.getSlcx())))
				&& ((this.getVercode() == castOther.getVercode()) || (this
						.getVercode() != null && castOther.getVercode() != null && this
						.getVercode().equals(castOther.getVercode())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getSlcx() == null ? 0 : this.getSlcx().hashCode());
		result = 37 * result
				+ (getVercode() == null ? 0 : this.getVercode().hashCode());
		return result;
	}

}