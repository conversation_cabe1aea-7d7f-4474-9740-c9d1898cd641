<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bv01" table="BV_01" >
        <id name="id" type="java.lang.String">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="dhbm" type="java.lang.String">
            <column name="DHBM" length="32" not-null="true" />
        </property>
        <property name="bmz" type="java.lang.String">
            <column name="BMZ" length="500" />
        </property>
        <property name="mark" type="java.lang.String">
            <column name="MARK" length="500" />
        </property>
        <property name="senddate" type="java.lang.String">
            <column name="SENDDATE" length="16" />
        </property>
        <property name="sendtime" type="java.lang.String">
            <column name="SENDTIME" length="32" />
        </property>
        <property name="ggcx" type="java.lang.String">
            <column name="GGCX" length="50" not-null="true" />
        </property>
        <property name="vercode" type="java.lang.String">
            <column name="VERCODE" length="10" not-null="true" />
        </property>
    </class>
</hibernate-mapping>
