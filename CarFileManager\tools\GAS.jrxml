<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="GAS" pageWidth="504" pageHeight="648" columnWidth="464" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="carConsistencyNumber" class="java.lang.String">
		<fieldDescription><![CDATA[carConsistencyNumber]]></fieldDescription>
	</field>
	<field name="cityStatus" class="java.lang.String">
		<fieldDescription><![CDATA[cityStatus]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="colligateStatus" class="java.lang.String">
		<fieldDescription><![CDATA[colligateStatus]]></fieldDescription>
	</field>
	<field name="date" class="java.lang.String">
		<fieldDescription><![CDATA[date]]></fieldDescription>
	</field>
	<field name="derailleurType" class="java.lang.String">
		<fieldDescription><![CDATA[derailleurType]]></fieldDescription>
	</field>
	<field name="driveType" class="java.lang.String">
		<fieldDescription><![CDATA[driveType]]></fieldDescription>
	</field>
	<field name="engineModel" class="java.lang.String">
		<fieldDescription><![CDATA[engineModel]]></fieldDescription>
	</field>
	<field name="enterprise" class="java.lang.String">
		<fieldDescription><![CDATA[enterprise]]></fieldDescription>
	</field>
	<field name="environsStatus" class="java.lang.String">
		<fieldDescription><![CDATA[environsStatus]]></fieldDescription>
	</field>
	<field name="fuelType" class="java.lang.String">
		<fieldDescription><![CDATA[fuelType]]></fieldDescription>
	</field>
	<field name="limitOne" class="java.lang.String">
		<fieldDescription><![CDATA[limitOne]]></fieldDescription>
	</field>
	<field name="limitTwo" class="java.lang.String">
		<fieldDescription><![CDATA[limitTwo]]></fieldDescription>
	</field>
	<field name="maxDesignSumQuality" class="java.lang.String">
		<fieldDescription><![CDATA[maxDesignSumQuality]]></fieldDescription>
	</field>
	<field name="model" class="java.lang.String">
		<fieldDescription><![CDATA[model]]></fieldDescription>
	</field>
	<field name="quality" class="java.lang.String">
		<fieldDescription><![CDATA[quality]]></fieldDescription>
	</field>
	<field name="range" class="java.lang.String">
		<fieldDescription><![CDATA[range]]></fieldDescription>
	</field>
	<field name="ratingPower" class="java.lang.String">
		<fieldDescription><![CDATA[ratingPower]]></fieldDescription>
	</field>
	<field name="remark" class="java.lang.String">
		<fieldDescription><![CDATA[remark]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="qebz" class="java.lang.String"/>
	<field name="gjbz" class="java.lang.String"/>
	<field name="dymb" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="63" splitType="Stretch">
			<textField>
				<reportElement x="1" y="-12" width="105" height="42"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="黑体" size="23" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{qebz}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="25" splitType="Stretch">
			<textField>
				<reportElement x="61" y="6" width="100" height="18"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{model}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="414" splitType="Stretch">
			<textField>
				<reportElement x="232" y="169" width="100" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{environsStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="244" y="56" width="100" height="20"/>
				<textElement textAlignment="Center">
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{maxDesignSumQuality}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="225" y="137" width="100" height="33"/>
				<textElement>
					<font fontName="黑体" size="28" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{colligateStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="73" y="36" width="100" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{derailleurType}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="76" width="100" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="35" y="18" width="100" height="18"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{range}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="232" y="117" width="100" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cityStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="246" y="37" width="100" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{driveType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="245" y="18" width="100" height="18"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ratingPower}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="56" width="100" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{quality}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="253" width="63" height="16"/>
				<textElement>
					<font fontName="黑体" size="11" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{limitTwo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="181" y="223" width="63" height="16"/>
				<textElement>
					<font fontName="黑体" size="11" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{limitOne}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="245" y="0" width="100" height="18"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{fuelType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="71" y="0" width="100" height="18"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{engineModel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="33" y="378" width="147" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vin}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="33" y="393" width="391" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{carConsistencyNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="260" y="392" width="147" height="20"/>
				<textElement>
					<font fontName="黑体" size="12" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{date}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="117" y="223" width="64" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[对应限值为]]></text>
			</staticText>
			<staticText>
				<reportElement x="117" y="208" width="281" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[第一阶段要求自2005年07月01日开始执行，]]></text>
			</staticText>
			<staticText>
				<reportElement x="244" y="223" width="137" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[L/100km；]]></text>
			</staticText>
			<staticText>
				<reportElement x="117" y="238" width="281" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[第二阶段要求自2008年01月01日开始执行，]]></text>
			</staticText>
			<staticText>
				<reportElement x="117" y="253" width="64" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[对应限值为]]></text>
			</staticText>
			<staticText>
				<reportElement x="244" y="253" width="137" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[L/100km；]]></text>
			</staticText>
			<staticText>
				<reportElement x="117" y="193" width="90" height="16" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<text><![CDATA[适用国家标准为]]></text>
			</staticText>
			<textField>
				<reportElement x="207" y="193" width="137" height="16"/>
				<textElement>
					<font fontName="黑体" size="11"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{gjbz}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="8" splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band height="7" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="13" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="11" splitType="Stretch"/>
	</summary>
</jasperReport>
