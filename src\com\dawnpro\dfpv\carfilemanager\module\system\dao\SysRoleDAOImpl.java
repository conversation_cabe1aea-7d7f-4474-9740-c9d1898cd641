package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import org.hibernate.HibernateException;
import org.springframework.dao.DataAccessResourceFailureException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;

public class SysRoleDAOImpl extends GenericHibernateDAOImpl<SysRole> implements SysRoleDAO {

	public List<SysRole> findAllRole() throws DataAccessException {
		try{
			List<SysRole> result = this.find("from SysRole g");
			return result;
		}catch(Exception e){
			throw new DataAccessException("findAllRole Method Error:",e);
		}
	}

	public void addRole(SysRole role) throws DataAccessException {
		try{
			this.add(role);
		}catch (DataAccessResourceFailureException e) {
			throw new DataAccessException("addRole Error:",e);
		} catch (HibernateException e) {
			throw new DataAccessException("addRole Error:",e);
		} catch (IllegalStateException e) {
			throw new DataAccessException("addRole Error:",e);
		} catch(DataAccessException e){
			throw new DataAccessException("addRole Error:",e);
		}
	}

	public void deleteRole(SysRole[] role) throws DataAccessException {
		try{
			this.deleteBatch(role);
		}catch(Exception e){
			throw new DataAccessException("deleteRole Method Error:",e);
		}
	}

	public List<SysRole> findRole(String sql, String[] params)
			throws DataAccessException {
		try{
			return this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findRole Method Error:",e);
		}
	}

	public List<SysRole> findRole(String sql) throws DataAccessException {
		try{
			return this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findRole Method Error:",e);
		}
	}

	public SysRole loadRoleObj(String roleId) throws DataAccessException {
		SysRole gg = null;
		try{
			List<SysRole> result = find("from SysRole g where g.roleid=?",new String[]{roleId});
			if(result!=null && result.size()>0)
				gg=result.get(0);
		}catch(Exception e){
			throw new DataAccessException("loadRoleObj Method Error:",e);
		}
		return gg;
	}

	public void updateRole(SysRole role) throws DataAccessException {
		try{
			this.update(role);
		}catch(Exception e){
			throw new DataAccessException("updateRole Method Error:",e);
		}
	}

}
