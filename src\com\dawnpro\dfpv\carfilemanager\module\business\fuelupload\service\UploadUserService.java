package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Uploaduser;

public interface UploadUserService {
	public Uploaduser loadUploadUser(String uploadUserType) throws DataAccessException;;
	public void addOrUpdateUploadUser(Uploaduser obj) throws DataAccessException;;
}
