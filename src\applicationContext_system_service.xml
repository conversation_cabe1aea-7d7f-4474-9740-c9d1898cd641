<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="autodetect" xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	 
	<bean id="paginationService" class="com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationServiceImpl" lazy-init="true" scope="request">
		<property name="paginationDAO">
			<ref bean="paginationDAO" />
		</property>
	</bean>
	
	<bean id="sPaginationService" class="com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationServiceImpl" lazy-init="true" scope="singleton">
		<property name="paginationDAO">
			<ref bean="sPaginationDAO" />
		</property>
	</bean>
	
	<bean id="sysUserService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysUserServiceImpl" lazy-init="true" scope="request" >
		<property name="sysUserDao">
			<ref bean="sysUserDao" />
		</property>
		<property name="sysRoleDAO">
			<ref bean="sysRoleDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="sysMenuService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysMenuServiceImpl" lazy-init="true" scope="request" >
		<property name="sysMenuDAO">
			<ref bean="sysMenuDAO" />
		</property>
	</bean>
	
	<bean id="systemConfigParamsService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SystemConfigParamsServiceImpl" lazy-init="true" scope="request">
		<property name="systemConfigParamsDAO">
			<ref bean="systemConfigParamsDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="sysRoleService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysRoleServiceImpl" lazy-init="true" scope="request" >
		<property name="dao">
			<ref bean="sysRoleDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="sysLogService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysLogServiceImpl" lazy-init="true" scope="singleton" >
		<property name="dao">
			<ref bean="sysLogDAO" />
		</property>
		<property name="paginationService">
			<ref bean="sPaginationService" />
		</property>
	</bean>
	
	<bean id="sysOperateService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysOperateServiceImpl" lazy-init="true" scope="request" >
		<property name="sysOperateDAO">
			<ref bean="sysOperateDAO" />
		</property>
	</bean>
	
	<bean id="sysPermissionService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysPermissionServiceImpl" lazy-init="true" scope="request" >
		<property name="sysPermissionDAO">
			<ref bean="sysPermissionDAO" />
		</property>
	</bean>
	
	<bean id="sysDataDictionaryService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryServiceImpl" lazy-init="true" scope="request" >
		<property name="sysDataDictionaryDAO">
			<ref bean="sysDataDictionaryDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="sysDataDictionaryTypeService" class="com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryTypeServiceImpl" lazy-init="true" scope="request" >
		<property name="dao">
			<ref bean="sysDataDictionaryTypeDAO" />
		</property>
		<property name="dataDictionaryDao">
			<ref bean="sysDataDictionaryDAO" />
		</property>
	</bean>
	
</beans>
