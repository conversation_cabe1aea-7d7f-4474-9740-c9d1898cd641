<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="COC" pageWidth="1190" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1150" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<queryString>
		<![CDATA[select * from CARTYPETEMPLATE]]>
	</queryString>
	<field name="barCodeImageUrl" class="java.lang.String">
		<fieldDescription><![CDATA[barCodeImageUrl]]></fieldDescription>
	</field>
	<field name="c1" class="java.lang.String">
		<fieldDescription><![CDATA[c1]]></fieldDescription>
	</field>
	<field name="c10" class="java.lang.String">
		<fieldDescription><![CDATA[c10]]></fieldDescription>
	</field>
	<field name="c100" class="java.lang.String">
		<fieldDescription><![CDATA[c100]]></fieldDescription>
	</field>
	<field name="c101" class="java.lang.String">
		<fieldDescription><![CDATA[c101]]></fieldDescription>
	</field>
	<field name="c102" class="java.lang.String">
		<fieldDescription><![CDATA[c102]]></fieldDescription>
	</field>
	<field name="c103" class="java.lang.String">
		<fieldDescription><![CDATA[c103]]></fieldDescription>
	</field>
	<field name="c104" class="java.lang.String">
		<fieldDescription><![CDATA[c104]]></fieldDescription>
	</field>
	<field name="c105" class="java.lang.String">
		<fieldDescription><![CDATA[c105]]></fieldDescription>
	</field>
	<field name="c106" class="java.lang.String">
		<fieldDescription><![CDATA[c106]]></fieldDescription>
	</field>
	<field name="c107" class="java.lang.String">
		<fieldDescription><![CDATA[c107]]></fieldDescription>
	</field>
	<field name="c108" class="java.lang.String">
		<fieldDescription><![CDATA[c108]]></fieldDescription>
	</field>
	<field name="c109" class="java.lang.String">
		<fieldDescription><![CDATA[c109]]></fieldDescription>
	</field>
	<field name="c11" class="java.lang.String">
		<fieldDescription><![CDATA[c11]]></fieldDescription>
	</field>
	<field name="c110" class="java.lang.String">
		<fieldDescription><![CDATA[c110]]></fieldDescription>
	</field>
	<field name="c112" class="java.lang.String">
		<fieldDescription><![CDATA[c112]]></fieldDescription>
	</field>
	<field name="c113" class="java.lang.String">
		<fieldDescription><![CDATA[c113]]></fieldDescription>
	</field>
	<field name="c114" class="java.lang.String">
		<fieldDescription><![CDATA[c114]]></fieldDescription>
	</field>
	<field name="c115" class="java.lang.String">
		<fieldDescription><![CDATA[c115]]></fieldDescription>
	</field>
	<field name="c116" class="java.lang.String">
		<fieldDescription><![CDATA[c116]]></fieldDescription>
	</field>
	<field name="c117" class="java.lang.String">
		<fieldDescription><![CDATA[c117]]></fieldDescription>
	</field>
	<field name="c118" class="java.lang.String">
		<fieldDescription><![CDATA[c118]]></fieldDescription>
	</field>
	<field name="c119" class="java.lang.String">
		<fieldDescription><![CDATA[c119]]></fieldDescription>
	</field>
	<field name="c12" class="java.lang.String">
		<fieldDescription><![CDATA[c12]]></fieldDescription>
	</field>
	<field name="c120" class="java.lang.String">
		<fieldDescription><![CDATA[c120]]></fieldDescription>
	</field>
	<field name="c121" class="java.lang.String">
		<fieldDescription><![CDATA[c121]]></fieldDescription>
	</field>
	<field name="c13" class="java.lang.String">
		<fieldDescription><![CDATA[c13]]></fieldDescription>
	</field>
	<field name="c131" class="java.lang.String">
		<fieldDescription><![CDATA[c131]]></fieldDescription>
	</field>
	<field name="c132" class="java.lang.String">
		<fieldDescription><![CDATA[c132]]></fieldDescription>
	</field>
	<field name="c133" class="java.lang.String">
		<fieldDescription><![CDATA[c133]]></fieldDescription>
	</field>
	<field name="c134" class="java.lang.String">
		<fieldDescription><![CDATA[c134]]></fieldDescription>
	</field>
	<field name="c135" class="java.lang.String">
		<fieldDescription><![CDATA[c135]]></fieldDescription>
	</field>
	<field name="c136" class="java.lang.String">
		<fieldDescription><![CDATA[c136]]></fieldDescription>
	</field>
	<field name="c137" class="java.lang.String">
		<fieldDescription><![CDATA[c137]]></fieldDescription>
	</field>
	<field name="c138" class="java.lang.String">
		<fieldDescription><![CDATA[c138]]></fieldDescription>
	</field>
	<field name="c139" class="java.lang.String">
		<fieldDescription><![CDATA[c139]]></fieldDescription>
	</field>
	<field name="c14" class="java.lang.String">
		<fieldDescription><![CDATA[c14]]></fieldDescription>
	</field>
	<field name="c140" class="java.lang.String">
		<fieldDescription><![CDATA[c140]]></fieldDescription>
	</field>
	<field name="c141" class="java.lang.String">
		<fieldDescription><![CDATA[c141]]></fieldDescription>
	</field>
	<field name="c142" class="java.lang.String">
		<fieldDescription><![CDATA[c142]]></fieldDescription>
	</field>
	<field name="c143" class="java.lang.String">
		<fieldDescription><![CDATA[c143]]></fieldDescription>
	</field>
	<field name="c144" class="java.lang.String">
		<fieldDescription><![CDATA[c144]]></fieldDescription>
	</field>
	<field name="c145" class="java.lang.String">
		<fieldDescription><![CDATA[c145]]></fieldDescription>
	</field>
	<field name="c146" class="java.lang.String">
		<fieldDescription><![CDATA[c146]]></fieldDescription>
	</field>
	<field name="c15" class="java.lang.String">
		<fieldDescription><![CDATA[c15]]></fieldDescription>
	</field>
	<field name="c16" class="java.lang.String">
		<fieldDescription><![CDATA[c16]]></fieldDescription>
	</field>
	<field name="c17" class="java.lang.String">
		<fieldDescription><![CDATA[c17]]></fieldDescription>
	</field>
	<field name="c18" class="java.lang.String">
		<fieldDescription><![CDATA[c18]]></fieldDescription>
	</field>
	<field name="c19" class="java.lang.String">
		<fieldDescription><![CDATA[c19]]></fieldDescription>
	</field>
	<field name="c2" class="java.lang.String">
		<fieldDescription><![CDATA[c2]]></fieldDescription>
	</field>
	<field name="c20" class="java.lang.String">
		<fieldDescription><![CDATA[c20]]></fieldDescription>
	</field>
	<field name="c21" class="java.lang.String">
		<fieldDescription><![CDATA[c21]]></fieldDescription>
	</field>
	<field name="c22" class="java.lang.String">
		<fieldDescription><![CDATA[c22]]></fieldDescription>
	</field>
	<field name="c23" class="java.lang.String">
		<fieldDescription><![CDATA[c23]]></fieldDescription>
	</field>
	<field name="c24" class="java.lang.String">
		<fieldDescription><![CDATA[c24]]></fieldDescription>
	</field>
	<field name="c25" class="java.lang.String">
		<fieldDescription><![CDATA[c25]]></fieldDescription>
	</field>
	<field name="c26" class="java.lang.String">
		<fieldDescription><![CDATA[c26]]></fieldDescription>
	</field>
	<field name="c27" class="java.lang.String">
		<fieldDescription><![CDATA[c27]]></fieldDescription>
	</field>
	<field name="c28" class="java.lang.String">
		<fieldDescription><![CDATA[c28]]></fieldDescription>
	</field>
	<field name="c29" class="java.lang.String">
		<fieldDescription><![CDATA[c29]]></fieldDescription>
	</field>
	<field name="c3" class="java.lang.String">
		<fieldDescription><![CDATA[c3]]></fieldDescription>
	</field>
	<field name="c30" class="java.lang.String">
		<fieldDescription><![CDATA[c30]]></fieldDescription>
	</field>
	<field name="c31" class="java.lang.String">
		<fieldDescription><![CDATA[c31]]></fieldDescription>
	</field>
	<field name="c32" class="java.lang.String">
		<fieldDescription><![CDATA[c32]]></fieldDescription>
	</field>
	<field name="c33" class="java.lang.String">
		<fieldDescription><![CDATA[c33]]></fieldDescription>
	</field>
	<field name="c34" class="java.lang.String">
		<fieldDescription><![CDATA[c34]]></fieldDescription>
	</field>
	<field name="c35" class="java.lang.String">
		<fieldDescription><![CDATA[c35]]></fieldDescription>
	</field>
	<field name="c36" class="java.lang.String">
		<fieldDescription><![CDATA[c36]]></fieldDescription>
	</field>
	<field name="c37" class="java.lang.String">
		<fieldDescription><![CDATA[c37]]></fieldDescription>
	</field>
	<field name="c38" class="java.lang.String">
		<fieldDescription><![CDATA[c38]]></fieldDescription>
	</field>
	<field name="c39" class="java.lang.String">
		<fieldDescription><![CDATA[c39]]></fieldDescription>
	</field>
	<field name="c4" class="java.lang.String">
		<fieldDescription><![CDATA[c4]]></fieldDescription>
	</field>
	<field name="c40" class="java.lang.String">
		<fieldDescription><![CDATA[c40]]></fieldDescription>
	</field>
	<field name="c41" class="java.lang.String">
		<fieldDescription><![CDATA[c41]]></fieldDescription>
	</field>
	<field name="c42" class="java.lang.String">
		<fieldDescription><![CDATA[c42]]></fieldDescription>
	</field>
	<field name="c43" class="java.lang.String">
		<fieldDescription><![CDATA[c43]]></fieldDescription>
	</field>
	<field name="c44" class="java.lang.String">
		<fieldDescription><![CDATA[c44]]></fieldDescription>
	</field>
	<field name="c45" class="java.lang.String">
		<fieldDescription><![CDATA[c45]]></fieldDescription>
	</field>
	<field name="c46" class="java.lang.String">
		<fieldDescription><![CDATA[c46]]></fieldDescription>
	</field>
	<field name="c47" class="java.lang.String">
		<fieldDescription><![CDATA[c47]]></fieldDescription>
	</field>
	<field name="c48" class="java.lang.String">
		<fieldDescription><![CDATA[c48]]></fieldDescription>
	</field>
	<field name="c49" class="java.lang.String">
		<fieldDescription><![CDATA[c49]]></fieldDescription>
	</field>
	<field name="c5" class="java.lang.String">
		<fieldDescription><![CDATA[c5]]></fieldDescription>
	</field>
	<field name="c50" class="java.lang.String">
		<fieldDescription><![CDATA[c50]]></fieldDescription>
	</field>
	<field name="c51" class="java.lang.String">
		<fieldDescription><![CDATA[c51]]></fieldDescription>
	</field>
	<field name="c52" class="java.lang.String">
		<fieldDescription><![CDATA[c52]]></fieldDescription>
	</field>
	<field name="c53" class="java.lang.String">
		<fieldDescription><![CDATA[c53]]></fieldDescription>
	</field>
	<field name="c54" class="java.lang.String">
		<fieldDescription><![CDATA[c54]]></fieldDescription>
	</field>
	<field name="c55" class="java.lang.String">
		<fieldDescription><![CDATA[c55]]></fieldDescription>
	</field>
	<field name="c56" class="java.lang.String">
		<fieldDescription><![CDATA[c56]]></fieldDescription>
	</field>
	<field name="c57" class="java.lang.String">
		<fieldDescription><![CDATA[c57]]></fieldDescription>
	</field>
	<field name="c58" class="java.lang.String">
		<fieldDescription><![CDATA[c58]]></fieldDescription>
	</field>
	<field name="c59" class="java.lang.String">
		<fieldDescription><![CDATA[c59]]></fieldDescription>
	</field>
	<field name="c6" class="java.lang.String">
		<fieldDescription><![CDATA[c6]]></fieldDescription>
	</field>
	<field name="c60" class="java.lang.String">
		<fieldDescription><![CDATA[c60]]></fieldDescription>
	</field>
	<field name="c61" class="java.lang.String">
		<fieldDescription><![CDATA[c61]]></fieldDescription>
	</field>
	<field name="c62" class="java.lang.String">
		<fieldDescription><![CDATA[c62]]></fieldDescription>
	</field>
	<field name="c63" class="java.lang.String">
		<fieldDescription><![CDATA[c63]]></fieldDescription>
	</field>
	<field name="c64" class="java.lang.String">
		<fieldDescription><![CDATA[c64]]></fieldDescription>
	</field>
	<field name="c65" class="java.lang.String">
		<fieldDescription><![CDATA[c65]]></fieldDescription>
	</field>
	<field name="c66" class="java.lang.String">
		<fieldDescription><![CDATA[c66]]></fieldDescription>
	</field>
	<field name="c67" class="java.lang.String">
		<fieldDescription><![CDATA[c67]]></fieldDescription>
	</field>
	<field name="c68" class="java.lang.String">
		<fieldDescription><![CDATA[c68]]></fieldDescription>
	</field>
	<field name="c69" class="java.lang.String">
		<fieldDescription><![CDATA[c69]]></fieldDescription>
	</field>
	<field name="c7" class="java.lang.String">
		<fieldDescription><![CDATA[c7]]></fieldDescription>
	</field>
	<field name="c70" class="java.lang.String">
		<fieldDescription><![CDATA[c70]]></fieldDescription>
	</field>
	<field name="c71" class="java.lang.String">
		<fieldDescription><![CDATA[c71]]></fieldDescription>
	</field>
	<field name="c72" class="java.lang.String">
		<fieldDescription><![CDATA[c72]]></fieldDescription>
	</field>
	<field name="c73" class="java.lang.String">
		<fieldDescription><![CDATA[c73]]></fieldDescription>
	</field>
	<field name="c74" class="java.lang.String">
		<fieldDescription><![CDATA[c74]]></fieldDescription>
	</field>
	<field name="c75" class="java.lang.String">
		<fieldDescription><![CDATA[c75]]></fieldDescription>
	</field>
	<field name="c76" class="java.lang.String">
		<fieldDescription><![CDATA[c76]]></fieldDescription>
	</field>
	<field name="c77" class="java.lang.String">
		<fieldDescription><![CDATA[c77]]></fieldDescription>
	</field>
	<field name="c78" class="java.lang.String">
		<fieldDescription><![CDATA[c78]]></fieldDescription>
	</field>
	<field name="c79" class="java.lang.String">
		<fieldDescription><![CDATA[c79]]></fieldDescription>
	</field>
	<field name="c8" class="java.lang.String">
		<fieldDescription><![CDATA[c8]]></fieldDescription>
	</field>
	<field name="c80" class="java.lang.String">
		<fieldDescription><![CDATA[c80]]></fieldDescription>
	</field>
	<field name="c81" class="java.lang.String">
		<fieldDescription><![CDATA[c81]]></fieldDescription>
	</field>
	<field name="c82" class="java.lang.String">
		<fieldDescription><![CDATA[c82]]></fieldDescription>
	</field>
	<field name="c83" class="java.lang.String">
		<fieldDescription><![CDATA[c83]]></fieldDescription>
	</field>
	<field name="c84" class="java.lang.String">
		<fieldDescription><![CDATA[c84]]></fieldDescription>
	</field>
	<field name="c85" class="java.lang.String">
		<fieldDescription><![CDATA[c85]]></fieldDescription>
	</field>
	<field name="c86" class="java.lang.String">
		<fieldDescription><![CDATA[c86]]></fieldDescription>
	</field>
	<field name="c87" class="java.lang.String">
		<fieldDescription><![CDATA[c87]]></fieldDescription>
	</field>
	<field name="c88" class="java.lang.String">
		<fieldDescription><![CDATA[c88]]></fieldDescription>
	</field>
	<field name="c89" class="java.lang.String">
		<fieldDescription><![CDATA[c89]]></fieldDescription>
	</field>
	<field name="c9" class="java.lang.String">
		<fieldDescription><![CDATA[c9]]></fieldDescription>
	</field>
	<field name="c90" class="java.lang.String">
		<fieldDescription><![CDATA[c90]]></fieldDescription>
	</field>
	<field name="c91" class="java.lang.String">
		<fieldDescription><![CDATA[c91]]></fieldDescription>
	</field>
	<field name="c92" class="java.lang.String">
		<fieldDescription><![CDATA[c92]]></fieldDescription>
	</field>
	<field name="c93" class="java.lang.String">
		<fieldDescription><![CDATA[c93]]></fieldDescription>
	</field>
	<field name="c94" class="java.lang.String">
		<fieldDescription><![CDATA[c94]]></fieldDescription>
	</field>
	<field name="c95" class="java.lang.String">
		<fieldDescription><![CDATA[c95]]></fieldDescription>
	</field>
	<field name="c96" class="java.lang.String">
		<fieldDescription><![CDATA[c96]]></fieldDescription>
	</field>
	<field name="c97" class="java.lang.String">
		<fieldDescription><![CDATA[c97]]></fieldDescription>
	</field>
	<field name="c98" class="java.lang.String">
		<fieldDescription><![CDATA[c98]]></fieldDescription>
	</field>
	<field name="c99" class="java.lang.String">
		<fieldDescription><![CDATA[c99]]></fieldDescription>
	</field>
	<field name="carModelPhoto" class="java.lang.String">
		<fieldDescription><![CDATA[carModelPhoto]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="cocNum" class="java.lang.String">
		<fieldDescription><![CDATA[cocNum]]></fieldDescription>
	</field>
	<field name="color" class="java.lang.String">
		<fieldDescription><![CDATA[color]]></fieldDescription>
	</field>
	<field name="engineNo" class="java.lang.String">
		<fieldDescription><![CDATA[engineNo]]></fieldDescription>
	</field>
	<field name="engineType" class="java.lang.String">
		<fieldDescription><![CDATA[engineType]]></fieldDescription>
	</field>
	<field name="imageInputStream" class="java.io.InputStream">
		<fieldDescription><![CDATA[imageInputStream]]></fieldDescription>
	</field>
	<field name="materialNo" class="java.lang.String">
		<fieldDescription><![CDATA[materialNo]]></fieldDescription>
	</field>
	<field name="message" class="java.lang.String">
		<fieldDescription><![CDATA[message]]></fieldDescription>
	</field>
	<field name="printtype" class="java.lang.String">
		<fieldDescription><![CDATA[printtype]]></fieldDescription>
	</field>
	<field name="prodDate" class="java.lang.String">
		<fieldDescription><![CDATA[prodDate]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="validate" class="java.lang.Boolean">
		<fieldDescription><![CDATA[validate]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="3" splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="786" splitType="Stretch">
			<staticText>
				<reportElement x="208" y="43" width="167" height="27"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="20" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆一致性证书]]></text>
			</staticText>
			<staticText>
				<reportElement x="170" y="243" width="159" height="18" isPrintInFirstWholeBand="true"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[第一部分 车辆总体信息]]></text>
			</staticText>
			<staticText>
				<reportElement x="125" y="268" width="107" height="12"/>
				<textElement verticalAlignment="Top">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车辆一致性性证书编号:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="232" y="268" width="219" height="12"/>
				<textElement>
					<font fontName="黑体" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c2}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="289" width="101" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[0.1   车辆生产厂名称:]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="303" width="89" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C 0.1 车辆制造国:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="186" y="303" width="172" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c4}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="331" width="98" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      单元代号/名称:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="195" y="331" width="136" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c98}+"/"+$F{c113}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="195" y="345" width="148" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c6}+"/"+$F{c114}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="345" width="98" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      车型代号/名称:]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="317" width="112" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[0.2   车型系列代号/名称:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="209" y="317" width="148" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c5}+"/"+$F{c112}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="359" width="82" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[0.2.1  车型名称:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="179" y="359" width="177" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c7}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="191" y="373" width="167" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c95}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="373" width="94" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C 0.2 车辆中文品牌:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="191" y="387" width="167" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c96}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="387" width="94" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C 0.3 车辆英文品牌:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="220" y="415" width="141" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c8}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="415" width="124" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[0.5   基本车辆制造商名称：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="179" y="401" width="178" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c97}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="401" width="82" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[0.4   车辆类别:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="250" y="443" width="123" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c10}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="443" width="153" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      最终制造阶段的制造商名称：]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="499" width="94" height="14" isRemoveLineWhenBlank="true"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      车辆识别代号: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="235" y="513" width="264" height="14" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c13}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="485" width="101" height="14" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[0.6   法定铭牌的位置：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="193" y="499" width="165" height="14" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="黑体" size="9" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{vin}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="513" width="138" height="14" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      车辆识别代号的打刻位置: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="198" y="485" width="158" height="14" isRemoveLineWhenBlank="true"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c12}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="541" width="222" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      电动机/发动机编号在电动机/发动机上的位置：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="319" y="541" width="117" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c14}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="97" y="527" width="128" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[21    电动机/发动机编号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="569" width="332" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[在所有方面与本证书第二部分描述的技术参数相符合的完整或多阶段制成车辆：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="203" y="594" width="133" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c15}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="594" width="106" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CCC证书编号(版本号)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="608" width="58" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[签发日期:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="155" y="608" width="109" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c16}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="221" y="194" width="126" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="宋体" size="10" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cocNum}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="764" y="33" width="195" height="20"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[第二部分 车辆一致性证书参数]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="59" width="559" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[(以下所示数值和单位是相应CCC认证文件中给出的。对于生产一致性（COP）试验，这些值必须按照相应标准中所描述的方法进行核对]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="86" width="71" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[1    车轴数量:]]></text>
			</staticText>
			<textField>
				<reportElement x="656" y="86" width="184" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c17}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="978" y="100" width="171" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c22}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="100" width="87" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[3    轴距(mm)：]]></text>
			</staticText>
			<textField>
				<reportElement x="664" y="100" width="176" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c19}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="100" width="80" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[2    驱动轴位置：]]></text>
			</staticText>
			<textField>
				<reportElement x="656" y="114" width="167" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c20}+"/"+$F{c21}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="656" y="128" width="121" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c25}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="296" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[26   最大净功率(kW)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="114" width="71" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[5    轮距(mm)：]]></text>
			</staticText>
			<textField>
				<reportElement x="663" y="156" width="167" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c29}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="184" width="96" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[14.1 额定总质量(kg)：]]></text>
			</staticText>
			<textField>
				<reportElement x="656" y="142" width="184" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c27}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="184" width="148" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c32}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="759" y="170" width="88" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c31}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="142" width="71" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C1   前悬(mm)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="156" width="77" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C2   接近角(°)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="170" width="175" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[12.1 行驶状态下带车身的车辆质量(kg)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="282" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[24   排量(ml)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="310" width="132" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c51}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="755" y="226" width="72" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c40}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="254" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[22   发动机工作原理：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="296" width="159" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c49}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="268" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[23   汽缸数量：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="746" y="212" width="54" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c38}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="254" width="125" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c44}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="310" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[27   离合器型式：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="128" width="71" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[7.1  宽度(mm)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="700" y="240" width="128" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c42}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="268" width="129" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c115}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="212" width="162" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[17   挂车的最大质量(制动下)(kg)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="226" width="171" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[18   牵引车与挂车的最大组合质量(kg)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="240" width="116" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[20   发动机制造商名称：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="324" width="104" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE01 主电动机型号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="366" width="116" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE07 动力电池生产厂名称：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="657" y="394" width="186" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c54}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="394" width="71" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[30   主传动比：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="740" y="338" width="120" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c133}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="722" y="380" width="121" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c139}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="352" width="156" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE05 主电动机最大输出扭矩（N.m)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="740" y="352" width="121" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c135}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="338" width="156" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE03 主电动机最大输出功率（kw）：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="688" y="324" width="124" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c131}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="380" width="138" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE09 动力电池工作电压(V):]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="436" width="138" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[41   车门数量：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="477" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[44   最高车速(km/h)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="766" y="464" width="121" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c63}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="655" y="408" width="232" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c55}+" , "+$F{c56}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="681" y="477" width="106" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c64}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="490" width="137" height="42"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[45   声级]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="464" width="182" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[43.1 如装有牵引装置，其CCC证书编号：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="408" width="71" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[32   轮胎规格：]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="450" width="138" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[42.1 座位数(包括驾驶员座)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="721" y="422" width="122" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c59}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="422" width="138" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[37   车身型式：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="721" y="436" width="122" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c61}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="114" width="87" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[6.1  长度(mm)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="128" width="87" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[8    高度(mm)：]]></text>
			</staticText>
			<textField>
				<reportElement x="978" y="142" width="158" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c28}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="978" y="128" width="171" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c26}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="142" width="87" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[11   后悬(mm)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="184" width="132" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[14.2 该质量的轴荷分配(kg)： ]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="156" width="87" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C3   离去角(°)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1023" y="184" width="127" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c33}+"/"+$F{c34}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="978" y="156" width="171" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c30}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1057" y="212" width="84" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c39}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="198" width="132" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[16   车顶最大允许载荷(kg)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="212" width="166" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[17   挂车的最大质量(非制动下)(kg):]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1023" y="198" width="118" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c37}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="226" width="209" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[19.1 牵引车与挂车连接点处的最大垂直负荷(kg)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="254" width="96" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[22.1 直接喷射：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="987" y="254" width="158" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c45}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="282" width="96" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[25   燃料种类：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1011" y="296" width="130" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c50}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="988" y="310" width="156" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c52}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="310" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[28   变速器型式：]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="324" width="120" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE02 主电动机生产厂名称：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1011" y="324" width="135" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c132}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="555" width="239" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[       CCC认证过程中车辆的制造阶段:  最终阶段]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="198" y="289" width="286" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c3}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="788" y="198" width="93" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c35}+"/"+$F{c36}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="584" y="198" width="203" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[14.3 各车轴或车轴组技术上允许的最大质量(kg)：]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="268" width="96" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[汽缸排列形式：]]></text>
			</staticText>
			<image>
				<reportElement x="244" y="115" width="80" height="80"/>
				<imageExpression class="java.lang.String"><![CDATA[$F{barCodeImageUrl}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="944" y="86" width="113" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c18}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="86" width="54" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车轮数量:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="680" y="282" width="164" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c47}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="721" y="450" width="122" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c62}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="978" y="114" width="79" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c24}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1100" y="226" width="43" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c41}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="97" y="429" width="123" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      基本车辆制造商地址：]]></text>
			</staticText>
			<staticText>
				<reportElement x="97" y="457" width="153" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[      最终制造阶段的制造商地址：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="220" y="429" width="141" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c9}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="160" y="471" width="291" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c11}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="436" width="53" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[车门构造：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="945" y="436" width="149" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c116}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="450" width="53" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[布置方式:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="945" y="450" width="149" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c117}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="987" y="240" width="148" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{engineType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="240" width="96" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[C4   发动机型号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="395" y="646" width="77" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{prodDate}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="584" y="717" width="523" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c120}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="700" y="366" width="178" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c137}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="225" y="527" width="133" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{engineNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="208" y="70" width="156" height="20"/>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="12" isBold="true" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ 用于完整/多阶段制成车辆]]></text>
			</staticText>
			<staticText>
				<reportElement x="337" y="646" width="57" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[发证日期:]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="71" width="538" height="12"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[并考虑这些标准中COP试验的允差。)]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="988" y="268" width="158" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c46}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="988" y="282" width="153" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c48}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="296" width="120" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[对应的发动机转速(min  ):]]></text>
			</staticText>
			<staticText>
				<reportElement x="985" y="296" width="7" height="14"/>
				<textElement>
					<font fontName="Serif" size="5"/>
				</textElement>
				<text><![CDATA[-1]]></text>
			</staticText>
			<staticText>
				<reportElement x="891" y="464" width="91" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[     或试验报告编号:]]></text>
			</staticText>
			<textField>
				<reportElement x="1001" y="464" width="93" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c118}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="338" width="131" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE04 主电动机工作电压（V）：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1022" y="338" width="120" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c134}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="352" width="131" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE06 动力电池型号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1022" y="352" width="120" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c136}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1022" y="366" width="120" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c138}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="366" width="131" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CE08 动力电池容量(Ah)：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="944" y="380" width="202" height="14"/>
				<textElement>
					<font fontName="宋体" size="8" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c53}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="380" width="53" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[29   速比:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="992" y="394" width="150" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c57}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="394" width="101" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[34   转向助力型式：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="992" y="408" width="154" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c58}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="408" width="101" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[35   制动装置简要说明:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="992" y="422" width="150" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{color}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="891" y="422" width="101" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[38   车辆颜色：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="807" y="504" width="62" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c66}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="892" y="490" width="178" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c65}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="979" y="504" width="51" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c119}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="869" y="518" width="121" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c68}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="868" y="504" width="112" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[对应的发动机转速(min  ):]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="504" width="87" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[1.定置噪声(dB(A))：]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="490" width="171" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CCC认证引用的标准号及对应的实施阶段：]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="518" width="147" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[2.加速行驶车外噪声(dB(A)):]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="532" width="137" height="98"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[46.1  排气排放物：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="739" y="560" width="48" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c71}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="532" width="185" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CCC认证引用的标准号及对应的实施阶段:]]></text>
			</staticText>
			<staticText>
				<reportElement x="961" y="560" width="22" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Nox:]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="546" width="109" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[试验用液体燃料：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="983" y="560" width="47" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c73}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="560" width="19" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO ：]]></text>
			</staticText>
			<staticText>
				<reportElement x="854" y="574" width="150" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[烟度(吸收系数(m  )的校正值):]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1004" y="574" width="66" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c75}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="762" y="574" width="81" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c76}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="831" y="546" width="117" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c70}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="574" width="41" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[微粒物：]]></text>
			</staticText>
			<staticText>
				<reportElement x="799" y="560" width="25" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[HC:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="906" y="532" width="124" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c69}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="823" y="560" width="46" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c72}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="630" width="96" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CCC认证引用的标准号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="761" y="658" width="103" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c85}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="761" y="672" width="103" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c86}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="761" y="686" width="103" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c87}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="686" width="40" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[综合]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="864" y="686" width="102" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c90}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="816" y="630" width="143" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c84}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="672" width="40" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[市郊]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="864" y="672" width="102" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c89}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="658" width="40" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[市区]]></text>
			</staticText>
			<staticText>
				<reportElement x="864" y="644" width="102" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[燃料种类消耗量(L/100km)]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="644" width="40" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="761" y="644" width="103" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO 排放量(g/km)]]></text>
			</staticText>
			<staticText>
				<reportElement x="787" y="648" width="7" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="Serif" size="5" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="864" y="658" width="102" height="14"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c88}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="961" y="504" width="7" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="Serif" size="5" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[-1]]></text>
			</staticText>
			<staticText>
				<reportElement x="878" y="560" width="27" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NMHC:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="905" y="560" width="54" height="14">
					<printWhenExpression><![CDATA[new Boolean("2".equals($F{printtype}))]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c74}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1038" y="560" width="45" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[HC+Nox：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="1083" y="560" width="60" height="14">
					<printWhenExpression><![CDATA[new Boolean(!"2".equals($F{printtype}))]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c74}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="906" y="574" width="7" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="Serif" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[-1]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="905" y="602" width="54" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c143}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="854" y="616" width="38" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[微粒物：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="739" y="602" width="48" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c141}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="831" y="588" width="117" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c140}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="965" y="602" width="19" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Nox:]]></text>
			</staticText>
			<staticText>
				<reportElement x="799" y="602" width="25" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[HC:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="892" y="616" width="138" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c146}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="751" y="616" width="43" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c145}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="987" y="602" width="70" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c144}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="823" y="602" width="46" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c142}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="721" y="588" width="110" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[试验用气体燃料如适用：]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="616" width="31" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CH4：]]></text>
			</staticText>
			<staticText>
				<reportElement x="721" y="602" width="19" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[CO ：]]></text>
			</staticText>
			<staticText>
				<reportElement x="878" y="602" width="23" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NMHC:]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="630" width="137" height="70"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[46.2  CO 排放量/燃油消耗量]]></text>
			</staticText>
			<staticText>
				<reportElement x="584" y="703" width="45" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[50 备注:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="629" y="703" width="478" height="14"/>
				<textElement>
					<font fontName="宋体" size="9" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c94}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="622" y="633" width="7" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="Serif" size="6" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[2]]></text>
			</staticText>
			<staticText>
				<reportElement x="925" y="574" width="7" height="14"/>
				<textElement textAlignment="Left">
					<font fontName="Serif" size="5" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[-1]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="584" y="731" width="557" height="38"/>
				<textElement>
					<font fontName="宋体" size="9" isBold="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{c121}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="5" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
