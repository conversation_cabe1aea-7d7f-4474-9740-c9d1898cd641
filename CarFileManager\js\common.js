/**
 * 描述:获得包含中文的字符串长度方法
 * **/
String.prototype.gblen = function() {   
	var len = 0;   
	for (var i=0; i<this.length; i++) {   
		if (this.charCodeAt(i)>127 || this.charCodeAt(i)==94) {   
			len += 2;   
	    } else {   
	    	len ++;   
	    }   
	}     
	    return len;   
}  


/**
 * 描述:校验数字方法
 * **/
function checkNumber(e) {     
	var key = window.event? e.keyCode : e.which;     
	var keychar = String.fromCharCode(key);     
	reg = /\d/;     var result = reg.test(keychar);     
	if(!result){         
		alert("只能输入数字!");   
		return false;    
	}else{		   
		
		return true;     
	} 
} 

/**
 * 描述:校验IP方法
 * **/
function isIP(strIP) {
	var re=/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/g
	if(re.test(strIP)){
		if( RegExp.$1 <256 && RegExp.$2<256 && RegExp.$3<256 && RegExp.$4<256) return true;
	}
	return false;
}

/**
 * 描述:检查日期
 * **/
function isDate(date) {
	var re=/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/g
	if(re.test(date)){		
	 return true;
	}
	return false;
}

function checkIPValid_1() 
{ 
    if(event.shiftKey) 
    { 
        event.returnValue=false; 
        return;                         
    } 
    var keyCode = parseInt(event.keyCode); 
    var result=true; 
    if((keyCode==8) || (keyCode==37) || (keyCode==39) || (keyCode==9)) return; 

    if((keyCode>=48) && (keyCode<=57)) return; 
    else result=false; 
    if((keyCode>=96) && (keyCode<=105)) return; 
    else result=false; 

    if((keyCode==110) || (keyCode==190) || (keyCode==32))  
    { 
        event.keyCode=9; 
        return; 
    } 
    if(result==false) event.returnValue=false; 
} 

function checkIPValid_2(formname,ip) 
{ 
    var form = eval("document."+formname) 
    var ip_1 = parseInt(eval("form."+ip+".value")); 
    if(ip_1>255) 
    { 
        eval("form."+ip).value="254"; 
        alert(ip_1+"不是个有效项目，请指定一个介于1和225之间的数值"); 
    } 
    else if(ip_1==127) 
    { 
        eval("form."+ip).value="1"; 
        alert("以127开头的IP地址无效，因为它们保留用作环回地址，请在1和223之间指定一些其他有效值"); 
    }   
} 

function checkIPValid_3(mask) 
{    
    if(typeof(mask) != 'object'){ 
        mask = eval(mask) 
    } 
    var maskInt=parseInt(mask.value); 
    if((maskInt<0) || (maskInt>255)) 
    { 
        mask.value=255; 
        alert(maskInt+"不是个有效项目，请指定一个介于0和255之间的数值"); 
    } 
} 

function checkIPValid_4(mask) 
{ 
    var maskInt=parseInt(mask.value); 
    if((maskInt<0) || (maskInt>32)) 
    { 
        mask.value=""; 
        alert("掩码错误，请指定一个介于0和32之间的数值"); 
    } 
} 

function checkIPValid_5() 
{ 
    var keyCode = parseInt(event.keyCode); 
    var result=true; 
    if((keyCode==219) || (keyCode==221) || (keyCode==191) || (keyCode==220) || (keyCode==186) || (keyCode==32))
    { 
        result=false;     
    } 
    if(result==false) event.returnValue=false; 
}


/**下面的方法需要用到jquery**/

function updateTips(obj,message) {
	obj.text(message).effect("highlight",{},1500);
}

function checkLength(obj,min,max) {
	if ( obj.val().gblen() > max || obj.val().gblen() < min ) {
		return false;
	} else {
		return true;
	}

}

function checkLengthWithoutSpace(obj,min,max) {
	if ( jQuery.trim(obj.val()).gblen() > max || jQuery.trim(obj.val()).gblen() < min ) {
		return false;
	} else {
		return true;
	}

}

function checkRegexp(obj,regexp) {
	if ( !( regexp.test( obj.val() ) ) ) {
		return false;
	}else{
		return true;
	}
}


/**
 * 描述:全角
 * **/
function checkQj(str) { 
	if(jQuery.trim(str).gblen() == 0){
		return false;
	}    
	reg =/[\uff00-\uffff]/g;
	return str.test(reg);	
} 

/**
 * 描述:半角
 * **/
function checkBj(str) { 
	if(jQuery.trim(str).gblen() == 0){
		return false;
	}    
	reg =/[\u0000-\u00ff]/g;
	return str.test(reg);	
}

/**
 * 定义转换方法
 * @param data 数据
 * @returns
 */
function json2Bean(data){
	try{
		if (typeof(data) == 'string')
			return eval('(' + data + ')');
		else
			return data;
	} catch (e){
		return null;
	}
}
