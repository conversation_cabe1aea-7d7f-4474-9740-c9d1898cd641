package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CarProductionModelId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarProductionModelId implements java.io.Serializable {

	// Fields

	private String model;
	private String vercode;

	// Constructors

	/** default constructor */
	public CarProductionModelId() {
	}

	/** full constructor */
	public CarProductionModelId(String model, String vercode) {
		this.model = model;
		this.vercode = vercode;
	}

	// Property accessors

	public String getModel() {
		return this.model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getVercode() {
		return this.vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof CarProductionModelId))
			return false;
		CarProductionModelId castOther = (CarProductionModelId) other;

		return ((this.getModel() == castOther.getModel()) || (this.getModel() != null
				&& castOther.getModel() != null && this.getModel().equals(
				castOther.getModel())))
				&& ((this.getVercode() == castOther.getVercode()) || (this
						.getVercode() != null
						&& castOther.getVercode() != null && this.getVercode()
						.equals(castOther.getVercode())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getModel() == null ? 0 : this.getModel().hashCode());
		result = 37 * result
				+ (getVercode() == null ? 0 : this.getVercode().hashCode());
		return result;
	}

}