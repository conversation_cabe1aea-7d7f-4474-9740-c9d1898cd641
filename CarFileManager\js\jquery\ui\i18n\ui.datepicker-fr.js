﻿/* French initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (<EMAIL>) and <PERSON><PERSON><PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['fr'] = {
		closeText: 'Fermer',
		prevText: '&#x3c;Préc',
		nextText: 'Suiv&#x3e;',
		currentText: 'Courant',
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON>in',
		'<PERSON><PERSON><PERSON>','Août','Septembre','Octobre','Novembre','Décembre'],
		monthNamesShort: ['Jan','Fév','Mar','Avr','Mai','Jun',
		'Jul','Ao<PERSON>','Sep','Oct','Nov','<PERSON>éc'],
		dayNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['Di<PERSON>','Lu<PERSON>','<PERSON>','Me<PERSON>','Jeu','V<PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON>','Lu','Ma','Me','Je','Ve','Sa'],
		dateFormat: 'dd/mm/yy', firstDay: 1,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['fr']);
});