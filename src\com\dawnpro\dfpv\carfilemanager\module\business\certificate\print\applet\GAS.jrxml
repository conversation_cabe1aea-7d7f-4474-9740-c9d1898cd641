<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="GAS"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="504"
		 pageHeight="648"
		 columnWidth="458"
		 columnSpacing="0"
		 leftMargin="23"
		 rightMargin="23"
		 topMargin="14"
		 bottomMargin="0"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="upimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="downimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G1image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="sjxz" isForPrompting="true" class="java.lang.String"/>

	<field name="carConsistencyNumber" class="java.lang.String">
		<fieldDescription><![CDATA[carConsistencyNumber]]></fieldDescription>
	</field>
	<field name="cityStatus" class="java.lang.String">
		<fieldDescription><![CDATA[cityStatus]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="colligateStatus" class="java.lang.String">
		<fieldDescription><![CDATA[colligateStatus]]></fieldDescription>
	</field>
	<field name="date" class="java.lang.String">
		<fieldDescription><![CDATA[date]]></fieldDescription>
	</field>
	<field name="derailleurType" class="java.lang.String">
		<fieldDescription><![CDATA[derailleurType]]></fieldDescription>
	</field>
	<field name="driveType" class="java.lang.String">
		<fieldDescription><![CDATA[driveType]]></fieldDescription>
	</field>
	<field name="engineModel" class="java.lang.String">
		<fieldDescription><![CDATA[engineModel]]></fieldDescription>
	</field>
	<field name="enterprise" class="java.lang.String">
		<fieldDescription><![CDATA[enterprise]]></fieldDescription>
	</field>
	<field name="environsStatus" class="java.lang.String">
		<fieldDescription><![CDATA[environsStatus]]></fieldDescription>
	</field>
	<field name="fuelType" class="java.lang.String">
		<fieldDescription><![CDATA[fuelType]]></fieldDescription>
	</field>
	<field name="limitOne" class="java.lang.String">
		<fieldDescription><![CDATA[limitOne]]></fieldDescription>
	</field>
	<field name="limitTwo" class="java.lang.String">
		<fieldDescription><![CDATA[limitTwo]]></fieldDescription>
	</field>
	<field name="maxDesignSumQuality" class="java.lang.String">
		<fieldDescription><![CDATA[maxDesignSumQuality]]></fieldDescription>
	</field>
	<field name="model" class="java.lang.String">
		<fieldDescription><![CDATA[model]]></fieldDescription>
	</field>
	<field name="quality" class="java.lang.String">
		<fieldDescription><![CDATA[quality]]></fieldDescription>
	</field>
	<field name="range" class="java.lang.String">
		<fieldDescription><![CDATA[range]]></fieldDescription>
	</field>
	<field name="ratingPower" class="java.lang.String">
		<fieldDescription><![CDATA[ratingPower]]></fieldDescription>
	</field>
	<field name="remark" class="java.lang.String">
		<fieldDescription><![CDATA[remark]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="qebz" class="java.lang.String"/>
	<field name="gjbz" class="java.lang.String"/>
	<field name="dymb" class="java.lang.String"/>
	<field name="lpz" class="java.lang.String"/>
	<field name="xs" class="java.lang.String"/>
	<field name="sjxz" class="java.lang.String"/>
	<field name="dsgk" class="java.lang.String"/>
	<field name="zsgk" class="java.lang.String"/>
	<field name="gsgk" class="java.lang.String"/>
	<field name="cgsgk" class="java.lang.String"/>
	<field name="rlxhlbt" class="java.lang.String"/>
	<field name="rlxhlsj" class="java.lang.String"/>
	<field name="ydzhrlxhl" class="java.lang.String"/>
	<field name="ygnycb" class="java.lang.String"/>
	<field name="rlxhlbj1" class="java.lang.String"/>
	<field name="rlxhlbj2" class="java.lang.String"/>
	<field name="zhcopl" class="java.lang.String"/>
	<field name="bacode" class="java.lang.String"/>
	<field name="zdjgl" class="java.lang.String">
		<fieldDescription><![CDATA[zdjgl]]></fieldDescription>
	</field>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="57"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="18"
						y="4"
						width="105"
						height="50"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="黑体" size="23" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{qebz}]]></textFieldExpression>
				</textField>
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="517"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="71"
						y="73"
						width="95"
						height="14"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{driveType}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="77"
						y="150"
						width="62"
						height="20"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dsgk}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="258"
						y="45"
						width="54"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{range}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="215"
						y="115"
						width="99"
						height="32"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="28" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{colligateStatus}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="259"
						y="59"
						width="63"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zdjgl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="106"
						y="59"
						width="73"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{maxDesignSumQuality}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="278"
						y="492"
						width="146"
						height="21"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{date}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="12"
						y="354"
						width="350"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="3.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="60"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="12"
						y="342"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="46"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="74"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="102"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="130"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="158"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="186"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="214"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="361"
						y="342"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="242"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="270"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="298"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="88"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="116"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="144"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="172"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="200"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="228"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="256"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="284"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<line direction="TopDown">
					<reportElement
						x="326"
						y="341"
						width="1"
						height="13"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="2.0"/>
</graphicElement>
				</line>
				<line direction="TopDown">
					<reportElement
						x="312"
						y="347"
						width="1"
						height="7"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="37"
						y="356"
						width="19"
						height="20"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{upimage}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="32"
						y="371"
						width="36"
						height="16"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[领跑值]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="317"
						y="371"
						width="24"
						height="16"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[限值]]></text>
				</staticText>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="317"
						y="356"
						width="19"
						height="20"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{upimage}]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="242"
						y="30"
						width="138"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{fuelType}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="45"
						width="94"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[整车整备质量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="59"
						width="103"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大设计总质量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="73"
						width="71"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动型式：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="87"
						width="71"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[其他信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="177"
						y="31"
						width="65"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[能源种类：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="177"
						y="45"
						width="47"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[排量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="176"
						y="59"
						width="81"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大净功率：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="106"
						y="45"
						width="73"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{quality}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="11"
						y="130"
						width="123"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement rotation="None">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[综合燃料消耗量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="140"
						y="154"
						width="52"
						height="16"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="69"
						y="312"
						width="31"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="71"
						y="326"
						width="19"
						height="20"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{downimage}]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="25"
						y="384"
						width="39"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{lpz}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="318"
						y="383"
						width="39"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xs}]]></textFieldExpression>
				</textField>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="321"
						y="424"
						width="39"
						height="58"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{G1image}]]></imageExpression>
				</image>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="485"
						width="364"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="1.0"/>
</graphicElement>
				</line>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="2"
						y="296"
						width="248"
						height="18"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{sjxz}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="65"
						y="370"
						width="250"
						height="115"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["    "+$F{gjbz}.replaceAll("\\\\n","\\\n    ")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="17"
						width="68"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="71"
						y="17"
						width="99"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{model}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="176"
						y="17"
						width="82"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机型号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="259"
						y="17"
						width="100"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{engineModel}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="71"
						y="3"
						width="217"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{enterprise}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="3"
						width="69"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产企业：]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="0"
						width="375"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="3.0"/>
</graphicElement>
				</line>
				<staticText>
					<reportElement
						x="352"
						y="45"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[mL]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="352"
						y="59"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kW]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="156"
						y="46"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="157"
						y="58"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="1"
						y="492"
						width="54"
						height="21"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[备案号：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="208"
						y="492"
						width="70"
						height="21"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[启用日期：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="31"
						width="78"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[变速器类型：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="80"
						y="31"
						width="99"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{derailleurType}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="55"
						y="492"
						width="150"
						height="21"
						key="textField"/>
					<box></box>
					<textElement verticalAlignment="Top">
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{bacode}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="71"
						y="87"
						width="359"
						height="14"
						key="textField-1"/>
					<box></box>
					<textElement textAlignment="Left">
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{remark}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="11"
						y="150"
						width="66"
						height="20"
						key="staticText-1"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[低速：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="11"
						y="190"
						width="123"
						height="20"
						key="staticText-2"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[预估能源成本：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="11"
						y="170"
						width="66"
						height="20"
						key="staticText-3"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[高速：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="11"
						y="210"
						width="123"
						height="20"
						key="staticText-4"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO2排放量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="77"
						y="170"
						width="62"
						height="20"
						key="textField-2"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{gsgk}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="140"
						y="174"
						width="52"
						height="16"
						key="staticText-6"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="194"
						y="150"
						width="66"
						height="20"
						key="staticText-7"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[中速：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="260"
						y="150"
						width="62"
						height="20"
						key="textField-3"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zsgk}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="309"
						y="154"
						width="52"
						height="16"
						key="staticText-8"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="194"
						y="170"
						width="66"
						height="20"
						key="staticText-9"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[超高速：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="260"
						y="170"
						width="62"
						height="20"
						key="textField-4"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{cgsgk}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="309"
						y="174"
						width="52"
						height="16"
						key="staticText-10"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="309"
						y="130"
						width="52"
						height="16"
						key="staticText-11"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="260"
						y="190"
						width="62"
						height="20"
						key="textField-5"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ygnycb}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="309"
						y="194"
						width="52"
						height="16"
						key="staticText-12"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[元/100 km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="260"
						y="210"
						width="62"
						height="20"
						key="textField-6"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zhcopl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="309"
						y="214"
						width="52"
						height="16"
						key="staticText-13"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[g/km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="299"
						y="239"
						width="62"
						height="20"
						key="textField-7"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{rlxhlsj}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="12"
						y="239"
						width="285"
						height="20"
						key="textField-8"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{rlxhlbt}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="11"
						y="233"
						width="358"
						height="5"
						key="staticText-14"/>
					<box>					<pen lineWidth="0.5" lineStyle="Dashed"/>
					<topPen lineWidth="1.25" lineStyle="Dashed"/>
					<leftPen lineWidth="0.0" lineStyle="Dashed"/>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
					<rightPen lineWidth="0.0" lineStyle="Dashed"/>
</box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
			</band>
		</columnHeader>
		<detail>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
