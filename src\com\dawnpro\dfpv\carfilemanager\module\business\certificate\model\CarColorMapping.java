package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * CarColorMapping entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarColorMapping implements java.io.Serializable {

	// Fields

	private String code;
	private String color;
	private String basecolor;
	private String encolor;

	// Constructors

	/** default constructor */
	public CarColorMapping() {
	}

	/** minimal constructor */
	public CarColorMapping(String code) {
		this.code = code;
	}

	/** full constructor */
	public CarColorMapping(String code, String color, String basecolor, String encolor) {
		this.code = code;
		this.color = color;
		this.basecolor = basecolor;
		this.encolor = encolor;
	}

	// Property accessors

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getColor() {
		return this.color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public String getBasecolor() {
		return this.basecolor;
	}

	public void setBasecolor(String basecolor) {
		this.basecolor = basecolor;
	}

	/**
	 * @return the encolor
	 */
	public String getEncolor() {
		return encolor;
	}

	/**
	 * @param encolor the encolor to set
	 */
	public void setEncolor(String encolor) {
		this.encolor = encolor;
	}

}