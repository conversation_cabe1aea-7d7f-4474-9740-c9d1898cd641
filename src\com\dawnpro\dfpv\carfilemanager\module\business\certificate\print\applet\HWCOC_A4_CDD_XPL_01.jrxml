<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="HWCOC_A4_CDD_XPL_01"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Landscape"
		 pageWidth="842"
		 pageHeight="595"
		 columnWidth="802"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.zoom" value="1.5" />
	<property name="ireport.x" value="20" />
	<property name="ireport.y" value="0" />
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />


	<field name="barCodeImageUrl" class="java.lang.String">
		<fieldDescription><![CDATA[barCodeImageUrl]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="year" class="java.lang.String">
		<fieldDescription><![CDATA[year]]></fieldDescription>
	</field>
	<field name="yearseq" class="java.lang.String">
		<fieldDescription><![CDATA[yearseq]]></fieldDescription>
	</field>
	<field name="color" class="java.lang.String">
		<fieldDescription><![CDATA[color]]></fieldDescription>
	</field>
	<field name="imageInputStream" class="java.io.InputStream">
		<fieldDescription><![CDATA[imageInputStream]]></fieldDescription>
	</field>
	<field name="prodDate" class="java.lang.String">
		<fieldDescription><![CDATA[prodDate]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="c1" class="java.lang.String">
		<fieldDescription><![CDATA[c1]]></fieldDescription>
	</field>
	<field name="c2" class="java.lang.String">
		<fieldDescription><![CDATA[c2]]></fieldDescription>
	</field>
	<field name="c3" class="java.lang.String">
		<fieldDescription><![CDATA[c3]]></fieldDescription>
	</field>
	<field name="c4" class="java.lang.String">
		<fieldDescription><![CDATA[c4]]></fieldDescription>
	</field>
	<field name="c5" class="java.lang.String">
		<fieldDescription><![CDATA[c5]]></fieldDescription>
	</field>
	<field name="c6" class="java.lang.String">
		<fieldDescription><![CDATA[c6]]></fieldDescription>
	</field>
	<field name="c7" class="java.lang.String">
		<fieldDescription><![CDATA[c7]]></fieldDescription>
	</field>
	<field name="c8" class="java.lang.String">
		<fieldDescription><![CDATA[c8]]></fieldDescription>
	</field>
	<field name="c9" class="java.lang.String">
		<fieldDescription><![CDATA[c9]]></fieldDescription>
	</field>
	<field name="c10" class="java.lang.String">
		<fieldDescription><![CDATA[c10]]></fieldDescription>
	</field>
	<field name="c11" class="java.lang.String">
		<fieldDescription><![CDATA[c11]]></fieldDescription>
	</field>
	<field name="c12" class="java.lang.String">
		<fieldDescription><![CDATA[c12]]></fieldDescription>
	</field>
	<field name="c13" class="java.lang.String">
		<fieldDescription><![CDATA[c13]]></fieldDescription>
	</field>
	<field name="c14" class="java.lang.String">
		<fieldDescription><![CDATA[c14]]></fieldDescription>
	</field>
	<field name="c15" class="java.lang.String">
		<fieldDescription><![CDATA[c15]]></fieldDescription>
	</field>
	<field name="c16" class="java.lang.String">
		<fieldDescription><![CDATA[c16]]></fieldDescription>
	</field>
	<field name="c17" class="java.lang.String">
		<fieldDescription><![CDATA[c17]]></fieldDescription>
	</field>
	<field name="c18" class="java.lang.String">
		<fieldDescription><![CDATA[c18]]></fieldDescription>
	</field>
	<field name="c19" class="java.lang.String">
		<fieldDescription><![CDATA[c19]]></fieldDescription>
	</field>
	<field name="c20" class="java.lang.String">
		<fieldDescription><![CDATA[c20]]></fieldDescription>
	</field>
	<field name="c21" class="java.lang.String">
		<fieldDescription><![CDATA[c21]]></fieldDescription>
	</field>
	<field name="c22" class="java.lang.String">
		<fieldDescription><![CDATA[c22]]></fieldDescription>
	</field>
	<field name="c23" class="java.lang.String">
		<fieldDescription><![CDATA[c23]]></fieldDescription>
	</field>
	<field name="c24" class="java.lang.String">
		<fieldDescription><![CDATA[c24]]></fieldDescription>
	</field>
	<field name="c25" class="java.lang.String">
		<fieldDescription><![CDATA[c25]]></fieldDescription>
	</field>
	<field name="c26" class="java.lang.String">
		<fieldDescription><![CDATA[c26]]></fieldDescription>
	</field>
	<field name="c27" class="java.lang.String">
		<fieldDescription><![CDATA[c27]]></fieldDescription>
	</field>
	<field name="c28" class="java.lang.String">
		<fieldDescription><![CDATA[c28]]></fieldDescription>
	</field>
	<field name="c29" class="java.lang.String">
		<fieldDescription><![CDATA[c29]]></fieldDescription>
	</field>
	<field name="c30" class="java.lang.String">
		<fieldDescription><![CDATA[c30]]></fieldDescription>
	</field>
	<field name="c31" class="java.lang.String">
		<fieldDescription><![CDATA[c31]]></fieldDescription>
	</field>
	<field name="c32" class="java.lang.String">
		<fieldDescription><![CDATA[c32]]></fieldDescription>
	</field>
	<field name="c33" class="java.lang.String">
		<fieldDescription><![CDATA[c33]]></fieldDescription>
	</field>
	<field name="c34" class="java.lang.String">
		<fieldDescription><![CDATA[c34]]></fieldDescription>
	</field>
	<field name="c35" class="java.lang.String">
		<fieldDescription><![CDATA[c35]]></fieldDescription>
	</field>
	<field name="c36" class="java.lang.String">
		<fieldDescription><![CDATA[c36]]></fieldDescription>
	</field>
	<field name="c37" class="java.lang.String">
		<fieldDescription><![CDATA[c37]]></fieldDescription>
	</field>
	<field name="c38" class="java.lang.String">
		<fieldDescription><![CDATA[c38]]></fieldDescription>
	</field>
	<field name="c39" class="java.lang.String">
		<fieldDescription><![CDATA[c39]]></fieldDescription>
	</field>
	<field name="c40" class="java.lang.String">
		<fieldDescription><![CDATA[c40]]></fieldDescription>
	</field>
	<field name="c41" class="java.lang.String">
		<fieldDescription><![CDATA[c41]]></fieldDescription>
	</field>
	<field name="c42" class="java.lang.String">
		<fieldDescription><![CDATA[c42]]></fieldDescription>
	</field>
	<field name="c43" class="java.lang.String">
		<fieldDescription><![CDATA[c43]]></fieldDescription>
	</field>
	<field name="c44" class="java.lang.String">
		<fieldDescription><![CDATA[c44]]></fieldDescription>
	</field>
	<field name="c45" class="java.lang.String">
		<fieldDescription><![CDATA[c45]]></fieldDescription>
	</field>
	<field name="c46" class="java.lang.String">
		<fieldDescription><![CDATA[c46]]></fieldDescription>
	</field>
	<field name="c47" class="java.lang.String">
		<fieldDescription><![CDATA[c47]]></fieldDescription>
	</field>
	<field name="c48" class="java.lang.String">
		<fieldDescription><![CDATA[c48]]></fieldDescription>
	</field>
	<field name="c49" class="java.lang.String">
		<fieldDescription><![CDATA[c49]]></fieldDescription>
	</field>
	<field name="c50" class="java.lang.String">
		<fieldDescription><![CDATA[c50]]></fieldDescription>
	</field>
	<field name="c51" class="java.lang.String">
		<fieldDescription><![CDATA[c51]]></fieldDescription>
	</field>
	<field name="c52" class="java.lang.String">
		<fieldDescription><![CDATA[c52]]></fieldDescription>
	</field>
	<field name="c53" class="java.lang.String">
		<fieldDescription><![CDATA[c53]]></fieldDescription>
	</field>
	<field name="c54" class="java.lang.String">
		<fieldDescription><![CDATA[c54]]></fieldDescription>
	</field>
	<field name="c55" class="java.lang.String">
		<fieldDescription><![CDATA[c55]]></fieldDescription>
	</field>
	<field name="c56" class="java.lang.String">
		<fieldDescription><![CDATA[c56]]></fieldDescription>
	</field>
	<field name="c57" class="java.lang.String">
		<fieldDescription><![CDATA[c57]]></fieldDescription>
	</field>
	<field name="c58" class="java.lang.String">
		<fieldDescription><![CDATA[c58]]></fieldDescription>
	</field>
	<field name="c59" class="java.lang.String">
		<fieldDescription><![CDATA[c59]]></fieldDescription>
	</field>
	<field name="c60" class="java.lang.String">
		<fieldDescription><![CDATA[c60]]></fieldDescription>
	</field>
	<field name="c61" class="java.lang.String">
		<fieldDescription><![CDATA[c61]]></fieldDescription>
	</field>
	<field name="c62" class="java.lang.String">
		<fieldDescription><![CDATA[c62]]></fieldDescription>
	</field>
	<field name="c63" class="java.lang.String">
		<fieldDescription><![CDATA[c63]]></fieldDescription>
	</field>
	<field name="c64" class="java.lang.String">
		<fieldDescription><![CDATA[c64]]></fieldDescription>
	</field>
	<field name="c65" class="java.lang.String">
		<fieldDescription><![CDATA[c65]]></fieldDescription>
	</field>
	<field name="c66" class="java.lang.String">
		<fieldDescription><![CDATA[c66]]></fieldDescription>
	</field>
	<field name="c67" class="java.lang.String">
		<fieldDescription><![CDATA[c67]]></fieldDescription>
	</field>
	<field name="c68" class="java.lang.String">
		<fieldDescription><![CDATA[c68]]></fieldDescription>
	</field>
	<field name="c69" class="java.lang.String">
		<fieldDescription><![CDATA[c69]]></fieldDescription>
	</field>
	<field name="c70" class="java.lang.String">
		<fieldDescription><![CDATA[c70]]></fieldDescription>
	</field>
	<field name="c71" class="java.lang.String">
		<fieldDescription><![CDATA[c71]]></fieldDescription>
	</field>
	<field name="c72" class="java.lang.String">
		<fieldDescription><![CDATA[c72]]></fieldDescription>
	</field>
	<field name="c73" class="java.lang.String">
		<fieldDescription><![CDATA[c73]]></fieldDescription>
	</field>
	<field name="c74" class="java.lang.String">
		<fieldDescription><![CDATA[c74]]></fieldDescription>
	</field>
	<field name="c75" class="java.lang.String">
		<fieldDescription><![CDATA[c75]]></fieldDescription>
	</field>
	<field name="c76" class="java.lang.String">
		<fieldDescription><![CDATA[c76]]></fieldDescription>
	</field>
	<field name="c77" class="java.lang.String">
		<fieldDescription><![CDATA[c77]]></fieldDescription>
	</field>
	<field name="c78" class="java.lang.String">
		<fieldDescription><![CDATA[c78]]></fieldDescription>
	</field>
	<field name="c79" class="java.lang.String">
		<fieldDescription><![CDATA[c79]]></fieldDescription>
	</field>
	<field name="c80" class="java.lang.String">
		<fieldDescription><![CDATA[c80]]></fieldDescription>
	</field>
	<field name="c81" class="java.lang.String">
		<fieldDescription><![CDATA[c81]]></fieldDescription>
	</field>
	<field name="c82" class="java.lang.String">
		<fieldDescription><![CDATA[c82]]></fieldDescription>
	</field>
	<field name="c83" class="java.lang.String">
		<fieldDescription><![CDATA[c83]]></fieldDescription>
	</field>
	<field name="c84" class="java.lang.String">
		<fieldDescription><![CDATA[c84]]></fieldDescription>
	</field>
	<field name="c85" class="java.lang.String">
		<fieldDescription><![CDATA[c85]]></fieldDescription>
	</field>
	<field name="c86" class="java.lang.String">
		<fieldDescription><![CDATA[c86]]></fieldDescription>
	</field>
	<field name="c87" class="java.lang.String">
		<fieldDescription><![CDATA[c87]]></fieldDescription>
	</field>
	<field name="c88" class="java.lang.String">
		<fieldDescription><![CDATA[c88]]></fieldDescription>
	</field>
	<field name="c89" class="java.lang.String">
		<fieldDescription><![CDATA[c89]]></fieldDescription>
	</field>
	<field name="c90" class="java.lang.String">
		<fieldDescription><![CDATA[c90]]></fieldDescription>
	</field>
	<field name="c91" class="java.lang.String">
		<fieldDescription><![CDATA[c91]]></fieldDescription>
	</field>
	<field name="c92" class="java.lang.String">
		<fieldDescription><![CDATA[c92]]></fieldDescription>
	</field>
	<field name="c93" class="java.lang.String">
		<fieldDescription><![CDATA[c93]]></fieldDescription>
	</field>
	<field name="c94" class="java.lang.String">
		<fieldDescription><![CDATA[c94]]></fieldDescription>
	</field>
	<field name="c95" class="java.lang.String">
		<fieldDescription><![CDATA[c95]]></fieldDescription>
	</field>
	<field name="c96" class="java.lang.String">
		<fieldDescription><![CDATA[c96]]></fieldDescription>
	</field>
	<field name="c97" class="java.lang.String">
		<fieldDescription><![CDATA[c97]]></fieldDescription>
	</field>
	<field name="c98" class="java.lang.String">
		<fieldDescription><![CDATA[c98]]></fieldDescription>
	</field>
	<field name="c99" class="java.lang.String">
		<fieldDescription><![CDATA[c99]]></fieldDescription>
	</field>
	<field name="c100" class="java.lang.String">
		<fieldDescription><![CDATA[c100]]></fieldDescription>
	</field>
	<field name="c101" class="java.lang.String">
		<fieldDescription><![CDATA[c101]]></fieldDescription>
	</field>
	<field name="c102" class="java.lang.String">
		<fieldDescription><![CDATA[c102]]></fieldDescription>
	</field>
	<field name="c103" class="java.lang.String">
		<fieldDescription><![CDATA[c103]]></fieldDescription>
	</field>
	<field name="c104" class="java.lang.String">
		<fieldDescription><![CDATA[c104]]></fieldDescription>
	</field>
	<field name="c105" class="java.lang.String">
		<fieldDescription><![CDATA[c105]]></fieldDescription>
	</field>
	<field name="c106" class="java.lang.String">
		<fieldDescription><![CDATA[c106]]></fieldDescription>
	</field>
	<field name="c107" class="java.lang.String">
		<fieldDescription><![CDATA[c107]]></fieldDescription>
	</field>
	<field name="c108" class="java.lang.String">
		<fieldDescription><![CDATA[c108]]></fieldDescription>
	</field>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="555"  isSplitAllowed="true" >
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="80"
						y="513"
						width="97"
						height="25"
						key="image-1"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$F{barCodeImageUrl}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="20"
						y="19"
						width="384"
						height="14"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="10" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[COMPLETE VEHICLES TYPE-APPROVED IN SMALL SERIES]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="20"
						y="47"
						width="384"
						height="14"
						key="staticText-2"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="10" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[CERTIFICATE OF CONFORMITY]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="20"
						y="61"
						width="82"
						height="12"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  PART 1]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="20"
						y="73"
						width="82"
						height="12"
						key="staticText-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  The undersigned:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="102"
						y="73"
						width="302"
						height="12"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="85"
						width="39"
						height="12"
						key="staticText-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="85"
						width="166"
						height="12"
						key="staticText-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Make (Trade name of manufacturer)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="85"
						width="15"
						height="12"
						key="staticText-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="85"
						width="164"
						height="12"
						key="textField-4"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c2}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="97"
						width="39"
						height="12"
						key="staticText-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="97"
						width="166"
						height="12"
						key="staticText-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Type]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="97"
						width="15"
						height="12"
						key="staticText-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="97"
						width="164"
						height="12"
						key="textField-5"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c3}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="109"
						width="39"
						height="12"
						key="staticText-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="109"
						width="166"
						height="12"
						key="staticText-12"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  — Variant]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="109"
						width="15"
						height="12"
						key="staticText-13"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="109"
						width="164"
						height="12"
						key="textField-6"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c4}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="121"
						width="39"
						height="12"
						key="staticText-14"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="121"
						width="166"
						height="12"
						key="staticText-15"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  — Version]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="121"
						width="15"
						height="12"
						key="staticText-16"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="121"
						width="164"
						height="12"
						key="textField-7"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c5}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="133"
						width="39"
						height="12"
						key="staticText-17"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="133"
						width="166"
						height="12"
						key="staticText-18"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Commercial name(s)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="133"
						width="15"
						height="12"
						key="staticText-19"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="133"
						width="164"
						height="12"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c6}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="145"
						width="39"
						height="44"
						key="staticText-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.2.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="145"
						width="166"
						height="44"
						key="staticText-21"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Allowed Parameter Values for multistage type approval to use the base vehicle emission values (insert range where applicable)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="145"
						width="15"
						height="44"
						key="staticText-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="145"
						width="164"
						height="44"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c7}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="189"
						width="39"
						height="12"
						key="staticText-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="189"
						width="166"
						height="12"
						key="staticText-24"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Identifiers]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="189"
						width="15"
						height="12"
						key="staticText-25"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="189"
						width="164"
						height="12"
						key="textField-10"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c8}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="201"
						width="39"
						height="12"
						key="staticText-26"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="201"
						width="166"
						height="12"
						key="staticText-27"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Interpolation family’s identifier]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="201"
						width="15"
						height="12"
						key="staticText-28"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="201"
						width="164"
						height="12"
						key="textField-11"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c9}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="213"
						width="39"
						height="12"
						key="staticText-29"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.2.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="213"
						width="166"
						height="12"
						key="staticText-30"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  ATCT family’s identifier]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="213"
						width="15"
						height="12"
						key="staticText-31"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="213"
						width="164"
						height="12"
						key="textField-12"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c10}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="225"
						width="39"
						height="12"
						key="staticText-32"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.3.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="225"
						width="166"
						height="12"
						key="staticText-33"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  PEMS family’s identifier]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="225"
						width="15"
						height="12"
						key="staticText-34"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="225"
						width="164"
						height="12"
						key="textField-13"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c11}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="237"
						width="39"
						height="12"
						key="staticText-35"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="237"
						width="166"
						height="12"
						key="staticText-36"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Roadload family’s identifier]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="237"
						width="15"
						height="12"
						key="staticText-37"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="237"
						width="164"
						height="12"
						key="textField-14"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c12}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="249"
						width="39"
						height="24"
						key="staticText-38"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.5.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="249"
						width="166"
						height="24"
						key="staticText-39"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Roadload Matrix family’s identifier (if applicable)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="249"
						width="15"
						height="24"
						key="staticText-40"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="249"
						width="164"
						height="24"
						key="textField-15"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c13}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="273"
						width="39"
						height="12"
						key="staticText-41"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.6.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="273"
						width="166"
						height="12"
						key="staticText-42"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Periodic regeneration family’s identifier]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="273"
						width="15"
						height="12"
						key="staticText-43"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="273"
						width="164"
						height="12"
						key="textField-16"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c14}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="285"
						width="39"
						height="12"
						key="staticText-44"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.2.3.7.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="285"
						width="166"
						height="12"
						key="staticText-45"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Evaporative test family’s identifier]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="285"
						width="15"
						height="12"
						key="staticText-46"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="285"
						width="164"
						height="12"
						key="textField-17"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c15}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="297"
						width="39"
						height="12"
						key="staticText-47"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="297"
						width="166"
						height="12"
						key="staticText-48"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Vehicle category]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="297"
						width="15"
						height="12"
						key="staticText-49"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="297"
						width="164"
						height="12"
						key="textField-18"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c16}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="309"
						width="39"
						height="55"
						key="staticText-50"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.5.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="309"
						width="166"
						height="55"
						key="staticText-51"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Company name and address of manufacture]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="309"
						width="15"
						height="55"
						key="staticText-52"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="309"
						width="164"
						height="55"
						key="textField-19"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c17}.replaceAll("\\\\n","\\\n")]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="363"
						width="39"
						height="22"
						key="staticText-53"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.6.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="363"
						width="166"
						height="22"
						key="staticText-54"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Location and method of attachment of the statutory plates]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="363"
						width="15"
						height="22"
						key="staticText-55"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="363"
						width="164"
						height="22"
						key="textField-20"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c18}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="385"
						width="39"
						height="22"
						key="staticText-56"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="385"
						width="166"
						height="22"
						key="staticText-57"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Location of the vehicle identification number]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="385"
						width="15"
						height="22"
						key="staticText-58"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="385"
						width="164"
						height="22"
						key="textField-21"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c19}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="407"
						width="39"
						height="32"
						key="staticText-59"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.9.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="407"
						width="166"
						height="32"
						key="staticText-60"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Name and address of the manufacturer’s representative (if any)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="407"
						width="15"
						height="32"
						key="staticText-61"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="407"
						width="164"
						height="32"
						key="textField-22"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c20}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="439"
						width="39"
						height="14"
						key="staticText-62"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.10.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="439"
						width="166"
						height="14"
						key="staticText-63"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Vehicle identification number]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="439"
						width="15"
						height="14"
						key="staticText-64"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="439"
						width="164"
						height="14"
						key="textField-23"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{vin}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="20"
						y="453"
						width="39"
						height="14"
						key="staticText-65"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 0.11.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="453"
						width="166"
						height="14"
						key="staticText-66"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Date of manufacture of the vehicle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="225"
						y="453"
						width="15"
						height="14"
						key="staticText-67"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="240"
						y="453"
						width="164"
						height="14"
						key="textField-24"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{prodDate}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="25"
						y="467"
						width="379"
						height="34"
						key="textField-25"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c21}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="25"
						y="501"
						width="54"
						height="14"
						key="staticText-68"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Place:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="79"
						y="501"
						width="130"
						height="14"
						key="textField-26"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c22}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="209"
						y="501"
						width="28"
						height="14"
						key="staticText-69"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Date:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="237"
						y="501"
						width="126"
						height="14"
						key="textField-27"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c23}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="26"
						y="519"
						width="54"
						height="14"
						key="staticText-70"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Signature:]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="19"
						width="390"
						height="14"
						key="staticText-71"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[PART 2 VEHICLE CATEGORY M1(complete and completed vehicles)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="33"
						width="390"
						height="14"
						key="staticText-72"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[General construction characteristics]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="47"
						width="24"
						height="12"
						key="staticText-73"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="47"
						width="187"
						height="12"
						key="staticText-74"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Number of axles and wheels]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="47"
						width="15"
						height="12"
						key="staticText-75"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="47"
						width="164"
						height="12"
						key="textField-28"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c24}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="59"
						width="24"
						height="24"
						key="staticText-76"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 3.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="59"
						width="187"
						height="24"
						key="staticText-77"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left" lineSpacing="Single">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Powered axles (number, position, interconnection)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="59"
						width="15"
						height="24"
						key="staticText-78"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="59"
						width="164"
						height="24"
						key="textField-29"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c25}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="83"
						width="24"
						height="14"
						key="staticText-79"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 3.1.]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="431"
						y="83"
						width="366"
						height="14"
						key="textField-30"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["  Specify if the vehicle is non-automated/automated/fully automated: "+$F{c26}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="106"
						width="390"
						height="14"
						key="staticText-80"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Main dimensions]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="120"
						width="24"
						height="12"
						key="staticText-81"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="120"
						width="187"
						height="12"
						key="staticText-82"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Wheelbase]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="120"
						width="15"
						height="12"
						key="staticText-83"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="120"
						width="164"
						height="12"
						key="textField-31"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c27}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="132"
						width="24"
						height="12"
						key="staticText-84"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 4.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="132"
						width="187"
						height="12"
						key="staticText-85"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Axle spacing]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="132"
						width="15"
						height="12"
						key="staticText-86"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="132"
						width="164"
						height="12"
						key="textField-32"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c28}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="144"
						width="24"
						height="12"
						key="staticText-87"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 5.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="144"
						width="187"
						height="12"
						key="staticText-88"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Length]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="144"
						width="15"
						height="12"
						key="staticText-89"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="144"
						width="164"
						height="12"
						key="textField-33"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c29}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="156"
						width="24"
						height="12"
						key="staticText-90"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 6.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="156"
						width="187"
						height="12"
						key="staticText-91"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Width]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="156"
						width="15"
						height="12"
						key="staticText-92"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="156"
						width="164"
						height="12"
						key="textField-34"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c30}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="168"
						width="24"
						height="12"
						key="staticText-93"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 7.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="168"
						width="187"
						height="12"
						key="staticText-94"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Height]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="168"
						width="15"
						height="12"
						key="staticText-95"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="168"
						width="164"
						height="12"
						key="textField-35"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c31}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="190"
						width="390"
						height="14"
						key="staticText-96"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Masses]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="204"
						width="24"
						height="12"
						key="staticText-97"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 13.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="204"
						width="187"
						height="12"
						key="staticText-98"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Mass in running order]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="204"
						width="15"
						height="12"
						key="staticText-99"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="204"
						width="164"
						height="12"
						key="textField-36"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c32}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="216"
						width="24"
						height="12"
						key="staticText-100"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 13.2.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="216"
						width="187"
						height="12"
						key="staticText-101"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Actual mass of the vehicle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="216"
						width="15"
						height="12"
						key="staticText-102"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="216"
						width="164"
						height="12"
						key="textField-37"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c33}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="228"
						width="24"
						height="12"
						key="staticText-103"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 16.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="228"
						width="366"
						height="12"
						key="staticText-104"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Technically permissible maximum masses]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="240"
						width="24"
						height="12"
						key="staticText-106"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 16.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="240"
						width="187"
						height="12"
						key="staticText-107"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Technically permissible maximum laden mass]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="240"
						width="15"
						height="12"
						key="staticText-108"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="240"
						width="164"
						height="12"
						key="textField-39"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c35}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="252"
						width="24"
						height="24"
						key="staticText-109"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 16.2.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="252"
						width="187"
						height="24"
						key="staticText-110"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Technically permissible mass on each axle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="252"
						width="15"
						height="24"
						key="staticText-111"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="252"
						width="164"
						height="24"
						key="textField-40"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c36}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="276"
						width="24"
						height="24"
						key="staticText-112"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 16.4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="276"
						width="187"
						height="24"
						key="staticText-113"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Technically permissible maximum mass of the combination]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="276"
						width="15"
						height="24"
						key="staticText-114"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="276"
						width="164"
						height="24"
						key="textField-41"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c37}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="300"
						width="24"
						height="12"
						key="staticText-115"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 18.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="300"
						width="366"
						height="12"
						key="staticText-116"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Technically permissible maximum towable mass in case of]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="312"
						width="24"
						height="12"
						key="staticText-118"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 18.1.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="312"
						width="187"
						height="12"
						key="staticText-119"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Drawbar trailer]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="312"
						width="15"
						height="12"
						key="staticText-120"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="312"
						width="164"
						height="12"
						key="textField-43"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c39}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="324"
						width="24"
						height="12"
						key="staticText-121"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 18.3.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="324"
						width="187"
						height="12"
						key="staticText-122"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Centre-axle trailer Towing Capacity]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="324"
						width="15"
						height="12"
						key="staticText-123"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="324"
						width="164"
						height="12"
						key="textField-44"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c40}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="336"
						width="24"
						height="12"
						key="staticText-124"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 18.4.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="336"
						width="187"
						height="12"
						key="staticText-125"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Unbraked trailer]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="336"
						width="15"
						height="12"
						key="staticText-126"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="336"
						width="164"
						height="12"
						key="textField-45"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c41}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="348"
						width="24"
						height="24"
						key="staticText-127"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 19.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="348"
						width="187"
						height="24"
						key="staticText-128"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Technically permissible maximum static vertical mass at the coupling point]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="348"
						width="15"
						height="24"
						key="staticText-129"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="348"
						width="164"
						height="24"
						key="textField-46"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c42}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="381"
						width="390"
						height="14"
						key="staticText-130"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="true" isPdfEmbedded ="false" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[Power plant]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="395"
						width="24"
						height="24"
						key="staticText-131"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 20.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="395"
						width="187"
						height="24"
						key="staticText-132"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Manufacturer of the engine]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="395"
						width="15"
						height="24"
						key="staticText-133"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="395"
						width="164"
						height="24"
						key="textField-47"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c43}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="419"
						width="24"
						height="24"
						key="staticText-134"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 21.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="419"
						width="187"
						height="24"
						key="staticText-135"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Engine code as marked on the engine]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="419"
						width="15"
						height="24"
						key="staticText-136"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="419"
						width="164"
						height="24"
						key="textField-48"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c44}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="443"
						width="24"
						height="24"
						key="staticText-137"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 22.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="443"
						width="187"
						height="24"
						key="staticText-138"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Working principle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="443"
						width="15"
						height="24"
						key="staticText-139"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="443"
						width="164"
						height="24"
						key="textField-49"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c45}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="467"
						width="24"
						height="12"
						key="staticText-140"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 23.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="467"
						width="187"
						height="12"
						key="staticText-141"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Pure electric (Yes/No)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="467"
						width="15"
						height="12"
						key="staticText-142"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="467"
						width="164"
						height="12"
						key="textField-50"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c46}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="479"
						width="24"
						height="24"
						key="staticText-143"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 23.1]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="479"
						width="187"
						height="24"
						key="staticText-144"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Class of Hybrid [electric/OVC-HEV/NOVC-HEV/OVC-FCHV/NOVC-FCHV] vehicle]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="479"
						width="15"
						height="24"
						key="staticText-145"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="479"
						width="164"
						height="24"
						key="textField-51"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c47}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="407"
						y="503"
						width="24"
						height="24"
						key="staticText-146"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[ 24.]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="431"
						y="503"
						width="187"
						height="24"
						key="staticText-147"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  Number and arrangement of cylinders]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="618"
						y="503"
						width="15"
						height="24"
						key="staticText-148"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="633"
						y="503"
						width="164"
						height="24"
						key="textField-52"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement>
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="8" isBold="false" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c48}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="20"
						y="33"
						width="192"
						height="14"
						key="textField-53"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="10" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{year}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="212"
						y="33"
						width="192"
						height="14"
						key="textField-54"/>
					<box>					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="Noto Sans" pdfFontName="NotoSans.ttf" size="10" isBold="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{yearseq}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
