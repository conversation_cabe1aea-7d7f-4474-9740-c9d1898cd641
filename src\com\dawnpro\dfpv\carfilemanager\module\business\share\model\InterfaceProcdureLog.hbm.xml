<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<!-- Generated 2018-9-18 13:11:19 by Hibernate Tools 3.6.0.Final -->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceProcdureLog" table="INTERFACE_PROCDURE_LOG" schema="VFILE">
        <comment>&#189;&#211;&#191;&#218;&#179;&#204;&#208;&#242;&#200;&#213;&#214;&#190;</comment>
        <id name="id" type="string">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="procno" type="string">
            <column name="PROCNO" length="20" />
        </property>
        <property name="starttime" type="date">
            <column name="STARTTIME" length="7" />
        </property>
        <property name="endtime" type="date">
            <column name="ENDTIME" length="7" />
        </property>
        <property name="errinfo" type="string">
            <column name="ERRINFO" length="500" />
        </property>
    </class>
</hibernate-mapping>
