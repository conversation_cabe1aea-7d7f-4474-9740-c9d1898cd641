package com.dawnpro.dfpv.carfilemanager.module.business.cardata.action;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import jxl.Cell;
import jxl.CellType;
import jxl.Sheet;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.read.biff.BiffException;
import jxl.write.WriteException;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.ApplicationException;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.common.tools.StringTools;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.YearCode;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.YearCodeService;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryService;
import com.dawnpro.util.OperateExcel;


public class YearCodeAction extends BaseSupportAction{
	private Logger logger = Logger.getLogger(YearCodeAction.class.getName());
	private YearCodeService yearCodeService = null;
	
	private String currentPage = null;
	private String fileName = null;
	private InputStream excelStream = null;
	
	private String year;				
	private String code;	
	private String creator;
	private String createdate;
	private String remark;

	
    private String id;
    
    private String ids;
 

	public YearCodeService getYearCodeService() {
		return yearCodeService;
	}

	public void setYearCodeService(YearCodeService yearCodeService) {
		this.yearCodeService = yearCodeService;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedate() {
		return createdate;
	}

	public void setCreatedate(String createdate) {
		this.createdate = createdate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	
    
	public String getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}


	public InputStream getExcelStream() {
		return excelStream;
	}

	public void setExcelStream(InputStream excelStream) {
		this.excelStream = excelStream;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	

	public String execute(){
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());

			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			
			this.year = year == null ? "" : year;
			this.year = java.net.URLDecoder.decode(this.year, "UTF-8");
			
			if (!"".equals(this.getYear()))
				params.put("year", this.getYear());
			
			results = yearCodeService.pagination(page, params);
			
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("yearCodePageData", results);
			this.getServletRequest().setAttribute("year", year);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
	
	public String addYearCode(){
		String id = java.util.UUID.randomUUID().toString();
		YearCode yearCode = new YearCode();
		yearCode.setId(id);
		yearCode.setYear(this.year);
		yearCode.setCode(this.code);

		SysUser user =(SysUser)getSession().get(SYSTEM_USER);
		yearCode.setCreator(user.getUsername());
		yearCode.setCreatedate(new Date());
		try{
			this.yearCodeService.addYearCode(yearCode);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String updateYearCode(){
		YearCode yearCode = new YearCode();
		yearCode.setId(id);
		yearCode.setYear(this.year);
		yearCode.setCode(this.code);
		SysUser user =(SysUser)getSession().get(SYSTEM_USER);
		yearCode.setCreator(user.getUsername());
		yearCode.setCreatedate(new Date());
		try{
			this.yearCodeService.updateYearCode(yearCode);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
	public String deleteYearCode(){
		String[] id = ids.split("&");
		try{
			this.yearCodeService.deleteYearCode(id);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return "view";
	}
	
    
	public String getYearCode(){
		try{
			YearCode obj = this.yearCodeService.loadYearCode(id);
			JSONObject jsonObject = JSONObject.fromObject(obj);
			if (obj != null)
				this.setJson(jsonObject.toString());
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
	}
	
    public String isYearCodeExist(){
		try{
			boolean isExist = this.yearCodeService.isYearCodeExist(year);
			this.setJson(String.valueOf(isExist));			
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
    }
    
    public String YearCode(){
		try{
			YearCode obj = this.yearCodeService.findYearCode(this.id);
			
			if (obj != null){
				JSONObject jsonObject = JSONObject.fromObject(obj);
				this.setJson(jsonObject.toString());
			}
							
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}
		return JSON;
    }

	/**
	 * @return the ids
	 */
	public String getIds() {
		return ids;
	}

	/**
	 * @param ids the ids to set
	 */
	public void setIds(String ids) {
		this.ids = ids;
	}
 
}

