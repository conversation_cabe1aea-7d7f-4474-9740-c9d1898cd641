package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * FuellabeltemplateId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CertificateSupplementInfoId implements java.io.Serializable {

	// Fields

	private String vin;
	

	private String printtime;

	// Constructors

	/** default constructor */
	public CertificateSupplementInfoId() {
	}

	/** full constructor */
	public CertificateSupplementInfoId(String vin, String printtime) {
		this.vin = vin;
		this.printtime = printtime;
	}

	// Property accessors

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}
	
	public String getPrinttime() {
		return printtime;
	}

	public void setPrinttime(String printtime) {
		this.printtime = printtime;
	}
	
	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof CertificateSupplementInfoId))
			return false;
		CertificateSupplementInfoId castOther = (CertificateSupplementInfoId) other;

		return ((this.getVin() == castOther.getVin()) || (this.getVin() != null
				&& castOther.getVin() != null && this.getVin().equals(
				castOther.getVin())))
				&& ((this.getPrinttime() == castOther.getPrinttime()) || (this
						.getPrinttime() != null
						&& castOther.getPrinttime() != null && this.getPrinttime()
						.equals(castOther.getPrinttime())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getVin() == null ? 0 : this.getVin().hashCode());
		result = 37 * result
				+ (getPrinttime() == null ? 0 : this.getPrinttime().hashCode());
		return result;
	}

}