package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;


import java.util.Date;

/**
 * Lpzinfo entity. <AUTHOR> Persistence Tools
 */

public class Lpzinfo implements java.io.Serializable {

	// Fields
	private String id;
	private String minzl;				
	private String maxzl;			
	private String lpz1;	
	private String lpz2;

	private String xs;
	private String engineType;
	private String state;
	private String creator;
	private Date createdate;
	private String remark;
	private String cyear;
	// Constructors

	/** default constructor */
	public Lpzinfo() {
	}

	/** minimal constructor */

	

	// Property accessors

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCyear() {
		return cyear;
	}

	public void setCyear(String cyear) {
		this.cyear = cyear;
	}

	
	public String getMinzl() {
		return minzl;
	}

	public void setMinzl(String minzl) {
		this.minzl = minzl;
	}

	public String getMaxzl() {
		return maxzl;
	}

	public void setMaxzl(String maxzl) {
		this.maxzl = maxzl;
	}

	public String getLpz1() {
		return lpz1;
	}

	public void setLpz1(String lpz1) {
		this.lpz1 = lpz1;
	}

	public String getLpz2() {
		return lpz2;
	}

	public void setLpz2(String lpz2) {
		this.lpz2 = lpz2;
	}

	public String getXs() {
		return this.xs;
	}

	public void setXs(String xs) {
		this.xs = xs;
	}

	public String getEngineType() {
		return this.engineType;
	}

	public void setEngineType(String engineType) {
		this.engineType = engineType;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	

	public Date getCreatedate() {
		return createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}