package com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao;

import java.io.Serializable;
import java.util.List;

import org.hibernate.ObjectNotFoundException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarPublicModelIcon;

public class CarPublicModelIconDAOImpl extends GenericHibernateDAOImpl<CarPublicModelIcon> implements CarPublicModelIconDAO {

	public void addCarPublicModelIcon(CarPublicModelIcon obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCarPublicModelIcon Method Error:",e);
		}
	}

	public void deleteCarPublicModelIcon(Serializable id) {
		try{
			this.delete(this.loadCarPublicModelIcon((String)id));
		}catch(Exception e){
			throw new DataAccessException("deleteCarPublicModelIcon Method Error:",e);
		}
	}

	public void deleteCarPublicModelIcon(Serializable[] id) {
		try{
			for(int i  = 0; i < id.length; i++){
				this.deleteCarPublicModelIcon(id[i]);
			}
		}catch(Exception e){
			throw new DataAccessException("deleteCarPublicModelIcon Method Error:",e);
		}
	}

	public List<CarPublicModelIcon> findCarPublicModelIcon(String sql) {
		List<CarPublicModelIcon> list = null;
		try{
			list = this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findCarPublicModelIcon Method Error:",e);
		}
		return list;
	}

	public List<CarPublicModelIcon> findCarPublicModelIcon(String sql,
			Object[] params) {
		List<CarPublicModelIcon> list = null;
		try{
			list = this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findCarPublicModelIcon Method Error:",e);
		}
		return list;
	}

	public CarPublicModelIcon loadCarPublicModelIcon(Serializable id) {
		CarPublicModelIcon carPublicModelIcon = null;
		try{
			carPublicModelIcon = (CarPublicModelIcon)this.load(CarPublicModelIcon.class, id);
		}catch(ObjectNotFoundException e){
			
		}catch(Exception e){
			
		}
		return carPublicModelIcon;
	}

	public void updateCarPublicModelIcon(CarPublicModelIcon obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCarPublicModelIcon Method Error:",e);
		}
	}

}
