/* Polish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['pl'] = {
		closeText: '<PERSON>amknij',
		prevText: '&#x3c;<PERSON>rzedni',
		nextText: 'Następny&#x3e;',
		currentText: '<PERSON><PERSON><PERSON>',
		monthNames: ['St<PERSON>cz<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>wi<PERSON>',
		'<PERSON><PERSON><PERSON>','Sierpień','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','Listopad','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON>y','<PERSON>','Mar','Kw','Maj','<PERSON><PERSON>',
		'Lip','Sie','Wrz','<PERSON>','<PERSON><PERSON>','<PERSON>ru'],
		dayNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>d<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Nie','Pn','Wt','Śr','Czw','Pt','So'],
		dayNamesMin: ['N','Pn','Wt','Śr','Cz','Pt','So'],
		dateFormat: 'yy-mm-dd', firstDay: 1,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['pl']);
});
