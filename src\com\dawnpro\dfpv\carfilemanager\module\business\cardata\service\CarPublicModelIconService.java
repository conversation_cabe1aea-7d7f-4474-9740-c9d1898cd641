package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarPublicModelIcon;

public interface CarPublicModelIconService {
	public void addCarPublicModelIcon(CarPublicModelIcon obj) throws DataAccessException;
	public void updateCarPublicModelIcon(CarPublicModelIcon obj) throws DataAccessException;
	public void deleteCarPublicModelIcon(Serializable id) throws DataAccessException;
	public void deleteCarPublicModelIcon(Serializable[] obj) throws DataAccessException;
	public CarPublicModelIcon loadCarPublicModelIcon(Serializable id) throws DataAccessException;
	public List<?> pagination(Page page,Map<String,String> params) throws DataAccessException;
	public Boolean existPublicModel(String pmodel) throws DataAccessException;
}
