package com.dawnpro.dfpv.carfilemanager.module.business.share.factory;

import java.io.File;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts2.ServletActionContext;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.FileSystemXmlApplicationContext;

import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceLogDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceData;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDataId;
import com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceDataProcessService;

public class InterfaceWorker{
	//public abstract String[] jiexi(String path);
	private static String VFL01 = "G95VFL01.DPVVFL01";
	private static String VFL02 = "G95VFL02.DPVVFL01";
	
	private InterfaceDataProcessService interfaceDataProcessService = null;
	
	
	public InterfaceDataProcessService getInterfaceDataProcessService() {
		return interfaceDataProcessService;
	}


	public void setInterfaceDataProcessService(
			InterfaceDataProcessService interfaceDataProcessService) {
		this.interfaceDataProcessService = interfaceDataProcessService;
	}

	public void process(String filename){
		if(filename != null){
			if(filename.indexOf(VFL01)!=-1){
				this.interfaceDataProcessService.processDpvVfl01(filename);
			}else if(filename.indexOf(VFL02)!=-1){
				this.interfaceDataProcessService.processDpvVfl02(filename);
			}
		}
	}
	
	
}
