<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="GASCD"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="504"
		 pageHeight="648"
		 columnWidth="458"
		 columnSpacing="0"
		 leftMargin="23"
		 rightMargin="23"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="upimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="downimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G1image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G2image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G3image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="dcximage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="dndl" isForPrompting="true" class="java.lang.String"/>

	<field name="carConsistencyNumber" class="java.lang.String">
		<fieldDescription><![CDATA[carConsistencyNumber]]></fieldDescription>
	</field>
	<field name="cityStatus" class="java.lang.String">
		<fieldDescription><![CDATA[cityStatus]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="colligateStatus" class="java.lang.String">
		<fieldDescription><![CDATA[colligateStatus]]></fieldDescription>
	</field>
	<field name="date" class="java.lang.String">
		<fieldDescription><![CDATA[date]]></fieldDescription>
	</field>
	<field name="derailleurType" class="java.lang.String">
		<fieldDescription><![CDATA[derailleurType]]></fieldDescription>
	</field>
	<field name="driveType" class="java.lang.String">
		<fieldDescription><![CDATA[driveType]]></fieldDescription>
	</field>
	<field name="engineModel" class="java.lang.String">
		<fieldDescription><![CDATA[engineModel]]></fieldDescription>
	</field>
	<field name="enterprise" class="java.lang.String">
		<fieldDescription><![CDATA[enterprise]]></fieldDescription>
	</field>
	<field name="environsStatus" class="java.lang.String">
		<fieldDescription><![CDATA[environsStatus]]></fieldDescription>
	</field>
	<field name="fuelType" class="java.lang.String">
		<fieldDescription><![CDATA[fuelType]]></fieldDescription>
	</field>
	<field name="limitOne" class="java.lang.String">
		<fieldDescription><![CDATA[limitOne]]></fieldDescription>
	</field>
	<field name="limitTwo" class="java.lang.String">
		<fieldDescription><![CDATA[limitTwo]]></fieldDescription>
	</field>
	<field name="maxDesignSumQuality" class="java.lang.String">
		<fieldDescription><![CDATA[maxDesignSumQuality]]></fieldDescription>
	</field>
	<field name="model" class="java.lang.String">
		<fieldDescription><![CDATA[model]]></fieldDescription>
	</field>
	<field name="quality" class="java.lang.String">
		<fieldDescription><![CDATA[quality]]></fieldDescription>
	</field>
	<field name="range" class="java.lang.String">
		<fieldDescription><![CDATA[range]]></fieldDescription>
	</field>
	<field name="ratingPower" class="java.lang.String">
		<fieldDescription><![CDATA[ratingPower]]></fieldDescription>
	</field>
	<field name="remark" class="java.lang.String">
		<fieldDescription><![CDATA[remark]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="qebz" class="java.lang.String"/>
	<field name="gjbz" class="java.lang.String"/>
	<field name="dymb" class="java.lang.String"/>
	<field name="zhgkdnxhl" class="java.lang.String"/>
	<field name="zhgkxslc" class="java.lang.String"/>
	<field name="dndl" class="java.lang.String"/>
	<field name="djedgl" class="java.lang.String"/>
	<field name="rlxhlbj1" class="java.lang.String"/>
	<field name="rlxhlbj2" class="java.lang.String"/>
	<field name="ygnycb" class="java.lang.String"/>
	<field name="bacode" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="50"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="20"
						y="-2"
						width="105"
						height="46"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="黑体" size="23" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{qebz}]]></textFieldExpression>
				</textField>
			</band>
		</title>
		<pageHeader>
			<band height="12"  isSplitAllowed="true" >
				<line direction="TopDown">
					<reportElement
						x="0"
						y="6"
						width="375"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="3.0"/>
</graphicElement>
				</line>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="511"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="274"
						y="481"
						width="80"
						height="21"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{date}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="106"
						y="26"
						width="66"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{quality}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="26"
						width="95"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[整车整备质量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="106"
						y="40"
						width="66"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{maxDesignSumQuality}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="40"
						width="106"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大设计总质量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="309"
						y="26"
						width="48"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{djedgl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="54"
						width="65"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[其他信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="46"
						y="104"
						width="127"
						height="16"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电能消耗量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="46"
						y="123"
						width="127"
						height="16"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电能当量燃料消耗量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="183"
						y="170"
						width="70"
						height="19"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[续驶里程：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="172"
						y="88"
						width="101"
						height="32"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="28" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zhgkdnxhl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="192"
						y="26"
						width="117"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动电机峰值功率：]]></text>
				</staticText>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="334"
						y="355"
						width="39"
						height="58"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{G2image}]]></imageExpression>
				</image>
				<image  scaleImage="FillFrame" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="12"
						y="189"
						width="358"
						height="44"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{dcximage}]]></imageExpression>
				</image>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="475"
						width="375"
						height="0"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="1.0"/>
</graphicElement>
				</line>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="65"
						y="328"
						width="265"
						height="111"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["    "+$F{gjbz}.replaceAll("\\\\n","\\\n    ")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="259"
						y="155"
						width="85"
						height="32"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="28" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zhgkxslc}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="1"
						y="481"
						width="54"
						height="21"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[备案号：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="208"
						y="481"
						width="66"
						height="21"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[启用日期：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="173"
						y="123"
						width="100"
						height="16"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dndl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="284"
						y="100"
						width="79"
						height="21"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kW·h/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="284"
						y="123"
						width="79"
						height="16"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="173"
						y="26"
						width="16"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="173"
						y="40"
						width="16"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="356"
						y="26"
						width="22"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kW]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="341"
						y="170"
						width="22"
						height="19"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="66"
						y="-2"
						width="194"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{enterprise}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="67"
						y="12"
						width="100"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{model}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="12"
						width="66"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="-2"
						width="66"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="265"
						y="12"
						width="80"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{fuelType}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="193"
						y="13"
						width="67"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[能源种类：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="55"
						y="481"
						width="144"
						height="21"
						key="textField"/>
					<box></box>
					<textElement verticalAlignment="Top">
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{bacode}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="65"
						y="54"
						width="310"
						height="28"
						key="textField-1"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{remark}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="284"
						y="139"
						width="79"
						height="16"
						key="staticText-1"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[元/100 km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="172"
						y="139"
						width="100"
						height="16"
						key="textField-2"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ygnycb}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="45"
						y="139"
						width="127"
						height="16"
						key="staticText-2"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[预估能源成本：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="45"
						y="235"
						width="318"
						height="16"
						key="textField-3"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{rlxhlbj1}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="46"
						y="252"
						width="318"
						height="16"
						key="textField-4"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{rlxhlbj2}]]></textFieldExpression>
				</textField>
			</band>
		</columnHeader>
		<detail>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
