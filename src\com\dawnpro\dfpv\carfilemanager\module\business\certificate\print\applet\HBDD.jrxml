<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="HBDD"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="555"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="chzhqbimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="fdjimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="cjdate" isForPrompting="true" class="java.lang.String"/>

	<field name="slcx" class="java.lang.String">
		<fieldDescription><![CDATA[slcx]]></fieldDescription>
	</field>
	<field name="vercode" class="java.lang.String">
		<fieldDescription><![CDATA[vercode]]></fieldDescription>
	</field>
	<field name="pzextno" class="java.lang.String">
		<fieldDescription><![CDATA[pzextno]]></fieldDescription>
	</field>
	<field name="jybgno" class="java.lang.String">
		<fieldDescription><![CDATA[jybgno]]></fieldDescription>
	</field>
	<field name="jyjgmc" class="java.lang.String">
		<fieldDescription><![CDATA[jyjgmc]]></fieldDescription>
	</field>
	<field name="ccjyjg" class="java.lang.String">
		<fieldDescription><![CDATA[ccjyjg]]></fieldDescription>
	</field>
	<field name="fdjxh" class="java.lang.String">
		<fieldDescription><![CDATA[fdjxh]]></fieldDescription>
	</field>
	<field name="fdjgc" class="java.lang.String">
		<fieldDescription><![CDATA[fdjgc]]></fieldDescription>
	</field>
	<field name="chzhq" class="java.lang.String">
		<fieldDescription><![CDATA[chzhq]]></fieldDescription>
	</field>
	<field name="chzhqgc" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqgc]]></fieldDescription>
	</field>
	<field name="ryzfkzqxh" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqxh]]></fieldDescription>
	</field>
	<field name="ryzfkzqgc" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqgc]]></fieldDescription>
	</field>
	<field name="egrxh" class="java.lang.String">
		<fieldDescription><![CDATA[egrxh]]></fieldDescription>
	</field>
	<field name="egrgc" class="java.lang.String">
		<fieldDescription><![CDATA[egrgc]]></fieldDescription>
	</field>
	<field name="obdxh" class="java.lang.String">
		<fieldDescription><![CDATA[obdxh]]></fieldDescription>
	</field>
	<field name="obdgc" class="java.lang.String">
		<fieldDescription><![CDATA[obdgc]]></fieldDescription>
	</field>
	<field name="iuprnox" class="java.lang.String">
		<fieldDescription><![CDATA[iuprnox]]></fieldDescription>
	</field>
	<field name="ecuxh" class="java.lang.String">
		<fieldDescription><![CDATA[ecuxh]]></fieldDescription>
	</field>
	<field name="ecugc" class="java.lang.String">
		<fieldDescription><![CDATA[ecugc]]></fieldDescription>
	</field>
	<field name="bsqxs" class="java.lang.String">
		<fieldDescription><![CDATA[bsqxs]]></fieldDescription>
	</field>
	<field name="bsqdws" class="java.lang.String">
		<fieldDescription><![CDATA[bsqdws]]></fieldDescription>
	</field>
	<field name="xsqxh" class="java.lang.String">
		<fieldDescription><![CDATA[xsqxh]]></fieldDescription>
	</field>
	<field name="xsqgc" class="java.lang.String">
		<fieldDescription><![CDATA[xsqgc]]></fieldDescription>
	</field>
	<field name="zyqxh" class="java.lang.String">
		<fieldDescription><![CDATA[zyqxh]]></fieldDescription>
	</field>
	<field name="zyqgc" class="java.lang.String">
		<fieldDescription><![CDATA[zyqgc]]></fieldDescription>
	</field>
	<field name="zlqxs" class="java.lang.String">
		<fieldDescription><![CDATA[zlqxs]]></fieldDescription>
	</field>
	<field name="fdjbs" class="java.lang.String">
		<fieldDescription><![CDATA[fdjbs]]></fieldDescription>
	</field>
	<field name="fdjpath" class="java.lang.String">
		<fieldDescription><![CDATA[fdjpath]]></fieldDescription>
	</field>
	<field name="chzhqbs" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqbs]]></fieldDescription>
	</field>
	<field name="chzhqpath" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqpath]]></fieldDescription>
	</field>
	<field name="ryzfkzqbs" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqbs]]></fieldDescription>
	</field>
	<field name="ryzfkzqpath" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqpath]]></fieldDescription>
	</field>
	<field name="ycgqbs" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqbs]]></fieldDescription>
	</field>
	<field name="ycgqpath" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqpath]]></fieldDescription>
	</field>
	<field name="egrbs" class="java.lang.String">
		<fieldDescription><![CDATA[egrbs]]></fieldDescription>
	</field>
	<field name="egrpath" class="java.lang.String">
		<fieldDescription><![CDATA[egrpath]]></fieldDescription>
	</field>
	<field name="ecubs" class="java.lang.String">
		<fieldDescription><![CDATA[ecubs]]></fieldDescription>
	</field>
	<field name="ecupath" class="java.lang.String">
		<fieldDescription><![CDATA[ecupath]]></fieldDescription>
	</field>
	<field name="xsqbs" class="java.lang.String">
		<fieldDescription><![CDATA[xsqbs]]></fieldDescription>
	</field>
	<field name="xsqpath" class="java.lang.String">
		<fieldDescription><![CDATA[xsqpath]]></fieldDescription>
	</field>
	<field name="zyqbs" class="java.lang.String">
		<fieldDescription><![CDATA[zyqbs]]></fieldDescription>
	</field>
	<field name="zyqpath" class="java.lang.String">
		<fieldDescription><![CDATA[zyqpath]]></fieldDescription>
	</field>
	<field name="mark" class="java.lang.String">
		<fieldDescription><![CDATA[mark]]></fieldDescription>
	</field>
	<field name="creator" class="java.lang.String">
		<fieldDescription><![CDATA[creator]]></fieldDescription>
	</field>
	<field name="state" class="java.lang.String">
		<fieldDescription><![CDATA[state]]></fieldDescription>
	</field>
	<field name="datadate" class="java.util.Date">
		<fieldDescription><![CDATA[datadate]]></fieldDescription>
	</field>
	<field name="ggcx" class="java.lang.String">
		<fieldDescription><![CDATA[ggcx]]></fieldDescription>
	</field>
	<field name="effectTime" class="java.lang.String">
		<fieldDescription><![CDATA[effectTime]]></fieldDescription>
	</field>
	<field name="fdjbsname" class="java.lang.String">
		<fieldDescription><![CDATA[fdjbsname]]></fieldDescription>
	</field>
	<field name="chzhqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqbsname]]></fieldDescription>
	</field>
	<field name="ryzfkzqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqbsname]]></fieldDescription>
	</field>
	<field name="ycgqbsname" class="java.lang.String"/>
	<field name="egrbsname" class="java.lang.String">
		<fieldDescription><![CDATA[egrbsname]]></fieldDescription>
	</field>
	<field name="ecubsname" class="java.lang.String">
		<fieldDescription><![CDATA[ecubsname]]></fieldDescription>
	</field>
	<field name="xsqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[xsqbsname]]></fieldDescription>
	</field>
	<field name="zyqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[zyqbsname]]></fieldDescription>
	</field>
	<field name="ycgqxh" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqxh]]></fieldDescription>
	</field>
	<field name="ycgqgc" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqgc]]></fieldDescription>
	</field>
	<field name="qzspfxh" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfxh]]></fieldDescription>
	</field>
	<field name="qzspfgc" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfgc]]></fieldDescription>
	</field>
	<field name="ycgqxh_1" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqxh]]></fieldDescription>
	</field>
	<field name="ycgqgc_1" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqgc]]></fieldDescription>
	</field>
	<field name="qzspfxh_1" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfxh]]></fieldDescription>
	</field>
	<field name="qzspfgc_1" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfgc]]></fieldDescription>
	</field>
	<field name="jyjg1" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg1]]></fieldDescription>
	</field>
	<field name="jyjg2" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg2]]></fieldDescription>
	</field>
	<field name="jyjg3" class="java.lang.String"/>
	<field name="jyjg4" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg4]]></fieldDescription>
	</field>
	<field name="jcjr1" class="java.lang.String"/>
	<field name="jcjr2" class="java.lang.String"/>
	<field name="jcjr3" class="java.lang.String"/>
	<field name="jcjr4" class="java.lang.String">
		<fieldDescription><![CDATA[jcjr4]]></fieldDescription>
	</field>
	<field name="hbtc" class="java.lang.String">
		<fieldDescription><![CDATA[hbtc]]></fieldDescription>
	</field>
	<field name="frdb" class="java.lang.String">
		<fieldDescription><![CDATA[frdb]]></fieldDescription>
	</field>
	<field name="hbzt" class="java.lang.String"/>
	<field name="hbfzscc" class="java.lang.String"/>
	<field name="dz" class="java.lang.String">
		<fieldDescription><![CDATA[dz]]></fieldDescription>
	</field>
	<field name="tel" class="java.lang.String">
		<fieldDescription><![CDATA[tel]]></fieldDescription>
	</field>
	<field name="hbdws" class="java.lang.String">
		<fieldDescription><![CDATA[hbdws]]></fieldDescription>
	</field>
	<field name="ecubb" class="java.lang.String">
		<fieldDescription><![CDATA[ecubb]]></fieldDescription>
	</field>
	<field name="rqhhq" class="java.lang.String">
		<fieldDescription><![CDATA[rqhhq]]></fieldDescription>
	</field>
	<field name="rqhhqgc" class="java.lang.String">
		<fieldDescription><![CDATA[rqhhqgc]]></fieldDescription>
	</field>
	<field name="rqpsdy" class="java.lang.String"/>
	<field name="rqpsdygc" class="java.lang.String"/>
	<field name="cnzl" class="java.lang.String"/>
	<field name="cnzlgc" class="java.lang.String"/>
	<field name="dcrl" class="java.lang.String"/>
	<field name="dhlc" class="java.lang.String">
		<fieldDescription><![CDATA[dhlc]]></fieldDescription>
	</field>
	<field name="ddjxh" class="java.lang.String"/>
	<field name="ddjgc" class="java.lang.String"/>
	<field name="ddjecu" class="java.lang.String">
		<fieldDescription><![CDATA[ddjecu]]></fieldDescription>
	</field>
	<field name="ddjecubbh" class="java.lang.String">
		<fieldDescription><![CDATA[ddjecubbh]]></fieldDescription>
	</field>
	<field name="ddjecuscc" class="java.lang.String"/>
	<field name="xxgkhao" class="java.lang.String"/>
	<field name="clxh" class="java.lang.String"/>
	<field name="hbsb" class="java.lang.String"/>
	<field name="qcfl" class="java.lang.String"/>
	<field name="cxsb" class="java.lang.String"/>
	<field name="clzzname" class="java.lang.String"/>
	<field name="field1" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="pfjd" class="java.lang.String">
		<fieldDescription><![CDATA[pfjd]]></fieldDescription>
	</field>
	<field name="scgdz" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="dbz" class="java.lang.String">
		<fieldDescription><![CDATA[dbz]]></fieldDescription>
	</field>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="5"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="755"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="29"
						y="221"
						width="117"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆制造商名称：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="163"
						width="81"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号:]]></text>
				</staticText>
				<image  scaleImage="RetainShape" vAlign="Middle" hAlign="Center" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="313"
						y="152"
						width="232"
						height="60"
						key="image"/>
					<box>					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{fdjimage}]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="175"
						width="113"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{hbsb}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="29"
						y="199"
						width="154"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车型的识别方法和位置：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="48"
						y="174"
						width="61"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[标：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="164"
						width="113"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="9"
						y="152"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第一部分 车辆信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="187"
						width="80"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[汽车分类：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="233"
						width="80"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产厂地址：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="12"
						y="87"
						width="534"
						height="58"
						key="textField"
						stretchType="RelativeToBandHeight"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["    "+$F{dbz}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="170"
						y="39"
						width="74"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[信息公开编号：]]></text>
				</staticText>
				<line direction="TopDown">
					<reportElement
						x="13"
						y="146"
						width="530"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<staticText>
					<reportElement
						x="9"
						y="259"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第二部分 检验信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="271"
						width="99"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[型式检验信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="284"
						width="76"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[依据的标准]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="188"
						y="282"
						width="66"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[检验机构]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="407"
						y="282"
						width="76"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[检测结论]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="296"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[GB1495-2002]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="320"
						width="129"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[出厂检验项目及结论：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="333"
						width="516"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车型环保生产一致性保证计划及执行情况，详见本公司官方网站和生态环境部信息公开平台（网址附后）。]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="347"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第三部分 污染控制技术信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="363"
						width="128"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电动机型号/生产厂：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="376"
						width="153"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[整车控制器型号/版本号/生产厂：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="169"
						y="16"
						width="198"
						height="23"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="16" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电动车环保信息随车清单]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="387"
						width="153"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[储能装置型号/生产厂：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="413"
						width="516"
						height="15"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[本车辆环保关键零部件（电动机、整车控制器、储能装置）明显标注了永久性标识，标识内容包括该零部件的型号和生产企业名称]]></text>
				</staticText>
				<image  scaleImage="RetainShape" vAlign="Middle" hAlign="Center" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="460"
						y="5"
						width="89"
						height="80"
						key="image"/>
					<box>					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{chzhqbimage}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="9"
						y="447"
						width="169"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第四部分 制造商/进口企业信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="465"
						width="153"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[法人代表：    ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="48"
						y="477"
						width="134"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[址： ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="490"
						width="153"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[联系电话:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="31"
						y="501"
						width="455"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["本清单内容及相关信息可查询本公司官方网站（http://www.dfmc.com.cn ）"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="8"
						y="515"
						width="419"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[和生态环境部机动车和非道路移动机械环保信息公开平台（http://www.vecc.org.cn）。]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="403"
						y="714"
						width="67"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆生产日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="296"
						width="219"
						height="23"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jyjg3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="407"
						y="296"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jcjr3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="465"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{frdb}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="478"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dz}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="491"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{tel}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="221"
						width="319"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clzzname}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="188"
						width="113"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{qcfl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="199"
						width="138"
						height="23"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{cxsb}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="233"
						width="319"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{scgdz}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="234"
						y="39"
						width="219"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xxgkhao}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="363"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ddjxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="374"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ddjecu}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="470"
						y="714"
						width="76"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{cjdate}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="389"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{cnzl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="400"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dcrl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="29"
						y="401"
						width="153"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电池容量/续航里程：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="188"
						y="320"
						width="219"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ccjyjg}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="9"
						y="426"
						width="535"
						height="15"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[（全称、缩写或徽标），详见本公司官方网站和生态环境部信息公开平台（网址附后）。]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="163"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[1]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="174"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="174"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[商]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="187"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[3]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="198"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[4]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="221"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[5]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="233"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[6]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="271"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[7]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="320"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[8]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="333"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[9]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="363"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[10]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="376"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[11]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="387"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[12]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="401"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[13]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="465"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[14]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="477"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[15]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="9"
						y="490"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[16]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="29"
						y="477"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[地]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="13"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="8"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="9"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
