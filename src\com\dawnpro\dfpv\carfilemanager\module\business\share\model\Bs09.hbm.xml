<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bs09" table="BS_09" >
        <comment>DPCA和DFPV车型对性关系表</comment>
        <id name="id" type="java.lang.String">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="dpcamodel" type="java.lang.String">
            <column name="DPCAMODEL" length="76">
                <comment>神龙整车编码</comment>
            </column>
        </property>
        <property name="dpcavel" type="java.lang.String">
            <column name="DPCAVEL" length="20">
                <comment>神龙车型</comment>
            </column>
        </property>
        <property name="dfvcmodel" type="java.lang.String">
            <column name="DFVCMODEL" length="30">
                <comment>东风实例化中性车代码</comment>
            </column>
        </property>
        <property name="productmodel" type="java.lang.String">
            <column name="PRODUCTMODEL" length="4">
                <comment>东风可生产车型</comment>
            </column>
        </property>
        <property name="dpcaproductmodelcolor" type="java.lang.String">
            <column name="DPCAPRODUCTMODELCOLOR" length="100">
                <comment>车身颜色</comment>
            </column>
        </property>
        <property name="dfvcproductdesc" type="java.lang.String">
            <column name="DFVCPRODUCTDESC" length="20">
                <comment>东风整车物料描述</comment>
            </column>
        </property>
        <property name="createuser" type="java.lang.String">
            <column name="CREATEUSER" length="20">
                <comment>输入人</comment>
            </column>
        </property>
        <property name="createtime" type="java.lang.String">
            <column name="CREATETIME" length="20">
                <comment>输入创建时间</comment>
            </column>
        </property>
        <property name="senddate" type="java.lang.String">
            <column name="SENDDATE" length="8">
                <comment>发送创建日期</comment>
            </column>
        </property>
        <property name="sendtime" type="java.lang.String">
            <column name="SENDTIME" length="8">
                <comment>发送创建时间</comment>
            </column>
        </property>
        <property name="datatime" type="java.util.Date">
            <column name="DATATIME" length="7">
                <comment>接收时间</comment>
            </column>
        </property>
    </class>
</hibernate-mapping>
