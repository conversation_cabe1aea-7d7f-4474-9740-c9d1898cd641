package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;


import java.util.Date;

/**
 * Lpzinfo entity. <AUTHOR> Persistence Tools
 */

public class YearCode implements java.io.Serializable {

	// Fields
	private String id;
	private String year;				
	private String code;

	private String creator;
	private Date createdate;
	private String remark;

	// Constructors

	/** default constructor */
	public YearCode() {
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public Date getCreatedate() {
		return createdate;
	}

	public void setCreatedate(Date createdate) {
		this.createdate = createdate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	

	

}