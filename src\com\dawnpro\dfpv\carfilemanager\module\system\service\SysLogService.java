package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysLog;

public interface SysLogService {
	
	void addLog(SysLog log) throws DataAccessException;
	
	List<?> pagination(Page page,Object[] params) throws DataAccessException;
}
