package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryId;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;

/**
 *
 * @author:
 * @since: 2010-4-1
 * **/
public interface SysDataDictionaryService {

	/**
	 *
	 * @param	sql:
	 * **/
	public List<DataDictionary> allDataDictionary();
	
	public void addDataDictionary(DataDictionary data) throws DataAccessException;
	
	public void updateDataDictionary(DataDictionaryId oldId, DataDictionary data) throws DataAccessException;
	
	public void deleteDataDictionary(DataDictionary[] data) throws DataAccessException;
	
	public List<DataDictionary> findDataDictionary(String type) throws DataAccessException;
	
	public DataDictionary loadDataDictionary(DataDictionaryId id) throws DataAccessException;
	
	public boolean isDataDictionaryExist(DataDictionaryId id) throws DataAccessException;
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
}
