<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Uploaduser" table="UPLOADUSER">
        <id name="username" type="java.lang.String">
            <column name="USERNAME" length="30" />
            <generator class="assigned" />
        </id>
        <property name="password" type="java.lang.String">
            <column name="PASSWORD" length="20" />
        </property>
        <property name="qyjmxx" type="java.lang.String">
            <column name="QYJMXX" length="20" />
        </property>
        <property name="flag" type="java.lang.String">
            <column name="FLAG" length="50" />
        </property>
    </class>
</hibernate-mapping>
