package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhoto;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhotoId;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.COCPhotoDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.COCPhotoService;

public class COCPhotoServiceImpl implements COCPhotoService {
	
	private COCPhotoDAO cocPhotoDAO = null;
	private PaginationService paginationService = null;
	
	public void setCocPhotoDAO(COCPhotoDAO cocPhotoDAO) {
		this.cocPhotoDAO = cocPhotoDAO;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	public void addPhoto(CocPhoto photo) {
		try{
			this.cocPhotoDAO.addPhoto(photo);
		}catch(DataAccessException e){
			throw new SystemException("addPhoto Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addPhoto Method Error:",e);
		}
	}
	
	public void addPhoto(CocPhoto[] photo) {
		try{
			this.cocPhotoDAO.addPhoto(photo);
		}catch(DataAccessException e){
			throw new SystemException("addPhoto Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("addPhoto Method Error:",e);
		}
	}

	public void deletePhoto(CocPhoto photo) {
		try{
			this.cocPhotoDAO.deletePhoto(photo);
		}catch(DataAccessException e){
			throw new SystemException("deletePhoto Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deletePhoto Method Error:",e);
		}
	}
	
	public void deletePhoto(CocPhoto[] photo) {
		try{
			this.cocPhotoDAO.deletePhoto(photo);
		}catch(DataAccessException e){
			throw new SystemException("deletePhoto Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("deletePhoto Method Error:",e);
		}
	}

	public void updatePhoto(String oldfilename,CocPhoto photo) {
		try{
			
			this.cocPhotoDAO.updatePhoto(oldfilename,photo);
		}catch(DataAccessException e){
			throw new SystemException("updatePhoto Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("updatePhoto Method Error:",e);
		}
	}
	
	public void effectCarModelPhoto(String version, CocPhoto photo) {
		try{
			this.cocPhotoDAO.updateEffectCarModelPhoto(new Object[]{photo.getTime(),photo.getEffecttime(),photo.getId().getModel(),photo.getId().getFilename(),photo.getId().getVercode()});
		}catch(DataAccessException e){
			throw new SystemException("effectCarModelPhoto Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("effectCarModelPhoto Method Error:",e);
		}
	}
	
	public List<CocPhoto> pagination(Page page, Object[] params) {
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id.model) from CocPhoto g";
				sql = "from CocPhoto g order by  g.time desc, g.id.model,g.id.filename,g.state desc";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " g.id.model like ('"+String.valueOf(params[0])+"%') and ";
				}
//				if(params[1]!=null&&!params[1].equals("")){
//					sub = sub+" g.id.color ='"+String.valueOf(params[1])+"' and ";
//				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" g.state = '"+params[1]+"' and ";
				}
				sub = sub.substring(0,sub.lastIndexOf("and"));
				//System.out.println("sub:"+sub);
				
				countSql = "select count(g.id.model) from CocPhoto g where "+sub;
				sql = "from CocPhoto g where "+sub+" order by g.time desc, g.id.model,g.id.filename,g.state desc";
			}
			//System.out.println("countSql:"+countSql);
			//System.out.println("sql:"+sql);
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		
		return results;
	}

	public boolean isCarModelPhotoExist(CocPhotoId id) {
		try{
			List<CocPhoto> result =   this.cocPhotoDAO.findPhoto("from CocPhoto c where c.id.model=? and c.id.filename=? and c.id.vercode=?",new Object[]{id.getModel(),id.getFilename()});
			if(result!=null&&result.size()>0){
				return true;
			}else{
				return false;
			}
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("isCarModelPhotoExist Method Error:",e1);
		}
	}

	public CocPhoto loadCarModelPhotoObj(CocPhotoId id) {
		CocPhoto photo = null;
		try{
			photo = this.cocPhotoDAO.loadCarModelPhotoObj(id);
		}catch(DataAccessException e){
			throw new SystemException("loadCarModelPhotoObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadCarModelPhotoObj Method Error:",e);
		}
		return photo;
	}

	public List<CocPhoto> findCarModelPhoto(CocPhotoId id) {
		return null;
	}

	public List<CocPhoto> findCarModelPhoto(CocPhotoId[] id) {
		try{
			StringBuffer sub = new StringBuffer();
			for(int i=0;i<id.length;i++){
				if(i==0){
					sub.append("(c.id.model='"+id[i].getModel()+"' and c.id.filename='"+id[i].getFilename()+"'");
					if(id[i].getVercode()==null){
						sub.append(")");
					}else{
						sub.append(" and id.vercode='"+id[i].getVercode()+"')");
					}
				}else{
					sub.append("or (c.id.model='"+id[i].getModel()+"' and c.id.filename='"+id[i].getFilename()+"'");
					if(id[i].getVercode()==null){
						sub.append(")");
					}else{
						sub.append(" and id.vercode='"+id[i].getVercode()+"')");
					}
				}
			}
			
			List<CocPhoto> result =   this.cocPhotoDAO.findPhoto("from CocPhoto c where "+sub.toString(),null);
			
			return result;
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelPhoto Method Error:",e1);
		}
	}
	
	public String getMexPhotoVercode(CocPhotoId id) {
		try{
			List<CocPhoto> result = this.cocPhotoDAO.findPhoto("from CocPhoto c where c.id.model=? and c.id.filename=? order by substr(c.id.vercode,length(c.id.vercode)-1,2) desc", new Object[]{id.getModel(),id.getFilename()});
			if(result==null||result.size()==0){
				return "P"+id.getModel()+"01";
			}else{
				String vercode = result.get(0).getId().getVercode();
				try{
					vercode = vercode.substring(vercode.length()-2, vercode.length());
					int xh = new Integer(vercode).intValue()+1;
					if(xh<10){
						vercode = "0"+xh;
					}else{
						vercode = String.valueOf(xh);
					}
				}catch(Exception e){
					vercode = "01";
				}
				
				return "P"+id.getModel()+vercode;
			}
			}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelPhoto Method Error:",e1);
		}
	}

	public List<CocPhoto> findEffectCarModelPhoto(String model) {
		try{
			return this.cocPhotoDAO.findPhoto("from CocPhoto c where c.id.model=? and c.state='1'  order by c.id.filename", new Object[]{model});
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelPhoto Method Error:",e1);
		}
	}

	public List<CocPhoto> allEffectCarModelPhoto() {
		try{
			return this.cocPhotoDAO.findPhoto("from CocPhoto c where c.state='1' order by c.id.filename",null);
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelPhoto Method Error:",e1);
		}
	}
	
	public List<CocPhoto> findCarModelPhoto(String hql, Object[] params) {
		try{
			return this.cocPhotoDAO.findPhoto(hql,params);
		}catch(RuntimeException e1){
			throw new SystemException("findCarModelPhoto Method Error:",e1);
		}
	}

	public List<CocPhoto> findCarMoldePhoto(Object[] params) {
		List results = null;
		try{
			String sql = null;
			String sub = null;
			if(params==null){
				sql = "from CocPhoto g order by  g.time desc, g.id.model,g.id.filename,g.state desc";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " g.id.model like ('"+String.valueOf(params[0])+"%') and ";
				}
//				if(params[1]!=null&&!params[1].equals("")){
//					sub = sub+" g.id.color ='"+String.valueOf(params[1])+"' and ";
//				}
				if(params[1]!=null&&!params[1].equals("")){
					sub = sub+" g.state = '"+params[1]+"' and ";
				}
				sub = sub.substring(0,sub.lastIndexOf("and"));
				//System.out.println("sub:"+sub);
				
				sql = "from CocPhoto g where "+sub+" order by g.time desc, g.id.model,g.id.filename,g.state desc";
			}
			
			results = this.cocPhotoDAO.findPhoto(sql, null);
		}catch(DataAccessException e){
			throw new SystemException("findCarMoldePhoto Method Error:",e);
		} catch(RuntimeException e){
			throw new SystemException("findCarMoldePhoto Method Error:",e);
		}
		
		return results;
	}

	public List findCarModelMaterialno(String materialno) {
		List results = null;
		try{
			results = this.cocPhotoDAO.findCarModelMaterialno("select b.sccxbm from  Bm07 b where b.sccxbm like ('"+materialno+"%')", null);
		}catch(DataAccessException e){
			throw new SystemException("findCarMoldePhoto Method Error:",e);
		} catch(RuntimeException e){
			throw new SystemException("findCarMoldePhoto Method Error:",e);
		}
		
		return results;
	}

	public List findCarModelMaterialnoNotExist() {
		List results = null;
		try{
			results = this.cocPhotoDAO.findCarModelMaterialno("select distinct new com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bm07(t.sccxbm,t.cpbm,t.fsrq,t.fssj) from Bm07 t where  not exists(select p.id.filename FROM CocPhoto p WHERE substr(p.id.filename,0,INSTR(p.id.filename, '.', 1, 1)-1)=t.sccxbm ) order by substr(t.sccxbm,2,2) desc,substr(t.sccxbm,5,3) desc desc", null);
		}catch(DataAccessException e){
			throw new SystemException("findCarMoldePhoto Method Error:",e);
		} catch(RuntimeException e){
			throw new SystemException("findCarMoldePhoto Method Error:",e);
		}
		
		return results;
	}

	public void updatePhoto(CocPhoto[] photo) {
		try{
			this.cocPhotoDAO.updatePhoto(photo);
		}catch(DataAccessException e){
			throw new SystemException("updatePhoto Method Error:",e);
		} catch(RuntimeException e){
			throw new SystemException("updatePhoto Method Error:",e);
		}
		
	}

}
