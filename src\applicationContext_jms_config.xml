<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:p="http://www.springframework.org/schema/p" xmlns:aop="http://www.springframework.org/schema/aop"
		xmlns:context="http://www.springframework.org/schema/context" xmlns:jee="http://www.springframework.org/schema/jee"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	
	<bean id="carCertificateService" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.service.CertificateServiceImpl" lazy-init="true">
		<property name="certificateDAO">
			<ref bean="carCertificateDAO" />
		</property>
	</bean>
	
	<bean id="carCertificateDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.dao.CertificateDAOImpl" lazy-init="true">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
	<bean id="cocPhotoServiceJ" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.COCPhotoServiceImpl" lazy-init="true">
		<property name="cocPhotoDAO">
			<ref bean="cocPhotoDAOJ" />
		</property>
	</bean>

	<bean id="cocPhotoDAOJ" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl.COCPhotoDAOImpl" lazy-init="true">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>	
	
	<bean id="logService" class="com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceLogServiceImpl" lazy-init="true">
		<property name="interfaceLogDAO">
			<ref bean="logDAO" />
		</property>
		<property name="certificateService" ref="carCertificateService"/>
		<property name="cocPhotoService" ref="cocPhotoServiceJ"/>
	</bean>
	
	<bean id="logDAO" class="com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceLogDAOImpl" lazy-init="true">
		<property name="sessionFactory">
			<ref bean="mySessionFactory" />
		</property>
	</bean>
	
   	<bean id="xmlObjectFactory" class="com.dawnpro.dfpv.carfilemanager.module.business.share.factory.XMLConvertObjectFactory" lazy-init="true"/>
   	
	<!-- -->
 	<bean id="mqRecevieService" class="com.dawnpro.dfpv.carfilemanager.module.business.share.jms.MQIMessageService" init-method="init" >
		<property name="xmlObjectFactory" ref="xmlObjectFactory"/>
    	<property name="logService" ref="logService"/>
    	<property name="qManager" value="BRK1_QM"/>
    	<property name="channel" value="DFPV.ESB.SVRCONN"/>
    	<!-- <property name="hostName" value="**********"/> -->
    	<property name="hostName" value="***************"/><!--  -->
    	<!-- <property name="hostName" value="************"/> -->
    	<property name="port" value="38801"/>
	</bean>
	 
	<bean id="mqSnedService" class="com.dawnpro.dfpv.carfilemanager.module.business.share.jms.MQIMessageService" >
		<property name="xmlObjectFactory" ref="xmlObjectFactory"/>
    	<property name="logService" ref="logService"/>
    	<property name="qManager" value="BRK1_QM"/>
    	<property name="channel" value="DFPV.ESB.SVRCONN"/>
    	<!-- <property name="hostName" value="**********"/> -->
    	<property name="hostName" value="***************"/><!--  -->
    	<!-- <property name="hostName" value="************"/> -->
    	<property name="port" value="38801"/>
	</bean>
	<!--  
	<bean id="sendJob" class="com.dawnpro.dfpv.carfilemanager.module.business.share.jms.MQFileService" init-method="init" ></bean>-->
	 <bean id="receiveJob" class="com.dawnpro.dfpv.carfilemanager.module.business.share.jms.MQFileServiceReceive" init-method="init"  >
		<property name="worker" ref="interfaceWorkerJ"/>
    	<property name="qManager" value="BRK1_QM"/>
    	<property name="channel" value="DFPV.ESB.SVRCONN"/>
    	<!-- <property name="hostName" value="**********"/> -->
    	<property name="hostName" value="***************"/><!--  -->
    	<!-- <property name="hostName" value="************"/> -->
    	<property name="port" value="38801"/>	
	</bean>
	
	<bean id="interfaceDataProcessServiceJ" class="com.dawnpro.dfpv.carfilemanager.module.business.share.service.impl.InterfaceDataProcessServiceImpl" lazy-init="true" >
		<property name="interfaceLogDAO">
			<ref bean="logDAO"/>
		</property>
		
		<property name="certificateService" ref="carCertificateService"/>
		<property name="cocPhotoService" ref="cocPhotoServiceJ"/>				
	</bean>
	
	<bean id="interfaceWorkerJ" class="com.dawnpro.dfpv.carfilemanager.module.business.share.factory.InterfaceWorker" >
		<property name="interfaceDataProcessService">
			<ref bean="interfaceDataProcessServiceJ"/>
		</property>
	</bean>
	<!-- -->
	
</beans>
