package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.COCModelTypeDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.COCModelType;



public class COCModelTypeServiceImpl implements COCModelTypeService {
	private COCModelTypeDAO cocModelTypeDAO = null;

	private PaginationService paginationService = null;

	
	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	public COCModelTypeDAO getCocModelTypeDAO() {
		return cocModelTypeDAO;
	}

	public void setCocModelTypeDAO(COCModelTypeDAO cocModelTypeDAO) {
		this.cocModelTypeDAO = cocModelTypeDAO;
	}

	public void addCOCModelType(COCModelType obj)
			throws DataAccessException {
		try{
			this.cocModelTypeDAO.addCOCModelType(obj);
		}catch (DataAccessException e) {
			throw new SystemException("addCOCModelType Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("addCOCModelType Method Error:", e);
		}
	}

	public void deleteCOCModelType(Serializable id)
			throws DataAccessException {
		try{
			this.cocModelTypeDAO.deleteCOCModelType(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteCOCModelType Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("deleteCOCModelType Method Error:", e);
		}
	}

	public void deleteCOCModelType(Serializable[] id)
			throws DataAccessException {
		try{
			this.cocModelTypeDAO.deleteCOCModelType(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteCOCModelType Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("delete COCModelType Method Error:", e);
		}
	}

	public boolean isCOCModelTypeExist(String model)
			throws DataAccessException {
		try{
			List list = cocModelTypeDAO.findCOCModelType(" from COCModelType g where g.model=? ", new Object[]{ model});
			if(list != null && list.size() > 0){
				return true;
			}
		}catch (DataAccessException e) {
			throw new SystemException("isCOCModelTypeExist Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("isCOCModelTypeExist Method Error:", e);
		}
		return false;

	}

	public COCModelType loadCOCModelType(String id)
			throws DataAccessException {
		try{
			return this.cocModelTypeDAO.loadCOCModelType(id);
		}catch (DataAccessException e) {
			throw new SystemException("loadCOCModelType Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("loadCOCModelType Method Error:", e);
		}

	}

	public List<?> pagination(Page page, Map<String, String> params)
			throws DataAccessException {
		List results = null;
		try{
			String model = params.get("model") == null ? "" : params.get("model");
			StringBuffer sub = new StringBuffer(30);
			StringBuffer countBuf = new StringBuffer(30);
			StringBuffer sqlBuf = new StringBuffer(30);
			
			countBuf.append("select count(*) from COCModelType g ");
			sqlBuf.append("from COCModelType g ");
				
			sub.append(" where 1 = 1 ");
			if(!model.equals("")){
				sub.append(" and g.model like '%").append(model.toUpperCase()).append("%' ");
			}

			countBuf.append(sub);
			sqlBuf.append(sub+" order by g.createdate desc");
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countBuf.toString());
			results = this.paginationService.pagination(sqlBuf.toString());
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;

	}

	public void updateCOCModelType(COCModelType obj)
			throws DataAccessException {
		try{
//			COCModelType tmp = this.loadCOCModelType(obj.getId());
//			tmp.setLpz(obj.getLpz());
//			tmp.setCyear(obj.getCyear());
//			tmp.setXs(obj.getXs());
			
			this.cocModelTypeDAO.updateCOCModelType(obj);
		}catch (DataAccessException e) {
			throw new SystemException("updateCOCModelType Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("updateCOCModelType Method Error:", e);
		}
	}

	public COCModelType findCOCModelType(String id) throws DataAccessException {
		try{
			List<COCModelType> list = cocModelTypeDAO.findCOCModelType(" from COCModelType g where g.id=?", new Object[]{id});
			if(list != null && list.size() > 0){
				return list.get(0);
			}
		}catch (DataAccessException e) {
			throw new SystemException("isCOCModelTypeExist Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("isCOCModelTypeExist Method Error:", e);
		}
		return null;
		
	}

}
