package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

public class ModelverId implements java.io.Serializable{
	// Fields

	private String carmodel;
	private String factory;
	
	
	public ModelverId() {
	}
	
	public ModelverId(String carmodel, String factory) {
		this.carmodel = carmodel;
		this.factory = factory;
	}
	public String getCarmodel() {
		return carmodel;
	}
	public void setCarmodel(String carmodel) {
		this.carmodel = carmodel;
	}
	public String getFactory() {
		return factory;
	}
	public void setFactory(String factory) {
		this.factory = factory;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((carmodel == null) ? 0 : carmodel.hashCode());
		result = prime * result + ((factory == null) ? 0 : factory.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ModelverId other = (ModelverId) obj;
		if (carmodel == null) {
			if (other.carmodel != null)
				return false;
		} else if (!carmodel.equals(other.carmodel))
			return false;
		if (factory == null) {
			if (other.factory != null)
				return false;
		} else if (!factory.equals(other.factory))
			return false;
		return true;
	}
	
	
}
