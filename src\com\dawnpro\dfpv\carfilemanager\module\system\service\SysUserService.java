package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.business.UserLoginFaildException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

/**
 * ����:�û�����ӿ���.
 * @author:�Ų�
 * @since: 2008-1-12
 * **/
public interface SysUserService {
	
	/**
	 * ����:��¼����
	 * @param	username:	�û���
	 * @param	password:	����
	 * @return	User:		�����û���Ϣ�Ķ���
	 * **/
	public SysUser login(String username,String password)throws UserLoginFaildException;
	
	/**
	 * ����:�����û�����
	 * @param	user:�û�����
	 * **/
	public void addUser(SysUser user) throws DataAccessException;
	
	/**
	 * ����:�����û�����
	 * @param	user:�û�����
	 * **/
	public void updateUser(SysUser user) throws DataAccessException;
	
	/**
	 * ����:ɾ���û�����
	 * @param	user:�û�����
	 * **/
	public void deleteUser(SysUser[] user) throws DataAccessException;
	
	/**
	 * ����:��ѯ�û�����
	 * @param	sql:��ѯ���
	 * **/
	public List<SysUser> findUser(String sql) throws DataAccessException;
//	
//	/**
//	 * ����:���ȫ����ɫ����
//	 *@return 
//	 * **/
//	public List<SysRole> getAllRole() throws DataAccessException;
//	
	/**
	 * ����:�ж��û��Ƿ���ڷ���
	 * @param loginName:�û���
	 * **/
	public boolean isUserExist(String loginName) throws DataAccessException;
	
	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
	
	public SysUser loadUserObj(String loginName) throws DataAccessException;
	
	public void updafteUserState(SysUser user) throws DataAccessException;
	
	public List<SysRole> getAllRole() throws DataAccessException;
}
