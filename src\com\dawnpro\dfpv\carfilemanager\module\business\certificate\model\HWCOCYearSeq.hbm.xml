<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HWCOCYearSeq" table="T_HWCOC_YEAR_SEQ" lazy="false">
        <id name="id" type="java.lang.String">
            <column name="id" length="50" />
            <generator class="assigned" />
        </id>
        <property name="year" type="java.lang.String">
            <column name="YEAR" length="4" />
        </property>
        <property name="seq" type="java.lang.String">
            <column name="SEQ" length="4" />
        </property>     
        <property name="vin" type="java.lang.String">
            <column name="VIN" length="17" />
        </property>
    </class>
</hibernate-mapping>
