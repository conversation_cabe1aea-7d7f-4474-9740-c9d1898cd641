<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$(".display").each(function(i){
		  $(this).click(function() {
			  location.href="business/business/interfaceLog!messageContent.action?path="+$(this)[0].value;

			  return false;
		 });
	});

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var obj = $('#startDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			obj = $('#endDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			location.href="interfaceLog.action?currentPage="+$('#jump').val()+"&tradeCode="+encodeURI(encodeURI($('#tradeCode').val()))+"&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val();
   		}   
   		
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var obj = $('#startDate');
				if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
					obj.focus();
					return ;
				}
				obj = $('#endDate');
				if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
					obj.focus();
					return ;
				}
			  	var sd = $('#startDepdatetime').val();
				var ed = $('#endDepdatetime').val();
				var sl = $('#sLoginName').val();
				var sdesc = $('#sDescript').val();
				location.href=$(this).attr('value')+"&tradeCode="+encodeURI(encodeURI($('#tradeCode').val()))+"&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val();
		 });
	});

	$("#query").click(function() {
		var tradeCode = $('#tradeCode').val();
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		if(tradeCode==""&&startDate==""&&endDate==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入查询条件！');
			messageObj.dialog("open");
		}else{
			var obj = $('#startDate');
			if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}

			obj = $('#endDate');
			if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			
			location.href="interfaceLog.action?currentPage="+$('#jump').val()+"&tradeCode="+encodeURI(encodeURI($('#tradeCode').val()))+"&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val();
		}
	});

	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#startDate").datepicker($.datepicker.regional['zh']);
	$("#endDate").datepicker($.datepicker.regional['zh']);
	
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

       //给这行添加class值为over，并且当鼠标一出该行时执行函数

       $(this).removeClass("over");})    //移除该行的class
});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr><!-- <input id="d121" type="text" onfocus="WdatePicker({isShowWeek:true})"/> -->
				<td align="left" colspan="8"><p>接口编号:
				  <input type="text" id="tradeCode" class="text ui-widget-content" size="10" <s:if test="#request.tradeCode!=null"> value="<s:property value="#request.tradeCode" />" </s:if>/>	
				 作废日期:<input type="text" id="startDate" class="text ui-widget-content " size="10" <s:if test="#request.startDate!=null"> value="<s:property value="#request.startDate" />" </s:if>/>-<input type="text" id="endDate" class="text ui-widget-content"  size="10" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />" </s:if>/>
  			    </td>
				<td align="right"><button id="query" name="query" class="ui-button ui-state-default"/>查询</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
<div align="center">
<div id="users-contain" class="ui-widget">
	<table id="users" class="ui-widget ui-widget-content" >
		<thead>
			<tr class="ui-widget-header ">
				<th>接口编号</th>
				<th>流水号</th>
				<th>业务系统处理时间</th>
				<th>系统编号</th>
				<th>类型</th>
				<th>创建时间</th>
				<th>操作</th>
			</tr>
		</thead>
		<tbody>
			<s:iterator value="#request.interfaceLogPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><s:property value="id.tradeCode" /></td>
			  		<td><s:property value="id.reqSerialNo" /></td>
   		      		<td><s:property value="tradeTime" /></td>				
			  		<td><s:property value="requesterId" /></td>
			  		<td><s:property value="type" /></td>
			  		<td><s:property value="id.createTime" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="path" />">下载</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
</div>
</div>
</div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.interfaceLogPage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="interfaceLog.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.interfaceLogPage.currentPage==#request.interfaceLogPage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="interfaceLog.action?currentPage=<s:property value="#request.interfaceLogPage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.interfaceLogPage.currentPage>=#request.interfaceLogPage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="interfaceLog.action?currentPage=<s:property value="#request.interfaceLogPage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.interfaceLogPage.currentPage==#request.interfaceLogPage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="interfaceLog.action?currentPage=<s:property value="#request.interfaceLogPage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.interfaceLogPage.currentPage" />/总页数 <s:property value="#request.interfaceLogPage.maxPage" /> 总记录数 <s:property value="#request.interfaceLogPage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>