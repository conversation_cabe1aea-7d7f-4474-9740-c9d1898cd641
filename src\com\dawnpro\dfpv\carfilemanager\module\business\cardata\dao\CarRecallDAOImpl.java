package com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecall;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecodInfo;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecodInfoId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;

public class CarRecallDAOImpl extends GenericHibernateDAOImpl<CarRecodInfo> implements CarRecallDAO {

	public void addCarModel(CarRecodInfo obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCarModel Method Error:",e);
		}
	}
	
	
	public void updateCarModel(CarRecodInfo obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCarModel Method Error:",e);
		}
	}
	
	
	public void deleteCarModel(CarRecodInfo obj) {
		try{
			this.delete(obj);
		}catch(Exception e){
			throw new DataAccessException("deleteCarModel Method Error:",e);
		}
	}
	
	public void deleteCarModels(CarRecodInfo[] obj) {
		try{
			this.deleteBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("deleteCarModels Method Error:",e);
		}
	}
	
	public CarRecodInfo loadCarModelObj(CarRecodInfoId id) {
		CarRecodInfo gg = null;
		try{
			List<CarRecodInfo> result = findCarModel("from CarRecodInfo g where g.id.c1=? and g.id.vercode=?", new Object[]{id.getC1(),id.getVercode()});
			if(result!=null && result.size()>0)
				gg=result.get(0);
		}catch(Exception e){
			throw new DataAccessException("loadCarModelObj Method Error:",e);
		}
		return gg;
	}

	public List<CarRecodInfo> findCarModel(String sql) {
		List<CarRecodInfo> results = null;
		try{
			results = this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findCarModel Method Error:",e);
		}
		return results;
	}

	public List<CarRecodInfo> findCarModel(String sql, Object[] params) {
		List<CarRecodInfo> results = null;
		try{
			results = this.find(sql,params);
		}catch(Exception e){
			throw new DataAccessException("findCarModel Method Error:",e);
		}
		return results;
	}

	public int updateCarModel(String sql,Object[] value){
		int num = 0;
		try{
			num = this.updateOrDeleteToHSQL(sql,value);
		}catch(Exception e){
			throw new DataAccessException("updateCarModel Method Error:",e);
		}
		return num;
	}
	
	public Object[] findCarModelInfo(String hql) {
		Object[] obj = null;
		try{
			List results = null;
			results = this.getHibernateTemplate().find(hql);
			if(results != null && results.size()>0)
				obj = (Object[])(results.get(0));
		}catch(Exception e){
			throw new DataAccessException("findCarModelInfo Method Error:",e);
		}
		return obj;
	}
	
	public CarRecodInfo findMaxCarModelBySccx(String c1) {
		CarRecodInfo obj = null;
		try{
			List<CarRecodInfo> results = null;
			String sql = "from CarRecodInfo t where t.id.c1=? order by substr(t.id.vercode,length(t.id.vercode)-1,2) desc";
			Object[] params = new Object[]{c1};
			results = this.find(sql,params);
			if(results != null && results.size()>0)
				obj = results.get(0);
		}catch(Exception e){
			throw new DataAccessException("findMaxCarModelBySccx Method Error:",e);
		}
		return obj;
	}
}
