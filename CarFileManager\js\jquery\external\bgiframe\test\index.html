<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
	"http://www.w3.org/TR/html4/strict.dtd">
<html debug="true">
	<head>
		<meta http-equiv="Content-type" content="text/html; charset=utf-8">
		<title>jQuery bgiframe Visual Test</title>
		
		<!-- load latest build of jquery.js -->
		<script type="text/javascript" src="../../../jquery/dist/jquery.js"></script>

		<!-- load dimensions.js (this is what we're testing! -->
		<script type="text/javascript" src="../jquery.bgiframe.js"></script>
		
		<!-- load firebug lite 
		<script type="text/javascript" src="http://brandon.jquery.com/firebuglite/firebug.js"></script>-->
		
		<link rel="Stylesheet" media="screen" href="../../../jquery/test/data/testsuite.css" />
		
		<script type="text/javascript" charset="utf-8">
			$(function() {
				$('#userAgent').html(navigator.userAgent);
				$('#box2').bgiframe();
				$('#box3').bgiframe({top: -5, left: -5});
				$('#box4').bgiframe({top: -5, left: -5, width: 270, height: 120});
				$('#box5').bgiframe({top: 0, left: 0, width: 260, height: 110});
				$('#box6').bgiframe({top: '-5px', left: '-5px', width: '270px', height: '120px'});
				$('#box7').bgiframe({top: '-.5em', left: '-.5em', width: '17em', height: '12em'});
				$('#box8').bgiframe({top: '-.5em', left: '-.5em'});
				$('#box9').bgiframe({opacity:false});
			});
		</script>
		
		<style type="text/css" media="screen">
			#wrapper { position: relative; width: 100%; font: 12px Arial; }
				form { position: absolute; top: 0; left: 0; width: 100%; }
					select { position: relative; width: 100%; margin: 0 0 2px; z-index: 1; }
				
				.box { position: relative; z-index: 2; float: left; margin: 5px; border: 5px solid #666; padding: 5px; width: 250px; height: 100px; color: #000; background-color: #999; }
					dl { margin: 0; padding: 0; }
						dt { float: left; margin: 0; padding: 0; width: 50px; }
						dd { margin: 0; padding: 0; }
				#box7, #box8 { border-width: .5em; padding: .5em; width: 15em; height: 10em; }
		</style>
	</head>
	<body>
		<h1 id="banner">jQuery bgiframe - Visual Test</h1>
		<h2 id="userAgent"></h2>
		<div id="wrapper">
			<form action="#" method="get" accept-charset="utf-8">
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
				<select name="test"><option>Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing Testing</option></select>
			</form>
			
			<div id="box1" class="box">nothing</div>
			<div id="box2" class="box">
				<dl>
					<dt>top:</dt>
					<dd>'auto'</dd>
					
					<dt>left:</dt>
					<dd>'auto'</dd>
					
					<dt>width:</dt>
					<dd>'auto'</dd>
					
					<dt>height:</dt>
					<dd>'auto'</dd>
				</dl>
			</div>
			<div id="box3" class="box">
				<dl>
					<dt>top:</dt>
					<dd>0</dd>
					
					<dt>left:</dt>
					<dd>0</dd>
					
					<dt>width:</dt>
					<dd>'auto'</dd>
					
					<dt>height:</dt>
					<dd>'auto'</dd>
				</dl>
			</div>
			<div id="box4" class="box">
				<dl>
					<dt>top:</dt>
					<dd>-5</dd>
					
					<dt>left:</dt>
					<dd>-5</dd>
					
					<dt>width:</dt>
					<dd>270</dd>
					
					<dt>height:</dt>
					<dd>120</dd>
				</dl>
			</div>
			<div id="box5" class="box">
				<dl>
					<dt>top:</dt>
					<dd>0</dd>
					
					<dt>left:</dt>
					<dd>0</dd>
					
					<dt>width:</dt>
					<dd>260</dd>
					
					<dt>height:</dt>
					<dd>110</dd>
				</dl>
			</div>
			<div id="box6" class="box">
				<dl>
					<dt>top:</dt>
					<dd>'-5px'</dd>
					
					<dt>left:</dt>
					<dd>'-5px'</dd>
					
					<dt>width:</dt>
					<dd>'270px'</dd>
					
					<dt>height:</dt>
					<dd>'120px'</dd>
				</dl>
			</div>
			<div id="box7" class="box">
				<dl>
					<dt>top:</dt>
					<dd>'-.5em'</dd>
					
					<dt>left:</dt>
					<dd>'-.5em'</dd>
					
					<dt>width:</dt>
					<dd>'17em'</dd>
					
					<dt>height:</dt>
					<dd>'12em'</dd>
				</dl>
			</div>
			<div id="box8" class="box">
				<dl>
					<dt>top:</dt>
					<dd>'-.5em'</dd>

					<dt>left:</dt>
					<dd>'-.5em'</dd>

					<dt>width:</dt>
					<dd>'auto'</dd>

					<dt>height:</dt>
					<dd>'auto'</dd>
				</dl>
			</div>
			<div id="box9" class="box">
				<dl>
					<dt>top:</dt>
					<dd>'auto'</dd>
					
					<dt>left:</dt>
					<dd>'auto'</dd>
					
					<dt>width:</dt>
					<dd>'auto'</dd>
					
					<dt>height:</dt>
					<dd>'auto'</dd>
					
					<dt>opacity:</dt>
					<dd>false</dd>
				</dl>
			</div>
		</div>
	</body>
</html>