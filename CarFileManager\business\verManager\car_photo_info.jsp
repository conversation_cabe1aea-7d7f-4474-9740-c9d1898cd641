<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<link href="js/jquery/preview/css.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
	
   	#input_text{position: absolute;font-size: 18px;xwidth: 390px;xheight: 23px;xborder: 1px solid #C9C9C9;}
 	#input_div{position: absolute;width: 230px;height: 150px;left: 108px; top: 105px;border: 1px solid #454545;
                display: none;overflow: scroll;z-index:9999}
  	#input_div{text-decoration: none;background-color: #FFFAFA;}
    #input_div div{vertical-align: middle;padding: 4px;font-weight: bold;color: #000000;width: 100%;}
   	.div_item_select {background-color: #E0EEEE;height: 22px;font-size: 14px;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/jquery/jquery-cookie.js"></script>
<script type="text/javascript" src="js/jquery/preview/preview.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/images.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';
	var a_i = null;
	var page = '<%= String.valueOf(request.getAttribute("page"))%>';
	
	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	$("#export_photo").attr("disabled", true);
	$("#print").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
        	var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
					$("#export_photo").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("print")!=-1){
					$("#print").attr("disabled", false);
					isprint = true;
				}
            }
        }
    });
	
	$("#create").click(function() {
		type = "add";
		$('#coc_photo_dialog').data('title.dialog', '新增车型照片').dialog('open');	
	});
	
	$("#update").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
   		if(index==1){
   			type = "update";
   			var params = "id="+id;
   			jQuery.ajax({
	            url: 'business/cocPhotoManager!carModelPhotoInfo.action',		           
	            data: params, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var content = data.json;
		            var photoObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#coc_photo_dialog');
					if(photoObj.state=="0"){
						setDialogValue(dialogObj,photoObj);
						
						dialogObj.find('#model').attr('readonly',true);
		       	    	dialogObj.data('title.dialog', '修改车型照片').dialog('open');
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
    	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
        checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "车型型号:"+tmp[0]+" 颜色:"+tmp[1]+" 版本号:"+tmp[2];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"车型型号:"+tmp[0]+" 颜色:"+tmp[1]+" 版本号:"+tmp[2];
				}
				if(tmp[3]=="1"||tmp[3]=="9"){
					effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
				}
             }
      	});
    	if(effIndex>0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效或是历史状态，不能删除！');
   	   		messageObj.dialog('open');   	   			
	   	}else if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#id').val(id);
   	   	}
	});
	
	$("#effect").click(function(){
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				id = this.value;
				info = "车型型号:"+tmp[0]+" 颜色:"+tmp[1]+" 版本号:"+tmp[2];
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		type = "effect";
     		var params = "id="+id;
   	   		jQuery.ajax({
            	url: 'business/cocPhotoManager!carModelPhotoInfo.action',		           
            	data: params, 
	        	type: 'POST',
            	beforeSend: function() {
            	
            	},
            	error: function(request) {
                
            	},
            	success: function(data) {
	            	var content = json2Bean(data).json;
	            	var carObj = eval("("+content.toString()+")"); 
            		var dialogObj = $('#public_notice_dialog');
					if(carObj.state=="0"){
						messageObj = $('#operate_dialog');
		   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共'+index+'条数据');
		   	   			messageObj.dialog('open');
		   	   			messageObj.find('#id').val(id);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
            	}
       		});
   	   	}
	});
	
	$("#upload").click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>/business/verManager/coc_photo_upload.jsp',winName,params);	
		
		return false;
	});
	
	$("#import").click(function() {
		var winName="上传窗口"; 
		 
		var awidth=screen.availWidth/15*5;   
		var aheight=screen.availHeight/10*4;  
		var atop=(screen.availHeight - aheight)/2;   
		var aleft=(screen.availWidth - awidth)/2;
		var param0="scrollbars=0,status=2,menubar=0,resizable=2,location=0";  
		
		var params="top=" + atop + ",left=" + aleft + ",width=" + awidth + ",height=" + aheight + "," + param0 ; 
				
		window.open ('<%=basePath%>business/verManager/coc_photo_batch_upload.jsp',winName,params);	
		
		return false;
	});
	
	$("#coc_photo_dialog").dialog({bgiframe: true,autoOpen: false,width: 520,height: 350,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#coc_photo_dialog');
					allFields = $([]).add(parent.find('#model')).add(parent.find('#photopath'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#coc_photo_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//
					dlgButton.attr('disabled', 'disabled');
			    	dlgButton.addClass('ui-state-disabled');
			    	var dialog = $('#coc_photo_dialog');
			    	
			    	var photoname = dialog.find('#photoname').val();
			    	photoname = photoname.substring(0,7);
			    	//alert(photoname);
			    	//判断是否为典型车型，如果是就弹出对话框让用户决定是否连同更新该典型车型下的中性车型参数。
		   			jQuery.ajax({
			            url: 'business/typicalityNeutralPhoto!findTypicalityCarPhotoModel.action',		           
			            data: {"dxcx":photoname}, 
				        type: 'POST',
				        dataType:'json', 
			            beforeSend: function() {
			            
			            },
			            error: function(request) {
			               
			            },
			            success: function(data) {
			            	var m_zxcx='';
			            	var content = data.json;
			     			
			            	if(content!=''){		            			            	
					            var carObj = eval("("+content.toString()+")"); 
				            	if(carObj!=null && carObj.zxcx!=null){
				            		if(window.confirm("是否同时对关联的中性车型"+ carObj.zxcx + "进行相应操作！")){
				            			m_zxcx=carObj.zxcx;
				            		}
				            	}
			            	}
			            	
			            	dialog.find('#zxcx').val(m_zxcx);
							if(type=="add"){
								dialog.find('#createForm')[0].action="business/cocPhotoManager!addPhoto.action";
								dialog.find('#createForm')[0].submit();
							}else if(type=="update"){
								dialog.find('#createForm')[0].action="business/cocPhotoManager!updatePhoto.action";
								dialog.find('#createForm')[0].submit();
							}            
			            }
			        }); 	        
			    	
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			clear($(this));
		}
		});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				if(type=="delete"){					
					formObj[0].action = "business/cocPhotoManager!deletePhoto.action";
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/cocPhotoManager!effectCarModel.action";
					formObj[0].submit();
				}
			}
		}
	});
	
	

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		

		return false;
	}
	
	$('#query').click(function() {
		var qmodel = $('#qmodel').val();
		var qstate = $('#qstate').val();
		location.href="cocPhotoManager.action?qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate+"&menuid="+menuid;  
	});
	
	$('#export').click(function() {
		var qmodel = $('#qmodel').val();
		var qstate = $('#qstate').val();
		location.href="cocPhotoManager!exportData.action?qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate;  
	});
	
	$('#export_photo').click(function() {
		var qmodel = $('#qmodel').val();
		var qstate = $('#qstate').val();
		location.href="cocPhotoManager!exportPhoto.action?qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate;  
	});
	
	$('#print').click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			var values = id.split(",");
   	   		var model = values[0];
   	   		var filename = values[1];
   	   		var vercode = values[2];
   	   		
   	   		var photo_printer = $.cookie("photoprinter");
			var messageObj = $('#message_dialog');
			if(photo_printer==null){
				messageObj.find('#message').text('警告:请设置车型照片打印机！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}
   	   		
   			window.document.carphotoprint.printCarPhoto(model,filename,vercode);
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要打印的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能打印一条数据！');
	   		messageObj.dialog('open');
   	   	 }
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

	$("#modelver_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 930,height: 350,modal: true,
		buttons: {
			'关闭': function() {
				$(this).dialog('close');
			}
		},
		close: function() {
			$(this).find('input').attr('value','');
		}
	});
	
	function validate(parent){
		var obj = $(parent).find('#model');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产车型字段不能为空，最大长度为50！');			
			return false;
		}
		obj = $(parent).find('#photopath');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'请上传车型图片！');			
			return false;
		}
		var cmodel = $(parent).find('#model').val();
		var pname = $("#photo").html().substr(0,$("#photo").html().lastIndexOf('.'));
		//alert("model="+$(parent).find('#model').val() + " : photoname=" + $("#photo").html().substr(0,$("#photo").html().lastIndexOf('.')));
		//if($(parent).find('#model').val()!=$("#photo").html().substr(0,$("#photo").html().lastIndexOf('.'))){
		if((cmodel.substr(0,11) + cmodel.substr(15,18))!=(pname.substr(0,11) + cmodel.substr(15,18))){
			updateTips($(parent).find('#validateTips'),'生产车型与车型图片的车型编码不一致！');
			return false;
		}
		
		
		return true;
	}
	
	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#model').val(jsonObj.id.filename.substring(0,jsonObj.id.filename.lastIndexOf(".")));
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		dialogObj.find('#oldphotoname').val(jsonObj.id.filename);
		dialogObj.find('#photo').html(jsonObj.id.filename);
		dialogObj.find('#photo').show();
		dialogObj.find('#photopath').val(jsonObj.path);
	}
	
	function clear(dialogObj){
		dialogObj.find('#model').val("");
		dialogObj.find('#photo').attr("value","");
		dialogObj.find('#photopath').val("");
		dialogObj.find('#model').attr('readonly',false);
		dialogObj.find('#photo').hide();
		
		type = null;
	}
	
	$('#jump').bind('keyup',function(event) {
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var qmodel = $('#qmodel').val();
			var qstate = $('#qstate').val();
			location.href="cocPhotoManager.action?currentPage="+$('#jump').val()+"&qmodel="+encodeURI(encodeURI(qmodel))
				+"&qstate="+qstate+"&menuid="+menuid;   
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var qmodel = $('#qmodel').val();
			   	var qstate = $('#qstate').val();
				location.href=$(this).attr('value')+"&qmodel="+encodeURI(encodeURI(qmodel))+"&qstate="+qstate+"&menuid="+menuid;
		 });
	});

	$('#qmodel').keyup(function() {
		
	});
	
	if(page=="alert"){
		type = "add";
		$('#coc_photo_dialog').data('title.dialog', '新增车型照片').dialog('open');	
		$('#coc_photo_dialog').find('#model').val("<%= String.valueOf(request.getAttribute("model"))%>");
	}
	
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  
		$(this).addClass("over");}).mouseout(function(){ 
		//给这行添加class值为over，并且当鼠标一出该行时执行函数
		$(this).removeClass("over");})    //移除该行的class
		
		/**自动提示框**/
		 $(document).click(function(event){
             if (event.target.id != "model") {
					$("#input_div").slideUp(200);
             }
         });
	 
	    /**
		 * 鼠标在文本框输入值
		 * @param {Object} event
		 */
	$("#coc_photo_dialog").find("#model").keyup(function(event){
		if($(this).attr("readonly")==false&&this.value.length>=11){
			  if (event.keyCode == 40) {//down
		             chageSelect(1);
		         }
		         else  if (event.keyCode == 38) {//up
		             chageSelect(-1);
		         }
		         else  if (event.keyCode == 13) {//回车
		        		return false;
		        	// item_click($("#input_div div[class='divItemSelect']"));
		         }else   if (this.value.length > 0) {
		             //..ajax请求， 返回的时候 调用。。
		             //以下为 Ajax 返回的时候 调用的数据
		            var params = "model="+this.value;
		            jQuery.ajax({
			            url: 'business/cocPhotoManager!findMaterialnoInfo.action',		           
			            data: params, 
				        type: 'POST',
				        dataType:'json', 
			            beforeSend: function() {
			            
			            },
			            error: function(request) {
			               
			            },
			            success: function(data) {
			            	var content = data.json;
			            	if(content!=""&&content!="null"){
			            		 var materialnos = eval("("+content.toString()+")"); 
			            		 var str = "";
			 		             var index = 0;
			 		             for (var i = 0; i < materialnos.length; i++){
			 		                 index = i;//此处 为展示顺序，必须要
			 		                 //此处拼接服务器返回的数据。。。。
			 		                 str += "<div>" + materialnos[i]  + "</div>" + //展示的数据
			 		                 "<input type='hidden' name='d_index' value='" +
			 		                 index +
			 		                 "' />" //位置，勿动
			 		             	//+ "<input type='hidden' name='d_value' value='" +index +"' />"; //用于放置数据
			 		             }
			 		             
			 		             //展示层，并展示数据
			 		             $("#input_div").html(str).slideDown(200);
			 		             //注册事件
			 		             registerInputEvent();
			            	}
			            }
			        });
		         }
		         else {
		             $("#input_div").slideUp(200);
		         }	
		}
	});
		//.blur(function(){$("#" + showDataDivId).slideUp(200);});
		/**
		 * 键盘操作  向上 或向上 
		 * @param {Object} opt   向上 -1  向下 1 
		 */
     function chageSelect(opt){
         if ($("#input_div").css('display') == 'block') {
             var obj = $("#input_div div[class='div_item_select']");
             if (obj.html() == null) {//当前还未选中。
                 if (opt == 1) {
                     $("#input_div div:first").addClass("div_item_select");
                 }
                 else {
                     $("#input_div div:last").addClass("div_item_select");
                 }
             }
             else { 
                 var curr = parseInt($("#input_div div[class='div_item_select'] ~ input[name='d_index']").val()) + opt;
                 var divCount = $("#input_div div").size();
                 $("#input_div div[class='div_item_select']").removeClass("div_item_select");
                 $("#input_div div:eq(" + ((curr < 0) ? (divCount - 1) : ((curr == divCount) ? 0 : curr) + ")")).addClass("div_item_select");
             }
         }
     }
		/**
		 * 注册事件
		 */
     function registerInputEvent(){
         $("#input_div div").click(function(){
             item_click($(this));
         }).mouseover(function(){
             $("#input_div div[class='div_item_select']").removeClass("div_item_select");
             $(this).addClass("div_item_select");
         }).mouseout(function(){
             $(this).removeClass("div_item_select");
         });
     }
     /**
      * 点击每一项的操作
      * @param {Object} obj
      */
     function item_click(obj){
         if (obj.html() == null) {//如果是按回车键。。。
             //暂时不做操作。。。。
         }
         else { //如果是点击 选择
             $("#model").val($(obj).html()).focus();
             //取得 你要放置的数据  d_value   为 变量名。。。
             //var currValue = $("#" + showDataDivId + " div[class='" + divItemSelect + "'] ~ input[name='d_value']").val();
         }
			$("#input_div").hide();
         //跳转。。。。。等 操作。
     }
      
     document.onkeydown = function(e){  
 	    var ev = document.all ? window.event : e;
 	    if(ev.keyCode==13) {// 如（ev.ctrlKey && ev.keyCode==13）为ctrl+Center 触发
 	     	return false;
 	    }
 	  }
});

function readPrinterCookie(){
	var cookie = new Array(2);
	cookie[0] = $.cookie("cocprinter");
	cookie[1] = $.cookie("photoprinter");
	
	return cookie;
}

function showInfo(c1,vercode,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="7"><p>
					生产车型:<input type="text" id="qmodel" name='qmodel' class="text ui-widget-content " size="10" <s:if test="#request.qmodel!=null"> value="<s:property value="#request.qmodel" />"</s:if> /> 
				 	状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 
  			    </td>
  			    <td width="60" align="right"></td>
			     <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all">查询</button></td>
			</tr>
			<tr>
				<td width="90%"></td>
				 <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  	 <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			     <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			     <td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
			     <td width="60" align="right"><button id="print" class="ui-button ui-state-default ui-corner-all">打印</button></td>
			     <td width="60" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">批量导入</button></td>
			     <td width="60" align="right"><button id="export_photo" class="ui-button ui-state-default ui-corner-all">照片导出</button></td>
			     <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
				<th width="4%">选择</th> 
                <th width="8%">生产车型</th>
			    <th width="15%">照片名称(物料号)</th>	
			    <th width="10%">版本</th>	
			    <th width="5%">状态</th>
			    <th width="8%">创建人</th>	
				<th width="15%">创建时间</th>
				<th width="15%">生效时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.photoPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
				<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.model" />,<s:property value="id.filename" />,<s:property value="id.vercode" />,<s:property value="state" />' ></td>
			  		<td><s:property value="id.model" /></td>
			  		<td><s:property value="id.filename" /></td>
   		      		<td><s:property value="id.vercode" /></td>
   		      		<td>
   		      			<s:if test="state==0">未生效</s:if>
			  			<s:elseif test="state==1">生效</s:elseif>
			  			<s:elseif test="state==9">历史</s:elseif>
   		      		</td>
   		      		<td><s:property value="creator" /></td>
   		      		<td><s:property value="time" /></td>
   		      		<td><s:property value="effecttime" /></td>
			  		<td><a class='preview' onclick="return false;" href='photo/modelphoto/<s:property value="path" />' title='生产车型:<s:property value="id.model" /> 照片名称(物料号):<s:property value="id.filename" /> 版本:<s:property value="id.vercode" />' >预览</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.photoPage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocPhotoManager.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.carVerInfoPage.currentPage==#request.photoPage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocPhotoManager.action?currentPage=<s:property value="#request.photoPage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.photoPage.currentPage>=#request.photoPage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocPhotoManager.action?currentPage=<s:property value="#request.photoPage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.photoPage.currentPage==#request.photoPage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocPhotoManager.action?currentPage=<s:property value="#request.photoPage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.photoPage.currentPage" />/总页数 <s:property value="#request.photoPage.maxPage" /> 总记录数 <s:property value="#request.photoPage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="coc_photo_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" >
	  	<table id="one" width="100%" border="0">
	  		<tr>
	  			<td><label><P>生产车型</label></td>
				<td><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="28" /><div id="input_div"></td>
	  		</tr>
	  		<tr>
	  			<td><label><P>车型图片</label></td>
				<td><a id="photo" style="display:none" value=""></a><button id="upload" class="ui-button ui-state-default ui-corner-all">上传</button></td>
	  		</tr>
		</Table>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		<input type='hidden' id='photopath' name='photopath' value=''/>
		<input type='hidden' id='photoname' name='photoname' value=''/>
		<input type='hidden' id='oldphotoname' name='oldphotoname' value=''/>
		<input type='hidden' id='vercode' name='vercode' value=''/>
		<input type='hidden' id='currentPage' name='currentPage' value='<s:property value="#request.photoPage.currentPage" />'/>
		<input type='hidden' id='qmodel' name='qmodel' value='<s:property value="#request.qmodel" />'/>
		<input type='hidden' id='qstate' name='qstate' value='<s:property value="#request.qstate" />'/>
		<input type='hidden' id='zxcx' name='zxcx' />
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='id' name='id' />
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	<input type='hidden' id='currentPage' name='currentPage' value='<s:property value="#request.photoPage.currentPage" />'/>
	<input type='hidden' id='qmodel' name='qmodel' value='<s:property value="#request.qmodel" />'/>
	<input type='hidden' id='qstate' name='qstate' value='<s:property value="#request.qstate" />'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div align="center"> 
	<jsp:plugin name="carphotoprint" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarVersionPrintApplet.class" codebase="." archive = "../applet/printVersion.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/com-jaspersoft-ireport.jar,../applet/Qrcode_encoder.jar,../applet/iText-2.1.0.jar,../applet/iTextAsian.jar" 
	iepluginurl="http://10.5.7.2/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="0" width="0">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="COCSupplement"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
</body>
</html>