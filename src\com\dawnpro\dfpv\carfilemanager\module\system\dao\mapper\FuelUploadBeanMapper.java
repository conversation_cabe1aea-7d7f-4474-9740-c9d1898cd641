package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FuelUploadBean;

public class FuelUploadBeanMapper implements RowMapper<List<FuelUploadBean>> {

	public List<FuelUploadBean> mapRow(ResultSet rs) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<FuelUploadBean> mapRow(Collection rs) {
		List<FuelUploadBean> list = new ArrayList<FuelUploadBean>();
		int point = 0;
		for(Object obj : rs){
			point = 0;
			Object[] objs = (Object[])obj;
			FuelUploadBean bean = new FuelUploadBean();
			bean.setVin(objs[point] == null ? "" : (objs[point]+"").trim());
			bean.setCxxh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setClzl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setProddate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEdzk(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setJkjxs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setBgbh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setJcjgmc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setQhlj(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setLtgg(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setScqe1(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setQdxs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setRllx(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTymc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setYyc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZbzl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setJdzzl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZgcs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZj(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZwps(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setBsqdws(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setBsqlx(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEdgl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFdjxh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZdjgl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setPl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setJcjnjs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setQgs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setQtxx(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSjgk(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSqgk(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZhcopl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZhgk(objs[++point] == null ? "" : (objs[point]+"").trim());
			
			bean.setCddzgcs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDczlzbzlb(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDcbnl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDczbcdy(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDczednl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDczzl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDjedgl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDjfznj(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDjlx(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZhgkdnxhl(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setZhgkxslc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setHhdljgxs(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setHhdlzddglb(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setXsmssdxzgn(objs[++point] == null ? "" : (objs[point]+"").trim());
			
			/*bean.setFuelupload(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFueldelay(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFuelmodified(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFueldel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDlareason(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setModreason(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDelreason(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setXshzhdel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setXshzhupload(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setXsmssdxzgn(objs[++point] == null ? "" : (objs[point]+"").trim());*/
			
			list.add(bean);

		}
		return list;
	}

}
