package com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhoto;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CocPhotoId;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bm07;

public interface COCPhotoDAO {

	public void addPhoto(CocPhoto photo);
	
	public void addPhoto(CocPhoto[] photo);
	
	public void updatePhoto(String oldfilename,CocPhoto photo);
	
	public void updatePhoto(CocPhoto[] photo);
	
	public void updateEffectCarModelPhoto(Object[] value);
	
	public void deletePhoto(CocPhoto photo);
	
	public void deletePhoto(CocPhoto[] photo);
	
	public CocPhoto loadCarModelPhotoObj(CocPhotoId id);
	
	public List<CocPhoto> findPhoto(String sql,Object[] params);
	
	public List findCarModelMaterialno(String sql,Object[] params);
	
}
