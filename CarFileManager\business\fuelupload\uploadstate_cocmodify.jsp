<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#export").attr("disabled", true);
	
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
        

	$("#query").click(function(){

		var vin = $("#vin").val();
		var perrtype = $("#perrtype").val();

		location.href="cocUploadStateModify.action?vin="+vin+"&perrtype="+perrtype;
	});

	$("#update").click(function(){
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = id + this.value + ","; 
             }
      	});
      	if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要修改上传的数据！');
   	   		messageObj.dialog('open');      
   	   		return false;		
      	}else{
      		type='update';
      		$('#cocUploadStateModify_dialog').find("#ids").val(id);
      		$('#cocUploadStateModify_dialog').data('title.dialog', '确定修改').dialog('open');	
      		return false;	
      	}
		
	});
	
	//20180514  重新上传
	$("#reupload").click(function(){
       	var id = "";
       	var index = 0;
       	var messageObj = null;
       	var errtype = $('#perrtype').val();
       	
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = id + this.value + ","; 
             }
      	});
      	
      	if(index==0 && errtype == ""){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要重新上传的数据或者设置错误描述的类型！');
   	   		messageObj.dialog('open');      
   	   		return false;		
      	}else{
	   		type='reupload';
	   		$('#cocUploadStateModify_dialog').find("#ids").val(id);
	   		$('#cocUploadStateModify_dialog').find("#errtype").val(errtype);
	   		$('#cocUploadStateModify_dialog').data('title.dialog', '重新上传').dialog('open');	
	   		return false;	
      	}
		
	});	
	
	
	
	
	$("#cocUploadStateModify_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 300,
		height: 200,
		modal: true,
		buttons: {
			'取消': function() {
				$(this).find("#ids").val('');
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'确定': function() {
				var dialog = $('#cocUploadStateModify_dialog');
				dialog.find('#createForm')[0].action="business/cocUploadStateModify!updateState.action";
				dialog.find('#createForm')[0].submit();				
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			$(this).find("#ids").val('');
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
		}
	});
	
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});


	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var vin = $("#vin").val();
			var perrtype = $("#perrtype").val();
			location.href="cocUploadStateModify.action?currentPage="+$('#jump').val()+"&vin="+encodeURI(encodeURI(vin))+"&perrtype="+encodeURI(encodeURI(perrtype))+"&menuid="+menuid;
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var vin = $("#vin").val();
				var perrtype = $("#perrtype").val();
				location.href="cocUploadStateModify.action?currentPage="+$('#jump').val()+"&vin="+encodeURI(encodeURI(vin))+"&perrtype="+encodeURI(encodeURI(perrtype))+"&menuid="+menuid;
		 });
	  });
	  
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
<!--form id="qfrm" name="qfrm"-->
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan="">VIN:<input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" <s:if test="#request.vin!=null"> value="<s:property value="#request.vin" />"</s:if> size="18" /> 
				
				错误描述：<s:select id="perrtype"  name="perrtype" list="#{'203':'203:vin 号码错误,不能为空','204':'204:VIN 检验码不正确','205':'205:本次上传列表中存在 vin 重复','206':'206:COC 在当前厂商关系下不存在','207':'207:发动机号不能为空','208':'208:颜色不能为空'}"  listKey="key" listValue="value" headerKey="" headerValue="全部" theme="simple" value="#request.perrtype"></s:select>
				
				</td>
			</tr>
			
			<tr>
			   <td width="80%" colspan="5"></td>			   
			   <td width="5%" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			   <td width="7%" align="right"><button id="reupload" class="ui-button ui-state-default ui-corner-all"/>重新上传</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="6%">vin</th>
			    <th width="6%">公告车型</th>
			    <th width="6%">生产日期</th>
			    <th width="6%">上传日期</th>
			    <th width="6%">一致性编号</th>
			    <th width="6%">上传状态</th>
			    <th width="6%">反馈信息</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator id="item" value="#request.uploadStateModifyPageData" status="obj"  > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
				
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="cxxh" /></td>
			  		<td><s:property value="proddate" /></td>
			  		<td><s:date name="adtcoc" format="yyyy-MM-dd"/></td>
			  		<td><s:property value="cxxhname" /></td>
			  		<td>
					<s:if test='cocupload=="T"'>
			  			上传成功
			  		</s:if><s:elseif test='cocupload=="F"'>
			  			上传失败
			  		</s:elseif><s:elseif test='cocupload=="I"'>
			  			正在上传
			  		</s:elseif><s:else>
						未上传
					</s:else>			  				  		
					</td>
			  		<td><s:property value="cocresponse" /></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocUploadStateModify.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocUploadStateModify.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocUploadStateModify.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocUploadStateModify.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
<!--/form-->
</div>

<div id="cocUploadStateModify_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td align="left"><label><P>确定要重新上传？</label></td>
	    	</tr>			
		</Table>
		<input type='hidden' id='ids' name='ids'/>
		<input type='hidden' id='errtype' name='errtype'/>

		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

</body>
</html>