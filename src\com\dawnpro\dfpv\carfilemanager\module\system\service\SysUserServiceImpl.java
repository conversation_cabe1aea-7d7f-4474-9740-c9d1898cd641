package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;


import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.business.UserLoginFaildException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysRoleDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysUserDA0;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

/**
 * ����:�û�������.
 * @author:�Ų�
 * @since: 2008-1-12
 * **/
public class SysUserServiceImpl implements SysUserService{
	private Logger logger = Logger.getLogger(SysUserServiceImpl.class.getName());
	private SysUserDA0 sysUserDao = null;
	private SysRoleDAO sysRoleDAO=null;
	private PaginationService paginationService = null;

	public void setSysUserDao(SysUserDA0 sysUserDao) {
		this.sysUserDao = sysUserDao;
	}

	public void setSysRoleDAO(SysRoleDAO sysRoleDAO) {
		this.sysRoleDAO = sysRoleDAO;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	/**
	 * ����:��¼����
	 * @param	username:	�û���
	 * @param	password:	����
	 * @return	user:		�����û���Ϣ�Ķ���
	 * **/
	public SysUser login(String username, String password)throws UserLoginFaildException{
		SysUser user = null;
		try{
			List<SysUser> result =  this.sysUserDao.findUser("from SysUser where loginname = ? and password = ? ",new String[]{username,password});
			if(result==null||result.size()==0){
				throw new UserLoginFaildException("�û���������������,���ʺŲ�����!");
			}else{
				user = (SysUser)result.get(0);
			}
		}catch(DataAccessException e){
			logger.error("login Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("login Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
		
		return user;
	}


	public void addUser(SysUser user) throws DataAccessException {
		try{
			this.sysUserDao.addUser(user);
		}catch(DataAccessException e){
			logger.error("addUser Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("addUser Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}


	public void deleteUser(SysUser[] user) throws DataAccessException {
		try{
			this.sysUserDao.deleteUser(user);
		}catch(DataAccessException e){
			logger.error("deleteUser Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("deleteUser Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}	
	}


	public List<SysUser> findUser(String sql) throws DataAccessException {
		try{
			return this.sysUserDao.findUser(sql);
		}catch(DataAccessException e){
			logger.error("findUser Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("findUser Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}


	public void updateUser(SysUser user) throws DataAccessException {
		try{
			this.sysUserDao.updateUser(user);
		}catch(DataAccessException e){
			logger.error("updateUser Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("updateUser Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}
//
//
//	public List<SysRole> getAllRole() throws DataAccessException {
//		try{
//			return this.sysUserDao.findRole("select r.rolecode,r.rolename  from sys_role r order by r.rolecode ", new SysRoleMapper());
//		}catch(DataAccessException e){
//			logger.error("Login Method DataAccess Error:",e);
//			throw e;
//		}catch(RuntimeException e1){
//			logger.error("login Method Error:",e1);
//			throw new SystemException("ogin Method Error:",e1);
//		}
//	}
//
	public boolean isUserExist(String loginName){
		try{
			List<SysUser> result =  this.sysUserDao.findUser("select loginname from SysUser where loginname = ?",new String[]{loginName});
			if(result!=null&&result.size()>0){
				return true;
			}else{
				return false;
			}
		}catch(DataAccessException e){
			logger.error("isUserExist Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("isUserExist Method Error:",e1);
			throw new SystemException("isUserExist Method Error:",e1);
		}
	}

	public List<?> pagination(Page page,Object[] params) throws DataAccessException {
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.loginname) from SysUser g order by g.time desc";
				sql = "from SysUser g order by g.time desc";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " g.username like '%"+String.valueOf(params[0])+"%' ";
				}
				
				countSql = "select count(g.loginname) from SysUser g where "+sub+" order by g.time desc";
				sql = "from SysUser g where "+sub+" order by g.time desc";
			}
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}
	
	public SysUser loadUserObj(String loginName){
		SysUser gg = null;
		try{
			gg = this.sysUserDao.loadUserObj(loginName);
		}catch(DataAccessException e){
			throw new SystemException("loadUserObj Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadUserObj Method Error:",e);
		}
		
		return gg;
	}

	public void updafteUserState(SysUser user) throws DataAccessException {
		try{
			SysUser realUser=sysUserDao.loadUserObj(user.getLoginname());
			realUser.setState(user.getState());
			this.updateUser(realUser);
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("updafteUserState Method Error:",e1);
		}
	}

	public List<SysRole> getAllRole() throws DataAccessException {
		return this.sysRoleDAO.findAllRole();
	}
}
