package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

import java.util.Date;

/**
 * Bs09 entity. <AUTHOR> Persistence Tools
 */

public class Bs09 implements java.io.Serializable {

	// Fields

	private String id;
	private String dpcamodel;
	private String dpcavel;
	private String dfvcmodel;
	private String productmodel;
	private String dpcaproductmodelcolor;
	private String dfvcproductdesc;
	private String createuser;
	private String createtime;
	private String senddate;
	private String sendtime;
	private Date datatime;

	// Constructors

	/** default constructor */
	public Bs09() {
	}

	/** minimal constructor */
	public Bs09(String id) {
		this.id = id;
	}

	/** full constructor */
	public Bs09(String id, String dpcamodel, String dpcavel, String dfvcmodel,
			String productmodel, String dpcaproductmodelcolor,
			String dfvcproductdesc, String createuser, String createtime,
			String senddate, String sendtime, Date datatime) {
		this.id = id;
		this.dpcamodel = dpcamodel;
		this.dpcavel = dpcavel;
		this.dfvcmodel = dfvcmodel;
		this.productmodel = productmodel;
		this.dpcaproductmodelcolor = dpcaproductmodelcolor;
		this.dfvcproductdesc = dfvcproductdesc;
		this.createuser = createuser;
		this.createtime = createtime;
		this.senddate = senddate;
		this.sendtime = sendtime;
		this.datatime = datatime;
	}

	// Property accessors

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDpcamodel() {
		return this.dpcamodel;
	}

	public void setDpcamodel(String dpcamodel) {
		this.dpcamodel = dpcamodel;
	}

	public String getDpcavel() {
		return this.dpcavel;
	}

	public void setDpcavel(String dpcavel) {
		this.dpcavel = dpcavel;
	}

	public String getDfvcmodel() {
		return this.dfvcmodel;
	}

	public void setDfvcmodel(String dfvcmodel) {
		this.dfvcmodel = dfvcmodel;
	}

	public String getProductmodel() {
		return this.productmodel;
	}

	public void setProductmodel(String productmodel) {
		this.productmodel = productmodel;
	}

	public String getDpcaproductmodelcolor() {
		return this.dpcaproductmodelcolor;
	}

	public void setDpcaproductmodelcolor(String dpcaproductmodelcolor) {
		this.dpcaproductmodelcolor = dpcaproductmodelcolor;
	}

	public String getDfvcproductdesc() {
		return this.dfvcproductdesc;
	}

	public void setDfvcproductdesc(String dfvcproductdesc) {
		this.dfvcproductdesc = dfvcproductdesc;
	}

	public String getCreateuser() {
		return this.createuser;
	}

	public void setCreateuser(String createuser) {
		this.createuser = createuser;
	}

	public String getCreatetime() {
		return this.createtime;
	}

	public void setCreatetime(String createtime) {
		this.createtime = createtime;
	}

	public String getSenddate() {
		return this.senddate;
	}

	public void setSenddate(String senddate) {
		this.senddate = senddate;
	}

	public String getSendtime() {
		return this.sendtime;
	}

	public void setSendtime(String sendtime) {
		this.sendtime = sendtime;
	}

	public Date getDatatime() {
		return this.datatime;
	}

	public void setDatatime(Date datatime) {
		this.datatime = datatime;
	}

}