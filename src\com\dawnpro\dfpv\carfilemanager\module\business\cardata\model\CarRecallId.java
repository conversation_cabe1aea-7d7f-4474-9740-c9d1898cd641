package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CarRecallId entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarRecallId implements java.io.Serializable {

	// Fields

	private String vin;
	private String vercode;

	// Constructors

	/** default constructor */
	public CarRecallId() {
	}

	/** full constructor */
	public CarRecallId(String vin, String vercode) {
		this.vin = vin;
		this.vercode = vercode;
	}

	// Property accessors

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getVercode() {
		return this.vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

	public boolean equals(Object other) {
		if ((this == other))
			return true;
		if ((other == null))
			return false;
		if (!(other instanceof CarRecallId))
			return false;
		CarRecallId castOther = (CarRecallId) other;

		return ((this.getVin() == castOther.getVin()) || (this.getVin() != null
				&& castOther.getVin() != null && this.getVin().equals(
				castOther.getVin())))
				&& ((this.getVercode() == castOther.getVercode()) || (this
						.getVercode() != null
						&& castOther.getVercode() != null && this.getVercode()
						.equals(castOther.getVercode())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result
				+ (getVin() == null ? 0 : this.getVin().hashCode());
		result = 37 * result
				+ (getVercode() == null ? 0 : this.getVercode().hashCode());
		return result;
	}

}