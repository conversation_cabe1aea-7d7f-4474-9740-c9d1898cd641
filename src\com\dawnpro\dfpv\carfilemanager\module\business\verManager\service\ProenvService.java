package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service;

import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelScjcUploadBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProeHBBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Proenvironment;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProenvironmentId;

public interface ProenvService {

	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
	
	
	
	/**
	 *
	 * @param obj
	 */
	public void addCocVer(Proenvironment obj)throws DataAccessException;
	public void addCocVer(Proenvironment[] objs)throws DataAccessException;
	
	/**
	 *
	 * @param obj
	 */
	public void updateCocVer(Proenvironment obj)throws DataAccessException;
	public void updateCocVer(Proenvironment[] objs)throws DataAccessException;
	
	
	/**
	 *
	 * @param id
	 */
	public void deleteCocVer(Proenvironment[] id)throws DataAccessException;
	
	/**
	 *
	 * @param id
	 * @return
	 */
	public Proenvironment loadCocVer(ProenvironmentId id)throws DataAccessException;
	
	/**
	 *
	 * @param id
	 * @return
	 */
	public Proenvironment findMaxCocVerBySccx(String c1)throws DataAccessException;
	
	/**
	 *
	 * @param hql
	 * @param params
	 * @return
	 */
	public List<Proenvironment> findCocVer(Object[] params)throws DataAccessException;
	
	public List<Proenvironment> findCocVer(String hql)throws DataAccessException;

	public ProeHBBean findprocertificate(String trim, String object);

	public FuelScjcUploadBean findscjcCertificate(String trim, String object);

	public ProeHBBean printProcertificate(String slcx,String vercode, String object);

	public boolean isHBPrint(String trim);

	public String isfindScjc(String trim);


}
