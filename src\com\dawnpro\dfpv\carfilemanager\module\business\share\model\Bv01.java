package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

/**
 * Bv01 generated by MyEclipse Persistence Tools
 */

public class Bv01 implements java.io.Serializable {

	// Fields

	private String id;

	private String dhbm;

	private String bmz;

	private String mark;

	private String senddate;

	private String sendtime;

	private String ggcx;

	private String vercode;

	// Constructors

	/** default constructor */
	public Bv01() {
	}

	/** minimal constructor */
	public Bv01(String ggcx, String vercode) {
		this.ggcx = ggcx;
		this.vercode = vercode;
	}
	
	/** minimal constructor */
	public Bv01(String id, String dhbm, String ggcx, String vercode) {
		this.id = id;
		this.dhbm = dhbm;
		this.ggcx = ggcx;
		this.vercode = vercode;
	}

	/** full constructor */
	public Bv01(String id, String dhbm, String bmz, String mark,
			String senddate, String sendtime, String ggcx, String vercode) {
		this.id = id;
		this.dhbm = dhbm;
		this.bmz = bmz;
		this.mark = mark;
		this.senddate = senddate;
		this.sendtime = sendtime;
		this.ggcx = ggcx;
		this.vercode = vercode;
	}

	// Property accessors

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDhbm() {
		return this.dhbm;
	}

	public void setDhbm(String dhbm) {
		this.dhbm = dhbm;
	}

	public String getBmz() {
		return this.bmz;
	}

	public void setBmz(String bmz) {
		this.bmz = bmz;
	}

	public String getMark() {
		return this.mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

	public String getSenddate() {
		return this.senddate;
	}

	public void setSenddate(String senddate) {
		this.senddate = senddate;
	}

	public String getSendtime() {
		return this.sendtime;
	}

	public void setSendtime(String sendtime) {
		this.sendtime = sendtime;
	}

	public String getGgcx() {
		return this.ggcx;
	}

	public void setGgcx(String ggcx) {
		this.ggcx = ggcx;
	}

	public String getVercode() {
		return this.vercode;
	}

	public void setVercode(String vercode) {
		this.vercode = vercode;
	}

}