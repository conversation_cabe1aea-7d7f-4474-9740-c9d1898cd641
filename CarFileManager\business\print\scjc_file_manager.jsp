<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
	fieldset { padding:0; border:0; margin-top:25px; }
	p {font-family:"宋体";font-size: 10pt;font-weight:bold;}
	input.text { width:12; padding: .4em; }
	input.text1 { width:12; padding: .4em; background:#bcd4ec;}
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align: center; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/jquery-cookie.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	$("#dialog").dialog({
		autoOpen: false,
		bgiframe: false,
		height: 400,
		weight:40,
		modal: false
	});
	
	var printers = window.document.coc.allPrintService();  
	
	$('#vin').bind('keyup',function(event) {   
		if(event.keyCode==13){ 
					
     		window.document.coc.printCOC($("#vin")[0].value);  
   		}   
   		//if($.trim($('#vin')[0].value).length==17){
   			//window.document.coc.printCOC($("#vin")[0].value);
   	   	//}
    });
       
	$("#search").click(function() {
			
	        window.document.coc.printJc($("#vin")[0].value); 
	      		
			
			//window.document.coc.printCOC($("#vin")[0].value); 
		});
	
	$("#printer").click(function() {
		$("#print_info_div").hide();
		var printerList = printers.split("@");
		var cocPrinterObl = $("#coc_printer");
		var photoPrinterObl = $("#photo_printer");
		var hbPrinterObl = $("#hb_printer");
		var hbviPrinterObl = $("#hbvi_printer");
		if($(cocPrinterObl).find("option").length==0){
			$(cocPrinterObl).append("<option value=''>请选择</option>");   
			$(photoPrinterObl).append("<option value=''>请选择</option>"); 
			$(hbPrinterObl).append("<option value=''>请选择</option>");
			$(hbviPrinterObl).append("<option value=''>请选择</option>");
			for(var i=0;i<printerList.length;i++){
				$(cocPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");   
				$(photoPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");  
				$(hbPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");
				$(hbviPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");
			}
		}
		
		var coc_printer = $.cookie("cocprinter");
		var photo_printer = $.cookie("photoprinter");
		var hb_printer = $.cookie("hbprinter");
		var hbvi_printer = $.cookie("hbviprinter");
		if(coc_printer!=null){
			$(cocPrinterObl).val(coc_printer);	
		}
		if(photo_printer!=null){
			$(photoPrinterObl).val(photo_printer);	
		}
		if(hb_printer!=null){
			$(hbPrinterObl).val(hb_printer);	
		}		
		if(hbvi_printer!=null){
			$(hbviPrinterObl).val(hbvi_printer);	
		}		
		
		$("#printer_setting_dialog").dialog('open');	
	});
	
	$("#printer_setting_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 450,
		height:240,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var coc = $("#coc_printer").val();
				var photo = $("#photo_printer").val();
				var hb = $("#hb_printer").val();
				var hbvi = $("#hbvi_printer").val();
				if(coc==""){
					updateTips($(this).find('#validateTips'),'请选择COC证书打印机！');		
					
					return ;
				}
				if(photo==""){
					updateTips($(this).find('#validateTips'),'请选择车型照片打印机！');		
					
					return ;
				}
				if(hb==""){
					updateTips($(this).find('#validateTips'),'请选择环保国五打印机！');		
					
					return ;
				}				
				if(hbvi==""){
					updateTips($(this).find('#validateTips'),'请选择环保国六打印机！');		
					
					return ;
				}				
            
				$.cookie("cocprinter",$("#coc_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				$.cookie("photoprinter",$("#photo_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				$.cookie("hbprinter",$("#hb_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				$.cookie("hbviprinter",$("#hbvi_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				
				$(this).dialog('close');
			}
		},
		close: function() {
			$("#print_info_div").show();
			$("#coc_printer").val("请选择");
			$("#photo_printer").val("请选择");
			$("#hb_printer").val("请选择");
			$("#hbvi_printer").val("请选择");
		}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {},
		close: function() {
			$("#print_info_div").show();
		}
	});
	
}
);

function vinFocus(){
	$("#vin")[0].focus(); 
	$("#vin").addClass("text ui-widget-content ui-corner-all");
}

function showInfo(model,color,engineType,engineModel,produceDate){
	
	$("#vin")[0].select();
}

function readPrinterCookie(){
	var cookie = new Array(4);
	cookie[0] = $.cookie("cocprinter");
	cookie[1] = $.cookie("photoprinter");
	cookie[2] = $.cookie("hbprinter");
	cookie[3] = $.cookie("hbviprinter");
	
	return cookie;
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<!-- 
<p>VIN:<input type="text" id="vin" class="text ui-widget-content ui-corner-all"/>&nbsp;&nbsp;<button id="create-user" class="ui-button ui-state-default ui-corner-all">查询</button></p>
 -->
<div id="print_info_div" align="center"> 
	<jsp:plugin name="coc" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet.class" codebase="." archive = "../../applet/com.dawnpro.dfpv.carfilemanager.print.applet.jar,../../applet/jasperreports-3.6.1.jar,../../applet/commons-logging-1.1.1.jar,../../applet/commons-collections-3.2.jar,../../applet/commons-digester-1.7.jar,../../applet/com-jaspersoft-ireport.jar,../../applet/Qrcode_encoder.jar,../../applet/iText-2.1.0.jar,../../applet/iTextAsian.jar
	,../../applet/barcode4j.jar,../../applet/groovy-all-1.5.5.jar"  
	iepluginurl="http://********/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="150" width="900">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="XCJC"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
<div align="center">
<table width="820" height="63" border="0">
	<tr>
	<td width="200" height="19" align="left" style="font-family:arial;font-size: 24pt; " >VIN:</td>
	<td colspan="3" align="left">
	<table cellpadding="0" cellspacing="0">
		<tr>
			<td><input id="vin" type="text" width="100" class="text ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; " /></td>
			<td>&nbsp;&nbsp;<button id="search" class="ui-button ui-state-default ui-corner-all">查询</button></td>
			<!--  
			<td>&nbsp;&nbsp;<button id="printer" class="ui-button ui-state-default ui-corner-all">打印机设置</button></td>
			-->
		</tr>
	</table>
	</tr>
	
</table>
</div>

<div id="printer_setting_dialog" style="display:none" title="打印机设置">
	<p id="validateTips"></p>
	<fieldset>
		<table id="one" width="100%" border="0">
	  		<tr>
	  			<td><label>COC证书打印机</label></td>
				<td><select id="coc_printer"></select></td>
	  		</tr>
	  		<tr>
	  			<td><label>车型照片打印机</label></td>
				<td><select id="photo_printer"></select></td>
	  		</tr>
	  		<tr>
	  			<td><label>环保国五打印机</label></td>
				<td><select id="hb_printer"></select></td>
	  		</tr>	  		
	  		<tr>
	  			<td><label>环保国六打印机</label></td>
				<td><select id="hbvi_printer"></select></td>
	  		</tr>	  		
		</Table>
	</fieldset>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>