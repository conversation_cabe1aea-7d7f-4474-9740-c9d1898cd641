package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service;

import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId;

public interface CocVerService {

	public List<?> pagination(Page page,Object[] params) throws DataAccessException;
	
	/**
	 *
	 * @param obj
	 */
	public void addCocVer(Cartypetemplate obj)throws DataAccessException;
	public void addCocVer(Cartypetemplate[] objs)throws DataAccessException;
	
	/**
	 *
	 * @param obj
	 */
	public void updateCocVer(Cartypetemplate obj)throws DataAccessException;
	public void updateCocVer(Cartypetemplate[] objs)throws DataAccessException;
	
	
	/**
	 *
	 * @param id
	 */
	public void deleteCocVer(Cartypetemplate[] id)throws DataAccessException;
	
	/**
	 *
	 * @param id
	 * @return
	 */
	public Cartypetemplate loadCocVer(CartypetemplateId id)throws DataAccessException;
	
	/**
	 *
	 * @param id
	 * @return
	 */
	public Cartypetemplate findMaxCocVerBySccx(String c1)throws DataAccessException;
	
	/**
	 *
	 * @param hql
	 * @param params
	 * @return
	 */
	public List<Cartypetemplate> findCocVer(Object[] params)throws DataAccessException;
	
	public List<Cartypetemplate> findCocVer(String hql)throws DataAccessException;
}
