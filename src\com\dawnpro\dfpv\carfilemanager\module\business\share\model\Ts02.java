package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

/**
 * Ts02 generated by MyEclipse Persistence Tools
 */

public class Ts02 implements java.io.Serializable {

	// Fields

	private Ts02Id id;

	private String vin;

	private String prepareEndTime;

	// Constructors

	/** default constructor */
	public Ts02() {
	}

	/** minimal constructor */
	public Ts02(Ts02Id id) {
		this.id = id;
	}

	/** full constructor */
	public Ts02(Ts02Id id, String vin, String prepareEndTime) {
		this.id = id;
		this.vin = vin;
		this.prepareEndTime = prepareEndTime;
	}

	// Property accessors

	public Ts02Id getId() {
		return this.id;
	}

	public void setId(Ts02Id id) {
		this.id = id;
	}

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getPrepareEndTime() {
		return this.prepareEndTime;
	}

	public void setPrepareEndTime(String prepareEndTime) {
		this.prepareEndTime = prepareEndTime;
	}

}