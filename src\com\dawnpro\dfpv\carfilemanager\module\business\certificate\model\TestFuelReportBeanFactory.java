package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRAbstractBeanDataSourceProvider;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class TestFuelReportBeanFactory extends JRAbstractBeanDataSourceProvider{
	public TestFuelReportBeanFactory() {
		super(TestBean.class);
		// TODO Auto-generated constructor stub
	}

	public static Collection getBeans(){
		List<FuelChartBean>  res = new ArrayList<FuelChartBean>();
		String type1 = "CO2";
		String type2 = "GK";
		FuelChartBean bean = new FuelChartBean("2009-2",type1,"1470");
		res.add(bean);
		
		bean = new FuelChartBean("2009-2",type2,"1870");
		res.add(bean);
		
		bean = new FuelChartBean("2009-3",type1,"2870");
		res.add(bean);
		
		bean = new FuelChartBean("2009-3",type2,"2070");
		res.add(bean);
		
		bean = new FuelChartBean("2009-4",type1,"2270");
		res.add(bean);
		
		bean = new FuelChartBean("2009-4",type2,"2670");
		res.add(bean);
		
		bean = new FuelChartBean("2009-5",type1,"2870");
		res.add(bean);
		
		bean = new FuelChartBean("2009-5",type2,"2070");
		res.add(bean);
		
		bean = new FuelChartBean("2009-6",type1,"2270");
		res.add(bean);
		
		bean = new FuelChartBean("2009-6",type2,"2670");
		res.add(bean);
		
		
		
		return res;
	}

	public JRDataSource create(JasperReport arg0) throws JRException {
		// TODO Auto-generated method stub
		return new JRBeanCollectionDataSource(getBeans());
	}

	public void dispose(JRDataSource arg0) throws JRException {
		// TODO Auto-generated method stub
		
	}
}
