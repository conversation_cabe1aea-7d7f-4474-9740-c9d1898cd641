<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="COCA4_NGCNG_23H"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="555"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.zoom" value="1.5" />
	<property name="ireport.x" value="20" />
	<property name="ireport.y" value="0" />
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />


	<field name="barCodeImageUrl" class="java.lang.String">
		<fieldDescription><![CDATA[barCodeImageUrl]]></fieldDescription>
	</field>
	<field name="c159" class="java.lang.String">
		<fieldDescription><![CDATA[c159]]></fieldDescription>
	</field>
	<field name="c158" class="java.lang.String">
		<fieldDescription><![CDATA[c158]]></fieldDescription>
	</field>
	<field name="c194" class="java.lang.String">
		<fieldDescription><![CDATA[c194]]></fieldDescription>
	</field>
	<field name="c191" class="java.lang.String">
		<fieldDescription><![CDATA[c191]]></fieldDescription>
	</field>
	<field name="c166" class="java.lang.String">
		<fieldDescription><![CDATA[c166]]></fieldDescription>
	</field>
	<field name="c167" class="java.lang.String">
		<fieldDescription><![CDATA[c167]]></fieldDescription>
	</field>
	<field name="c168" class="java.lang.String">
		<fieldDescription><![CDATA[c168]]></fieldDescription>
	</field>
	<field name="c169" class="java.lang.String">
		<fieldDescription><![CDATA[c169]]></fieldDescription>
	</field>
	<field name="c195" class="java.lang.String">
		<fieldDescription><![CDATA[c195]]></fieldDescription>
	</field>
	<field name="c196" class="java.lang.String">
		<fieldDescription><![CDATA[c196]]></fieldDescription>
	</field>
	<field name="c179" class="java.lang.String">
		<fieldDescription><![CDATA[c179]]></fieldDescription>
	</field>
	<field name="c184" class="java.lang.String">
		<fieldDescription><![CDATA[c184]]></fieldDescription>
	</field>
	<field name="c180" class="java.lang.String">
		<fieldDescription><![CDATA[c180]]></fieldDescription>
	</field>
	<field name="c185" class="java.lang.String">
		<fieldDescription><![CDATA[c185]]></fieldDescription>
	</field>
	<field name="c181" class="java.lang.String">
		<fieldDescription><![CDATA[c181]]></fieldDescription>
	</field>
	<field name="c186" class="java.lang.String">
		<fieldDescription><![CDATA[c186]]></fieldDescription>
	</field>
	<field name="c192" class="java.lang.String">
		<fieldDescription><![CDATA[c192]]></fieldDescription>
	</field>
	<field name="c193" class="java.lang.String">
		<fieldDescription><![CDATA[c193]]></fieldDescription>
	</field>
	<field name="c182" class="java.lang.String">
		<fieldDescription><![CDATA[c182]]></fieldDescription>
	</field>
	<field name="c183" class="java.lang.String">
		<fieldDescription><![CDATA[c183]]></fieldDescription>
	</field>
	<field name="c187" class="java.lang.String">
		<fieldDescription><![CDATA[c187]]></fieldDescription>
	</field>
	<field name="c188" class="java.lang.String">
		<fieldDescription><![CDATA[c188]]></fieldDescription>
	</field>
	<field name="c1" class="java.lang.String">
		<fieldDescription><![CDATA[c1]]></fieldDescription>
	</field>
	<field name="c10" class="java.lang.String">
		<fieldDescription><![CDATA[c10]]></fieldDescription>
	</field>
	<field name="c100" class="java.lang.String">
		<fieldDescription><![CDATA[c100]]></fieldDescription>
	</field>
	<field name="c101" class="java.lang.String">
		<fieldDescription><![CDATA[c101]]></fieldDescription>
	</field>
	<field name="c102" class="java.lang.String">
		<fieldDescription><![CDATA[c102]]></fieldDescription>
	</field>
	<field name="c103" class="java.lang.String">
		<fieldDescription><![CDATA[c103]]></fieldDescription>
	</field>
	<field name="c104" class="java.lang.String">
		<fieldDescription><![CDATA[c104]]></fieldDescription>
	</field>
	<field name="c105" class="java.lang.String">
		<fieldDescription><![CDATA[c105]]></fieldDescription>
	</field>
	<field name="c106" class="java.lang.String">
		<fieldDescription><![CDATA[c106]]></fieldDescription>
	</field>
	<field name="c107" class="java.lang.String">
		<fieldDescription><![CDATA[c107]]></fieldDescription>
	</field>
	<field name="c108" class="java.lang.String">
		<fieldDescription><![CDATA[c108]]></fieldDescription>
	</field>
	<field name="c109" class="java.lang.String">
		<fieldDescription><![CDATA[c109]]></fieldDescription>
	</field>
	<field name="c11" class="java.lang.String">
		<fieldDescription><![CDATA[c11]]></fieldDescription>
	</field>
	<field name="c110" class="java.lang.String">
		<fieldDescription><![CDATA[c110]]></fieldDescription>
	</field>
	<field name="c112" class="java.lang.String">
		<fieldDescription><![CDATA[c112]]></fieldDescription>
	</field>
	<field name="c113" class="java.lang.String">
		<fieldDescription><![CDATA[c113]]></fieldDescription>
	</field>
	<field name="c114" class="java.lang.String">
		<fieldDescription><![CDATA[c114]]></fieldDescription>
	</field>
	<field name="c115" class="java.lang.String">
		<fieldDescription><![CDATA[c115]]></fieldDescription>
	</field>
	<field name="c116" class="java.lang.String">
		<fieldDescription><![CDATA[c116]]></fieldDescription>
	</field>
	<field name="c117" class="java.lang.String">
		<fieldDescription><![CDATA[c117]]></fieldDescription>
	</field>
	<field name="c118" class="java.lang.String">
		<fieldDescription><![CDATA[c118]]></fieldDescription>
	</field>
	<field name="c119" class="java.lang.String">
		<fieldDescription><![CDATA[c119]]></fieldDescription>
	</field>
	<field name="c12" class="java.lang.String">
		<fieldDescription><![CDATA[c12]]></fieldDescription>
	</field>
	<field name="c120" class="java.lang.String">
		<fieldDescription><![CDATA[c120]]></fieldDescription>
	</field>
	<field name="c121" class="java.lang.String">
		<fieldDescription><![CDATA[c121]]></fieldDescription>
	</field>
	<field name="c13" class="java.lang.String">
		<fieldDescription><![CDATA[c13]]></fieldDescription>
	</field>
	<field name="c14" class="java.lang.String">
		<fieldDescription><![CDATA[c14]]></fieldDescription>
	</field>
	<field name="c15" class="java.lang.String">
		<fieldDescription><![CDATA[c15]]></fieldDescription>
	</field>
	<field name="c16" class="java.lang.String">
		<fieldDescription><![CDATA[c16]]></fieldDescription>
	</field>
	<field name="c17" class="java.lang.String">
		<fieldDescription><![CDATA[c17]]></fieldDescription>
	</field>
	<field name="c18" class="java.lang.String">
		<fieldDescription><![CDATA[c18]]></fieldDescription>
	</field>
	<field name="c19" class="java.lang.String">
		<fieldDescription><![CDATA[c19]]></fieldDescription>
	</field>
	<field name="c2" class="java.lang.String">
		<fieldDescription><![CDATA[c2]]></fieldDescription>
	</field>
	<field name="c20" class="java.lang.String">
		<fieldDescription><![CDATA[c20]]></fieldDescription>
	</field>
	<field name="c21" class="java.lang.String">
		<fieldDescription><![CDATA[c21]]></fieldDescription>
	</field>
	<field name="c22" class="java.lang.String">
		<fieldDescription><![CDATA[c22]]></fieldDescription>
	</field>
	<field name="c23" class="java.lang.String">
		<fieldDescription><![CDATA[c23]]></fieldDescription>
	</field>
	<field name="c24" class="java.lang.String">
		<fieldDescription><![CDATA[c24]]></fieldDescription>
	</field>
	<field name="c25" class="java.lang.String">
		<fieldDescription><![CDATA[c25]]></fieldDescription>
	</field>
	<field name="c26" class="java.lang.String">
		<fieldDescription><![CDATA[c26]]></fieldDescription>
	</field>
	<field name="c27" class="java.lang.String">
		<fieldDescription><![CDATA[c27]]></fieldDescription>
	</field>
	<field name="c28" class="java.lang.String">
		<fieldDescription><![CDATA[c28]]></fieldDescription>
	</field>
	<field name="c29" class="java.lang.String">
		<fieldDescription><![CDATA[c29]]></fieldDescription>
	</field>
	<field name="c3" class="java.lang.String">
		<fieldDescription><![CDATA[c3]]></fieldDescription>
	</field>
	<field name="c30" class="java.lang.String">
		<fieldDescription><![CDATA[c30]]></fieldDescription>
	</field>
	<field name="c31" class="java.lang.String">
		<fieldDescription><![CDATA[c31]]></fieldDescription>
	</field>
	<field name="c32" class="java.lang.String">
		<fieldDescription><![CDATA[c32]]></fieldDescription>
	</field>
	<field name="c33" class="java.lang.String">
		<fieldDescription><![CDATA[c33]]></fieldDescription>
	</field>
	<field name="c34" class="java.lang.String">
		<fieldDescription><![CDATA[c34]]></fieldDescription>
	</field>
	<field name="c35" class="java.lang.String">
		<fieldDescription><![CDATA[c35]]></fieldDescription>
	</field>
	<field name="c36" class="java.lang.String">
		<fieldDescription><![CDATA[c36]]></fieldDescription>
	</field>
	<field name="c37" class="java.lang.String">
		<fieldDescription><![CDATA[c37]]></fieldDescription>
	</field>
	<field name="c38" class="java.lang.String">
		<fieldDescription><![CDATA[c38]]></fieldDescription>
	</field>
	<field name="c39" class="java.lang.String">
		<fieldDescription><![CDATA[c39]]></fieldDescription>
	</field>
	<field name="c4" class="java.lang.String">
		<fieldDescription><![CDATA[c4]]></fieldDescription>
	</field>
	<field name="c40" class="java.lang.String">
		<fieldDescription><![CDATA[c40]]></fieldDescription>
	</field>
	<field name="c41" class="java.lang.String">
		<fieldDescription><![CDATA[c41]]></fieldDescription>
	</field>
	<field name="c42" class="java.lang.String">
		<fieldDescription><![CDATA[c42]]></fieldDescription>
	</field>
	<field name="c43" class="java.lang.String">
		<fieldDescription><![CDATA[c43]]></fieldDescription>
	</field>
	<field name="c44" class="java.lang.String">
		<fieldDescription><![CDATA[c44]]></fieldDescription>
	</field>
	<field name="c45" class="java.lang.String">
		<fieldDescription><![CDATA[c45]]></fieldDescription>
	</field>
	<field name="c46" class="java.lang.String">
		<fieldDescription><![CDATA[c46]]></fieldDescription>
	</field>
	<field name="c47" class="java.lang.String">
		<fieldDescription><![CDATA[c47]]></fieldDescription>
	</field>
	<field name="c48" class="java.lang.String">
		<fieldDescription><![CDATA[c48]]></fieldDescription>
	</field>
	<field name="c49" class="java.lang.String">
		<fieldDescription><![CDATA[c49]]></fieldDescription>
	</field>
	<field name="c5" class="java.lang.String">
		<fieldDescription><![CDATA[c5]]></fieldDescription>
	</field>
	<field name="c50" class="java.lang.String">
		<fieldDescription><![CDATA[c50]]></fieldDescription>
	</field>
	<field name="c51" class="java.lang.String">
		<fieldDescription><![CDATA[c51]]></fieldDescription>
	</field>
	<field name="c52" class="java.lang.String">
		<fieldDescription><![CDATA[c52]]></fieldDescription>
	</field>
	<field name="c53" class="java.lang.String">
		<fieldDescription><![CDATA[c53]]></fieldDescription>
	</field>
	<field name="c54" class="java.lang.String">
		<fieldDescription><![CDATA[c54]]></fieldDescription>
	</field>
	<field name="c55" class="java.lang.String">
		<fieldDescription><![CDATA[c55]]></fieldDescription>
	</field>
	<field name="c56" class="java.lang.String">
		<fieldDescription><![CDATA[c56]]></fieldDescription>
	</field>
	<field name="c57" class="java.lang.String">
		<fieldDescription><![CDATA[c57]]></fieldDescription>
	</field>
	<field name="c58" class="java.lang.String">
		<fieldDescription><![CDATA[c58]]></fieldDescription>
	</field>
	<field name="c59" class="java.lang.String">
		<fieldDescription><![CDATA[c59]]></fieldDescription>
	</field>
	<field name="c6" class="java.lang.String">
		<fieldDescription><![CDATA[c6]]></fieldDescription>
	</field>
	<field name="c60" class="java.lang.String">
		<fieldDescription><![CDATA[c60]]></fieldDescription>
	</field>
	<field name="c61" class="java.lang.String">
		<fieldDescription><![CDATA[c61]]></fieldDescription>
	</field>
	<field name="c62" class="java.lang.String">
		<fieldDescription><![CDATA[c62]]></fieldDescription>
	</field>
	<field name="c63" class="java.lang.String">
		<fieldDescription><![CDATA[c63]]></fieldDescription>
	</field>
	<field name="c64" class="java.lang.String">
		<fieldDescription><![CDATA[c64]]></fieldDescription>
	</field>
	<field name="c65" class="java.lang.String">
		<fieldDescription><![CDATA[c65]]></fieldDescription>
	</field>
	<field name="c66" class="java.lang.String">
		<fieldDescription><![CDATA[c66]]></fieldDescription>
	</field>
	<field name="c67" class="java.lang.String">
		<fieldDescription><![CDATA[c67]]></fieldDescription>
	</field>
	<field name="c68" class="java.lang.String">
		<fieldDescription><![CDATA[c68]]></fieldDescription>
	</field>
	<field name="c69" class="java.lang.String">
		<fieldDescription><![CDATA[c69]]></fieldDescription>
	</field>
	<field name="c7" class="java.lang.String">
		<fieldDescription><![CDATA[c7]]></fieldDescription>
	</field>
	<field name="c70" class="java.lang.String">
		<fieldDescription><![CDATA[c70]]></fieldDescription>
	</field>
	<field name="c71" class="java.lang.String">
		<fieldDescription><![CDATA[c71]]></fieldDescription>
	</field>
	<field name="c72" class="java.lang.String">
		<fieldDescription><![CDATA[c72]]></fieldDescription>
	</field>
	<field name="c73" class="java.lang.String">
		<fieldDescription><![CDATA[c73]]></fieldDescription>
	</field>
	<field name="c74" class="java.lang.String">
		<fieldDescription><![CDATA[c74]]></fieldDescription>
	</field>
	<field name="c75" class="java.lang.String">
		<fieldDescription><![CDATA[c75]]></fieldDescription>
	</field>
	<field name="c76" class="java.lang.String">
		<fieldDescription><![CDATA[c76]]></fieldDescription>
	</field>
	<field name="c77" class="java.lang.String">
		<fieldDescription><![CDATA[c77]]></fieldDescription>
	</field>
	<field name="c78" class="java.lang.String">
		<fieldDescription><![CDATA[c78]]></fieldDescription>
	</field>
	<field name="c79" class="java.lang.String">
		<fieldDescription><![CDATA[c79]]></fieldDescription>
	</field>
	<field name="c8" class="java.lang.String">
		<fieldDescription><![CDATA[c8]]></fieldDescription>
	</field>
	<field name="c80" class="java.lang.String">
		<fieldDescription><![CDATA[c80]]></fieldDescription>
	</field>
	<field name="c81" class="java.lang.String">
		<fieldDescription><![CDATA[c81]]></fieldDescription>
	</field>
	<field name="c82" class="java.lang.String">
		<fieldDescription><![CDATA[c82]]></fieldDescription>
	</field>
	<field name="c83" class="java.lang.String">
		<fieldDescription><![CDATA[c83]]></fieldDescription>
	</field>
	<field name="c84" class="java.lang.String">
		<fieldDescription><![CDATA[c84]]></fieldDescription>
	</field>
	<field name="c85" class="java.lang.String">
		<fieldDescription><![CDATA[c85]]></fieldDescription>
	</field>
	<field name="c86" class="java.lang.String">
		<fieldDescription><![CDATA[c86]]></fieldDescription>
	</field>
	<field name="c87" class="java.lang.String">
		<fieldDescription><![CDATA[c87]]></fieldDescription>
	</field>
	<field name="c88" class="java.lang.String">
		<fieldDescription><![CDATA[c88]]></fieldDescription>
	</field>
	<field name="c89" class="java.lang.String">
		<fieldDescription><![CDATA[c89]]></fieldDescription>
	</field>
	<field name="c9" class="java.lang.String">
		<fieldDescription><![CDATA[c9]]></fieldDescription>
	</field>
	<field name="c90" class="java.lang.String">
		<fieldDescription><![CDATA[c90]]></fieldDescription>
	</field>
	<field name="c91" class="java.lang.String">
		<fieldDescription><![CDATA[c91]]></fieldDescription>
	</field>
	<field name="c92" class="java.lang.String">
		<fieldDescription><![CDATA[c92]]></fieldDescription>
	</field>
	<field name="c93" class="java.lang.String">
		<fieldDescription><![CDATA[c93]]></fieldDescription>
	</field>
	<field name="c94" class="java.lang.String">
		<fieldDescription><![CDATA[c94]]></fieldDescription>
	</field>
	<field name="c95" class="java.lang.String">
		<fieldDescription><![CDATA[c95]]></fieldDescription>
	</field>
	<field name="c96" class="java.lang.String">
		<fieldDescription><![CDATA[c96]]></fieldDescription>
	</field>
	<field name="c97" class="java.lang.String">
		<fieldDescription><![CDATA[c97]]></fieldDescription>
	</field>
	<field name="c98" class="java.lang.String">
		<fieldDescription><![CDATA[c98]]></fieldDescription>
	</field>
	<field name="c99" class="java.lang.String">
		<fieldDescription><![CDATA[c99]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="cocNum" class="java.lang.String">
		<fieldDescription><![CDATA[cocNum]]></fieldDescription>
	</field>
	<field name="color" class="java.lang.String">
		<fieldDescription><![CDATA[color]]></fieldDescription>
	</field>
	<field name="engineNo" class="java.lang.String">
		<fieldDescription><![CDATA[engineNo]]></fieldDescription>
	</field>
	<field name="engineType" class="java.lang.String">
		<fieldDescription><![CDATA[engineType]]></fieldDescription>
	</field>
	<field name="imageInputStream" class="java.io.InputStream">
		<fieldDescription><![CDATA[imageInputStream]]></fieldDescription>
	</field>
	<field name="prodDate" class="java.lang.String">
		<fieldDescription><![CDATA[prodDate]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="validate" class="java.lang.Boolean">
		<fieldDescription><![CDATA[validate]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="c131" class="java.lang.String"/>
	<field name="c137" class="java.lang.String"/>
	<field name="c133" class="java.lang.String"/>
	<field name="c136" class="java.lang.String"/>
	<field name="c138" class="java.lang.String"/>
	<field name="c139" class="java.lang.String"/>
	<field name="c141" class="java.lang.String"/>
	<field name="c150" class="java.lang.String"/>
	<field name="c151" class="java.lang.String"/>
	<field name="c152" class="java.lang.String"/>
	<field name="c153" class="java.lang.String"/>
	<field name="c148" class="java.lang.String"/>
	<field name="c149" class="java.lang.String"/>
	<field name="c146" class="java.lang.String"/>
	<field name="c143" class="java.lang.String"/>
	<field name="c142" class="java.lang.String"/>
	<field name="c144" class="java.lang.String"/>
	<field name="c145" class="java.lang.String"/>
	<field name="c147" class="java.lang.String"/>
	<field name="c155" class="java.lang.String"/>
	<field name="c156" class="java.lang.String"/>
	<field name="c157" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="3"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="786"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="141"
						y="1"
						width="270"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="16" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[强制性产品认证车辆一致性证书]]></text>
				</staticText>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="23"
						width="98"
						height="98"
						key="image"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$F{barCodeImageUrl}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="18"
						y="23"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement verticalAlignment="Top">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆一致性证书编号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" pattern="" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="23"
						width="307"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H" isStrikeThrough="false" />
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c2}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="65"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆生产企业名称]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="51"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 最终阶段车辆制造国]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="51"
						width="307"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c4}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="121"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Left">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 新能源车]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="121"
						width="161"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c150}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="135"
						width="161"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{vin}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="135"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆识别代号]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="107"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 生产者（制造商）名称]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="107"
						width="307"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c10}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="135"
						width="146"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆识别代号是否使用车型年份]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="135"
						width="98"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c158}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="65"
						width="307"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="79"
						width="307"
						height="14"
						key="textField"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c157}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="79"
						width="111"
						height="14"
						key="staticText"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆生产企业地址]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="37"
						width="111"
						height="14"
						key="staticText-1"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 基本车辆制造国]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="37"
						width="307"
						height="14"
						key="textField-1"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c194}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="93"
						width="111"
						height="14"
						key="staticText-2"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 认证委托人名称]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="93"
						width="307"
						height="14"
						key="textField-2"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c191}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="121"
						width="146"
						height="14"
						key="staticText-3"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆制造日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="121"
						width="98"
						height="14"
						key="textField-3"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{prodDate}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="149"
						width="111"
						height="14"
						key="staticText-4"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆识别代号打刻位置]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="149"
						width="405"
						height="14"
						key="textField-4"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c13}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="163"
						width="111"
						height="14"
						key="staticText-5"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 产品标牌的位置]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="163"
						width="405"
						height="14"
						key="textField-5"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c12}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="177"
						width="111"
						height="14"
						key="staticText-6"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车型名称]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="177"
						width="161"
						height="14"
						key="textField-6"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c7}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="177"
						width="146"
						height="14"
						key="staticText-7"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆型号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="177"
						width="98"
						height="14"
						key="textField-7"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c6}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="191"
						width="111"
						height="14"
						key="staticText-8"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆注册类型]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="191"
						width="161"
						height="14"
						key="textField-8"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c151}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="191"
						width="146"
						height="14"
						key="staticText-9"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆类别]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="191"
						width="98"
						height="14"
						key="textField-9"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c97}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="205"
						width="111"
						height="14"
						key="staticText-10"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆品牌（中文/英文）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="205"
						width="161"
						height="14"
						key="textField-10"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c95}+"/"+$F{c96}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="205"
						width="146"
						height="14"
						key="staticText-11"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆颜色]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="205"
						width="98"
						height="14"
						key="textField-11"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{color}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="219"
						width="111"
						height="14"
						key="staticText-12"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车辆型式]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="219"
						width="161"
						height="14"
						key="textField-12"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c152}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="219"
						width="146"
						height="14"
						key="staticText-13"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 驱动型式]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="219"
						width="98"
						height="14"
						key="textField-13"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c159}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="233"
						width="272"
						height="14"
						key="staticText-14"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 最终阶段车辆CCC证书编号（版本号）/签发日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="233"
						width="244"
						height="14"
						key="textField-14"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c15}+"/"+$F{c16}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="247"
						width="405"
						height="14"
						key="textField-15"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{engineNo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="247"
						width="111"
						height="14"
						key="staticText-15"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 发动机编号]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="275"
						width="111"
						height="14"
						key="staticText-16"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 发动机型号/燃料种类]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="275"
						width="161"
						height="14"
						key="textField-16"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{engineType}+"/"+$F{c48}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="275"
						width="146"
						height="14"
						key="staticText-17"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 直接喷射]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="275"
						width="98"
						height="14"
						key="textField-17"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c45}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="261"
						width="244"
						height="14"
						key="textField-18"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c14}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="261"
						width="272"
						height="14"
						key="staticText-18"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 发动机编号在发动机上的打刻位置]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="289"
						width="111"
						height="14"
						key="staticText-19"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 气缸数量和排列]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="289"
						width="161"
						height="14"
						key="textField-19"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c115}+"/"+$F{c46}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="289"
						width="146"
						height="14"
						key="staticText-20"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 排量（ml）/最大净功率（kW）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="289"
						width="98"
						height="14"
						key="textField-20"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c47}+"/"+$F{c49}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="303"
						width="111"
						height="14"
						key="staticText-21"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车轴数量]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="303"
						width="161"
						height="14"
						key="textField-21"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c17}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="303"
						width="146"
						height="14"
						key="staticText-22"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车轮数量]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="303"
						width="98"
						height="14"
						key="textField-22"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c18}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="317"
						width="111"
						height="14"
						key="staticText-23"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 驱动轴位置]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="317"
						width="161"
						height="14"
						key="textField-23"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c19}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="317"
						width="146"
						height="14"
						key="staticText-24"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 前悬（mm）/后悬（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="317"
						width="98"
						height="14"
						key="textField-24"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c27}+"/"+$F{c28}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="331"
						width="111"
						height="14"
						key="staticText-25"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 轮距（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="331"
						width="161"
						height="14"
						key="textField-25"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c20}+"/"+$F{c21}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="331"
						width="146"
						height="14"
						key="staticText-26"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 外廓尺寸（长/宽/高）（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="331"
						width="98"
						height="14"
						key="textField-26"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c24}+"/"+$F{c25}+"/"+$F{c26}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="345"
						width="111"
						height="14"
						key="staticText-27"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 轴距（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="345"
						width="161"
						height="14"
						key="textField-27"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c22}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="345"
						width="146"
						height="14"
						key="staticText-28"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 货箱内部尺寸（长/宽/高）（mm）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="345"
						width="98"
						height="14"
						key="textField-28"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="9" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c166}+"/"+$F{c167}+"/"+$F{c168}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="359"
						width="111"
						height="14"
						key="staticText-29"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 整备质量（kg）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="359"
						width="161"
						height="14"
						key="textField-29"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c155}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="359"
						width="146"
						height="14"
						key="staticText-30"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 接近角/离去角（°）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="359"
						width="98"
						height="14"
						key="textField-30"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c29}+"/"+$F{c30}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="373"
						width="111"
						height="14"
						key="staticText-31"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 最高设计车速（km/h）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="373"
						width="161"
						height="14"
						key="textField-31"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c64}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="373"
						width="146"
						height="14"
						key="staticText-32"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 车门数量和结构]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="373"
						width="98"
						height="14"
						key="textField-32"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c61}+"/"+$F{c116}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="387"
						width="111"
						height="14"
						key="staticText-33"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 最大允许总质量（kg）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="387"
						width="161"
						height="14"
						key="textField-33"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c32}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="387"
						width="146"
						height="14"
						key="staticText-34"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 最大允许总质量下的轴荷（kg）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="387"
						width="98"
						height="14"
						key="textField-34"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c33}+"/"+$F{c34}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="401"
						width="111"
						height="14"
						key="staticText-35"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 离合器型式]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="401"
						width="161"
						height="14"
						key="textField-35"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c51}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="401"
						width="146"
						height="14"
						key="staticText-36"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 变速器型式]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="401"
						width="98"
						height="14"
						key="textField-36"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c52}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="415"
						width="405"
						height="14"
						key="textField-37"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" 第1轴:"+$F{c55}+" 第2轴:"+$F{c56}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="415"
						width="111"
						height="14"
						key="staticText-37"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 轮胎规格型号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="429"
						width="405"
						height="14"
						key="textField-38"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c58}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="429"
						width="111"
						height="14"
						key="staticText-38"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 制动装置简要说明]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="443"
						width="111"
						height="14"
						key="staticText-39"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 是否带防抱死系统]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="443"
						width="161"
						height="14"
						key="textField-39"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c153}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="443"
						width="146"
						height="14"
						key="staticText-40"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 额定载客人数]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="443"
						width="98"
						height="14"
						key="textField-40"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c62}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="457"
						width="111"
						height="14"
						key="staticText-41"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 钢板弹簧片数]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="129"
						y="457"
						width="161"
						height="14"
						key="textField-41"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c147}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="457"
						width="146"
						height="14"
						key="staticText-42"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆是否适合拖挂/准牵引总质量]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="436"
						y="457"
						width="98"
						height="14"
						key="textField-42"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c169}+"/"+$F{c195}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="471"
						width="43"
						height="14"
						key="staticText-43"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 速比]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="61"
						y="471"
						width="375"
						height="14"
						key="textField-43"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c53}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="436"
						y="471"
						width="40"
						height="14"
						key="staticText-44"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[主传动比]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="476"
						y="471"
						width="58"
						height="14"
						key="textField-44"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="9" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c54}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="485"
						width="244"
						height="14"
						key="textField-45"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c65}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="61"
						y="485"
						width="229"
						height="14"
						key="staticText-45"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ CCC认证所依据的标准号及实施阶段]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="485"
						width="43"
						height="42"
						key="staticText-46"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[声级]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="499"
						width="244"
						height="14"
						key="textField-46"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c66}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="61"
						y="499"
						width="229"
						height="14"
						key="staticText-47"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 定置噪声（dB（A））]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="513"
						width="244"
						height="14"
						key="textField-47"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c68}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="61"
						y="513"
						width="229"
						height="14"
						key="staticText-49"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 加速行驶车外噪声（dB（A））]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="527"
						width="244"
						height="14"
						key="textField-48"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c69}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="61"
						y="527"
						width="229"
						height="14"
						key="staticText-50"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ CCC认证所依据的标准号及实施阶段]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="18"
						y="527"
						width="43"
						height="42"
						key="staticText-51"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 排气污染 物]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="61"
						y="541"
						width="68"
						height="28"
						key="staticText-52"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="9" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 试验用液体燃料(mg/km)：CNG]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="129"
						y="541"
						width="35"
						height="14"
						key="staticText-53"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ CO：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="164"
						y="541"
						width="47"
						height="14"
						key="textField-50"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c71}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="211"
						y="541"
						width="32"
						height="14"
						key="staticText-54"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ THC：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="243"
						y="541"
						width="47"
						height="14"
						key="textField-51"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c72}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="290"
						y="541"
						width="32"
						height="14"
						key="staticText-55"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ NMHC:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="322"
						y="541"
						width="41"
						height="14"
						key="textField-52"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c196}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="363"
						y="541"
						width="32"
						height="14"
						key="staticText-56"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ NOx：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="395"
						y="541"
						width="41"
						height="14"
						key="textField-53"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c73}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="436"
						y="541"
						width="32"
						height="14"
						key="staticText-57"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ N O：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="468"
						y="541"
						width="66"
						height="14"
						key="textField-54"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c149}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="129"
						y="555"
						width="35"
						height="14"
						key="staticText-58"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ PM：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="164"
						y="555"
						width="126"
						height="14"
						key="textField-55"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c76}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="447"
						y="544"
						width="7"
						height="10"
						key="staticText-59"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="仿宋" size="7" isBold="false"/>
					</textElement>
				<text><![CDATA[2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="555"
						width="32"
						height="14"
						key="staticText-60"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.0"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ PN：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="322"
						y="555"
						width="212"
						height="14"
						key="textField-56"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{c148}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="569"
						width="111"
						height="98"
						key="staticText-61"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO2排放量/燃料消耗量]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="129"
						y="569"
						width="161"
						height="14"
						key="staticText-62"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ CCC认证所依据的标准号]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="290"
						y="569"
						width="244"
						height="14"
						key="textField-57"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c84}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="129"
						y="583"
						width="161"
						height="14"
						key="staticText-63"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Center" rotation="None">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO2排放量]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="583"
						width="244"
						height="14"
						key="staticText-64"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Center">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 燃料消耗量]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="129"
						y="597"
						width="82"
						height="14"
						key="staticText-65"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 低速段（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="597"
						width="90"
						height="14"
						key="staticText-66"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 低速段（L/100km）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="211"
						y="597"
						width="79"
						height="14"
						key="textField-58"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c179}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="380"
						y="597"
						width="154"
						height="14"
						key="textField-59"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c184}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="129"
						y="611"
						width="82"
						height="14"
						key="staticText-67"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 中速段（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="611"
						width="90"
						height="14"
						key="staticText-68"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 中速段（L/100km）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="211"
						y="611"
						width="79"
						height="14"
						key="textField-60"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c180}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="380"
						y="611"
						width="154"
						height="14"
						key="textField-61"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c185}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="129"
						y="625"
						width="82"
						height="14"
						key="staticText-69"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 高速段（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="625"
						width="90"
						height="14"
						key="staticText-70"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 高速段（L/100km）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="211"
						y="625"
						width="79"
						height="14"
						key="textField-62"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c181}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="380"
						y="625"
						width="154"
						height="14"
						key="textField-63"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c186}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="129"
						y="639"
						width="82"
						height="14"
						key="staticText-71"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[超高速段（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="639"
						width="90"
						height="14"
						key="staticText-72"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[超高速段（L/100km)]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="211"
						y="639"
						width="79"
						height="14"
						key="textField-64"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c182}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="380"
						y="639"
						width="154"
						height="14"
						key="textField-65"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c187}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="129"
						y="653"
						width="82"
						height="14"
						key="staticText-73"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 综合（g/km）]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="653"
						width="90"
						height="14"
						key="staticText-74"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 综合（L/100km）]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="211"
						y="653"
						width="79"
						height="14"
						key="textField-66"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c183}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="380"
						y="653"
						width="154"
						height="14"
						key="textField-67"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c188}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="667"
						width="111"
						height="14"
						key="staticText-75"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 认证委托人联系方式]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="129"
						y="667"
						width="82"
						height="14"
						key="staticText-76"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 联系人]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="290"
						y="667"
						width="90"
						height="14"
						key="staticText-77"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 联系电话]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="211"
						y="667"
						width="79"
						height="14"
						key="textField-68"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c192}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="380"
						y="667"
						width="154"
						height="14"
						key="textField-69"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c193}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="18"
						y="681"
						width="43"
						height="14"
						key="staticText-78"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[ 备注]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="61"
						y="681"
						width="473"
						height="14"
						key="textField-70"/>
					<box>					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
</box>
					<textElement>
						<font fontName="仿宋" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[" "+$F{c94}]]></textFieldExpression>
				</textField>
			</band>
		</detail>
		<columnFooter>
			<band height="5"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
