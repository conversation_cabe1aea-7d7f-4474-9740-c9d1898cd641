/**
 * 
 */
package com.dawnpro.dfpv.carfilemanager.module.business.share.dao;

import java.io.Serializable;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceProcdureLog;

/**
 * <AUTHOR>
 *
 */
public interface InterfaceProcdureLogDAO {
	public void addInterfaceProcdureLog(InterfaceProcdureLog prodlog);
	public void updateInterfaceProcdureLog(InterfaceProcdureLog prodlog);
	public void deleteInterfaceProcdureLog(Serializable id);
	public InterfaceProcdureLog loadInterfaceProcdureLog(Serializable id);
	public List<InterfaceProcdureLog> findInterfaceProcdureLog(String sql, Object[] params);

}
