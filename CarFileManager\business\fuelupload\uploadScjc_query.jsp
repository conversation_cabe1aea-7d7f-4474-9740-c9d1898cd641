<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				
            }
        }
    });
 

	$("#query").click(function(){
		//var frm = $("#qfrm");
		//frm.action = "business/uploadScjcQuery.action";
		//frm.submit();
		
		var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
		

		if(beginDate!='' && !isDate(beginDate)){
			alert("请按yyyy-MM-dd格式填写日期");
			return;
		}
		if(endDate!='' && !isDate(endDate)){
			alert("请按yyyy-MM-dd格式填写日期");
			return;			
		}		
		if(csbeginDate!='' && !isDate(csbeginDate)){
			alert("请按yyyy-MM-dd格式填写日期");
			return;
		}
		if(csendDate!='' && !isDate(csendDate)){
			alert("请按yyyy-MM-dd格式填写日期");
			return;			
		}	
		location.href="/CarFileManager/business/uploadScjcQuery.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
	});
	
    //导出COC结果
    $('#exportfueltar').click(function() {
    	//$('#COCexportcoc_dialog').data('title.dialog', '年份').dialog('open');
    	var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
      	window.location.href="uploadScjcQuery!exportFueltar.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
    	//return false;	
    });
    
    $('#exportfueltardata').click(function() {
    	//$('#COCexportcoc_dialog').data('title.dialog', '年份').dialog('open');
    	var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
      	window.location.href="uploadScjcQuery!exportFueltarData.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
    	//return false;	
    });
		
	$(".display").each(function(i){
		  $(this).click(function() {
			var vin=  $(this).attr("value");
			display(vin,"查看窗口");
		 });
	  });
	
    function display(vin,title){
		jQuery.ajax({
	        url: 'business/uploadScjcQuery!uploaDisplay.action',		           
	        data: {'vin' : vin}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	           var content = json2Bean(data).json;
	           var carObj = eval("("+content.toString()+")");
	           var dialogObj;
	           	dialogObj = $('#uploadScjcQuery_display_dialog');
				setDialogValue(dialogObj,carObj);
				dialogObj.data('title.dialog', title).dialog('open');
	        }
	    });

		return false;
	}
  
    function setDialogValue(dialogObj,jsonObj){
    		dialogObj.find(":text").each(function(i){
			$(this).val(jsonObj[$(this).attr("name")]);
		}) ;
	}
    
        $("#uploadScjcQuery_display_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 1000,
		height: 350,
		modal: true,        
		buttons: {  
        	'取消': function() {
				$("#uploadScjcQuery_display_dialog").dialog('close');
			}
		}                            
	});
    	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});


	function validate(parent){
		//return true;
		var obj = $(parent).find('#reason');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过100！');
			obj.focus();			
			return false;
		}

		return true;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var vin = $('#vin').val();
			var pmodel = $('#pmodel').val();
			var uploadType = $('#uploadType').val();
			var uploadState = $('#uploadState').val();
			var beginDate = $('#beginDate').val();
			var endDate = $('#endDate').val();
			var csbeginDate = $("#csbeginDate").val();
			var csendDate = $("#csendDate").val();
			if(beginDate!='' && isDate(beginDate)){
				alert("请按yyyy-MM-dd格式填写日期");
				return;
			}
			if(endDate!='' && isDate(endDate)){
				alert("请按yyyy-MM-dd格式填写日期");
				return;			
			}		
			if(csbeginDate!='' && !isDate(csbeginDate)){
				alert("请按yyyy-MM-dd格式填写日期");
				return;
			}
			if(csendDate!='' && !isDate(csendDate)){
				alert("请按yyyy-MM-dd格式填写日期");
				return;			
			}	
			location.href="uploadScjcQuery.action?currentPage="+$('#jump').val()+
			"&vin="+encodeURI(encodeURI(vin))+
			"&pmodel="+encodeURI(encodeURI(pmodel))+
			"&uploadType="+encodeURI(encodeURI(uploadType))+
			"&uploadState="+encodeURI(encodeURI(uploadState))+
			"&beginDate="+encodeURI(encodeURI(beginDate))+
			"&endDate="+encodeURI(encodeURI(endDate))+
			"&csbeginDate="+encodeURI(encodeURI(csbeginDate))+
			"&csendDate="+encodeURI(encodeURI(csendDate))+		
			"&menuid="+menuid;
   		}     
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var vin = $('#vin').val();
				var pmodel = $('#pmodel').val();
				var uploadType = $('#uploadType').val();
				var uploadState = $('#uploadState').val();
				var beginDate = $('#beginDate').val();
				var endDate = $('#endDate').val();
				var csbeginDate = $('#csbeginDate').val();
				var csendDate = $('#csendDate').val();
				if(beginDate!='' && !isDate(beginDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;
				}
				if(endDate!='' && !isDate(endDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;			
				}		
				if(csbeginDate!='' && !isDate(csbeginDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;
				}
				if(csendDate!='' && !isDate(csendDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;			
				}	
				location.href=$(this).attr('value')+
				"&vin="+encodeURI(encodeURI(vin))+
				"&pmodel="+encodeURI(encodeURI(pmodel))+
				"&uploadType="+encodeURI(encodeURI(uploadType))+
				"&uploadState="+encodeURI(encodeURI(uploadState))+
				"&beginDate="+encodeURI(encodeURI(beginDate))+
				"&endDate="+encodeURI(encodeURI(endDate))+
				"&csbeginDate="+encodeURI(encodeURI(csbeginDate))+
				"&csendDate="+encodeURI(encodeURI(csendDate))+
				"&menuid="+menuid;
		 });
	  });
	  
	
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif',
  	   		buttonImageOnly: true}, $.datepicker.regional['zh']));
	$('#beginDate').datepicker($.datepicker.regional['zh']); 
	$('#endDate').datepicker($.datepicker.regional['zh']); 
	$('#csbeginDate').datepicker($.datepicker.regional['zh']); 
	$('#csendDate').datepicker($.datepicker.regional['zh']); 
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class
        
        
        
        function validate1(parent){
			//return true;
			var obj = $(parent).find('#cocyears');
			if(!checkLength(obj,1,4)||!checkRegexp(obj,/^(\d+)$/)){
				obj.addClass('ui-state-error');
				updateTips($("#COCexportcoc_dialog").find('#validateTips'),'请输入年份，最大长度为4且必须是整数！');	
				obj.focus();			
				return false;
			}

			return true;
		}
	 
        $("#COCexportcoc_dialog").dialog({
    		bgiframe: true,
    		autoOpen: false,
    		width: 300,
    		height: 200,  
    		modal: true,
    		buttons: {
    			'取消': function() {
    				//clear($(this));
    				updateTips($(this).find('#validateTips'),'');	
    				$(this).dialog('close');
    			},
    		'导出': function() {
    				var cocyears = $('#cocyears').val();
    				if(validate1('#COCexportcoc_dialog')==true){
	    				window.location.href="uploadScjcQuery!exportFueltar.action?cocyears="+cocyears;
    				}
    				$(this).dialog('close');
    		}
    			},
    		close: function() {
    			updateTips($(this).find('#validateTips'),'');
    			//clear($(this));
    			//if(allFields!=null){
    				//allFields.val('').removeClass('ui-state-error');
    			//}
    				
    			type = null;
    		}
    		
    	});
	 
    

});




</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
<!--form id="qfrm" name="qfrm"-->
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan="">VIN:<input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" <s:if test="#request.vin!=null"> value="<s:property value="#request.vin" />"</s:if> size="18" /> 
				车型:<input type="text" id="pmodel" name="pmodel" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.pmodel!=null"> value="<s:property value="#request.pmodel" />"</s:if>/> 
				上传状态：<s:select name="uploadState" list="#{'I':'未上传','T':'已上传'}"  listKey="key" listValue="value" headerKey="" headerValue="全部" theme="simple" ></s:select>
				</td>
				<td align="right" colspan='3'></td>
			</tr>
			<tr>
				<td align="left" colspan="">上传时间：<input type="text" id="beginDate" name="beginDate" class="text ui-widget-content ui-corner-all"  size="14" <s:if test="#request.beginDate!=null"> value="<s:property value="#request.beginDate" />"</s:if>/>  -  <input type="text" id="endDate" name="endDate" class="text ui-widget-content ui-corner-all"  size="14" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />"</s:if>/>
				生产时间：<input type="text" id="csbeginDate" name="csbeginDate" class="text ui-widget-content ui-corner-all"  size="14" <s:if test="#request.csbeginDate!=null"> value="<s:property value="#request.csbeginDate" />"</s:if>/>  -  <input type="text" id="csendDate" name="csendDate" class="text ui-widget-content ui-corner-all"  size="14" <s:if test="#request.csendDate!=null"> value="<s:property value="#request.csendDate" />"</s:if>/>
				</td>
				<td align="right" colspan='3'></td>
			</tr>
			<tr>
			   <td width="80%"></td>
			   	
			   <td width="20" align="right"></td>		   
			   <td width="40" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			   <td width="40" align="right"><button id="exportfueltar" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			   <td width="70" align="right"><button id="exportfueltardata" class="ui-button ui-state-default ui-corner-all">缺OBD导出</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="6%">vin</th>
			    <th width="6%">公告车型</th>
			    <th width="6%">生产日期</th>
			    <th width="6%">上传日期</th>
			    
			    <th width="6%">上传状态</th>
			    <th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator id="item" value="#request.uploadScjcQueryPageData" status="obj"  > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="pmodelcode" /></td>
			  		<td><s:property value="proddate" /></td>
			  		<td><s:property value="adtjc" /></td>
			  		<td>
			  		<s:if test='jcuploadflg=="D"'>
			  			删除成功
			  		</s:if><s:elseif test='jcuploadflg=="F"'>
			  			上传失败
			  		</s:elseif><s:elseif test='jcuploadflg=="T"'>
			  			已上传
			  		</s:elseif><s:else>
						未上传
					</s:else>			  				  		
					</td>
	  			  	<td><a class='display' onclick="return false;" href='#' value='<s:property value="vin" />'>查看</a></td>
			  		
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadScjcQuery.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadScjcQuery.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadScjcQuery.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadScjcQuery.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
<!--/form-->
</div>

<div id="uploadScjcQuery_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td align="left"><label><P>原因:</label></td>
				<td><input type="text" id="reason" name="reason"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20"/>
					</td>
	    	</tr>			
		</Table>
		<input type='hidden' id='ids' name='ids'/>
		<input type='hidden' id='type' name='type'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div id="uploadScjcQuery_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm3" method="post" > 
	  	  <table id = "tuest" width="100%">
	    	<tr>
				<td align="left"><label><P>VIN:</label></td>
				<td><input type="text" id="vin" name="vin"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机编号</label></td>
				<td><input type="text" id="engineno" name="engineno"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>信息公开编号</label></td>
				<td><input type="text" id="xxgkhao" name="xxgkhao"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	<tr>
				<td align="left"><label><P>车辆制造日期</label></td>
				<td><input type="text" id="proddate" name="proddate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>商标</label></td>
				<td><input type="text" id="hbsb" name="hbsb"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>生产厂地址</label></td>
				<td><input type="text" id="scgdz" name="scgdz"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>生产厂名称</label></td>
				<td><input type="text" id="clzzname" name="clzzname"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机厂牌</label></td>
				<td><input type="text" id="fdjcp" name="fdjcp"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机生产地址</label></td>
				<td><input type="text" id="fdjscdz" name="fdjscdz"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	<tr>
				<td align="left"><label><P>相对湿度（单位%）</label></td>
				<td><input type="text" id="rh" name="rh"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>环境温度（单位°C）</label></td>
				<td><input type="text" id="et" name="et"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>大气压力（单位kPa）</label></td>
				<td><input type="text" id="ap" name="ap"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	<tr>
				<td align="left"><label><P>排气检测方法</label></td>
				<td><input type="text" id="testtype" name="testtype"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>检测报告编号</label></td>
				<td><input type="text" id="testNo" name="testNo"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>排放检测日期</label></td>
				<td><input type="text" id="testDate" name="testDate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    		
	    	<tr>
				<td align="left"><label><P>外观检验判定</label></td>
				<td><input type="text" id="apass" name="apass"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>OBD通讯判定</label></td>
				<td><input type="text" id="opass" name="opass"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>OBD检测日期</label></td>
				<td><input type="text" id="otestdate" name="otestdate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    		
	    	<tr>
				<td align="left"><label><P>排放检验结果</label></td>
				<td><input type="text" id="epass" name="epass"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>最终判定</label></td>
				<td><input type="text" id="finalresult" name="finalresult"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>型式检验时的OBD要求</label></td>
				<td><input type="text" id="obd" name="obd"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	<tr>
				<td align="left"><label><P>车辆行驶里程</label></td>
				<td><input type="text" id="odo" name="odo"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机控制单元模块ID</label></td>
				<td><input type="text" id="ecmmoduleid" name="ecmmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机控制单元CALID</label></td>
				<td><input type="text" id="ecmcalid" name="ecmcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>发动机控制单元CVN</label></td>
				<td><input type="text" id="ecmcvn" name="ecmcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>变速箱控制单元模块ID</label></td>
				<td><input type="text" id="tcmmoduleid" name="tcmmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>变速箱控制单元CALID</label></td>
				<td><input type="text" id="tcmcalid" name="tcmcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	
	    	<tr>
				<td align="left"><label><P>变速箱控制单元CVN</label></td>
				<td><input type="text" id="tcmcvn" name="tcmcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机附属控制单元模块ID</label></td>
				<td><input type="text" id="ecm2moduleid" name="ecm2moduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>发动机附属控制单元CALID</label></td>
				<td><input type="text" id="ecm2calid" name="ecm2calid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>发动机附属控制单元CVN</label></td>
				<td><input type="text" id="ecm2cvn" name="ecm2cvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>电驱动控制单元模块ID</label></td>
				<td><input type="text" id="dmcmmoduleid" name="dmcmmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>电驱动控制单元CALID</label></td>
				<td><input type="text" id="dmcmcalid" name="dmcmcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	
	    	<tr>
				<td align="left"><label><P>电驱动控制单元CVN</label></td>
				<td><input type="text" id="dmcmcvn" name="dmcmcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>选择性催化还原单元模块ID</label></td>
				<td><input type="text" id="scrmoduleid" name="scrmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>选择性催化还原单元CALID</label></td>
				<td><input type="text" id="scrcalid" name="scrcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	<tr>
				<td align="left"><label><P>选择性催化还原单元CVN</label></td>
				<td><input type="text" id="scrcvn" name="scrcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>高压电池控制单元模块ID</label></td>
				<td><input type="text" id="hvbecmmoduleid" name="hvbecmmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>高压电池控制单元CALID</label></td>
				<td><input type="text" id="hvbecmcalid" name="hvbecmcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>高压电池控制单元CVN</label></td>
				<td><input type="text" id="hvbecmcvn" name="hvbecmcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>车身稳定控制单元模块ID</label></td>
				<td><input type="text" id="bcmmoduleid" name="bcmmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>车身稳定控制单元CALID</label></td>
				<td><input type="text" id="bcmcalid" name="bcmcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	<tr>
				<td align="left"><label><P>车身稳定控制单元CVN</label></td>
				<td><input type="text" id="bcmcvn" name="bcmcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>其他控制单元模块ID</label></td>
				<td><input type="text" id="othmoduleid" name="othmoduleid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>其他控制单元CALID</label></td>
				<td><input type="text" id="othcalid" name="othcalid"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	<tr>
				<td align="left"><label><P>其他控制单元CVN</label></td>
				<td><input type="text" id="othcvn" name="othcvn"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>HC结果</label></td>
				<td><input type="text" id="vrhc" name="vrhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>HC限值</label></td>
				<td><input type="text" id="vlhc" name="vlhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>    
	    	
	    	<tr>
				<td align="left"><label><P>CO结果</label></td>
				<td><input type="text" id="vrco" name="vrco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>CO限值</label></td>
				<td><input type="text" id="vlco" name="vlco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>NOx结果</label></td>
				<td><input type="text" id="vrnox" name="vrnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>NOx限值</label></td>
				<td><input type="text" id="vlnox" name="vlnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>尾气分析仪制造厂</label></td>
				<td><input type="text" id="analyManuf" name="analyManuf"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>尾气分析仪名称</label></td>
				<td><input type="text" id="analyName" name="analyName"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>第二种HC结果</label></td>
				<td><input type="text" id="secvrhc" name="secvrhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>第二种HC限值</label></td>
				<td><input type="text" id="secvlhc" name="secvlhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>第二种瞬态CO结果 </label></td>
				<td><input type="text" id="secvrco" name="secvrco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>第二种瞬态CO限值 </label></td>
				<td><input type="text" id="secvlco" name="secvlco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>第二种瞬态NOx结果 </label></td>
				<td><input type="text" id="secvrnox" name="secvrnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>第二种瞬态NOx限值 </label></td>
				<td><input type="text" id="secvlnox" name="secvlnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>  
	    	
	    	<tr>
				<td align="left"><label><P>尾气分析仪型号</label></td>
				<td><input type="text" id="analyModel" name="analyModel"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>尾气分析仪检定日期</label></td>
				<td><input type="text" id="analyDate" name="analyDate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>底盘测功机型号</label></td>
				<td><input type="text" id="dynoModel" name="dynoModel"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>   
	    	
	    	<tr>
				<td align="left"><label><P>底盘测功机生产厂</label></td>
				<td><input type="text" id="dynoManuf" name="dynoManuf"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>委托的检测机构</label></td>
				<td><input type="text" id="ctest" name="ctest"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>检测地点</label></td>
				<td><input type="text" id="ctestlocation" name="ctestlocation"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>      				
	    	</div>	    				
	    	<tr>
			</tr>    				
		</Table>
		</form>
	</fieldset>
</div>


</body>
</html>