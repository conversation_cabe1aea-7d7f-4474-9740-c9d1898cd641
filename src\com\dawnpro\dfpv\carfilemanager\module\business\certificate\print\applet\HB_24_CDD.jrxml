<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="HB_24_CDD"

		 language="groovy"		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="555"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="AllSectionsNoDetail"
		 isTitleNewPage="true"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="chzhqbimage" isForPrompting="false" class="java.lang.String">
		<defaultValueExpression ><![CDATA["‪C:\\110_0.jpg"]]></defaultValueExpression>
	</parameter>
	<parameter name="fdjimage" isForPrompting="false" class="java.lang.String">
		<defaultValueExpression ><![CDATA["‪C:\\110_0.jpg"]]></defaultValueExpression>
	</parameter>
	<parameter name="cjdate" isForPrompting="true" class="java.lang.String"/>
	<parameter name="showflag" isForPrompting="true" class="java.lang.Boolean"/>

	<field name="slcx" class="java.lang.String">
		<fieldDescription><![CDATA[slcx]]></fieldDescription>
	</field>
	<field name="vercode" class="java.lang.String">
		<fieldDescription><![CDATA[vercode]]></fieldDescription>
	</field>
	<field name="pzextno" class="java.lang.String">
		<fieldDescription><![CDATA[pzextno]]></fieldDescription>
	</field>
	<field name="jybgno" class="java.lang.String">
		<fieldDescription><![CDATA[jybgno]]></fieldDescription>
	</field>
	<field name="jyjgmc" class="java.lang.String">
		<fieldDescription><![CDATA[jyjgmc]]></fieldDescription>
	</field>
	<field name="ccjyjg" class="java.lang.String">
		<fieldDescription><![CDATA[ccjyjg]]></fieldDescription>
	</field>
	<field name="fdjxh" class="java.lang.String">
		<fieldDescription><![CDATA[fdjxh]]></fieldDescription>
	</field>
	<field name="fdjgc" class="java.lang.String">
		<fieldDescription><![CDATA[fdjgc]]></fieldDescription>
	</field>
	<field name="chzhq" class="java.lang.String">
		<fieldDescription><![CDATA[chzhq]]></fieldDescription>
	</field>
	<field name="chzhqgc" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqgc]]></fieldDescription>
	</field>
	<field name="ryzfkzqxh" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqxh]]></fieldDescription>
	</field>
	<field name="ryzfkzqgc" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqgc]]></fieldDescription>
	</field>
	<field name="egrxh" class="java.lang.String">
		<fieldDescription><![CDATA[egrxh]]></fieldDescription>
	</field>
	<field name="egrgc" class="java.lang.String">
		<fieldDescription><![CDATA[egrgc]]></fieldDescription>
	</field>
	<field name="obdxh" class="java.lang.String">
		<fieldDescription><![CDATA[obdxh]]></fieldDescription>
	</field>
	<field name="obdgc" class="java.lang.String">
		<fieldDescription><![CDATA[obdgc]]></fieldDescription>
	</field>
	<field name="iuprnox" class="java.lang.String">
		<fieldDescription><![CDATA[iuprnox]]></fieldDescription>
	</field>
	<field name="ecuxh" class="java.lang.String">
		<fieldDescription><![CDATA[ecuxh]]></fieldDescription>
	</field>
	<field name="ecugc" class="java.lang.String">
		<fieldDescription><![CDATA[ecugc]]></fieldDescription>
	</field>
	<field name="bsqxs" class="java.lang.String">
		<fieldDescription><![CDATA[bsqxs]]></fieldDescription>
	</field>
	<field name="bsqdws" class="java.lang.String">
		<fieldDescription><![CDATA[bsqdws]]></fieldDescription>
	</field>
	<field name="xsqxh" class="java.lang.String">
		<fieldDescription><![CDATA[xsqxh]]></fieldDescription>
	</field>
	<field name="xsqgc" class="java.lang.String">
		<fieldDescription><![CDATA[xsqgc]]></fieldDescription>
	</field>
	<field name="zyqxh" class="java.lang.String">
		<fieldDescription><![CDATA[zyqxh]]></fieldDescription>
	</field>
	<field name="zyqgc" class="java.lang.String">
		<fieldDescription><![CDATA[zyqgc]]></fieldDescription>
	</field>
	<field name="zlqxs" class="java.lang.String">
		<fieldDescription><![CDATA[zlqxs]]></fieldDescription>
	</field>
	<field name="fdjbs" class="java.lang.String">
		<fieldDescription><![CDATA[fdjbs]]></fieldDescription>
	</field>
	<field name="fdjpath" class="java.lang.String">
		<fieldDescription><![CDATA[fdjpath]]></fieldDescription>
	</field>
	<field name="chzhqbs" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqbs]]></fieldDescription>
	</field>
	<field name="chzhqpath" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqpath]]></fieldDescription>
	</field>
	<field name="ryzfkzqbs" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqbs]]></fieldDescription>
	</field>
	<field name="ryzfkzqpath" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqpath]]></fieldDescription>
	</field>
	<field name="ycgqbs" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqbs]]></fieldDescription>
	</field>
	<field name="ycgqpath" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqpath]]></fieldDescription>
	</field>
	<field name="egrbs" class="java.lang.String">
		<fieldDescription><![CDATA[egrbs]]></fieldDescription>
	</field>
	<field name="egrpath" class="java.lang.String">
		<fieldDescription><![CDATA[egrpath]]></fieldDescription>
	</field>
	<field name="ecubs" class="java.lang.String">
		<fieldDescription><![CDATA[ecubs]]></fieldDescription>
	</field>
	<field name="ecupath" class="java.lang.String">
		<fieldDescription><![CDATA[ecupath]]></fieldDescription>
	</field>
	<field name="xsqbs" class="java.lang.String">
		<fieldDescription><![CDATA[xsqbs]]></fieldDescription>
	</field>
	<field name="xsqpath" class="java.lang.String">
		<fieldDescription><![CDATA[xsqpath]]></fieldDescription>
	</field>
	<field name="zyqbs" class="java.lang.String">
		<fieldDescription><![CDATA[zyqbs]]></fieldDescription>
	</field>
	<field name="zyqpath" class="java.lang.String">
		<fieldDescription><![CDATA[zyqpath]]></fieldDescription>
	</field>
	<field name="mark" class="java.lang.String">
		<fieldDescription><![CDATA[mark]]></fieldDescription>
	</field>
	<field name="creator" class="java.lang.String">
		<fieldDescription><![CDATA[creator]]></fieldDescription>
	</field>
	<field name="state" class="java.lang.String">
		<fieldDescription><![CDATA[state]]></fieldDescription>
	</field>
	<field name="datadate" class="java.util.Date">
		<fieldDescription><![CDATA[datadate]]></fieldDescription>
	</field>
	<field name="ggcx" class="java.lang.String">
		<fieldDescription><![CDATA[ggcx]]></fieldDescription>
	</field>
	<field name="effectTime" class="java.lang.String">
		<fieldDescription><![CDATA[effectTime]]></fieldDescription>
	</field>
	<field name="fdjbsname" class="java.lang.String">
		<fieldDescription><![CDATA[fdjbsname]]></fieldDescription>
	</field>
	<field name="chzhqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[chzhqbsname]]></fieldDescription>
	</field>
	<field name="ryzfkzqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[ryzfkzqbsname]]></fieldDescription>
	</field>
	<field name="ycgqbsname" class="java.lang.String"/>
	<field name="egrbsname" class="java.lang.String">
		<fieldDescription><![CDATA[egrbsname]]></fieldDescription>
	</field>
	<field name="ecubsname" class="java.lang.String">
		<fieldDescription><![CDATA[ecubsname]]></fieldDescription>
	</field>
	<field name="xsqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[xsqbsname]]></fieldDescription>
	</field>
	<field name="zyqbsname" class="java.lang.String">
		<fieldDescription><![CDATA[zyqbsname]]></fieldDescription>
	</field>
	<field name="ycgqxh" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqxh]]></fieldDescription>
	</field>
	<field name="ycgqgc" class="java.lang.String">
		<fieldDescription><![CDATA[ycgqgc]]></fieldDescription>
	</field>
	<field name="qzspfxh" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfxh]]></fieldDescription>
	</field>
	<field name="qzspfgc" class="java.lang.String">
		<fieldDescription><![CDATA[qzspfgc]]></fieldDescription>
	</field>
	<field name="jyjg1" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg1]]></fieldDescription>
	</field>
	<field name="jyjg2" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg2]]></fieldDescription>
	</field>
	<field name="jyjg3" class="java.lang.String"/>
	<field name="jyjg4" class="java.lang.String">
		<fieldDescription><![CDATA[jyjg4]]></fieldDescription>
	</field>
	<field name="jcjr1" class="java.lang.String"/>
	<field name="jcjr2" class="java.lang.String"/>
	<field name="jcjr3" class="java.lang.String"/>
	<field name="jcjr4" class="java.lang.String">
		<fieldDescription><![CDATA[jcjr4]]></fieldDescription>
	</field>
	<field name="hbtc" class="java.lang.String">
		<fieldDescription><![CDATA[hbtc]]></fieldDescription>
	</field>
	<field name="frdb" class="java.lang.String">
		<fieldDescription><![CDATA[frdb]]></fieldDescription>
	</field>
	<field name="hbzt" class="java.lang.String"/>
	<field name="hbfzscc" class="java.lang.String"/>
	<field name="dz" class="java.lang.String">
		<fieldDescription><![CDATA[dz]]></fieldDescription>
	</field>
	<field name="tel" class="java.lang.String">
		<fieldDescription><![CDATA[tel]]></fieldDescription>
	</field>
	<field name="hbdws" class="java.lang.String">
		<fieldDescription><![CDATA[hbdws]]></fieldDescription>
	</field>
	<field name="ecubb" class="java.lang.String">
		<fieldDescription><![CDATA[ecubb]]></fieldDescription>
	</field>
	<field name="rqhhq" class="java.lang.String">
		<fieldDescription><![CDATA[rqhhq]]></fieldDescription>
	</field>
	<field name="rqhhqgc" class="java.lang.String">
		<fieldDescription><![CDATA[rqhhqgc]]></fieldDescription>
	</field>
	<field name="rqpsdy" class="java.lang.String"/>
	<field name="rqpsdygc" class="java.lang.String"/>
	<field name="cnzl" class="java.lang.String"/>
	<field name="cnzlgc" class="java.lang.String"/>
	<field name="dcrl" class="java.lang.String"/>
	<field name="dhlc" class="java.lang.String">
		<fieldDescription><![CDATA[dhlc]]></fieldDescription>
	</field>
	<field name="ddjxh" class="java.lang.String"/>
	<field name="ddjgc" class="java.lang.String"/>
	<field name="ddjecu" class="java.lang.String">
		<fieldDescription><![CDATA[ddjecu]]></fieldDescription>
	</field>
	<field name="ddjecubbh" class="java.lang.String">
		<fieldDescription><![CDATA[ddjecubbh]]></fieldDescription>
	</field>
	<field name="ddjecuscc" class="java.lang.String"/>
	<field name="xxgkhao" class="java.lang.String"/>
	<field name="clxh" class="java.lang.String"/>
	<field name="hbsb" class="java.lang.String"/>
	<field name="qcfl" class="java.lang.String"/>
	<field name="cxsb" class="java.lang.String"/>
	<field name="clzzname" class="java.lang.String"/>
	<field name="field1" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="pfjd" class="java.lang.String">
		<fieldDescription><![CDATA[pfjd]]></fieldDescription>
	</field>
	<field name="scgdz" class="java.lang.String">
		<fieldDescription><![CDATA[scgdz]]></fieldDescription>
	</field>
	<field name="dbz" class="java.lang.String"/>
	<field name="fdjbh" class="java.lang.String"/>
	<field name="jzzl" class="java.lang.String"/>
	<field name="klbjqxh" class="java.lang.String"/>
	<field name="hbtc2" class="java.lang.String">
		<fieldDescription><![CDATA[hbtc2]]></fieldDescription>
	</field>
	<field name="tgxh" class="java.lang.String"/>
	<field name="jyjg5" class="java.lang.String"/>
	<field name="jcjr5" class="java.lang.String"/>
	<field name="klbjqscc" class="java.lang.String"/>
	<field name="tgscc" class="java.lang.String"/>
	<field name="pfbz" class="java.lang.String"/>
	<field name="hbzt2" class="java.lang.String"/>
	<field name="hbfzscc2" class="java.lang.String"/>
	<field name="vin" class="java.lang.String"/>
	<field name="ccjyjg2" class="java.lang.String"/>
	<field name="dpxh" class="java.lang.String"/>
	<field name="dpscc" class="java.lang.String"/>
	<field name="ktzljzl" class="java.lang.String"/>
	<field name="ktzljjzl" class="java.lang.String"/>
	<field name="xxjyyj" class="java.lang.String"/>
	<field name="xxjyjl" class="java.lang.String"/>
	<field name="dcrl1" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="793"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="144"
						y="118"
						width="267"
						height="40"
						key="staticText">
							<printWhenExpression><![CDATA[$P{showflag}]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="方正大标宋简体" pdfFontName="STSong-Light" size="30" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[中华人民共和国]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="88"
						y="158"
						width="379"
						height="40"
						key="staticText">
							<printWhenExpression><![CDATA[$P{showflag}]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="方正大标宋简体" pdfFontName="STSong-Light" size="30" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[机动车环保信息随车清单]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="161"
						y="200"
						width="233"
						height="50"
						key="staticText">
							<printWhenExpression><![CDATA[$P{showflag}]]></printWhenExpression>
						</reportElement>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="方正大标宋简体" pdfFontName="STSong-Light" size="24" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[（纯电动汽车）]]></text>
				</staticText>
				<image  scaleImage="RetainShape" vAlign="Middle" hAlign="Center" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="199"
						y="657"
						width="160"
						height="22"
						key="image"/>
					<box>					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{fdjimage}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="217"
						y="592"
						width="120"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="黑体" pdfFontName="STSong-Light" size="15" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[信息公开编号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="142"
						y="620"
						width="270"
						height="20"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="Arial" pdfFontName="STSong-Light" size="15" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{xxgkhao}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="199"
						y="684"
						width="156"
						height="20"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="Arial"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["VIN:"+$F{vin}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="117"
						y="540"
						width="320"
						height="38"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Center">
						<font fontName="宋体" pdfFontName="STSong-Light" size="25" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clzzname}]]></textFieldExpression>
				</textField>
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnHeader>
		<detail>
			<band height="794"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="31"
						y="166"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆制造商名称：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="127"
						width="82"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号:]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="140"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{hbsb}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="58"
						y="140"
						width="55"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[标：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="127"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="114"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第一部分 车辆信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="153"
						width="82"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[汽车分类：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="179"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产厂地址：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="13"
						y="22"
						width="433"
						height="66"
						key="textField"
						stretchType="RelativeToBandHeight"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["   " + $F{dbz}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="14"
						y="107"
						width="526"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch"/>
				</line>
				<staticText>
					<reportElement
						x="13"
						y="266"
						width="137"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第二部分 检验信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="279"
						width="101"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[型式检验信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="292"
						width="76"
						height="12"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[依据的标准]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="179"
						y="292"
						width="66"
						height="12"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[检验机构]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="410"
						y="292"
						width="76"
						height="12"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[检验结论]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="304"
						width="100"
						height="13"
						key="staticText"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[GB 1495-2002]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="338"
						width="149"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第三部分 环保关键技术信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="351"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动电机型号/生产企业：]]></text>
				</staticText>
				<image  scaleImage="RetainShape" vAlign="Middle" hAlign="Center" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="469"
						y="22"
						width="65"
						height="65"
						key="image"/>
					<box>					<pen lineWidth="0.0"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
</box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{chzhqbimage}]]></imageExpression>
				</image>
				<staticText>
					<reportElement
						x="12"
						y="433"
						width="164"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[第四部分 生产企业/进口企业信息]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="446"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[法定代表人：    ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="59"
						y="459"
						width="117"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[址： ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="472"
						width="145"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[联系电话：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="31"
						y="485"
						width="455"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["本清单内容及污染控制装置永久性标识相关信息可查询本企业官方网站（ http://www.dfmc.com.cn ）"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="12"
						y="498"
						width="419"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[和生态环境部机动车环保信息公开平台（https://www.vecc.org.cn）。]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="402"
						y="761"
						width="59"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆生产日期]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="304"
						width="219"
						height="22"
						key="textField"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jyjg3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="411"
						y="304"
						width="100"
						height="13"
						key="textField"
						isRemoveLineWhenBlank="true"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{jcjr3}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="446"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{frdb}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="459"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dz}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="472"
						width="324"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{tel}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="351"
						width="360"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ddjxh}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" pattern="yyyy年MM月dd日" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="460"
						y="761"
						width="76"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{cjdate}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="153"
						width="100"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{qcfl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="166"
						width="139"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{clzzname}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="179"
						width="372"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{scgdz}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="279"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[11]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="179"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[05]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="127"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[01]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="140"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[02]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="153"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[03]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="140"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[商]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="166"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[04]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="351"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[12]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="446"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[17]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="459"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[18]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="12"
						y="472"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[19]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="459"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[地]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="192"
						width="15"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[06]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="192"
						width="306"
						height="13"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{fdjbh}.replace('/',',')]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="192"
						width="100"
						height="13"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动电机顺序号：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="205"
						width="15"
						height="13"
						key="staticText-1"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[07]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="205"
						width="100"
						height="13"
						key="staticText-2"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[底盘型号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="205"
						width="151"
						height="13"
						key="textField-1"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dpxh}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="218"
						width="100"
						height="13"
						key="staticText-6"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[底盘生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="218"
						width="151"
						height="13"
						key="textField-3"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dpscc}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="218"
						width="15"
						height="13"
						key="staticText-7"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[08]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="13"
						y="231"
						width="15"
						height="13"
						key="staticText-8"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[09]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="231"
						width="100"
						height="13"
						key="staticText-9"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车用空调制冷剂种类：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="231"
						width="151"
						height="13"
						key="textField-4"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ktzljzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="31"
						y="244"
						width="100"
						height="13"
						key="staticText-10"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车用空调制冷剂加注量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="131"
						y="244"
						width="151"
						height="13"
						key="textField-5"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ktzljjzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="244"
						width="15"
						height="13"
						key="staticText-11"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[10]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="435"
						y="735"
						width="59"
						height="13"
						key="staticText-14"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[(企业盖章处)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="364"
						width="145"
						height="13"
						key="staticText-17"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[整车控制器型号/版本号/生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="364"
						width="360"
						height="13"
						key="textField-9"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ddjecu}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="364"
						width="15"
						height="13"
						key="staticText-18"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[13]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="377"
						width="145"
						height="13"
						key="staticText-19"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[储能装置型号/生产企业：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="377"
						width="360"
						height="13"
						key="textField-10"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{cnzl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="377"
						width="15"
						height="13"
						key="staticText-20"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[14]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="390"
						width="145"
						height="13"
						key="staticText-21"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电池容量（储能装置总储电量）：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="390"
						width="360"
						height="13"
						key="textField-11"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dcrl1}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="390"
						width="15"
						height="13"
						key="staticText-22"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[15]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="31"
						y="403"
						width="145"
						height="13"
						key="staticText-23"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[续航里程及对应工况：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="true" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="179"
						y="403"
						width="360"
						height="13"
						key="textField-12"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{dhlc}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="13"
						y="403"
						width="15"
						height="13"
						key="staticText-24"/>
					<box></box>
					<textElement>
						<font fontName="宋体" pdfFontName="STSong-Light" size="9" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[16]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
