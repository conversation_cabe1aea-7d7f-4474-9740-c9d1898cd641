<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.Bv02" table="BV_02">
        <id name="id" type="java.lang.String">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="ggcx" type="java.lang.String">
            <column name="GGCX" length="50" not-null="true" />
        </property>
        <property name="vercode" type="java.lang.String">
            <column name="VERCODE" length="10" not-null="true" />
        </property>
        <property name="slcx" type="java.lang.String">
            <column name="SLCX" length="50" not-null="true" />
        </property>
        <property name="senddate" type="java.lang.String">
            <column name="SENDDATE" length="16" />
        </property>
        <property name="sendtime" type="java.lang.String">
            <column name="SENDTIME" length="16" />
        </property>
    </class>
</hibernate-mapping>
