/*
 * Image preview script 
 * powered by jQuery (http://www.jquery.com)
 * 
 * written by <PERSON><PERSON> (http://cssglobe.com)
 * 
 * for more info visit http://cssglobe.com/post/1695/easiest-tooltip-and-image-preview-using-jquery
 *
 */
 
this.imagePreview = function(){	
	/* CONFIG */
		
		xOffset = 10;
		yOffset = 30;
		
		// these 2 variable determine popup's distance from the cursor
		// you might want to adjust to get the right result
		
	/* END CONFIG */
	$("a.preview").hover(function(e){
		this.t = this.title;
		this.title = "";	
		var c = (this.t != "") ? "<br/>" + this.t : "";
		$("body").append("<p id='preview'><img id='imgview' src='"+ this.href +"'   alt='Image preview' />"+ c +"</p>");								 
		
		var documnetObj = $(document);
		var previweObj = $("#preview");
	
		previweObj.css("top",(documnetObj.height()/2-previweObj.height()/2) + "px")
			.css("left",(document.body.clientWidth/2-previweObj.width()/2) + "px")
			.fadeIn("fast");	
    },
	function(){
		this.title = this.t;	
		$("#preview").remove();
    });	
	$("a.preview").mousemove(function(e){
		var documnetObj = $(document);
		var previweObj = $("#preview");
																																																																																																																																																																																																																																																																																																																																																																																																																																																																						
		previweObj.css("top",(documnetObj.height()/2-previweObj.height()/2) + "px")
			.css("left",(document.body.clientWidth/2-previweObj.width()/2) + "px");
	});			
};


// starting the script on page load
$(document).ready(function(){
	imagePreview();
});