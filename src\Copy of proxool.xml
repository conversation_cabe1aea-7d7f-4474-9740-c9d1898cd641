<?xml version="1.0" encoding="UTF-8"?>
<something-else-entirely>  
    <proxool>
        <alias>DBPool</alias>   
          <driver-url>***************************************</driver-url>
        <!--<driver-url>*************************************</driver-url> -->
        <driver-class>oracle.jdbc.driver.OracleDriver</driver-class> 
        <driver-properties>  
            <property name="user" value="v_file" />  
            <property name="password" value="v_file20091210" /> 
        </driver-properties>    
        <!-- 最大连接数(默认5个),超过了这个连接数,再有请求时,就排在队列中等候,最大的等待请求数由maximum-new-connections决定 -->  
        <maximum-connection-count>50</maximum-connection-count>    
        <!-- 最小连接数(默认2个)-->  
        <minimum-connection-count>10</minimum-connection-count>    
        <!-- 没有空闲连接可以分配而在队列中等候的最大请求数,超过这个请求数的用户连接就不会被接受-->  
        <maximum-new-connections>30</maximum-new-connections>    
        <!-- 最少保持的空闲连接数(默认2个)-->  
        <prototype-count>10</prototype-count>
        <!-- 连接池中可用的连接数量 -->
        <prototype-count>5</prototype-count>
         <!-- proxool自动侦察各个连接状态的时间间隔(毫秒),侦察到空闲的连接就马上回收,超时的销毁 默认30秒-->  
        <house-keeping-sleep-time>90000</house-keeping-sleep-time>  
        <!-- 一个线程的最大寿命5小时(默认4小时)  -->
        <maximum-connection-lifetime>18000000</maximum-connection-lifetime>    
        <!--在使用之前测试-->  
        <test-before-use>true</test-before-use>  
        <!--用于保持连接的测试语句 -->  
        <house-keeping-test-sql>select sysdate from dual</house-keeping-test-sql>  
        <statistics>1m,15m,1d</statistics>
		<statistics-log-level>INFO</statistics-log-level>
		<fatal-sql-exception>Connection is closed,SQLSTATE=08003,Error opening socket. SQLSTATE=08S01,SQLSTATE=08S01</fatal-sql-exception>
		<fatal-sql-exception-wrapper-class>org.logicalcobwebs.proxool.FatalRuntimeException</fatal-sql-exception-wrapper-class>
    </proxool>  
</something-else-entirely>  