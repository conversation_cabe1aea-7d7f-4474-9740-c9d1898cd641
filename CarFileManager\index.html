<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link type="text/css" href="css/login.css" rel="stylesheet" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt; margin-top: 120px;}
		p {font-family:"宋体";font-size: 9pt;color:red }
		label, input { display:block; }
		input.text { margin-bottom:12px; width:95%; padding: .4em; }
		fieldset { padding:0; border:0; margin-top:25px; }
		h1 { font-size: 1.2em; margin: .6em 0; }
		div#users-contain {  width: 350px; margin: 20px 0; }
		div#users-contain table { margin: 1em 0; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: left; }
		.ui-button { outline: 0; margin:0; padding: .4em 1em .5em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align: center; }
		.ui-dialog .ui-state-highlight, .ui-dialog .ui-state-error { padding: .3em;  }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript">
function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}
function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}


		function login(){
			var loginName = $('#loginName').val();
			var password = $('#password').val();
			if(loginName==""){
				alert('用户名不能为空！');
			}else if(password==""){
				alert('密码不能为空！');
			}else{
				jQuery.ajax({
		            url: 'system/login!login.action', 
		            data:"loginName="+loginName+"&password="+password, 
		            type: 'POST',
		            //beforeSend: function(){},
		            error: function(request) {
		            
		            },
		            success: function(data) {
		            	 $(data).find('login').each(function(){
		            			if($(this).children("result").text()=="success"){
			            			window.location.href="main.jsp"; 
			            			
			            			//window.open ('main.jsp', 'newwindow', 'height='+window.screen.height+', width='+window.screen.width+', top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=yes,location=n o, status=no')
			            			//window.opener=null;
			            			//window.close();
				            	}else{
			            			alert($(this).children("text").text());
				            	}		   
		            	});
		            					            	 
		            }});
			}
		}
	$(function() {
		$(document).keydown( function(e) { 
	    	var key = window.event?e.keyCode:e.which; 
	   		if(key.toString() == "13"){ 
		    		login();
	                return false; 
	    	} 
		});
	});
</script>
<title></title>
</head>
<body onload="MM_preloadImages('images/bt1-2.gif','images/bt2-2.gif')">
<table width="626" border="0" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td height="222"><img src="images/pic1.jpg" width="626" height="222" /></td>
  </tr>
</table>
<table width="626" border="0" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td height="96"><img src="images/logan2.jpg" width="21" height="96" /></td>
    <td width="605" background="images/logan3.jpg"><table width="100%" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td width="39" height="26" class="text1">帐号：</td>
        <td width="117"><input name="loginName" type="text" class="text_box" id="loginName" size="20" /></td>
        <td width="449"><p id="validateTips"></p></td>
      </tr>
      <tr>
        <td height="26" class="text1">密码：</td>
        <td><input name="password" type="password" class="text_box" id="password" size="20" /></td>
        <td>
	        <a href="#" onclick="login();" onmouseout="MM_swapImgRestore()" onmouseover="MM_swapImage('Image3','','images/bt1-2.gif',1)">
	        	<img src="images/bt1.gif" name="Image3" width="58" height="20" border="0" id="Image3" />&nbsp;
	        </a>
	        <a href="#" onclick="windows.close();" onmouseout="MM_swapImgRestore()" onmouseover="MM_swapImage('Image4','','images/bt2-2.gif',1)">
	        	<img src="images/bt2.gif" name="Image4" width="58" height="20" border="0" id="Image4" />
	        </a>
        </td>
      </tr>
    </table></td>
  </tr>
</table>
</body>
</html>