package com.dawnpro.dfpv.carfilemanager.module.system.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.tools.CalendarUtil;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryId;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryService;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysDataDictionaryTypeService;

public class SysDataDictionaryAction extends BaseSupportAction{
	private static final long serialVersionUID = -6371005813002910434L;
	private Logger logger = Logger.getLogger(SysDataDictionaryTypeAction.class.getName());
	private SysDataDictionaryService service=null;
	private SysDataDictionaryTypeService typeService=null;
	private String currentPage = null;
	public void setService(SysDataDictionaryService service) {
		this.service = service;
	}
	public void setTypeService(SysDataDictionaryTypeService typeService) {
		this.typeService = typeService;
	}
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}

	private String sname;
	private String stype;
	public void setSname(String sname) {
		this.sname = sname;
	}
	public void setStype(String stype) {
		this.stype = stype;
	}

	private String id;
	private String name;
	private String value;
	private String type;
	public void setId(String id) {
		this.id = id;
	}
	public void setName(String name) {
		this.name = name;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public void setType(String type) {
		this.type = type;
	}

	private HashMap<String, DataDictionaryType> typeCache=new HashMap<String, DataDictionaryType>();
	private Map<String,String> getTypeData(){
		List<DataDictionaryType> result=this.typeService.findDataDictionaryType("from DataDictionaryType");
		if(result!=null && result.size()>0){
			Map<String,String> data=new LinkedHashMap<String,String>();
			for(DataDictionaryType ele:result){
				data.put(ele.getName(), ele.getType());
				typeCache.put(ele.getType(), ele);
			}
			return data;
		}
		return null;
	}
	public String execute(){
		try{
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());
			
			Page page = new Page();
			List results = null;
			
			if(this.currentPage!=null&&!this.currentPage.trim().equals("")){
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			}else{
				page.setCurrentPage(1);
				currentPage="1";
			}
			
			Map<String,String> types=this.getTypeData();
			if(types!=null && types.size()>0){
				this.getServletRequest().setAttribute("types", types);
			}
			
			List resultTemp=null;
			if((this.sname==null||this.sname.equals(""))&&(this.stype==null||this.stype.equals(""))){
				resultTemp = this.service.pagination(page,null);
			}else{
				if(sname!=null&&!sname.equals("")){
					sname = java.net.URLDecoder.decode(this.sname, "UTF-8");
				}
				resultTemp = this.service.pagination(page,new Object[]{this.stype,this.sname});
			}
			if(resultTemp!=null && resultTemp.size()>0){
				results=new ArrayList();
				for(Object ele:resultTemp){
					if(ele instanceof DataDictionary){
						DataDictionaryId value=((DataDictionary)ele).getId();
						DataDiruectionShowData e=new DataDiruectionShowData();
						e.setId(value.getName()+","+value.getValue()+","+value.getType());
						e.setName(value.getName());
						e.setValue(value.getValue());
						e.setCreator(((DataDictionary)ele).getCreator());
						e.setTime(((DataDictionary)ele).getTime());
						String type=value.getType();
						if(type!=null){
							type=typeCache.get(type).getName();
							e.setType(type);
						}
						results.add(e);
					}
				}
			}
			
			this.getServletRequest().setAttribute("sname",this.sname==null?"":this.sname.trim());
			this.getServletRequest().setAttribute("stype",this.stype==null?"":this.stype.trim());
			
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("pageData", results);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		
		return SUCCESS;
	}
	
	public String isModelExist(){
		boolean isExist = false;
		try{
			if(id!=null && id.trim().length()>0){
				String[] temp=id.split(",");
				DataDictionaryId idObject=new DataDictionaryId();
				idObject.setName(temp[0]);
				idObject.setValue(temp[1]);
				idObject.setType(temp[2]);
				isExist = this.service.isDataDictionaryExist(idObject);
			}
			this.setJson(String.valueOf(isExist));
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return JSON;
	}

	public String modelInfo(){
		try{
			String[] temp=id.split(",");
			DataDictionaryId idObject=new DataDictionaryId();
			idObject.setName(temp[0]);
			idObject.setValue(temp[1]);
			idObject.setType(temp[2]);
			DataDictionary gg = this.service.loadDataDictionary(idObject);
			
			DataDictionaryId value=gg.getId();
			DataDiruectionShowData e=new DataDiruectionShowData();
			e.setId(value.getName()+","+value.getValue()+","+value.getType());
			e.setName(value.getName());
			e.setValue(value.getValue());
			e.setType(value.getType());
			JSONObject jsonObject = JSONObject.fromObject(e); 
			this.setJson(jsonObject.toString());
		}catch(DataAccessException ex){
			logger.error("Action DataAccessException Error:",ex);
			throw ex;
		}catch(SystemException ex){
			logger.error("Action SystemException Error:",ex);
			throw ex;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}catch(Exception e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return JSON;
	}
	
	public String addModel(){
		try{
			DataDictionary gg = new DataDictionary();
			DataDictionaryId idObject=new DataDictionaryId();
			idObject.setType(this.type==null?"":this.type.trim());
			idObject.setName(this.name==null?"":this.name.trim());
			idObject.setValue(this.value==null?"":this.value.trim());
			gg.setId(idObject);
			
			SysUser user =(SysUser)getSession().get(SYSTEM_USER);
			gg.setCreator(user.getLoginname());
			gg.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));

			this.service.addDataDictionary(gg);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}

	public String updateModel(){
		try{
			DataDictionary gg = new DataDictionary();
			DataDictionaryId idObject=new DataDictionaryId();
			idObject.setType(this.type==null?"":this.type.trim());
			idObject.setName(this.name==null?"":this.name.trim());
			idObject.setValue(this.value==null?"":this.value.trim());
			gg.setId(idObject);

			SysUser user =(SysUser)getSession().get(SYSTEM_USER);
			gg.setCreator(user.getLoginname());
			gg.setTime(CalendarUtil.getCurrentLocatlTime("yyyy-MM-dd hh:mm:ss"));

			String[] temp=id.split(",");
			DataDictionaryId oldId=new DataDictionaryId();
			oldId.setName(temp[0]);
			oldId.setValue(temp[1]);
			oldId.setType(temp[2]);
			
			this.service.updateDataDictionary(oldId,gg);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}

	public String deleteModel(){
		try{
			String tmp = this.id.trim();
			String[] ides = tmp.split("&");
			if(ides!=null && ides.length>0){
				DataDictionary[] gg = new DataDictionary[ides.length];
				for(int i=0;i<ides.length;i++){
					String[] temp=ides[i].split(",");
					DataDictionaryId idObject=new DataDictionaryId();
					idObject.setName(temp[0]);
					idObject.setValue(temp[1]);
					idObject.setType(temp[2]);
					gg[i] = new DataDictionary();
					gg[i].setId(idObject);
				}
				this.service.deleteDataDictionary(gg);
			}
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		}
		return "view";
	}
	
}