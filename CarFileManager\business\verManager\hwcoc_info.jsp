<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; /**position: relative;**/ text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">
$(function() {
	var allFields = null;
	var type = null;
	var wizardModel = "two";
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	//$("#create").attr("disabled", true);
	//$("#update").attr("disabled", true);
	//$("#delete").attr("disabled", true);
	//$("#effect").attr("disabled", true);
	//$("#published").attr("disabled", true);
	//$("#import").attr("disabled", true);
	//$("#export").attr("disabled", true);
	//$("#print").attr("disabled", true);
	//$("#compare").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("print")!=-1){
					$("#print").attr("disabled", false);
					isprint = true;
				}
            }
        }
    });
	

	$("#query").click(function(){
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		if(qc1==""&&qstate==""&&qfactory==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="hwcocVer.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&menuid="+menuid;
		}
	});
	
	

	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
      	
    	if(index==1){
        	var params = "slcx="+id;
        	jQuery.ajax({
	            url: 'business/hwcocVer!cocVerInfo.action',		           
	            data: params,
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#public_coc_dialog');
	            	type = "add";
					setDialogValue(dialogObj,model);
					dialogObj.find('#vercode').attr('readonly',true);
					dialogObj.data('title.dialog', '新增海外COC信息').dialog('open');
					dialogObj.find('#vercode').val('');
					getMaxVercode();
					printtypechange();
					setC106Value(dialogObj,model);
	            }
	        });
   			
   	    	
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
   	  		type = "add";
   	  		var dialogObj = $('#public_coc_dialog');
   	  		dialogObj.find('#vercode').attr('readonly',true);
   	  		dialogObj.data('title.dialog', '新增海外COC数据').dialog('open');
   	   	 }
		
	});


	$("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			var params = "slcx="+id;
   			jQuery.ajax({
	            url: 'business/hwcocVer!cocVerInfo.action',		           
	            data: params, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var content = data.json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_coc_dialog');
					if(carObj.state == null || carObj.state=="0" || carObj.state=="" ){
						setDialogValue(dialogObj,carObj);

		       	    	dialogObj.find('#slcx').attr('readonly',true);
		       	    	dialogObj.find('#vercode').attr('readonly',true);
		       	    	printtypechange(); //2014-12-30 lmc
		       	    	dialogObj.data('title.dialog', '修改海外COC信息').dialog('open');
		       	    	setC106Value(dialogObj,carObj);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var state = "";
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				state=$(this).parent().find("#state").val();
         		if(state=="1" || state=="9")	{
         			effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
         		}
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});
      	if(effIndex>0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效或是历史状态，不能删除！');
   	   		messageObj.dialog('open');   	   			
	   	}else if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#c1').val(id);
   	   	}
	
	});
	

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		var params = "slcx="+id;
		jQuery.ajax({
            url: 'business/hwcocVer!cocVerInfo.action',		           
            data: params, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	        	var dialogObj = $('#public_coc_dialog');

	        	type = "look";
				setDialogValue(dialogObj,carObj);
				$('input').attr("readonly", true);
				$('.ui-dialog-buttonpane button[value="保存"]').css("display","true");
				$('.ui-dialog-buttonpane button').eq(3).css("display","none");
					
				printtypechange();	
				dialogObj.data('title.dialog', '查看海外COC信息').dialog('open');
				setC106Value(dialogObj,carObj);
				
	        }
	    });

		return false;
	}
	
	$('#public_coc_dialog').find('#slcx').bind('keyup',function(event) { 
		if(type=="add")
			getMaxVercode();
    });
	
    function getMaxVercode()
    {
    	var obj = $('#public_coc_dialog').find('#slcx');
		if(obj.val().gblen()==18){
			jQuery.ajax({
	            url: 'business/hwcocVer!getMaxCocVercode.action',		           
	            data: {"slcx":obj.val()},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            		       
	            	var content = data.json;
		          	var dialogObj = $('#public_coc_dialog');
					dialogObj.find('#vercode').val(content);
	            }
	        });
		}  
    }

    $('#public_coc_dialog').find('#tempc6').bind('keyup',function(event) { 
        var value = $('#public_coc_dialog').find('#tempc6').val();
    	$('#public_coc_dialog').find('#c6').val(value);
    });
    $('#public_coc_dialog').find('#tempc6').bind('blur',function(event) { 
        var value = $('#public_coc_dialog').find('#tempc6').val();
    	$('#public_coc_dialog').find('#c6').val(value);
    });

	
	$('#export').click(function() {
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		
		location.href="hwcocVer!exportData.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory;  
	});

	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		var params = "slcx="+id;
    		jQuery.ajax({
                url: 'business/hwcocVer!cocVerInfo.action',		           
                data: params, 
    	        type: 'POST',
    	        beforeSend: function() {
    	        
    	        },
    	        error: function(request) {
    	            
    	        },
    	        success: function(data) {
    	            var content = json2Bean(data).json;
    	            var carObj = eval("("+content.toString()+")");

    	            if(carObj.state==null||carObj.state==""||carObj.state=="0"){
    	            	var dialogObj = $('#public_coc_dialog');
        	        	type = "effect";
        				setDialogValue(dialogObj,carObj);
        				dialogObj.find('#imp').css('display','none');
        				dialogObj.find('#tempc6').attr('size','20');
        				$('input').attr("readonly", true);
        				$('.ui-dialog-buttonpane button').eq(3).attr('value','生效');
        				printtypechange();
        				dialogObj.data('title.dialog', '生效COC信息').dialog('open');
        				setC106Value(dialogObj,carObj);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
    				
    	        }
    	    });
         	
   	   	}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);

				var dlgButton = $('.ui-dialog-buttonpane button');	

				var qc1 = $('#qc1').val();
				var qstate = $('#qstate').val();
				var qfactory = $('#qfactory').val();
				var currentPage=$('#currentPage_temp').val();
				
				if(type=="delete"){	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');			
					formObj[0].action = "business/hwcocVer!deleteCocs.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');	        
	            	formObj[0].action = "business/hwcocVer!effectCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage;
					formObj[0].submit();
							
				}else if(type=="imp"){
					$(this).dialog('close');
					//impPubData($("#public_coc_dialog").find("#tempc6").val());
					//type = "add";
				}
			}
		}
	});
	
	$("#public_coc_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 555,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'保存': function() {
				addOrUpdate(this);
			},
			//'打印': function() {
				//var slcx = $('#public_coc_dialog').find('#slcx').val();
	   	   		//var vercode = $('#public_coc_dialog').find('#vercode').val();
	   			//window.document.coc.printCocVersion(slcx,vercode);
			//},
			'下一页': function() {
				wizard();
			}
		},
		close: function() {
			clear($(this));
			updateTips($(this).find('#validateTips'),'');	
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$(this).find('#imp').attr('disabled',false);
			wizardModel = "one";
			wizard();
		}
		
	});

	function wizard(){
		if(wizardModel=="two"){
			
			$('#public_coc_dialog').dialog('option', 'buttons', { 
				"取消": function() {
					$(this).dialog('close');
				}, 
				"保存": function() { 
					addOrUpdate();
				}, 
				//'打印': function() {
				//	var c1 = $('#public_coc_dialog').find('#c1').val();
		   	  // 		var vercode = $('#public_coc_dialog').find('#vercode').val();
		   		//	window.document.coc.printCocVersion(c1,vercode);
				//},
				//"下一页": function() { 
			//		wizardModel = "three"; 
			//		wizard();
			//	},
				"上一页": function() {  
					wizardModel = "one";
					wizard();
				}
			} );
			$('#public_coc_dialog').find('#one').hide();
			//$('#public_coc_dialog').find('#two').show();
			showtables2();//2014-11-13 lmc
			//$('#public_coc_dialog').find('#three').hide();
			//hidetables3();
			
		}else if(wizardModel=="one"){
			$('#public_coc_dialog').dialog('option', 'buttons', {
				"取消": function() { 
					$(this).dialog('close');
				}, 
				"保存": function() {
					addOrUpdate();
				}, 
				"下一页": function() {  
					wizardModel = "two";
					wizard();
				}
			} );
			$('#public_coc_dialog').find('#one').show();
			//$('#public_coc_dialog').find('#two').hide();
			hidetables2();//2014-11-13 lmc
			//$('#public_coc_dialog').find('#three').hide();
		}
		if(type=="effect")
			$('.ui-dialog-buttonpane button').eq(3).attr('value','生效');
		
	}
	//2014-11-13 lmc 第二页的显示效果
	function showtables2(){
		var prt = $('#public_coc_dialog').find('#printtype').val();
		$('#public_coc_dialog').find('#two').show();
	}

	function hidetables2(){
		$('#public_coc_dialog').find('#two').hide();
	}
	

	function addOrUpdate(button){
		if(allFields==null){
			var parent = $('#public_coc_dialog');
			if(!parent.find("#printtype").val()){
				alert("请选择打印模板!");
				return false;
			}
			if(!parent.find("#printmodel").val()){
				alert("请选择模板用途!");
				return false;
			}
			allFields = $([]).add(parent.find('#slcx')).add(parent.find('#vercode'))
			.add(parent.find('#ggcx')).add(parent.find('#factory'))
			.add(parent.find('#printtype')).add(parent.find('#printmodel'))
			.add(parent.find('#state'))
			.add(parent.find('#c1')).add(parent.find('#c2')).add(parent.find('#c3'))
			.add(parent.find('#c4')).add(parent.find('#c5'))
			.add(parent.find('#c6')).add(parent.find('#c7')).add(parent.find('#c8'))
			.add(parent.find('#c9')).add(parent.find('#c10')).add(parent.find('#c11'))
			.add(parent.find('#c12')).add(parent.find('#c13')).add(parent.find('#c14'))
			.add(parent.find('#c15')).add(parent.find('#c16')).add(parent.find('#c17'))
			.add(parent.find('#c18')).add(parent.find('#c19')).add(parent.find('#c20'))
			.add(parent.find('#c21')).add(parent.find('#c22')).add(parent.find('#c23'))
			.add(parent.find('#c24'))
			.add(parent.find('#c25')).add(parent.find('#c26')).add(parent.find('#c27'))
			.add(parent.find('#c28')).add(parent.find('#c29')).add(parent.find('#c30'))
			.add(parent.find('#c31')).add(parent.find('#c32')).add(parent.find('#c33'))
			.add(parent.find('#c34')).add(parent.find('#c35')).add(parent.find('#c36'))
			.add(parent.find('#c37')).add(parent.find('#c38')).add(parent.find('#c39'))
			.add(parent.find('#c40')).add(parent.find('#c41')).add(parent.find('#c42'))
			.add(parent.find('#c43'))
			.add(parent.find('#c44')).add(parent.find('#c45')).add(parent.find('#c46'))
			.add(parent.find('#c47')).add(parent.find('#c48')).add(parent.find('#c49'))
			.add(parent.find('#c50')).add(parent.find('#c51')).add(parent.find('#c52'))
			.add(parent.find('#c53')).add(parent.find('#c54')).add(parent.find('#c55'))
			.add(parent.find('#c56')).add(parent.find('#c57')).add(parent.find('#c58'))
			.add(parent.find('#c59')).add(parent.find('#c60')).add(parent.find('#c61'))
			.add(parent.find('#c62')).add(parent.find('#c63')).add(parent.find('#c64'))
			.add(parent.find('#c65')).add(parent.find('#c66')).add(parent.find('#c67'))
			.add(parent.find('#c68'))
			.add(parent.find('#c69')).add(parent.find('#c70')).add(parent.find('#c71'))
			.add(parent.find('#c72')).add(parent.find('#c73')).add(parent.find('#c74'))
			.add(parent.find('#c75')).add(parent.find('#c76')).add(parent.find('#c77')).add(parent.find('#c78'))
			.add(parent.find('#c79')).add(parent.find('#c80')).add(parent.find('#c81'))
			.add(parent.find('#c82')).add(parent.find('#c83')).add(parent.find('#c84'))
			.add(parent.find('#c85')).add(parent.find('#c86')).add(parent.find('#c87'))
			.add(parent.find('#c88')).add(parent.find('#c89')).add(parent.find('#c90'))
			.add(parent.find('#c91')).add(parent.find('#c92')).add(parent.find('#c93'))
			.add(parent.find('#c94')).add(parent.find('#c95')).add(parent.find('#c96'))
			.add(parent.find('#c97')).add(parent.find('#c98')).add(parent.find('#c99'))
			.add(parent.find('#c100')).add(parent.find('#c101')).add(parent.find('#c102'))
			.add(parent.find('#c103')).add(parent.find('#c104')).add(parent.find('#c105'))
			.add(parent.find('#c106')).add(parent.find('#c107')).add(parent.find('#c108'))
			.add(parent.find('#c17u01')).add(parent.find('#c17u02')).add(parent.find('#c17u03')).add(parent.find('#c17u04')).add(parent.find('#c17u05')).add(parent.find('#c17u06'))
			.add(parent.find('#c18u01')).add(parent.find('#c18u02')).add(parent.find('#c18u03')).add(parent.find('#c18u04')).add(parent.find('#c18u05'))
			.add(parent.find('#c19u01')).add(parent.find('#c19u02')).add(parent.find('#c19u03')).add(parent.find('#c19u04'))
			.add(parent.find('#c20u01')).add(parent.find('#c20u02')).add(parent.find('#c20u03')).add(parent.find('#c20u04')).add(parent.find('#c20u05'))
			.add(parent.find('#c21u01')).add(parent.find('#c21u02')).add(parent.find('#c22u01')).add(parent.find('#c22u02'))
			.add(parent.find('#c24u01')).add(parent.find('#c24u02')).add(parent.find('#c25u01')).add(parent.find('#c25u02')).add(parent.find('#c25u03')).add(parent.find('#c25u04'))
			.add(parent.find('#c26u01')).add(parent.find('#c27u01')).add(parent.find('#c28u01')).add(parent.find('#c29u01')).add(parent.find('#c30u01'))
			.add(parent.find('#c31u01')).add(parent.find('#c32u01')).add(parent.find('#c33u01')).add(parent.find('#c35u01'))
			.add(parent.find('#c36u01')).add(parent.find('#c36u02')).add(parent.find('#c37u01')).add(parent.find('#c40u01'))
			.add(parent.find('#c41u01')).add(parent.find('#c42u01')).add(parent.find('#c43u01')).add(parent.find('#c44u01'))
			.add(parent.find('#c45u01')).add(parent.find('#c46u01')).add(parent.find('#c48u01')).add(parent.find('#c48u02'))
			.add(parent.find('#c49u01')).add(parent.find('#c50u01')).add(parent.find('#c50c1')).add(parent.find('#c52u01')).add(parent.find('#c52u02'))
			.add(parent.find('#c53u01')).add(parent.find('#c53u02')).add(parent.find('#c54u01')).add(parent.find('#c54u02'))
			.add(parent.find('#c55u01')).add(parent.find('#c56u01')).add(parent.find('#c56u02')).add(parent.find('#c59u01'))
			.add(parent.find('#c60u01')).add(parent.find('#c60u02'))
			.add(parent.find('#c61u01')).add(parent.find('#c61u02')).add(parent.find('#c61u03')).add(parent.find('#c61u04')).add(parent.find('#c61u05')).add(parent.find('#c61u06')).add(parent.find('#c61u07')).add(parent.find('#c61u08'))
			.add(parent.find('#c61u11')).add(parent.find('#c61u12')).add(parent.find('#c61u13')).add(parent.find('#c61u14')).add(parent.find('#c61u15')).add(parent.find('#c61u16')).add(parent.find('#c61u17')).add(parent.find('#c61u18'))
			.add(parent.find('#c65u01')).add(parent.find('#c65u02')).add(parent.find('#c65u03')).add(parent.find('#c65u04')).add(parent.find('#c65u05'))
			.add(parent.find('#c69u01')).add(parent.find('#c70u01')).add(parent.find('#c7101')).add(parent.find('#c72u01'))
			.add(parent.find('#c80u01')).add(parent.find('#c81c1')).add(parent.find('#c81c2')).add(parent.find('#c81c3')).add(parent.find('#c81c3u01')).add(parent.find('#c81c3u02')).add(parent.find('#c81c3u03')).add(parent.find('#c81c3u04')).add(parent.find('#c81c3u05')).add(parent.find('#c81c3u06')).add(parent.find('#c81c3u07')).add(parent.find('#c81c3u08')).add(parent.find('#c81c3u09'))
			.add(parent.find('#c81c4')).add(parent.find('#c81c5')).add(parent.find('#c81c5u01')).add(parent.find('#c81c5u02')).add(parent.find('#c81c5u03')).add(parent.find('#c81c6')).add(parent.find('#c81c6u01')).add(parent.find('#c81c6u02')).add(parent.find('#c81c6u03')).add(parent.find('#c81c7'))
			.add(parent.find('#c82u01')).add(parent.find('#c83u01')).add(parent.find('#c84u01')).add(parent.find('#c85u01'))
			.add(parent.find('#c86u01')).add(parent.find('#c87u01')).add(parent.find('#c88u01')).add(parent.find('#c89u01'))
			.add(parent.find('#c90u01')).add(parent.find('#c91u01')).add(parent.find('#c92u01')).add(parent.find('#c93u01'))
			.add(parent.find('#c94u01')).add(parent.find('#c95u01')).add(parent.find('#c96u01')).add(parent.find('#c98u01')).add(parent.find('#c99u01'))
			.add(parent.find('#c100u01'))
			.add(parent.find('#c101c1')).add(parent.find('#c101c2')).add(parent.find('#c101c3')).add(parent.find('#c101c4')).add(parent.find('#c101c5')).add(parent.find('#c101c6')).add(parent.find('#c101c7')).add(parent.find('#c101c8')).add(parent.find('#c101c9')).add(parent.find('#c101c10')).add(parent.find('#c101c11')).add(parent.find('#c101c12')).add(parent.find('#c101c13')).add(parent.find('#c101c14')).add(parent.find('#c101c15')).add(parent.find('#c101c16')).add(parent.find('#c101c17')).add(parent.find('#c101c18')).add(parent.find('#c101c19')).add(parent.find('#c101c20')).add(parent.find('#c101c21'))
			.add(parent.find('#c101u01')).add(parent.find('#c101u02')).add(parent.find('#c101u03')).add(parent.find('#c101u04')).add(parent.find('#c101u05')).add(parent.find('#c101u06')).add(parent.find('#c101u07')).add(parent.find('#c101u08')).add(parent.find('#c101u09')).add(parent.find('#c101u10')).add(parent.find('#c101u11')).add(parent.find('#c101u12')).add(parent.find('#c101u13')).add(parent.find('#c101u14')).add(parent.find('#c101u15')).add(parent.find('#c101u16')).add(parent.find('#c101u17')).add(parent.find('#c101u18')).add(parent.find('#c101u19')).add(parent.find('#c101u20')).add(parent.find('#c101u21'))
			.add(parent.find('#c102c1')).add(parent.find('#c102c2')).add(parent.find('#c102c3')).add(parent.find('#c102c4'))
			.add(parent.find('#c102u01')).add(parent.find('#c102u02')).add(parent.find('#c102u03')).add(parent.find('#c102u04'))
			.add(parent.find('#c107u01')).add(parent.find('#c108u01'))
			.add(parent.find('#c109u02')).add(parent.find('#c109u05')).add(parent.find('#c109u06')).add(parent.find('#c109u07')).add(parent.find('#c109u08')).add(parent.find('#c109u09')).add(parent.find('#c109u10'))
		}
		allFields.removeClass('ui-state-error');

		
		if(type=="effect"){
	        var dialogObj =$('#public_coc_dialog');
	        effectCoc(dialogObj);//生效COC
		}else if(validate('#public_coc_dialog')==true){
			var dlgButton = $('.ui-dialog-buttonpane button');//	
			dlgButton.attr('disabled', 'disabled');
	        dlgButton.addClass('ui-state-disabled');
	        
	        var dialogObj = $('#public_coc_dialog');
	        var qc1 = $('#qc1').val();
	        var m_c1=dialogObj.find('#createForm').find('#c1').val();
	        var qstate = $('#qstate').val();
	        var qfactory = $('#qfactory').val();
	        var currentPage=$('#currentPage_temp').val();
			//判断是否为典型车型，如果是就弹出对话框让用户决定是否连同更新该典型车型下的中性车型参数。
   			/*jQuery.ajax({
	            url: 'business/typicalityNeutral!findTypicalityCarModel.action',		           
	            data: {"dxcx":m_c1}, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var m_zxcx='';
	            	var content = json2Bean(data).json;
	            	
	            	if(content!=''){		            			            	
			            var carObj = eval("("+content.toString()+")"); 
		            	if(carObj!=null && carObj.zxcx!=null){
		            		if(window.confirm("是否同时对关联的中性车型"+ carObj.zxcx + "进行相应操作！")){
		            			m_zxcx=carObj.zxcx;
		            		}
		            	}
	            	}
	            	//alert(m_zxcx);
					if(type=="add"){
				        var dialog = $('#public_coc_dialog');
				        dialog.find('#createForm').find('#menuid').val(menuid);
						dialog.find('#createForm')[0].action="business/cocVer!addCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
						dialog.find('#createForm')[0].submit();
					}else if(type=="update"){
						var dialog = $('#public_coc_dialog');
					 	dialog.find('#createForm')[0].action="business/cocVer!updateCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
						dialog.find('#createForm')[0].submit();
					}	            
	            }
	        }); 
			*/
			if(type=="add"){
		        var dialog = $('#public_coc_dialog');
		        dialog.find('#createForm').find('#menuid').val(menuid);
				dialog.find('#createForm')[0].action="business/hwcocVer!addCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&currentPage="+currentPage;
				dialog.find('#createForm')[0].submit();
			}else if(type=="update"){
				var dialog = $('#public_coc_dialog');
			 	dialog.find('#createForm')[0].action="business/hwcocVer!updateCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&currentPage="+currentPage;
				dialog.find('#createForm')[0].submit();
			}
		}
	}

	function effectCoc(dialogObj){
		var id = dialogObj.find("#slcx").val()+","+dialogObj.find("#vercode").val();
 		var params = "slcx="+id;
 		var messageObj;
	   		jQuery.ajax({
        	url: 'business/hwcocVer!cocVerInfo.action',		           
        	data: params, 
        	type: 'POST',
        	beforeSend: function() {
        	
        	},
        	error: function(request) {
            
        	},
        	success: function(data) {
            	var content = json2Bean(data).json;
            	var carObj = eval("("+content.toString()+")");
            	info = "生产车型:"+dialogObj.find("#slcx").val()+" 版本号:"+dialogObj.find("#vercode").val();
				if(carObj.state==null||carObj.state=="0"||carObj.state==""){

					params = "c1="+$('#public_coc_dialog').find('#ggcx').val();			
					jQuery.ajax({
			            url: 'business/publicNoticeCarModelManager!isCarModelExistByC1.action',		           
			            data: params, 
				        type: 'POST',
			            success: function(data) {					       									
				            var dialog = $('#public_coc_dialog');
				            if(json2Bean(data).json=="false"){
								updateTips(dialog.find('#validateTips'),'车型代号:['+dialog.find('#ggcx').val()+'] 此公告车型不存在！');	
								//var messageObj = $('#message_dialog');
					   	   		//messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
					   	   		//messageObj.dialog('open');	
							}else{
								messageObj = $('#operate_dialog');
				   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共1条数据');
				   	   			messageObj.dialog('open');
				   	   			messageObj.find('#c1').val(id);
							}
			            }
			        });
	   	   			
				}else{
					messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
		   	   		messageObj.dialog('open');
				}
        	}
    	});
	}
	
	
	
    $("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 460,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var gk = $('#pubilc_notice_dialog').find('#gkxxList').val();
							
				if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
					var value = $(this).find('#pmodelList').val().split(',');
					impPubData(value[0],value[1]);
				}
				
				
				$(this).dialog('close');
			}
		}
	});		
	


	function setDialogValue(dialogObj,jsonObj){
		var printtype = jsonObj.printtype;
		dialogObj.find('#slcx').val(jsonObj.id.slcx);
		dialogObj.find('#ggcx').val(jsonObj.ggcx);
		
		dialogObj.find('#state').val(jsonObj.state);
		dialogObj.find('#factory').val(jsonObj.factory);
		dialogObj.find('#printtype').val(jsonObj.printtype);
		dialogObj.find('#printmodel').val(jsonObj.printmodel);
		
		
		if(type=="look"||type=="update"||type=="effect"){
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		}
		dialogObj.find('#c1').val(jsonObj.c1);
		dialogObj.find('#c2').val(jsonObj.c2);
		dialogObj.find('#c3').val(jsonObj.c3);
		dialogObj.find('#c4').val(jsonObj.c4);
		dialogObj.find('#c5').val(jsonObj.c5);
		dialogObj.find('#c6').val(jsonObj.c6);
		dialogObj.find('#c7').val(jsonObj.c7);
		dialogObj.find('#c8').val(jsonObj.c8);
		dialogObj.find('#c9').val(jsonObj.c9);
		dialogObj.find('#c10').val(jsonObj.c10);
		dialogObj.find('#c11').val(jsonObj.c11);
		dialogObj.find('#c12').val(jsonObj.c12);
		dialogObj.find('#c13').val(jsonObj.c13);
		dialogObj.find('#c14').val(jsonObj.c14);
		dialogObj.find('#c15').val(jsonObj.c15);
		dialogObj.find('#c16').val(jsonObj.c16);
		dialogObj.find('#c17').val(jsonObj.c17);
		dialogObj.find('#c18').val(jsonObj.c18);
		dialogObj.find('#c19').val(jsonObj.c19);
		dialogObj.find('#c20').val(jsonObj.c20);
		dialogObj.find('#c21').val(jsonObj.c21);
		dialogObj.find('#c22').val(jsonObj.c22);
		dialogObj.find('#c23').val(jsonObj.c23);
		dialogObj.find('#c24').val(jsonObj.c24);
		dialogObj.find('#c25').val(jsonObj.c25);
		dialogObj.find('#c26').val(jsonObj.c26);
		dialogObj.find('#c27').val(jsonObj.c27);
		dialogObj.find('#c28').val(jsonObj.c28);
		dialogObj.find('#c29').val(jsonObj.c29);
		dialogObj.find('#c30').val(jsonObj.c30);
		dialogObj.find('#c31').val(jsonObj.c31);
		dialogObj.find('#c32').val(jsonObj.c32);
		dialogObj.find('#c33').val(jsonObj.c33);
		dialogObj.find('#c34').val(jsonObj.c34);
		dialogObj.find('#c35').val(jsonObj.c35);
		dialogObj.find('#c36').val(jsonObj.c36);
		dialogObj.find('#c37').val(jsonObj.c37);
		dialogObj.find('#c38').val(jsonObj.c38);
		dialogObj.find('#c39').val(jsonObj.c39);
		dialogObj.find('#c40').val(jsonObj.c40);
		dialogObj.find('#c41').val(jsonObj.c41);
		dialogObj.find('#c42').val(jsonObj.c42);
		dialogObj.find('#c43').val(jsonObj.c43);
		dialogObj.find('#c44').val(jsonObj.c44);
		dialogObj.find('#c45').val(jsonObj.c45);
		dialogObj.find('#c46').val(jsonObj.c46);
		dialogObj.find('#c47').val(jsonObj.c47);
		dialogObj.find('#c48').val(jsonObj.c48);
		dialogObj.find('#c49').val(jsonObj.c49);
		dialogObj.find('#c50').val(jsonObj.c50);
		dialogObj.find('#c51').val(jsonObj.c51);
		dialogObj.find('#c52').val(jsonObj.c52);
		dialogObj.find('#c53').val(jsonObj.c53);
		dialogObj.find('#c54').val(jsonObj.c54);
		dialogObj.find('#c55').val(jsonObj.c55);
		dialogObj.find('#c56').val(jsonObj.c56);
		dialogObj.find('#c57').val(jsonObj.c57);
		dialogObj.find('#c58').val(jsonObj.c58);
		dialogObj.find('#c59').val(jsonObj.c59);
		dialogObj.find('#c60').val(jsonObj.c60);
		dialogObj.find('#c61').val(jsonObj.c61);
		dialogObj.find('#c62').val(jsonObj.c62);
		dialogObj.find('#c63').val(jsonObj.c63);
		dialogObj.find('#c64').val(jsonObj.c64);
		dialogObj.find('#c65').val(jsonObj.c65);
		dialogObj.find('#c66').val(jsonObj.c66);
		dialogObj.find('#c67').val(jsonObj.c67);
		dialogObj.find('#c68').val(jsonObj.c68);
		dialogObj.find('#c69').val(jsonObj.c69);
		dialogObj.find('#c70').val(jsonObj.c70);
		dialogObj.find('#c71').val(jsonObj.c71);
		dialogObj.find('#c72').val(jsonObj.c72);
		dialogObj.find('#c73').val(jsonObj.c73);
		dialogObj.find('#c74').val(jsonObj.c74);
		dialogObj.find('#c75').val(jsonObj.c75);
		dialogObj.find('#c76').val(jsonObj.c76);
		dialogObj.find('#c77').val(jsonObj.c77);
		dialogObj.find('#c78').val(jsonObj.c78);
		dialogObj.find('#c79').val(jsonObj.c79);
		dialogObj.find('#c80').val(jsonObj.c80);
		dialogObj.find('#c81').val(jsonObj.c81);
		dialogObj.find('#c82').val(jsonObj.c82);
		dialogObj.find('#c83').val(jsonObj.c83);
		dialogObj.find('#c84').val(jsonObj.c84);
		dialogObj.find('#c85').val(jsonObj.c85);
		dialogObj.find('#c86').val(jsonObj.c86);
		dialogObj.find('#c87').val(jsonObj.c87);
		dialogObj.find('#c88').val(jsonObj.c88);
		dialogObj.find('#c89').val(jsonObj.c89);
		dialogObj.find('#c90').val(jsonObj.c90);
		dialogObj.find('#c91').val(jsonObj.c91);
		dialogObj.find('#c92').val(jsonObj.c92);
		dialogObj.find('#c93').val(jsonObj.c93);
		dialogObj.find('#c94').val(jsonObj.c94);
		dialogObj.find('#c95').val(jsonObj.c95);
		dialogObj.find('#c96').val(jsonObj.c96);
		dialogObj.find('#c97').val(jsonObj.c97);
		dialogObj.find('#c98').val(jsonObj.c98);
		dialogObj.find('#c99').val(jsonObj.c99);
		dialogObj.find('#c100').val(jsonObj.c100);
		dialogObj.find('#c101').val(jsonObj.c101);
		dialogObj.find('#c102').val(jsonObj.c102);
		dialogObj.find('#c103').val(jsonObj.c103);
		dialogObj.find('#c104').val(jsonObj.c104);
		dialogObj.find('#c105').val(jsonObj.c105);
		// Handle c106 checkbox group
		//alert(jsonObj.c106);
		
		
		dialogObj.find('#c107').val(jsonObj.c107);
		dialogObj.find('#c108').val(jsonObj.c108);
		dialogObj.find('#c17u01').val(jsonObj.c17u01);dialogObj.find('#c17u02').val(jsonObj.c17u02);dialogObj.find('#c17u03').val(jsonObj.c17u03);dialogObj.find('#c17u04').val(jsonObj.c17u04);dialogObj.find('#c17u05').val(jsonObj.c17u05);dialogObj.find('#c17u06').val(jsonObj.c17u06);
		dialogObj.find('#c18u01').val(jsonObj.c18u01);dialogObj.find('#c18u02').val(jsonObj.c18u02);dialogObj.find('#c18u03').val(jsonObj.c18u03);dialogObj.find('#c18u04').val(jsonObj.c18u04);dialogObj.find('#c18u05').val(jsonObj.c18u05);
		dialogObj.find('#c19u01').val(jsonObj.c19u01);dialogObj.find('#c19u02').val(jsonObj.c19u02);dialogObj.find('#c19u03').val(jsonObj.c19u03);dialogObj.find('#c19u04').val(jsonObj.c19u04);
		dialogObj.find('#c20u01').val(jsonObj.c20u01);dialogObj.find('#c20u02').val(jsonObj.c20u02);dialogObj.find('#c20u03').val(jsonObj.c20u03);dialogObj.find('#c20u04').val(jsonObj.c20u04);dialogObj.find('#c20u05').val(jsonObj.c20u05);
		dialogObj.find('#c21u01').val(jsonObj.c21u01);dialogObj.find('#c21u02').val(jsonObj.c21u02);dialogObj.find('#c22u01').val(jsonObj.c22u01);dialogObj.find('#c22u02').val(jsonObj.c22u02);
		dialogObj.find('#c24u01').val(jsonObj.c24u01);dialogObj.find('#c24u02').val(jsonObj.c24u02);dialogObj.find('#c25u01').val(jsonObj.c25u01);dialogObj.find('#c25u02').val(jsonObj.c25u02);dialogObj.find('#c25u03').val(jsonObj.c25u03);dialogObj.find('#c25u04').val(jsonObj.c25u04);
		dialogObj.find('#c26u01').val(jsonObj.c26u01);dialogObj.find('#c27u01').val(jsonObj.c27u01);dialogObj.find('#c28u01').val(jsonObj.c28u01);dialogObj.find('#c29u01').val(jsonObj.c29u01);dialogObj.find('#c30u01').val(jsonObj.c30u01);
		dialogObj.find('#c31u01').val(jsonObj.c31u01);dialogObj.find('#c32u01').val(jsonObj.c32u01);dialogObj.find('#c33u01').val(jsonObj.c33u01);dialogObj.find('#c35u01').val(jsonObj.c35u01);
		dialogObj.find('#c36u01').val(jsonObj.c36u01);dialogObj.find('#c36u02').val(jsonObj.c36u02);dialogObj.find('#c37u01').val(jsonObj.c37u01);dialogObj.find('#c40u01').val(jsonObj.c40u01);
		dialogObj.find('#c41u01').val(jsonObj.c41u01);dialogObj.find('#c42u01').val(jsonObj.c42u01);dialogObj.find('#c43u01').val(jsonObj.c43u01);dialogObj.find('#c44u01').val(jsonObj.c44u01);
		dialogObj.find('#c45u01').val(jsonObj.c45u01);dialogObj.find('#c46u01').val(jsonObj.c46u01);dialogObj.find('#c48u01').val(jsonObj.c48u01);dialogObj.find('#c48u02').val(jsonObj.c48u02);
		dialogObj.find('#c49u01').val(jsonObj.c49u01);dialogObj.find('#c50u01').val(jsonObj.c50u01);dialogObj.find('#c50c1u01').val(jsonObj.c50c1u01);dialogObj.find('#c50c1').val(jsonObj.c50c1);dialogObj.find('#c52u01').val(jsonObj.c52u01);dialogObj.find('#c52u02').val(jsonObj.c52u02);
		dialogObj.find('#c53u01').val(jsonObj.c53u01);dialogObj.find('#c53u02').val(jsonObj.c53u02);dialogObj.find('#c54u01').val(jsonObj.c54u01);dialogObj.find('#c54u02').val(jsonObj.c54u02);
		dialogObj.find('#c55u01').val(jsonObj.c55u01);dialogObj.find('#c56u01').val(jsonObj.c56u01);dialogObj.find('#c56u02').val(jsonObj.c56u02);dialogObj.find('#c59u01').val(jsonObj.c59u01);
		dialogObj.find('#c60u01').val(jsonObj.c60u01);dialogObj.find('#c60u02').val(jsonObj.c60u02);
		dialogObj.find('#c61u01').val(jsonObj.c61u01);dialogObj.find('#c61u02').val(jsonObj.c61u02);dialogObj.find('#c61u03').val(jsonObj.c61u03);dialogObj.find('#c61u04').val(jsonObj.c61u04);dialogObj.find('#c61u05').val(jsonObj.c61u05);dialogObj.find('#c61u06').val(jsonObj.c61u06);dialogObj.find('#c61u07').val(jsonObj.c61u07);dialogObj.find('#c61u08').val(jsonObj.c61u08);
		dialogObj.find('#c61u11').val(jsonObj.c61u11);dialogObj.find('#c61u12').val(jsonObj.c61u12);dialogObj.find('#c61u13').val(jsonObj.c61u13);dialogObj.find('#c61u14').val(jsonObj.c61u14);dialogObj.find('#c61u15').val(jsonObj.c61u15);dialogObj.find('#c61u16').val(jsonObj.c61u16);dialogObj.find('#c61u17').val(jsonObj.c61u17);dialogObj.find('#c61u18').val(jsonObj.c61u18);
		dialogObj.find('#c65u01').val(jsonObj.c65u01);dialogObj.find('#c65u02').val(jsonObj.c65u02);dialogObj.find('#c65u03').val(jsonObj.c65u03);dialogObj.find('#c65u04').val(jsonObj.c65u04);dialogObj.find('#c65u05').val(jsonObj.c65u05);
		dialogObj.find('#c69u01').val(jsonObj.c69u01);dialogObj.find('#c70u01').val(jsonObj.c70u01);dialogObj.find('#c71u01').val(jsonObj.c71u01);dialogObj.find('#c7101').val(jsonObj.c7101);dialogObj.find('#c72u01').val(jsonObj.c72u01);
		dialogObj.find('#c80u01').val(jsonObj.c80u01);dialogObj.find('#c81c1').val(jsonObj.c81c1);dialogObj.find('#c81c2').val(jsonObj.c81c2);dialogObj.find('#c81c3').val(jsonObj.c81c3);dialogObj.find('#c81c3u01').val(jsonObj.c81c3u01);dialogObj.find('#c81c3u02').val(jsonObj.c81c3u02);dialogObj.find('#c81c3u03').val(jsonObj.c81c3u03);dialogObj.find('#c81c3u04').val(jsonObj.c81c3u04);dialogObj.find('#c81c3u05').val(jsonObj.c81c3u05);dialogObj.find('#c81c3u06').val(jsonObj.c81c3u06);dialogObj.find('#c81c3u07').val(jsonObj.c81c3u07);dialogObj.find('#c81c3u08').val(jsonObj.c81c3u08);dialogObj.find('#c81c3u09').val(jsonObj.c81c3u09);
		dialogObj.find('#c81c4').val(jsonObj.c81c4);dialogObj.find('#c81c5').val(jsonObj.c81c5);dialogObj.find('#c81c5u01').val(jsonObj.c81c5u01);dialogObj.find('#c81c5u02').val(jsonObj.c81c5u02);dialogObj.find('#c81c5u03').val(jsonObj.c81c5u03);dialogObj.find('#c81c6').val(jsonObj.c81c6);dialogObj.find('#c81c6u01').val(jsonObj.c81c6u01);dialogObj.find('#c81c6u02').val(jsonObj.c81c6u02);dialogObj.find('#c81c6u03').val(jsonObj.c81c6u03);dialogObj.find('#c81c7').val(jsonObj.c81c7);
		dialogObj.find('#c82u01').val(jsonObj.c82u01);dialogObj.find('#c83u01').val(jsonObj.c83u01);dialogObj.find('#c84u01').val(jsonObj.c84u01);dialogObj.find('#c85u01').val(jsonObj.c85u01);
		dialogObj.find('#c86u01').val(jsonObj.c86u01);dialogObj.find('#c87u01').val(jsonObj.c87u01);dialogObj.find('#c88u01').val(jsonObj.c88u01);dialogObj.find('#c89u01').val(jsonObj.c89u01);
		dialogObj.find('#c90u01').val(jsonObj.c90u01);dialogObj.find('#c91u01').val(jsonObj.c91u01);dialogObj.find('#c92u01').val(jsonObj.c92u01);dialogObj.find('#c93u01').val(jsonObj.c93u01);
		dialogObj.find('#c94u01').val(jsonObj.c94u01);dialogObj.find('#c95u01').val(jsonObj.c95u01);dialogObj.find('#c96u01').val(jsonObj.c96u01);dialogObj.find('#c98u01').val(jsonObj.c98u01);dialogObj.find('#c99u01').val(jsonObj.c99u01);
		dialogObj.find('#c100u01').val(jsonObj.c100u01);
		dialogObj.find('#c101c1').val(jsonObj.c101c1);dialogObj.find('#c101c2').val(jsonObj.c101c2);dialogObj.find('#c101c3').val(jsonObj.c101c3);dialogObj.find('#c101c4').val(jsonObj.c101c4);dialogObj.find('#c101c5').val(jsonObj.c101c5);dialogObj.find('#c101c6').val(jsonObj.c101c6);dialogObj.find('#c101c7').val(jsonObj.c101c7);dialogObj.find('#c101c8').val(jsonObj.c101c8);dialogObj.find('#c101c9').val(jsonObj.c101c9);dialogObj.find('#c101c10').val(jsonObj.c101c10);dialogObj.find('#c101c11').val(jsonObj.c101c11);dialogObj.find('#c101c12').val(jsonObj.c101c12);dialogObj.find('#c101c13').val(jsonObj.c101c13);dialogObj.find('#c101c14').val(jsonObj.c101c14);dialogObj.find('#c101c15').val(jsonObj.c101c15);dialogObj.find('#c101c16').val(jsonObj.c101c16);dialogObj.find('#c101c17').val(jsonObj.c101c17);dialogObj.find('#c101c18').val(jsonObj.c101c18);dialogObj.find('#c101c19').val(jsonObj.c101c19);dialogObj.find('#c101c20').val(jsonObj.c101c20);dialogObj.find('#c101c21').val(jsonObj.c101c21);
		dialogObj.find('#c101u01').val(jsonObj.c101u01);dialogObj.find('#c101u02').val(jsonObj.c101u02);dialogObj.find('#c101u03').val(jsonObj.c101u03);dialogObj.find('#c101u04').val(jsonObj.c101u04);dialogObj.find('#c101u05').val(jsonObj.c101u05);dialogObj.find('#c101u06').val(jsonObj.c101u06);dialogObj.find('#c101u07').val(jsonObj.c101u07);dialogObj.find('#c101u08').val(jsonObj.c101u08);dialogObj.find('#c101u09').val(jsonObj.c101u09);dialogObj.find('#c101u10').val(jsonObj.c101u10);dialogObj.find('#c101u11').val(jsonObj.c101u11);dialogObj.find('#c101u12').val(jsonObj.c101u12);dialogObj.find('#c101u13').val(jsonObj.c101u13);dialogObj.find('#c101u14').val(jsonObj.c101u14);dialogObj.find('#c101u15').val(jsonObj.c101u15);dialogObj.find('#c101u16').val(jsonObj.c101u16);dialogObj.find('#c101u17').val(jsonObj.c101u17);dialogObj.find('#c101u18').val(jsonObj.c101u18);dialogObj.find('#c101u19').val(jsonObj.c101u19);dialogObj.find('#c101u20').val(jsonObj.c101u20);dialogObj.find('#c101u21').val(jsonObj.c101u21);
		dialogObj.find('#c102c1').val(jsonObj.c102c1);dialogObj.find('#c102c2').val(jsonObj.c102c2);dialogObj.find('#c102c3').val(jsonObj.c102c3);dialogObj.find('#c102c4').val(jsonObj.c102c4);
		dialogObj.find('#c102u01').val(jsonObj.c102u01);dialogObj.find('#c102u02').val(jsonObj.c102u02);dialogObj.find('#c102u03').val(jsonObj.c102u03);dialogObj.find('#c102u04').val(jsonObj.c102u04);
		dialogObj.find('#c107u01').val(jsonObj.c107u01);dialogObj.find('#c108u01').val(jsonObj.c108u01);
		dialogObj.find('#c109u02').val(jsonObj.c109u02);dialogObj.find('#c109u05').val(jsonObj.c109u05);dialogObj.find('#c109u06').val(jsonObj.c109u06);dialogObj.find('#c109u07').val(jsonObj.c109u07);dialogObj.find('#c109u08').val(jsonObj.c109u08);dialogObj.find('#c109u09').val(jsonObj.c109u09);dialogObj.find('#c109u10').val(jsonObj.c109u10);
		dialogObj.find('#c17u01').val(jsonObj.c17u01);dialogObj.find('#c17u02').val(jsonObj.c17u02);dialogObj.find('#c17u03').val(jsonObj.c17u03);dialogObj.find('#c17u04').val(jsonObj.c17u04);dialogObj.find('#c17u05').val(jsonObj.c17u05);dialogObj.find('#c17u06').val(jsonObj.c17u06);
		dialogObj.find('#c18u01').val(jsonObj.c18u01);dialogObj.find('#c18u02').val(jsonObj.c18u02);dialogObj.find('#c18u03').val(jsonObj.c18u03);dialogObj.find('#c18u04').val(jsonObj.c18u04);dialogObj.find('#c18u05').val(jsonObj.c18u05);
		dialogObj.find('#c19u01').val(jsonObj.c19u01);dialogObj.find('#c19u02').val(jsonObj.c19u02);dialogObj.find('#c19u03').val(jsonObj.c19u03);dialogObj.find('#c19u04').val(jsonObj.c19u04);
		dialogObj.find('#c20u01').val(jsonObj.c20u01);dialogObj.find('#c20u02').val(jsonObj.c20u02);dialogObj.find('#c20u03').val(jsonObj.c20u03);dialogObj.find('#c20u04').val(jsonObj.c20u04);dialogObj.find('#c20u05').val(jsonObj.c20u05);
		dialogObj.find('#c21u01').val(jsonObj.c21u01);dialogObj.find('#c21u02').val(jsonObj.c21u02);
		dialogObj.find('#c22u01').val(jsonObj.c22u01);dialogObj.find('#c22u02').val(jsonObj.c22u02);
		dialogObj.find('#c24u01').val(jsonObj.c24u01);dialogObj.find('#c24u02').val(jsonObj.c24u02);
		dialogObj.find('#c25u01').val(jsonObj.c25u01);dialogObj.find('#c25u02').val(jsonObj.c25u02);dialogObj.find('#c25u03').val(jsonObj.c25u03);dialogObj.find('#c25u04').val(jsonObj.c25u04);
		dialogObj.find('#c26u01').val(jsonObj.c26u01);
		dialogObj.find('#c27u01').val(jsonObj.c27u01);
		dialogObj.find('#c28u01').val(jsonObj.c28u01);
		dialogObj.find('#c29u01').val(jsonObj.c29u01);
		dialogObj.find('#c30u01').val(jsonObj.c30u01);
		dialogObj.find('#c31u01').val(jsonObj.c31u01);
		dialogObj.find('#c32u01').val(jsonObj.c32u01);
		dialogObj.find('#c33u01').val(jsonObj.c33u01);
		dialogObj.find('#c35u01').val(jsonObj.c35u01);
		dialogObj.find('#c36u01').val(jsonObj.c36u01);dialogObj.find('#c36u02').val(jsonObj.c36u02);
		dialogObj.find('#c37u01').val(jsonObj.c37u01);
		dialogObj.find('#c40u01').val(jsonObj.c40u01);
		dialogObj.find('#c41u01').val(jsonObj.c41u01);
		dialogObj.find('#c42u01').val(jsonObj.c42u01);
		dialogObj.find('#c43u01').val(jsonObj.c43u01);
		dialogObj.find('#c44u01').val(jsonObj.c44u01);
		dialogObj.find('#c45u01').val(jsonObj.c45u01);
		dialogObj.find('#c48u01').val(jsonObj.c48u01);dialogObj.find('#c48u02').val(jsonObj.c48u02);
		dialogObj.find('#c49u01').val(jsonObj.c49u01);
		dialogObj.find('#c50u01').val(jsonObj.c50u01);dialogObj.find('#c50c1').val(jsonObj.c50c1);
		dialogObj.find('#c52u01').val(jsonObj.c52u01);dialogObj.find('#c52u02').val(jsonObj.c52u02);
		dialogObj.find('#c53u01').val(jsonObj.c53u01);dialogObj.find('#c53u02').val(jsonObj.c53u02);
		dialogObj.find('#c54u01').val(jsonObj.c54u01);dialogObj.find('#c54u02').val(jsonObj.c54u02);
		dialogObj.find('#c55u01').val(jsonObj.c55u01);
		dialogObj.find('#c56u01').val(jsonObj.c56u01);dialogObj.find('#c56u02').val(jsonObj.c56u02);
		dialogObj.find('#c59u01').val(jsonObj.c59u01);
		dialogObj.find('#c60u01').val(jsonObj.c60u01);dialogObj.find('#c60u02').val(jsonObj.c60u02);
		dialogObj.find('#c61u01').val(jsonObj.c61u01);dialogObj.find('#c61u02').val(jsonObj.c61u02);dialogObj.find('#c61u03').val(jsonObj.c61u03);dialogObj.find('#c61u04').val(jsonObj.c61u04);dialogObj.find('#c61u05').val(jsonObj.c61u05);dialogObj.find('#c61u06').val(jsonObj.c61u06);dialogObj.find('#c61u07').val(jsonObj.c61u07);dialogObj.find('#c61u08').val(jsonObj.c61u08);
		dialogObj.find('#c61u11').val(jsonObj.c61u11);dialogObj.find('#c61u12').val(jsonObj.c61u12);dialogObj.find('#c61u13').val(jsonObj.c61u13);dialogObj.find('#c61u14').val(jsonObj.c61u14);dialogObj.find('#c61u15').val(jsonObj.c61u15);dialogObj.find('#c61u16').val(jsonObj.c61u16);dialogObj.find('#c61u17').val(jsonObj.c61u17);dialogObj.find('#c61u18').val(jsonObj.c61u18);
		dialogObj.find('#c65u01').val(jsonObj.c65u01);dialogObj.find('#c65u02').val(jsonObj.c65u02);dialogObj.find('#c65u03').val(jsonObj.c65u03);dialogObj.find('#c65u04').val(jsonObj.c65u04);dialogObj.find('#c65u05').val(jsonObj.c65u05);
		dialogObj.find('#c69u01').val(jsonObj.c69u01);
		dialogObj.find('#c70u01').val(jsonObj.c70u01);
		dialogObj.find('#c71u01').val(jsonObj.c71u01);
		dialogObj.find('#c7101').val(jsonObj.c7101);
		dialogObj.find('#c72u01').val(jsonObj.c72u01);
		dialogObj.find('#c80u01').val(jsonObj.c80u01);
		dialogObj.find('#c81c1').val(jsonObj.c81c1);dialogObj.find('#c81c2').val(jsonObj.c81c2);dialogObj.find('#c81c3').val(jsonObj.c81c3);
		dialogObj.find('#c81c3u01').val(jsonObj.c81c3u01);dialogObj.find('#c81c3u02').val(jsonObj.c81c3u02);dialogObj.find('#c81c3u03').val(jsonObj.c81c3u03);dialogObj.find('#c81c3u04').val(jsonObj.c81c3u04);dialogObj.find('#c81c3u05').val(jsonObj.c81c3u05);dialogObj.find('#c81c3u06').val(jsonObj.c81c3u06);dialogObj.find('#c81c3u07').val(jsonObj.c81c3u07);dialogObj.find('#c81c3u08').val(jsonObj.c81c3u08);dialogObj.find('#c81c3u09').val(jsonObj.c81c3u09);
		dialogObj.find('#c81c4').val(jsonObj.c81c4);dialogObj.find('#c81c5').val(jsonObj.c81c5);
		dialogObj.find('#c81c5u01').val(jsonObj.c81c5u01);dialogObj.find('#c81c5u02').val(jsonObj.c81c5u02);dialogObj.find('#c81c5u03').val(jsonObj.c81c5u03);
		dialogObj.find('#c81c6').val(jsonObj.c81c6);
		dialogObj.find('#c81c6u01').val(jsonObj.c81c6u01);dialogObj.find('#c81c6u02').val(jsonObj.c81c6u02);dialogObj.find('#c81c6u03').val(jsonObj.c81c6u03);
		dialogObj.find('#c81c7').val(jsonObj.c81c7);
		dialogObj.find('#c82u01').val(jsonObj.c82u01);
		dialogObj.find('#c83u01').val(jsonObj.c83u01);
		dialogObj.find('#c84u01').val(jsonObj.c84u01);
		dialogObj.find('#c85u01').val(jsonObj.c85u01);
		dialogObj.find('#c86u01').val(jsonObj.c86u01);
		dialogObj.find('#c87u01').val(jsonObj.c87u01);
		dialogObj.find('#c88u01').val(jsonObj.c88u01);
		dialogObj.find('#c89u01').val(jsonObj.c89u01);
		dialogObj.find('#c90u01').val(jsonObj.c90u01);
		dialogObj.find('#c91u01').val(jsonObj.c91u01);
		dialogObj.find('#c92u01').val(jsonObj.c92u01);
		dialogObj.find('#c93u01').val(jsonObj.c93u01);
		dialogObj.find('#c94u01').val(jsonObj.c94u01);
		dialogObj.find('#c95u01').val(jsonObj.c95u01);
		dialogObj.find('#c96u01').val(jsonObj.c96u01);
		dialogObj.find('#c98u01').val(jsonObj.c98u01);
		dialogObj.find('#c99u01').val(jsonObj.c99u01);
		dialogObj.find('#c100u01').val(jsonObj.c100u01);
		dialogObj.find('#c101c1').val(jsonObj.c101c1);dialogObj.find('#c101c2').val(jsonObj.c101c2);dialogObj.find('#c101c3').val(jsonObj.c101c3);dialogObj.find('#c101c4').val(jsonObj.c101c4);dialogObj.find('#c101c5').val(jsonObj.c101c5);dialogObj.find('#c101c6').val(jsonObj.c101c6);dialogObj.find('#c101c7').val(jsonObj.c101c7);dialogObj.find('#c101c8').val(jsonObj.c101c8);dialogObj.find('#c101c9').val(jsonObj.c101c9);dialogObj.find('#c101c10').val(jsonObj.c101c10);dialogObj.find('#c101c11').val(jsonObj.c101c11);dialogObj.find('#c101c12').val(jsonObj.c101c12);dialogObj.find('#c101c13').val(jsonObj.c101c13);dialogObj.find('#c101c14').val(jsonObj.c101c14);dialogObj.find('#c101c15').val(jsonObj.c101c15);dialogObj.find('#c101c16').val(jsonObj.c101c16);dialogObj.find('#c101c17').val(jsonObj.c101c17);dialogObj.find('#c101c18').val(jsonObj.c101c18);dialogObj.find('#c101c19').val(jsonObj.c101c19);dialogObj.find('#c101c20').val(jsonObj.c101c20);dialogObj.find('#c101c21').val(jsonObj.c101c21);
		dialogObj.find('#c101u01').val(jsonObj.c101u01);dialogObj.find('#c101u02').val(jsonObj.c101u02);dialogObj.find('#c101u03').val(jsonObj.c101u03);dialogObj.find('#c101u04').val(jsonObj.c101u04);dialogObj.find('#c101u05').val(jsonObj.c101u05);dialogObj.find('#c101u06').val(jsonObj.c101u06);dialogObj.find('#c101u07').val(jsonObj.c101u07);dialogObj.find('#c101u08').val(jsonObj.c101u08);dialogObj.find('#c101u09').val(jsonObj.c101u09);dialogObj.find('#c101u10').val(jsonObj.c101u10);dialogObj.find('#c101u11').val(jsonObj.c101u11);dialogObj.find('#c101u12').val(jsonObj.c101u12);dialogObj.find('#c101u13').val(jsonObj.c101u13);dialogObj.find('#c101u14').val(jsonObj.c101u14);dialogObj.find('#c101u15').val(jsonObj.c101u15);dialogObj.find('#c101u16').val(jsonObj.c101u16);dialogObj.find('#c101u17').val(jsonObj.c101u17);dialogObj.find('#c101u18').val(jsonObj.c101u18);dialogObj.find('#c101u19').val(jsonObj.c101u19);dialogObj.find('#c101u20').val(jsonObj.c101u20);dialogObj.find('#c101u21').val(jsonObj.c101u21);
		dialogObj.find('#c102c1').val(jsonObj.c102c1);dialogObj.find('#c102c2').val(jsonObj.c102c2);dialogObj.find('#c102c3').val(jsonObj.c102c3);dialogObj.find('#c102c4').val(jsonObj.c102c4);
		dialogObj.find('#c102u01').val(jsonObj.c102u01);dialogObj.find('#c102u02').val(jsonObj.c102u02);dialogObj.find('#c102u03').val(jsonObj.c102u03);dialogObj.find('#c102u04').val(jsonObj.c102u04);
		dialogObj.find('#c107u01').val(jsonObj.c107u01);
		dialogObj.find('#c108u01').val(jsonObj.c108u01);
		dialogObj.find('#c109u02').val(jsonObj.c109u02);dialogObj.find('#c109u05').val(jsonObj.c109u05);dialogObj.find('#c109u06').val(jsonObj.c109u06);dialogObj.find('#c109u07').val(jsonObj.c109u07);dialogObj.find('#c109u08').val(jsonObj.c109u08);dialogObj.find('#c109u09').val(jsonObj.c109u09);dialogObj.find('#c109u10').val(jsonObj.c109u10);
	
	}
	
	//设置c106的值
	function setC106Value(dialogObj,jsonObj){
		if(!jsonObj.c106) {
			// 如果c106为空，清除所有选中状态
			dialogObj.find('input[name="c106"]').each(function() {
				this.checked = false;
			});
			return;
		}

		var c106Values = jsonObj.c106.split('/');
		// 处理所有c106复选框，不仅仅是已选中的
        dialogObj.find('input[name="c106"]').each(function() {
        	var checkbox = this;
        	var isChecked = false;

        	// 检查当前复选框的值是否在c106Values数组中
        	for(var i = 0; i < c106Values.length; i++) {
        		if(c106Values[i] === checkbox.value) {
        			isChecked = true;
        			break;
        		}
        	}

        	// 使用原生DOM方法设置选中状态，兼容IE
        	checkbox.checked = isChecked;
        });
    }

	function clear(dialogObj){
		dialogObj.find('input').attr('value','');
		$('input').attr("readonly", false);
		dialogObj.find('#imp').css('display','');
		dialogObj.find('#tempc6').attr('size','11');
		$('.ui-dialog-buttonpane button').eq(3).attr('value','保存');
		type = null;
	}

	function isNotNull(parent,id){
		var obj = $(parent).find('#'+id);
		var value=obj.val();
		if(value!=null && value!="")
			return true;
		return false;
	}

	//获取tableid下的所有input框的id
	function FF(tableid){
			var arr=[];
		    $('#public_coc_dialog').find('#'+tableid).find("input").each(function(){
		    	var id=$(this).attr('id');
		        if(id!='undefined'){
		        arr.push(id)};
		    });
		    return arr;   
	}
	
	//获取多个tableid下的所有input框的id
	function FFS(tableids){
    	var ids=[];
    	var str= new Array();   
		str=tableids.split(",");  
		all = str.length;
		for (i=0;i<str.length ;i++ )   
		{  
        	var inputid=FF(str[i]);
            if(inputid!=''){
              ids = ids.concat(inputid)
            }
			
        }
        return ids;
	}
	
	//获取多个tableid下的所有input框的id 减去对应的多个cid下的所有input框的id
	function FFSMU(tableids,cid){
    	var allids = FFS(tableids);
		var arr=[];
		$('#public_coc_dialog').find(cid).each(function(){
			var idid=$(this).find("input").attr('id');
        	if(idid!=undefined){
				arr.push(idid);
			} 
    	});		
		
		for (var i = 0; i < arr.length; i++) {
		    for (var j = 0; j < allids.length; j++) {
		     if (allids[ j ] == arr[ i ]) {
		    	 allids.splice(j, 1);
		      j = j - 1;
		     }
		    }
		   }

		return allids;
	}	
	
	function checkLengthWithoutNull(obj,min,max) {
		var value=obj.val();
		if(value==null || value=="")
			return false;
		if ( obj.val().gblen() > max || obj.val().gblen() < min ) {
			return false;
		} else {
			return true;
		}

	}

	function check(id){
		var dialog = $('#public_coc_dialog');
		var printtype = dialog.find('#c61').val();
		if(printtype=='3'){
			var trclass = dialog.find('#'+id).parent().parent().attr('class');
			if(trclass=='noev'){
				return false;
			}
		}else{
			var trclass = dialog.find('#'+id).parent().parent().attr('class');
			if(trclass=='nohev'){
				return false;
			}
		}
		return true;
	}

		//那些字段需要校验
		function isNeedValidate(parent, id) {
			if (check(id)) {
				return true;
			}
			return false;

		}
		//if(isNeedValue(obj,'M1')){alNeedValue(obj,'M1');return false;}
		//需要为对应字符串字段，如果不是则反馈true,进行后续的提示
		function isNeedValue(obj,value){
			var val = obj.val();
            if (val!= value) {
                return true;
            }
            return false;
		}
		function alNeedValue(obj,value){
            obj.addClass('ui-state-error');
            updateTips($('#public_coc_dialog').find('#validateTips'), obj.parent().prev().prev()
                    .find('p').text()
                    + '字段必须为' + value + '！');
            obj.focus();
        }
		//if(isNeedNA(obj)){alNeedNA(obj);return false;}
		//需要为N/A的字段，如果不为N/A则反馈true，进行后续的提示
		function isNeedNA(obj) {
            var val = obj.val();
            if (val!= "N/A") {
                return true;
            }
            return false;
        }
		function alNeedNA(obj) {
            obj.addClass('ui-state-error');
            updateTips($('#public_coc_dialog').find('#validateTips'), obj.parent().prev().prev()
                    .find('p').text()
                    + '字段必须为N/A！');
            obj.focus();
        }
		//if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
		//需要不为N/A的字段，如果为N/A则反馈true，进行后续的提示
		function isNeedNotNA(obj) {
			var val = obj.val();
            if (val== "N/A") {
                return true;
            }
            return false;
        }
		function alNeedNotNA(obj) {
            obj.addClass('ui-state-error');
            updateTips($('#public_coc_dialog').find('#validateTips'), obj.parent().prev().prev()
                    .find('p').text()
                    + '字段不能为N/A！');
            obj.focus();
        }
		

		function validate(parent) {
			var parentObj = $(parent);
			var tipsObj = parentObj.find('#validateTips');
			var printtype = parentObj.find('#printtype').val();
			var obj = parentObj.find('#slcx');
			var max = 18;
			if (isNeedValidate(parent, 'slcx') && !checkLengthWithoutNull(obj, 18, max)) {
				obj.addClass('ui-state-error');
				updateTips(tipsObj, obj.parent().prev()
						.find('p').text()
						+ '字段长度必须为' + max + '！');
				obj.focus();
				return false;
			}
			obj = parentObj.find('#vercode');
			max = 30;
			if (isNeedValidate(parent, 'vercode') && !checkLengthWithoutNull(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips(tipsObj, obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为30！');
				obj.focus();
				return false;
			}
			obj = parentObj.find('#ggcx');
			max = 20;
			if (isNeedValidate(parent, 'ggcx') && !checkLengthWithoutNull(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips(tipsObj, obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为20！');
				obj.focus();
				return false;
			}
			
			obj = parentObj.find('#printtype');
			max = 1;
			if (isNeedValidate(parent, 'printtype') && !checkLengthWithoutNull(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips(tipsObj, obj.parent().prev().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为1！');
				obj.focus();
				return false;
			}
			obj = parentObj.find('#printmodel');
			max = 1;
			if (isNeedValidate(parent, 'printmodel') && !checkLengthWithoutNull(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips(tipsObj, obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为1！');
				obj.focus();
				return false;
			}
			
			obj = parentObj.find('#c109u02');
			   max = 10;
			   if (isNeedValidate(parent, 'c109u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c109u05');
			   max = 10;
			   if (isNeedValidate(parent, 'c109u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c109u06');
			   max = 1;
			   if (isNeedValidate(parent, 'c109u06') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c109u07');
			   max = 3;
			   if (isNeedValidate(parent, 'c109u07') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c109u08');
			   max = 1;
			   if (isNeedValidate(parent, 'c109u08') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c109u09');
			   max = 1;
			   if (isNeedValidate(parent, 'c109u09') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c109u10');
			   max = 1;
			   if (isNeedValidate(parent, 'c109u10') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   
			obj = parentObj.find('#c1');
			   max = 500;
			   if (isNeedValidate(parent, 'c2') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c2');
			   max = 52;
			   if (isNeedValidate(parent, 'c2') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c3');
			   max = 15;
			   if (isNeedValidate(parent, 'c3') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c4');
			   max = 25;
			   if (isNeedValidate(parent, 'c4') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = parentObj.find('#c5');
			   max = 35;
			   if (isNeedValidate(parent, 'c5') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c6');
			   max = 50;
			   if (isNeedValidate(parent, 'c6') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c7');
			   max = 3;
			   if (isNeedValidate(parent, 'c7') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c9');
			   max = 100;
			   if (isNeedValidate(parent, 'c9') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c10');
			   max = 100;
			   if (isNeedValidate(parent, 'c10') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c11');
			   max = 100;
			   if (isNeedValidate(parent, 'c11') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c12');
			   max = 100;
			   if (isNeedValidate(parent, 'c12') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c13');
			   max = 100;
			   if (isNeedValidate(parent, 'c13') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c14');
			   max = 100;
			   if (isNeedValidate(parent, 'c14') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c15');
			   max = 100;
			   if (isNeedValidate(parent, 'c15') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c16');
			   max = 2;
			   if (isNeedValidate(parent, 'c16') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips(tipsObj, obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedValue(obj,'M1')){alNeedValue(obj,'M1');return false;}
			obj = $(parent).find('#c17');
			   max = 500;
			   if (isNeedValidate(parent, 'c17') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c17u01');
			   max = 2;
			   if (isNeedValidate(parent, 'c17u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c17u02');
			   max = 150;
			   if (isNeedValidate(parent, 'c17u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c17u03');
			   max = 150;
			   if (isNeedValidate(parent, 'c17u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c17u04');
			   max = 150;
			   if (isNeedValidate(parent, 'c17u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c17u05');
			   max = 80;
			   if (isNeedValidate(parent, 'c17u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c17u06');
			   max = 80;
			   if (isNeedValidate(parent, 'c17u06') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c18');
			   max = 500;
			   if (isNeedValidate(parent, 'c18') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c18u01');
			   max = 10;
			   if (isNeedValidate(parent, 'c18u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c18u02');
			   max = 10;
			   if (isNeedValidate(parent, 'c18u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c18u03');
			   max = 10;
			   if (isNeedValidate(parent, 'c18u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c18u04');
			   max = 10;
			   if (isNeedValidate(parent, 'c18u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   } 
			obj = $(parent).find('#c18u05');
			   max = 10;
			   if (isNeedValidate(parent, 'c18u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c19');
			   max = 500;
			   if (isNeedValidate(parent, 'c19') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c19u01');
			   max = 10;
			   if (isNeedValidate(parent, 'c19u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c19u02');
			   max = 10;
			   if (isNeedValidate(parent, 'c19u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c19u03');
			   max = 10;
			   if (isNeedValidate(parent, 'c19u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c19u04');
			   max = 10;
			   if (isNeedValidate(parent, 'c19u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c20');
			   max = 500;
			   if (isNeedValidate(parent, 'c20') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c20u01');
			   max = 150;
			   if (isNeedValidate(parent, 'c20u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c20u02');
			   max = 150;
			   if (isNeedValidate(parent, 'c20u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}  
			obj = $(parent).find('#c20u03');
			   max = 150;
			   if (isNeedValidate(parent, 'c20u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;} 
			obj = $(parent).find('#c20u04');
			   max = 80;
			   if (isNeedValidate(parent, 'c20u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c20u05');
			   max = 80;
			   if (isNeedValidate(parent, 'c20u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c21');
			   max = 500;
			   if (isNeedValidate(parent, 'c21') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c21u01');
			   max = 35;
			   if (isNeedValidate(parent, 'c21u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c21u02');
			   max = 10;
			   if (isNeedValidate(parent, 'c21u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,格式应为YYYY-MM-DD,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c22');
			   max = 80;
			   if (isNeedValidate(parent, 'c22') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c22u01');
			   max = 150;
			   if (isNeedValidate(parent, 'c22u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c22u02');
			   max = 80;
			   if (isNeedValidate(parent, 'c22u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c24');
			   max = 500;
			   if (isNeedValidate(parent, 'c24') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c24u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c24u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c24u02');
			   max = 1;
			   if (isNeedValidate(parent, 'c24u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c25');
			   max = 500;
			   if (isNeedValidate(parent, 'c25') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c25u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c25u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c25u02');
			   max = 1;
			   if (isNeedValidate(parent, 'c25u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c25u03');
			   max = 1;
			   if (isNeedValidate(parent, 'c25u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c25u04');
			   max = 3;
			   if (isNeedValidate(parent, 'c25u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c26');
			   max = 500;
			   if (isNeedValidate(parent, 'c26') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c26u01');
			   max = 3;
			   if (isNeedValidate(parent, 'c26u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c27');
			   max = 500;
			   if (isNeedValidate(parent, 'c27') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c27u01');
			   max = 5;
			   if (isNeedValidate(parent, 'c27u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c28');
			   max = 500;
			   if (isNeedValidate(parent, 'c28') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c28u01');
			   max = 5;
			   if (isNeedValidate(parent, 'c28u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c29');
			   max = 500;
			   if (isNeedValidate(parent, 'c29') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c29u01');
			   max = 4;
			   if (isNeedValidate(parent, 'c29u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c30');
			   max = 500;
			   if (isNeedValidate(parent, 'c30') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c30u01');
			   max = 4;
			   if (isNeedValidate(parent, 'c30u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c31');
			   max = 500;
			   if (isNeedValidate(parent, 'c31') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c31u01');
			   max = 4;
			   if (isNeedValidate(parent, 'c31u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c32');
			   max = 500;
			   if (isNeedValidate(parent, 'c32') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c32u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c32u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c33');
			   max = 500;
			   if (isNeedValidate(parent, 'c33') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c33u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c32u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			  
			obj = $(parent).find('#c35');
			   max = 500;
			   if (isNeedValidate(parent, 'c35') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c35u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c35u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c36');
			   max = 500;
			   if (isNeedValidate(parent, 'c36') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c36u01');
			   max = 4;
			   if (isNeedValidate(parent, 'c36u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c36u02');
			   max = 4;
			   if (isNeedValidate(parent, 'c36u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c37');
			   max = 500;
			   if (isNeedValidate(parent, 'c37') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c37u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c37u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c39');
			   max = 3;
			   if (isNeedValidate(parent, 'c39') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c40');
			   max = 500;
			   if (isNeedValidate(parent, 'c40') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c40u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c40u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c41');
			   max = 500;
			   if (isNeedValidate(parent, 'c41') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c41u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c41u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c42');
			   max = 500;
			   if (isNeedValidate(parent, 'c42') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c42u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c42u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c43');
			   max = 500;
			   if (isNeedValidate(parent, 'c43') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c43u01');
			   max = 150;
			   if (isNeedValidate(parent, 'c43u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c44');
			   max = 500;
			   if (isNeedValidate(parent, 'c44') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c44u01');
			   max = 150;
			   if (isNeedValidate(parent, 'c44u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c45');
			   max = 500;
			   if (isNeedValidate(parent, 'c45') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c45u01');
			   max = 2;
			   if (isNeedValidate(parent, 'c45u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c46');
			   max = 500;
			   if (isNeedValidate(parent, 'c46') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c46u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c46u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c47');
			   max = 10;
			   if (isNeedValidate(parent, 'c47') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(printtype == '3'){
			     if(isNeedNA(obj)){alNeedNA(obj);return false;}
			   }else{
				 if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			   }
			obj = $(parent).find('#c48');
			   max = 500;
			   if (isNeedValidate(parent, 'c48') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(printtype == '3'){
			     if(isNeedNA(obj)){alNeedNA(obj);return false;}
			   }
			obj = $(parent).find('#c48u01');
			   max = 2;
			   if (printtype != '3' && isNeedValidate(parent, 'c48u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c48u02');
			   max = 2;
			   if (printtype != '3' && isNeedValidate(parent, 'c48u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c49');
			   max = 500;
			   if (isNeedValidate(parent, 'c49') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(printtype == '3'){
			     if(isNeedNA(obj)){alNeedNA(obj);return false;}
			   }else{
				 if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			   }
			obj = $(parent).find('#c49u01');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c49u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c50');
			   max = 500;
			   if (isNeedValidate(parent, 'c50') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c50u01');
			   max = 10;
			   if (printtype != '3' && isNeedValidate(parent, 'c50u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c50c1');
			   max = 10;
			   if (printtype != '3' && isNeedValidate(parent, 'c50c1') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c50c1u01');
			   max = 1;
			   if (printtype != '3' && isNeedValidate(parent, 'c50c1u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			
			obj = $(parent).find('#c52');
			   max = 500;
			   if (isNeedValidate(parent, 'c52') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c52u01');
			   max = 62;
			   if (printtype != '3' && isNeedValidate(parent, 'c52u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c52u02');
			   max = 5;
			   if (printtype != '3' && isNeedValidate(parent, 'c52u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c53');
			   max = 500;
			   if (isNeedValidate(parent, 'c53') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c53u01');
			   max = 62;
			   if (isNeedValidate(parent, 'c53u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c53u02');
			   max = 62;
			   if (isNeedValidate(parent, 'c53u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c54');
			   max = 500;
			   if (isNeedValidate(parent, 'c54') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c54u01');
			   max = 62;
			   if (isNeedValidate(parent, 'c54u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c54u02');
			   max = 62;
			   if (isNeedValidate(parent, 'c54u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c55');
			   max = 30;
			   if (isNeedValidate(parent, 'c55') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c55u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c55u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c56');
			   max = 20;
			   if (isNeedValidate(parent, 'c56') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c56u01');
			   max = 3;
			   if (isNeedValidate(parent, 'c56u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if($(parent).find('#c56').val()=='N/A') $("#c56u01").val('N/A');
			obj = $(parent).find('#c56u02');
			   max = 106;
			   if (isNeedValidate(parent, 'c56u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if($(parent).find('#c56').val()=='N/A') $("#c56u01").val('N/A');
			obj = $(parent).find('#c57');
			   max = 75;
			   if (isNeedValidate(parent, 'c57') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c58');
			   max = 2;
			   if (isNeedValidate(parent, 'c58') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c59');
			   max = 500;
			   if (isNeedValidate(parent, 'c59') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c59u01');
			   max = 52;
			   if (isNeedValidate(parent, 'c59u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c60');
			   max = 500;
			   if (isNeedValidate(parent, 'c60') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c60u01');
			   max = 4;
			   if (isNeedValidate(parent, 'c60u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c60u02');
			   max = 4;
			   if (isNeedValidate(parent, 'c60u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61');
			   max = 500;
			   if (isNeedValidate(parent, 'c61') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u01');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u02');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u03');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u04');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u05');
			   max = 10;
			   if (isNeedValidate(parent, 'c61u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u06');
			   max = 1;
			   if (isNeedValidate(parent, 'c61u06') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u07');
			   max = 1;
			   if (isNeedValidate(parent, 'c61u07') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u08');
			   max = 2;
			   if (isNeedValidate(parent, 'c61u08') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u11');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u11') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u12');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u12') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u13');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u13') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u14');
			   max = 20;
			   if (isNeedValidate(parent, 'c61u14') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u15');
			   max = 10;
			   if (isNeedValidate(parent, 'c61u15') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u16');
			   max = 1;
			   if (isNeedValidate(parent, 'c61u16') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u17');
			   max = 1;
			   if (isNeedValidate(parent, 'c61u17') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c61u18');
			   max = 2;
			   if (isNeedValidate(parent, 'c61u18') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c62');
			   max = 3;
			   if (isNeedValidate(parent, 'c62') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c63');
			   max = 2;
			   if (isNeedValidate(parent, 'c63') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c65');
			   max = 500;
			   if (isNeedValidate(parent, 'c65') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c65u01');
			   max = 2;
			   if (isNeedValidate(parent, 'c65u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c65u02');
			   max = 2;
			   if (isNeedValidate(parent, 'c65u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c65u03');
			   max = 2;
			   if (isNeedValidate(parent, 'c65u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c65u04');
			   max = 2;
			   if (isNeedValidate(parent, 'c65u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c65u05');
			   max = 2;
			   if (isNeedValidate(parent, 'c65u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c66');
			   max = 3;
			   if (isNeedValidate(parent, 'c66') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			
			obj = $(parent).find('#c67');
			   max = 3;
			   if (isNeedValidate(parent, 'c67') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c68');
			   max = 3;
			   if (isNeedValidate(parent, 'c68') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c69');
			   max = 52;
			   if (isNeedValidate(parent, 'c69') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c69u01');
			   max = 5;
			   if (isNeedValidate(parent, 'c69u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c70');
			   max = 500;
			   if (isNeedValidate(parent, 'c70') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c70u01');
			   max = 52;
			   if (isNeedValidate(parent, 'c70u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c71');
			   max = 500;
			   if (isNeedValidate(parent, 'c71') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c71u01');
			   max = 10;
			   if (isNeedValidate(parent, 'c71u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c72');
			   max = 500;
			   if (isNeedValidate(parent, 'c72') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c72u01');
			   max = 6;
			   if (isNeedValidate(parent, 'c72u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c73');
			   max = 500;
			   if (isNeedValidate(parent, 'c73') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c74');
			   max = 500;
			   if (isNeedValidate(parent, 'c74') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c75');
			   max = 96;
			   if (isNeedValidate(parent, 'c75') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c76');
			   max = 96;
			   if (isNeedValidate(parent, 'c76') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c77');
			   max = 96;
			   if (isNeedValidate(parent, 'c77') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNotNA(obj)){alNeedNotNA(obj);return false;}
			obj = $(parent).find('#c78');
			   max = 10;
			   if (isNeedValidate(parent, 'c78') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c79');
			   max = 10;
			   if (isNeedValidate(parent, 'c79') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			obj = $(parent).find('#c80');
			   max = 500;
			   if (isNeedValidate(parent, 'c80') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c80u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c80u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c1');
			   max = 35;
			   if (isNeedValidate(parent, 'c81c1') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c2');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c2') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u01');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u02');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u03');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u04');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u05');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u06');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u06') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u07');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u07') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u08');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u08') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c3u09');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c3u09') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c4');
			   max = 95;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c4') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(printtype != '3'){
				   if(isNeedNA(obj)){alNeedNA(obj);return false;}
			   }
			   
			obj = $(parent).find('#c81c5');
			   max = 95;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c5') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c5u01');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c5u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c5u02');
			   max = 95;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c5u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c5u03');
			   max = 2;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c5u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c6');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c6') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c6u01');
			   max = 82;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c6u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c6u02');
			   max = 95;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c6u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c6u03');
			   max = 2;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c6u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c81c7');
			   max = 3;
			   if (printtype != '3' && isNeedValidate(parent, 'c81c7') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }

			obj = $(parent).find('#c82');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c82') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c82u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c82u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   obj = $(parent).find('#c83');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c83') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c83u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c83u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c84');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c84') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c84u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c84u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c85');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c85') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c85u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c85u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c86');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c86') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c86u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c86u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c87');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c87') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c87u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c87u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c88');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c88') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c88u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c88u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c89');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c89') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c89u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c89u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c90');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c90') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c90u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c90u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c91');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c91') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c91u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c91u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   obj = $(parent).find('#c92');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c92') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c92u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c92u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c93');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c93') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c93u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c93u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c94');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c94') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c94u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c94u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c95');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c95') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c95u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c95u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c96');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c96') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c96u01');
			   max = 52;
			   if (printtype == '3' && isNeedValidate(parent, 'c96u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			
			obj = $(parent).find('#c97');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c97') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c98');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c98') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c98u01');
			   max = 2;
			   if (printtype == '3' && isNeedValidate(parent, 'c98u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c99');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c99') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c99u01');
			   max = 2;
			   if (printtype == '3' && isNeedValidate(parent, 'c99u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c100');
			   max = 500;
			   if (isNeedValidate(parent, 'c100') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c100u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c100u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c101') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(printtype == '3'){
			     if(isNeedNA(obj)){alNeedNA(obj);return false;}
			   }else{
				 $(parent).find('#c101').val("");
			   }
			obj = $(parent).find('#c101c1');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c1') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u01');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c2');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c2') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u02');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   } 
			obj = $(parent).find('#c101c3');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c3') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u03');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c4');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c4') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u04');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c5');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c5') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u05');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u05') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c6');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c6') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u06');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u06') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c7');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c7') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u07');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u07') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c8');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c8') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u08');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u08') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c9');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c9') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u09');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u09') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c10');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c10') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u10');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u10') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c11');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c11') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u11');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u11') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c12');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c12') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u12');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u12') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c13');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c13') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u13');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u13') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c14');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c14') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u14');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u14') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c15');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c15') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u15');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u15') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c16');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c16') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u16');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u16') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   obj = $(parent).find('#c101c17');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c17') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u17');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u17') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c18');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c18') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u18');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u18') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c19');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c19') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u19');
			   max = 52;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u19') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c20');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c20') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u20');
			   max = 53;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u20') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101c21');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c101c21') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c101u21');
			   max = 72;
			   if (printtype != '3' && isNeedValidate(parent, 'c101u21') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102');
			   max = 500;
			   if (printtype == '3' && isNeedValidate(parent, 'c102') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			   if(printtype == '3'){
			     if(isNeedNA(obj)){alNeedNA(obj);return false;}
			   }else{
				 $(parent).find('#c102').val("");
			   }
			obj = $(parent).find('#c102c1');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c102c1') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102u01');
			   max = 5;
			   if (printtype != '3' && isNeedValidate(parent, 'c102u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102c2');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c102c2') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102u02');
			   max = 5;
			   if (printtype != '3' && isNeedValidate(parent, 'c102u02') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102c3');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c102c3') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102u03');
			   max = 5;
			   if (printtype != '3' && isNeedValidate(parent, 'c102u03') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102c4');
			   max = 500;
			   if (printtype != '3' && isNeedValidate(parent, 'c102c4') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c102u04');
			   max = 5;
			   if (printtype != '3' && isNeedValidate(parent, 'c102u04') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }

			obj = $(parent).find('#c103');
			   max = 500;
			   if (isNeedValidate(parent, 'c103') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c104');
			   max = 500;
			   if (isNeedValidate(parent, 'c104') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c105');
			   max = 500;
			   if (isNeedValidate(parent, 'c105') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c107');
			   max = 500;
			   if (isNeedValidate(parent, 'c107') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c107u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c107u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c108');
			   max = 500;
			   if (isNeedValidate(parent, 'c108') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }
			obj = $(parent).find('#c108u01');
			   max = 1;
			   if (isNeedValidate(parent, 'c108u01') && !checkLengthWithoutNull(obj, 1, max)) {
			    obj.addClass('ui-state-error');
			    updateTips($(parent).find('#validateTips'), obj.parent().prev().prev()
			      .find('p').text()
			      + '字段不能为空,最大长度为'+max+'！');
			    obj.focus();
			    return false;
			   }

			return true;
		}


		$('#jump').bind(
				'keyup',
				function(event) {
					var obj = $('#jump');
					if (!checkRegexp(obj, /^([0-9])+$/)) {
						obj.val("");
						return;
					}
					$(this).blur(
							function() {
								var qc1 = $('#qc1').val();
								var qstate = $('#qstate').val();
								location.href = "hwcocVer.action?currentPage="
										+ $('#jump').val() + "&qc1="
										+ encodeURI(encodeURI(qc1))
										+ "&qstate=" + qstate + "&qfactory="
										+ qfactory + "&menuid=" + menuid;
							});
				});

		$(".jumpPage").each(
				function(i) {
					$(this).click(
							function() {
								var qc1 = $('#qc1').val();
								var qstate = $('#qstate').val();
								var qfactory = $('#qfactory').val();

								location.href = $(this).attr('value') + "&qc1="
										+ encodeURI(encodeURI(qc1))
										+ "&qstate=" + qstate + "&qfactory="
										+ qfactory + "&menuid=" + menuid;
							});
				});

		$("#print").click(
				function() {
					var id = "";
					var index = 0;
					var messageObj = null;
					var checkedObj = $('#users-contain').find(
							"[name='checkPK'][@checked]");
					checkedObj.each(function() {
						if (this.checked == true) {
							index++;
							id = this.value;
						}
					});

					if (index == 1) {
						var c1 = id.split(",")[0];
						var vercode = id.split(",")[1];
						//alert(window.document);
						window.document.coc.printHWCocVersion(c1, vercode);
					} else if (index < 1) {
						messageObj = $('#message_dialog');
						messageObj.find('#message').text('警告:请选要打印的数据！');
						messageObj.dialog('open');
					} else if (index > 1) {
						messageObj = $('#message_dialog');
						messageObj.find('#message').text('警告:一次只能打印一条数据！');
						messageObj.dialog('open');
					}
				});

		$(document).ready(function() {
			$("#users").find("tr").mouseover(function() {
				$(this).addClass("over");
			}).mouseout(function() {
				//给这行添加class值为over，并且当鼠标一出该行时执行函数
				$(this).removeClass("over");
			}) //移除该行的class
		});
	});
	function printtypechange() {
		var dailog = $("#public_coc_dialog");
		var printtype = dailog.find("#printtype").val();
		var printmodel = dailog.find("#printmodel").val();
		
		if(printtype=="3"){
			//显示 class="nohev"
			dailog.find(".nohev").show();
			//隐藏 class="noev" 并将其内的input置为空
			dailog.find(".noev").hide();
			dailog.find(".noev").find("input").val("");
		}else if(printtype=="5"){
			//显示 class="noev"
			dailog.find(".noev").show();
			//隐藏 class="nohev" 并将其内的input置为空
			dailog.find(".nohev").hide();
			dailog.find(".nohev").find("input").val("");
        }
	}


function showInfo(c1,vercode,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
	  		
			<tr>
				<td align="left" colspan="6"><p>生产车型:<input type="text" id="qc1" name='qc1' class="text ui-widget-content " size="18" <s:if test="#request.qc1!=null"> value="<s:property value="#request.qc1" />"</s:if> />	
				  状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 
				  工厂:<s:select name="qfactory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.qfactory"></s:select>
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr>
				<td width="80%"></td>
			  	<td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  	<td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  	<td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
  			  	<td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
  			  	<td width="60" align="right"><button id="print" class="ui-button ui-state-default ui-corner-all">打印</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="6%">选择</th> 
			    <th width="10%">生产车型</th>
			    <th width="10%">公告车型</th>	
				<th width="8%">状态</th>
				<th width="10%">版本</th>
				<th width="10%">创建人</th>
				<th width="18%">创建时间</th>
				<th width="18%">生效时间</th>
				<th width="6%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.publicNoticePageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td>
			  			<input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.slcx" />,<s:property value="id.vercode" />' >
			  			<input type="hidden" name="state" id="state" value='<s:property value="state"/>'>
			  		</td>
			  		<td><s:property value="id.slcx" /></td>
   		      		<td><s:property value="ggcx" /></td>				
			  		<td>
			  			<s:if test="state==1">生效</s:if>
			  			<s:else>
			  				<s:if test="state==9">历史</s:if>
			  				<s:else>未生效</s:else>
						</s:else>
			  		</td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="creator" /></td>		
			  		<td><s:date name="createdate" format="yyyy-MM-dd HH:mm:ss"/></td>	
			  		<td><s:property value="effecttime" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.slcx" />,<s:property value="id.vercode" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.publicNoticePage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="hwcocVer.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="hwcocVer.action?currentPage=<s:property value="#request.publicNoticePage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage>=#request.publicNoticePage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="chwocVer.action?currentPage=<s:property value="#request.publicNoticePage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="hwcocVer.action?currentPage=<s:property value="#request.publicNoticePage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.publicNoticePage.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.publicNoticePage.currentPage" />/总页数 <s:property value="#request.publicNoticePage.maxPage" /> 总记录数 <s:property value="#request.publicNoticePage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="public_coc_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" >
	  	<table id="one" width="100%" border="0">
	
	  		<tr><td><label><P><b>基础信息设置</b></label></td></tr>
			<tr>			  		
				<td><label><P>生产车型</label></td>
				<td  colspan="2"><input type="text" id="slcx" name="slcx" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td ><label><P>版本号</label></td>
				<td  colspan="2"><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="30" /></td>
			</tr>
			<tr>			  		
				<td><label><P>公告车型</label></td>
				<td colspan="2"><input type="text" id="ggcx" name="ggcx" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>工厂</label></td>
				<td colspan="2"><s:select name="factory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value=""></s:select></td>	

				</tr>
	  		<tr>		
	  			<td ><label><p>打印模板类型</label></td>
				<td  colspan="2">
				<select id="printtype" name="printtype"  onchange="printtypechange()">
					<option value="">请选择</option>
					<option value="3">纯电动</option>
					<option value="5">插电混动</option>
				</select>
				</td>
				<td><label><p>模板用途</label></td>
				<td  colspan="2">
							<select id="printmodel" name="printmodel"  onchange="printtypechange()">
							<option value="">请选择</option>
							<option value="1">小批量</option>
							<option value="2">量产</option>
							</select>
				</td>	
	  		</tr>
	  		
	  		<tr><td><label><P><b>XML信息设置</b></label></td></tr>
	  		<tr>			  		
	  			<td  colspan="2"><label><P>报文格式版本IviVersionNumberXsd</label></td>
				<td  ><label><P>上传</label></td>
				<td colspan="3"><input type="text" id="c109u02" name="c109u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>发证国家编码TypeApprovalCountry</label></td>
				<td><label><P>上传</label></td>
				<td colspan="3"><input type="text" id="c109u05" name="c109u05" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>完工度StageOfCompletion</label></td>
				<td><label><P>上传</label></td>
				<td colspan="3">
					<select id="c109u06" name="c109u06">
						<option value="">请选择</option>
						<option value="C">C</option>
						<option value="I">I</option>
						<option value="V">V</option>
					</select>
				</td>
	  		</tr>
 
	  		<tr>			  		
	  			<td colspan="2"><label><P>型式实验TypeApprovalType</label></td>
				<td><label><P>上传</label></td>
				<td colspan="3">
					<select id="c109u07" name="c109u07">
						<option value="">请选择</option>
						<option value="NAT">NAT</option>
						<option value="NKS">NKS</option>
						<option value="KS">KS</option>
						<option value="EC">EC</option>
						<option value="IND">IND</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>左舵右舵RightLeftHandTraffic</label></td>
				<td><label><P>上传</label></td>
				<td colspan="3">
					<select id="c109u08" name="c109u08">
						<option value="">请选择</option>
						<option value="L">L</option>
						<option value="R">R</option>
						<option value="B">B</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>时速表单位MetricImperialSpeedometer</label></td>
				<td><label><P>上传</label></td>
				<td colspan="3">
					<select id="c109u09" name="c109u09">
						<option value="">请选择</option>
                        <option value="I">I</option>
                        <option value="M">M</option>
                        <option value="B">B</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>里程表单位MetricImperialOdometer</label></td>
				<td><label><P>上传</label></td>
				<td colspan="3">
					<select id="c109u10" name="c109u10">
						<option value="">请选择</option>
                        <option value="I">I</option>
                        <option value="M">M</option>
                        <option value="B">B</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr><td><label><P><b>PART1</b></label></td></tr>
	  		<tr>			  		
	  			<td  colspan="2" ><label><P>The undersigned</label></td>
	  			<td ><label><P>打印</label></td>
				<td colspan="3"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		<tr>
				<td   colspan="2"  ><label><P>0.1. Make (Trade name of manufacturer)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="3"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td   colspan="2" ><label><P>0.2. Type</label></td>
	  			<td><label><P>打印及上传</label></td>
				<td colspan="3"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
			<tr>	
				<td   colspan="2" ><label><P>Variant</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="3"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2" ><label><P>Version</label></td>
	  			<td><label><P>打印及上传</label></td>
				<td colspan="3"><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		<tr>
				<td colspan="2"><label><P>0.2.1. Commercial name(s)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>Allowed Parameter Values for multistage type approval to use the base vehicle emission values (insert range where applicable)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>

	
	  		<tr>			  		
	  			<td colspan="2"><label><P>0.2.3.1. Interpolation family’s identifier</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		<tr>
				<td colspan="2"><label><P>0.2.3.2. ATCT family’s identifier</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>0.2.3.3. PEMS family’s identifier</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		<tr>
				<td colspan="2"><label><P>0.2.3.4. Roadload family’s identifier</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c12" name="c12" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>0.2.3.5. Roadload Matrix family’s identifier (if applicable)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c13" name="c13" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		<tr>
				<td colspan="2"><label><P>0.2.3.6. Periodic regeneration family’s identifier</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c14" name="c14" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>0.2.3.7. Evaporative test family’s identifier</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c15" name="c15" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		<tr>
				<td colspan="2"><label><P>0.4. Vehicle category</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c16" name="c16" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>0.5. Company name and address of manufacture</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c17" name="c17" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerStageNumber</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c17u01" name="c17u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerName</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c17u02" name="c17u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerAddressLine1</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c17u03" name="c17u03" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerAddressLine3</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c17u04" name="c17u04" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerPlaceOfResidence</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c17u05" name="c17u05" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerCountryOfResidence</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c17u06" name="c17u06" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  	
	  		<tr>
				<td colspan="2"><label><P>0.6. Location and method of attachment of the statutory plates</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c18" name="c18" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsSubject</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c18u01" name="c18u01">
						<option value="">请选择</option>
						<option value="VIN">VIN</option>
						<option value="STAT">STAT</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsVehiclePart</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c18u02" name="c18u02">
						<option value="">请选择</option>
						<option value="ENGCT">ENGCT</option>
						<option value="PASCT">PASCT</option>
						<option value="LUGCT">LUGCT</option>
						<option value="INSPL">INSPL</option>
						<option value="DRAWB">DRAWB</option>
						<option value="CHASS">CHASS</option>
						<option value="APILR">APILR</option>
						<option value="BPILR">BPILR</option>
						<option value="CPILR">CPILR</option>
						<option value="DPILR">DPILR</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsVehiclePartSide</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c18u03" name="c18u03">
						<option value="">请选择</option>
						<option value="FRONT">FRONT</option>
						<option value="LEFT">LEFT</option>
						<option value="RIGHT">RIGHT</option>
						<option value="BACK">BACK</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsVehiclePartSideSection</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c18u04" name="c18u04">
						<option value="">请选择</option>
						<option value="LEFT">LEFT</option>
						<option value="MIDLE">MIDLE</option>
						<option value="RIGHT">RIGHT</option>
						<option value="FRONT">FRONT</option>
						<option value="BACK">BACK</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>MethodAttachmentStatutoryPlate</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c18u05" name="c18u05">
						<option value="">请选择</option>
						<option value="A1">A1</option>
						<option value="A2">A2</option>
						<option value="A3">A3</option>
						<option value="A4">A4</option>
					</select>
				</td>
	  		</tr>
	  		
			<tr>			  		
				<td colspan="2"><label><P>Location of the vehicle identification number</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
			<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsSubject</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c19u01" name="c19u01">
						<option value="">请选择</option>
						<option value="VIN">VIN</option>
						<option value="STAT">STAT</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsVehiclePart</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c19u02" name="c19u02">
						<option value="">请选择</option>
						<option value="ENGCT">ENGCT</option>
						<option value="PASCT">PASCT</option>
						<option value="LUGCT">LUGCT</option>
						<option value="INSPL">INSPL</option>
						<option value="DRAWB">DRAWB</option>
						<option value="CHASS">CHASS</option>
						<option value="APILR">APILR</option>
						<option value="BPILR">BPILR</option>
						<option value="CPILR">CPILR</option>
						<option value="DPILR">DPILR</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsVehiclePartSide</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c19u03" name="c19u03">
						<option value="">请选择</option>
						<option value="FRONT">FRONT</option>
						<option value="LEFT">LEFT</option>
						<option value="RIGHT">RIGHT</option>
						<option value="BACK">BACK</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>LocationMarkingsVehiclePartSideSection</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c19u04" name="c19u04">
						<option value="">请选择</option>
						<option value="LEFT">LEFT</option>
						<option value="MIDLE">MIDLE</option>
						<option value="RIGHT">RIGHT</option>
						<option value="FRONT">FRONT</option>
						<option value="BACK">BACK</option>
					</select>
				</td>
	  		</tr>
			
			<tr>
				<td colspan="2"><label><P>0.9. Name and address of the manufacturer's representative (if any)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c20" name="c20" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
			<tr>			  		
	  			<td colspan="2"><label><P>EURepresentativeName</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c20u01" name="c20u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>EURepresentativeAddressLine1</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c20u02" name="c20u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>EURepresentativeAddressLine3</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c20u03" name="c20u03" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>EURepresentativePlaceOfResidence</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c20u04" name="c20u04" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>EURepresentativeCountryOfResidence</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c20u05" name="c20u05" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
			
			<tr>			  		
				<td colspan="2"><label><P>0.11. Conforms in all respects to the type described in approval (e13*KS18/858*?????*00) granted on (MM, DD, YYYY) and can be permanently registered in Member States having right/left hand traffic and using metric/imperial units for the speedometer and metric/imperial units for the odometer.</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c21" name="c21" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TypeApprovalNumber</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c21u01" name="c21u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TypeApprovalIssueDate</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c21u02" name="c21u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>0.11. Place:</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c22" name="c22" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		 <tr>			  		
	  			<td colspan="2"><label><P>SignerName</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c22u01" name="c22u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		 </tr>
	  		 <tr>			  		
	  			<td colspan="2"><label><P>SignerPosition</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c22u02" name="c22u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		 </tr>
	
		</Table>
		<table id="two" width="100%" style="display:none"  border="0">
	  		
			<tr><td><label><P><b>PART2</b></label></td></tr>
			<tr><td><label><P><b>General construction characteristics</b></label></td></tr>
			<tr>			  		
	  			<td colspan="2" ><label><P>1. Number of axles and wheels</label></td>
				<td><label><P>打印</label></td>
				<td  colspan="2"><input type="text" id="c24" name="c24" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
			<tr>			  		
	  			<td colspan="2"><label><P>NumberOfAxles</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c24u01" name="c24u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		 </tr>
	  		 <tr>			  		
	  			<td colspan="2"><label><P>NumberOfWheels</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c24u02" name="c24u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		 </tr>
			
      		<tr>
				<td  colspan="2"><label><P>3. Powered axles (number, position, interconnection)</label></td>
				<td><label><P>打印</label></td>
				<td ><input type="text" id="c25" name="c25" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>NumberOfPoweredAxles</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c25u01" name="c25u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
            <tr>			  		
	  			<td colspan="2"><label><P>PoweredAxleIndicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c25u02" name="c25u02">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
            </tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>InterconnectionWithPoweredAxleNumber</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c25u03" name="c25u03" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
			<tr>			  		
	  			<td colspan="2"><label><P>InterconnectionWithPoweredAxleConnection</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c25u04" name="c25u04">
						<option value="">请选择</option>
						<option value="TRA">TRA</option>
						<option value="ELE">ELE</option>
						<option value="SHA">SHA</option>
					</select>
				</td>
            </tr>
	  	
	  		<tr>			  		
	  			<td colspan="2"><label><P>3.1. Specify if the vehicle is non-automated/automated/fully automated</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>AutomatedVehicle</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c26u01" name="c26u01">
						<option value="">请选择</option>
						<option value="NON">NON</option>
						<option value="AUT">AUT</option>
						<option value="FUL">FUL</option>
					</select>
				</td>
            </tr>
	  		
	  		<tr><td><label><P><b>Main dimensions</b></label></td></tr>
	  		<tr>			  		
				<td colspan="2"><label><P>4. Wheelbase</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>Wheelbase</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c27u01" name="c27u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>4.1. Axle spacing</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>AxleSpacing</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c28u01" name="c28u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>5. Length</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>Length</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c29u01" name="c29u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>6. Width</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>Width</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c30u01" name="c30u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>7. Height</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c31" name="c31" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>Height</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c31u01" name="c31u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr><td><label><P><b>Masses</b></label></td></tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>13. Mass in running order</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c32" name="c32" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>MassInRunningOrder</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c32u01" name="c32u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>13.2. Actual mass of the vehicle</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c33" name="c33" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ActualMass</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c33u01" name="c33u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>		
				<td colspan="2"><label><P>16.1. Technically permissible maximum laden mass</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c35" name="c35" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TechnicallyPermissibleMaximumLadenMass</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c35u01" name="c35u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>

	  		<tr>			  		
	  			<td colspan="2"><label><P>16.2. Technically permissible mass on each axle</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c36" name="c36" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>第1轴TechnicallyPermissibleMassAxle</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c36u01" name="c36u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>第2轴TechnicallyPermissibleMassAxle</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c36u02" name="c36u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>16.4. Technically permissible maximum mass of the combination</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c37" name="c37" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TechnicallyPermissibleMaximumCombinationMass</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c37u01" name="c37u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  	
	  		<tr>			
				<td colspan="2"><label><P>18.1. Drawbar trailer</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c39" name="c39" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>18.3. Centre-axle trailer Towing Capacity</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c40" name="c40" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TechPermMaxTowMassCentAxTrail</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c40u01" name="c40u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>18.4. Unbraked trailer</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c41" name="c41" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TechPermMaxTowMassUnbrTrailer</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c41u01" name="c41u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>19. Technically permissible maximum static vertical mass at the coupling point</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c42" name="c42" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>TechPermMaxStatVertMassCouplPt</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c42u01" name="c42u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
			
			<tr><td><label><P><b>Power plant</b></label></td></tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>20. Manufacturer of the engine</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c43" name="c43" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ManufacturerEnergyConvertor</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c43u01" name="c43u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>21. Engine code as marked on the engine</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c44" name="c44" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>EnergyConvertorCodeMarkedOnEnergyConvertor</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c44u01" name="c44u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
			<tr>			  		
	  			<td colspan="2"><label><P>22. Working principle</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c45" name="c45" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>WorkingPrinciple</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c45u01" name="c45u01">
						<option value="">请选择</option>
						<option value="C4">C4</option>
                        <option value="CA">CA</option>
                        <option value="CI">CI</option>
                        <option value="DF">DF</option>
                        <option value="E2">E2</option>
                        <option value="E4">E4</option>
                        <option value="EC">EC</option>
                        <option value="EE">EE</option>
                        <option value="EF">EF</option>
                        <option value="EP">EP</option>
                        <option value="ER">ER</option>
                        <option value="IC">IC</option>
                        <option value="IE">IE</option>
                        <option value="PI">PI</option>
                        <option value="TB">TB</option>
					</select>
				</td>
            </tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>23. Pure electric</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c46" name="c46" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>PureElectricVehicleIndicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c46u01" name="c46u01">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
            </tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>23.1 Class of Hybrid [electric] vehicle</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2">
					<select id="c47" name="c47">
						<option value="">请选择</option>
						<option value="N/A">N/A</option>
						<option value="OVC-HEV">OVC-HEV</option>
						<option value="NOVC-HEV">NOVC-HEV</option>
						<option value="OVC-FCHV">OVC-FCHV</option>
						<option value="NOVC-FCHV">NOVC-FCHV</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>24. Number and arrangement of cylinders</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c48" name="c48" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>NumberOfCylinders</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c48u01" name="c48u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>ArrangementCylinders</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c48u02" name="c48u02">
						<option value="">请选择</option>
						<option value="LI">LI</option>
						<option value="O">O</option>
						<option value="R">R</option>
						<option value="S">S</option>
						<option value="W">W</option>
						<option value="V">V</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>25. Engine capacity</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>EngineCapacity</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c49u01" name="c49u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>26. Fuel</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c50" name="c50" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>EnergySource</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c50u01" name="c50u01">
						<option value="">请选择</option>
						<option value="10">10</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr class="noev">
				<td colspan="2"><label><P>26.1 Mono fuel/Bi fuel/Flex fuel/Dual-fuel</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2">
					<select id="c50c1" name="c50c1">
						<option value="">请选择</option>
						<option value="Mono fuel">Mono fuel</option>
						<option value="Bi fuel">Bi fuel</option>
						<option value="Flex fuel">Flex fuel</option>
						<option value="Dual-fuel">Dual-fuel</option>
					</select>
				</td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>FuelType</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c50c1u01" name="c50c1u01">
						<option value="">请选择</option>
						<option value="B">B</option>
						<option value="D">D</option>
						<option value="F">F</option>
						<option value="M">M</option>
						<option value="T">T</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>			  		
				<td colspan="2"><label><P>27.1. Maximum net power</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c52" name="c52" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>MaximumNetPowerCombustion</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c52u01" name="c52u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>EngineSpeedMaximumNetPower</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c52u02" name="c52u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>27.3. Maximum net power</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ConsolidatedMaximumNetPowerElectricEngine</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c53u01" name="c53u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ConsolidatedMaximumNetPowerElectricEngine</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c53u02" name="c53u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>27.4. Maximum 30 minutes power</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c54" name="c54" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ConsolidatedMaximum30MinutesPower</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c54u01" name="c54u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ConsolidatedMaximum30MinutesPower</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c54u02" name="c54u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>28. Gearbox (type)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2">
					<select id="c55" name="c55" onchange="var v=$(this).val();var m={'Manual':'M','Automatic':'A','CVT':'C','Double clutch (gear change)':'D','Fixed ratio':'F','Automised':'G','Other':'O','Hydrostatic':'H','Semi-automatic (gear change)':'S','Wheel hub':'W'};$('#c55u01').val(m[v]||'');">
						<option value="">请选择</option>
						<option value="Manual">Manual</option>
		                <option value="Automatic">Automatic</option>
                        <option value="CVT">CVT</option>
                        <option value="Double clutch (gear change)">Double clutch (gear change)</option>
                        <option value="Fixed ratio">Fixed ratio</option>
                        <option value="Automised">Automised</option>
                        <option value="Other">Other</option>
                        <option value="Hydrostatic">Hydrostatic</option>
                        <option value="Semi-automatic (gear change)">Semi-automatic (gear change)</option>
                        <option value="Wheel hub">Wheel hub</option>	
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>GearboxType</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c55u01" name="c55u01" class="text ui-widget-content ui-corner-all" size="40"  readonly="readonly" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>28.1. Gearbox ratios (to complete for vehicles with manual shift transmissions)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c56" name="c56" class="text ui-widget-content ui-corner-all" size="40" onchange="$('#c56u01').val($(this).val());$('#c56u02').val($(this).val());" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>GearNumber</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c56u01" name="c56u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>GearRatio</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c56u02" name="c56u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>28.1.1. Final drive ratio (if applicable)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c57" name="c57" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>28.1.2. Final drive ratios (to complete if and where applicable)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c58" name="c58" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr><td><label><P><b>Maximum speed</b></label></td></tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>29. Maximum speed</label></td>
	  			<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c59" name="c59" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>MaximumSpeed</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c59u01" name="c59u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr><td><label><P><b>Axles and suspension</b></label></td></tr>
	  		<tr>			  		
				<td colspan="2"><label><P>30. Axle(s) track</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c60" name="c60" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴AxleTrack</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c60u01" name="c60u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴AxleTrack</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c60u02" name="c60u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>35. Fitted tyre/wheel combination/energy efficiency class of rolling resistance coefficients (RRC) and tyre category used for CO2 determination (if applicable)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c61" name="c61" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴TyreSize</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u01" name="c61u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴LoadCapacityIndexSingleWheel</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u02" name="c61u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴SpeedCategorySymbol</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u03" name="c61u03" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴RimSize</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u04" name="c61u04" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴RimOffSet</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u05" name="c61u05" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴RollingResistanceClass</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c61u06" name="c61u06">
						<option value="">请选择</option>
						<option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                        <option value="D">D</option>
                        <option value="E">E</option>
                        <option value="F">F</option>
                        <option value="G">G</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴TyreFittedProductionIndicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c61u07" name="c61u07">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>前轴TyreCategory</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c61u08" name="c61u08">
						<option value="">请选择</option>
						<option value="C1">C1</option>
                        <option value="C2">C2</option>
                        <option value="C3">C3</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴TyreSize</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u11" name="c61u11" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴LoadCapacityIndexSingleWheel</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u12" name="c61u12" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴SpeedCategorySymbol</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u13" name="c61u13" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴RimSize</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u14" name="c61u14" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴RimOffSet</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c61u15" name="c61u15" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴RollingResistanceClass</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c61u16" name="c61u16">
						<option value="">请选择</option>
						<option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                        <option value="D">D</option>
                        <option value="E">E</option>
                        <option value="F">F</option>
                        <option value="G">G</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴TyreFittedProductionIndicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c61u17" name="c61u17">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>后轴TyreCategory</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c61u18" name="c61u18">
						<option value="">请选择</option>
						<option value="C1">C1</option>
                        <option value="C2">C2</option>
                        <option value="C3">C3</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr><td><label><P><b>Brakes</b></label></td></tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>36. Trailer brake connections mechanical/electric/pneumatic/hydraulic</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr><td><label><P><b>Bodywork</b></label></td></tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>38. Code for bodywork</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2">
					<select id="c63" name="c63">
						<option value="">请选择</option>
						<option value="AC">AC</option>
					</select>
				</td>
	  		</tr>
	  		
			<tr>			  		
				<td colspan="2"><label><P>41. Number and configuration of doors</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c65" name="c65" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
			<tr>			  		
	  			<td colspan="2"><label><P>NumberOfDoors</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c65u01" name="c65u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>NumberOfDoorsFront</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c65u02" name="c65u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>NumberOfDoorsLeft</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c65u03" name="c65u03" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>NumberOfDoorsRight</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c65u04" name="c65u04" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>NumberOfDoorsRear</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c65u05" name="c65u05" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
			
			<tr>
				<td colspan="2"><label><P>42. Number of seating positions (including the driver)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c66" name="c66" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>	
			<tr>			  		
				<td colspan="2"><label><P>42.1. Seat(s) designated for use only when the vehicle is stationary</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c67" name="c67" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>	
			<tr>
				<td colspan="2"><label><P>42.3. Number of wheelchair user accessible position</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c68" name="c68" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
	  		
	  		<tr><td><label><P><b>Environmental performances</b></label></td></tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>46. Sound level— Stationary N/A dB(A) at engine speed</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c69" name="c69" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>SoundLevelStationaryEngineSpeed</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c69u01" name="c69u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>46. Sound level— Drive-by</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c70" name="c70" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>SoundLevelDriveBy</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c70u01" name="c70u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>47. Exhaust emission level</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c71" name="c71" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>ExhaustEmissionLevel</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c71u01" name="c71u01">
						<option value="">请选择</option>
						<option value="AC">AC</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>47.1.1. Test mass, kg</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c72" name="c72" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>WltpEmissionTestMass</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c72u01" name="c72u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>47.1.2. Frontal area, m2</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>47.1.2.1. Projected frontal area of air entrance of the front grille (if applicable), cm2</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c74" name="c74" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>	  		
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>47.1.3.0. f0, N</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c75" name="c75" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>
				<td colspan="2"><label><P>47.1.3.1. f1, N/(km/h)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c76" name="c76" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>47.1.3.2. f2, N/(km/h)2</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c77" name="c77" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>47.2.1. Driving Cycle class</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c78" name="c78" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>47.2.2. Downscaling factor (fdsc)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c79" name="c79" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
				<td colspan="2"><label><P>47.2.3. Capped speed</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c80" name="c80" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>DrivingCycleCappedSpeedIndicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c80u01" name="c80u01">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>48. Number of the base regulatory act and latest amending regulatory act </label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c81c1" name="c81c1" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>1.2. Test procedure</label></td>
	  			<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c81c2" name="c81c2" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>1.2. Test procedure的结果</label></td>
	  			<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c81c3" name="c81c3" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1Co</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u01" name="c81c3u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1Thc</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u02" name="c81c3u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1Nmhc</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u03" name="c81c3u03" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1Nox</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u04" name="c81c3u04" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1ThcNox</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u05" name="c81c3u05" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1Nh3</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u06" name="c81c3u06" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1Particulates</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u07" name="c81c3u07" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1NumberOfParticles</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u08" name="c81c3u08" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>WltpType1NumberOfParticlesExponent</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c3u09" name="c81c3u09" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  			  		
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>48.1. Smoke corrected absorption coefficient </label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c81c4" name="c81c4" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>48.2. Complete RDE trip: NOx: …, Particles (number) </label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c81c5" name="c81c5" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>RdeCompleteNox</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c5u01" name="c81c5u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>RdeCompleteNumberOfParticles</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c5u02" name="c81c5u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>RdeCompleteNumberOfParticlesExponent</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c5u03" name="c81c5u03" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>48.2. Urban RDE trip: NOx: …, Particles (number) </label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c81c6" name="c81c6" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>RdeUrbanNox</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c6u01" name="c81c6u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>RdeUrbanNumberOfParticles</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c6u02" name="c81c6u02" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>RdeUrbanNumberOfParticlesExponent</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c81c6u03" name="c81c6u03" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr  class="noev">	
	  			<td colspan="2"><label><P>49.1. All powertrains, except OVC hybrid electric (if applicable)</label></td>
	  			<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c81c7" name="c81c7" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
			<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-CO2-Low</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c82" name="c82" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WltpCo2Low</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c82u01" name="c82u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
			<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-CO2-Medium</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c83" name="c83" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WltpCo2Medium</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c83u01" name="c83u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
			<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-CO2-High</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c84" name="c84" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WltpCo2High</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c84u01" name="c84u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-CO2-Extra High</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c85" name="c85" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WltpCo2ExtraHigh</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2"><input type="text" id="c85u01" name="c85u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
	  		</tr>
	  		
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-CO2-Combined</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c86" name="c86" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpCo2Combined</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c86u01" name="c86u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
	  		
	  		<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-Fuel-Low</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c87" name="c87" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpFuelConsumptionLow</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c87u01" name="c87u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
	  		
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-Fuel-Medium</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpFuelConsumptionMedium</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c88u01" name="c88u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-Fuel-High</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpFuelConsumptionHigh</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c89u01" name="c89u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      			
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-Fuel-Extra High</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c90" name="c90" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpFuelConsumptionExtraHigh</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c90u01" name="c90u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-Fuel-Combined</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c91" name="c91" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>	
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpFuelConsumptionCombined</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c91u01" name="c91u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-Electric-Low</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c92" name="c92" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpEnergyConsumptionPureElectricLow</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c92u01" name="c92u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-Electric-Medium</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c93" name="c93" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpEnergyConsumptionPureElectricMedium</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c93u01" name="c93u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>WLTP-Electric-High</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c94" name="c94" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpEnergyConsumptionPureElectricHigh</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c94u01" name="c94u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">
	  			<td colspan="2"><label><P>WLTP-Electric-Extra High</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c95" name="c95" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpEnergyConsumptionPureElectricExtraHigh</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c95u01" name="c95u01" class="text ui-widget-content ui-corner-all" size="40"  /></td>
      		</tr>
      		
	  		<tr class="nohev">			  		
	  			<td><label><P>WLTP-Electric-Combined</label></td>
	  			<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c96" name="c96" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpEnergyConsumptionPureElectricCombined</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c96u01" name="c96u01" class="text ui-widget-content ui-corner-all" size="40"/></td>
      		</tr>
      			  	
	  		<tr class="noev">	
	  			<td colspan="2"><label><P>2. Electric range of pure electric vehicles (if applicable)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c97" name="c97" class="text ui-widget-content ui-corner-all" size="40" /></td>
			</tr>
			
	  		<tr class="nohev">
				<td colspan="2"><label><P>Electric range</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c98" name="c98" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpPureElectricRange</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c98u01" name="c98u01" class="text ui-widget-content ui-corner-all" size="40"/></td>
      		</tr>
	  		
	  		<tr class="nohev">			  		
	  			<td colspan="2"><label><P>Electric range city</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c99" name="c99" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="nohev">			  		
      			<td colspan="2"><label><P>WltpPureElectricRangeCity</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c99u01" name="c99u01" class="text ui-widget-content ui-corner-all" size="40"/></td>
      		</tr>
      		
	  		<tr>
	  			<td colspan="2"><label><P>3. Vehicle fitted with eco-innovation(s)</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c100" name="c100" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>	
	  		<tr>			  		
	  			<td colspan="2"><label><P>EcoInnovationsFittedIndicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c100u01" name="c100u01">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>4. OVC hybrid electric vehicles (if applicable)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c101" name="c101" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr class="noev">			  		
	  			<td colspan="2"><label><P>sustaining-CO2-Low</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c101c1" name="c101c1" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">			  		
      			<td colspan="2"><label><P>WltpCo2ChargeSustainingLow</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c101u01" name="c101u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
      		</tr>
      		<tr class="noev">			  		
      			<td colspan="2"><label><P>sustaining-CO2-Medium</label></td>
      			<td><label><P>打印</label></td>
      			<td colspan="2"><input type="text" id="c101c2" name="c101c2" class="text ui-widget-content ui-corner-all" size="40" /></td>
      		</tr>
      		<tr class="noev">
      			<td colspan="2"><label><P>WltpCo2ChargeSustainingMedium</label></td>
      			<td><label><P>上传</label></td>
      			<td colspan="2"><input type="text" id="c101u02" name="c101u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
      		</tr>
      		<tr class="noev">			  		
      			<td colspan="2"><label><P>sustaining-CO2-High</label></td>
      			<td><label><P>打印</label></td>
      			<td colspan="2"><input type="text" id="c101c3" name="c101c3" class="text ui-widget-content ui-corner-all" size="40" /></td>
      		</tr>
      		<tr class="noev">
      		    <td colspan="2"><label><P>WltpCo2ChargeSustainingHigh</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u03" name="c101u03" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-CO2-ExtraHigh</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c4" name="c101c4" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpFuelConsumptionChargeSustainingExtraHigh</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u04" name="c101u04" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-CO2-Combined</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c5" name="c101c5" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpCo2ChargeSustainingCombined</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u05" name="c101u05" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-Fuel-Low</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c6" name="c101c6" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpFuelConsumptionChargeSustainingLow</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u06" name="c101u06" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-Fuel-Medium</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c7" name="c101c7" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpFuelConsumptionChargeSustainingMedium</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u07" name="c101u07" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
			<tr class="noev">
      	       <td colspan="2"><label><P>sustaining-Fuel-High</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c8" name="c101c8" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpFuelConsumptionChargeSustainingHigh</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u08" name="c101u08" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
			<tr class="noev">
      	       <td colspan="2"><label><P>sustaining-Fuel-ExtraHigh</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c9" name="c101c9" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpFuelConsumptionChargeSustainingExtraHigh</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u09" name="c101u09" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-Fuel-Combined</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c10" name="c101c10" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpFuelConsumptionChargeSustainingCombined</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u10" name="c101u10" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-EC-Low</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c11" name="c101c11" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedLow</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u11" name="c101u11" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
			<tr class="noev">
      	       <td colspan="2"><label><P>sustaining-EC-Medium</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c12" name="c101c12" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedMedium</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u12" name="c101u12" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
			<tr class="noev">
      	       <td colspan="2"><label><P>ssustaining-EC-High</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c13" name="c101c13" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedHigh</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u13" name="c101u13" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
			<tr class="noev">
      	       <td colspan="2"><label><P>sustaining-EC-ExtraHigh</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c14" name="c101c14" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedExtraHigh</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u14" name="c101u14" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>sustaining-EC-City</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c15" name="c101c15" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      		    <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedCity</label></td>
      		    <td><label><P>上传</label></td>
      	        <td colspan="2"><input type="text" id="c101u15" name="c101u15" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	        <td colspan="2"><label><P>sustaining-EC-Combined</label></td>
      	        <td><label><P>打印</label></td>
      	        <td colspan="2"><input type="text" id="c101c16" name="c101c16" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedCombined</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c101u16" name="c101u16" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    
      	    <tr class="noev">
      	       <td colspan="2"><label><P>depleting-CO2-Low</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c17" name="c101c17" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpCo2ChargeDepletingCombined</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c101u17" name="c101u17" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>depleting-Fuel</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c18" name="c101c18" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpFuelConsumptionChargeDepletingCombined</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c101u18" name="c101u18" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    
      	    <tr class="noev">
      	       <td colspan="2"><label><P>Weighted-CO2</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c19" name="c101c19" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpCo2WeightedCombined</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c101u19" name="c101u19" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>Weighted-Fuel</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c20" name="c101c20" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpFuelConsumptionWeightedCombined</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c101u20" name="c101u20" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>Weighted-EC</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c101c21" name="c101c21" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpEnergyConsumptionExternallyChargedWeightedCombined</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c101u21" name="c101u21" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>          		            	              	        

	  		<tr>
	  			<td colspan="2"><label><P>5. Electric range of OVC hybrid electric vehicles (if applicable)</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c102" name="c102" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr class="noev">
      	       <td colspan="2"><label><P>WLTP equivalent all electric range OVC (EAER)</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c102c1" name="c102c1" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpEquivalentAllElectricOffVehicleChargingRange</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c102u01" name="c102u01" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WLTP equivalent all range city OVC (EAER)</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c102c2" name="c102c2" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpEquivalentAllElectricOffVehicleChargingRangeCity</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c102u02" name="c102u02" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WLTP electric range OVC (AER)</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c102c3" name="c102c3" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpAllElectricOffVehicleChargingRange</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c102u03" name="c102u03" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WLTP range city OVC (AER)</label></td>
      	       <td><label><P>打印</label></td>
      	       <td colspan="2"><input type="text" id="c102c4" name="c102c4" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>
      	    <tr class="noev">
      	       <td colspan="2"><label><P>WltpAllElectricOffVehicleChargingRangeCity</label></td>
      	       <td><label><P>上传</label></td>
      	       <td colspan="2"><input type="text" id="c102u04" name="c102u04" class="text ui-widget-content ui-corner-all" size="40" /></td>
      	    </tr>     
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>51. For special purpose vehicles: designation in accordance with point 5 of Part A of Annex I to Regulation (EU) 2018/858 of the European Parliament and of the Council</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c103" name="c103" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>
	  			<td colspan="2"><label><P>52. Remarks</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c104" name="c104" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>Additional tyre/wheel combinations</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2"><input type="text" id="c105" name="c105" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		
	  		<tr>
	  			<td colspan="2"><label><P>54. Vehicle fitted with</label></td>
				<td><label><P>打印及上传</label></td>
				<td colspan="2">
					<div class="checkbox-item">
        <input type="checkbox"  name="c106" id="tech_TPMS" value="TPMS" ><label for="tech_TPMS">TPMS</label>
        <input type="checkbox"  name="c106" id="tech_ELKS" value="ELKS"><label for="tech_ELKS">ELKS</label>
        <input type="checkbox"  name="c106" id="tech_AEBS" value="AEBS"><label for="tech_AEBS">AEBS</label>
        <input type="checkbox"  name="c106" id="tech_ESS" value="ESS"><label for="tech_ESS">ESS</label>
		<input type="checkbox"  name="c106" id="tech_AIF" value="AIF"><label for="tech_AIF">AIF</label>
		<input type="checkbox"  name="c106" id="tech_ISA" value="ISA"><label for="tech_ISA">ISA</label>
        <input type="checkbox"  name="c106" id="tech_DDAW" value="DDAW"><label for="tech_DDAW">DDAW</label>
        <input type="checkbox"  name="c106" id="tech_ADDW" value="ADDW"><label for="tech_ADDW">ADDW</label>
        <input type="checkbox"  name="c106" id="tech_EDR" value="EDR"><label for="tech_EDR">EDR</label>
        <input type="checkbox"  name="c106" id="tech_eCall" value="eCall"><label for="tech_eCall">eCall</label>
   					</div>
				</td>
	  		</tr>
	  		
	  		<tr>			  		
	  			<td colspan="2"><label><P>55. Vehicle certified in accordance with UN Regulation No 155</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c107" name="c107" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>VehicleCertified155Indicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c107u01" name="c107u01">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<tr>
	  			<td colspan="2"><label><P>56. Vehicle certified in accordance with UN Regulation No 156</label></td>
				<td><label><P>打印</label></td>
				<td colspan="2"><input type="text" id="c108" name="c108" class="text ui-widget-content ui-corner-all" size="40" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td colspan="2"><label><P>VehicleCertified156Indicator</label></td>
				<td><label><P>上传</label></td>
				<td colspan="2">
					<select id="c108u01" name="c108u01">
						<option value="">请选择</option>
						<option value="Y">Y</option>
						<option value="N">N</option>
					</select>
				</td>
	  		</tr>
	  		
	  		<!-- -->
			</table>

			
	
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='c1' name='c1'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div align="center">
	<jsp:plugin name="coc" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarVersionPrintApplet.class" codebase="." archive = "../applet/printVersion.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/com-jaspersoft-ireport.jar,../applet/Qrcode_encoder.jar,../applet/iText-2.1.0.jar,../applet/iTextAsian.jar" 
	iepluginurl="http://localhost:8980/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="0" width="0">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath%>"/>
			<jsp:param name="model" value="COCSupplement"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>

</body>
</html>
