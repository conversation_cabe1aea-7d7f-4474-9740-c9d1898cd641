<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecodInfo" table="CAR_RECOD_INFO">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarRecodInfoId">
            <key-property name="c1" type="java.lang.String">
                <column name="C1" length="10" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="12" />
            </key-property>
        </composite-id>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="10" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="2" />
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7" />
        </property>
        <property name="effectTime" type="java.util.Date">
            <column name="EFFECT_TIME" length="7" />
        </property>
        <property name="c2" type="java.lang.String">
            <column name="C2" length="30" />
        </property>
        <property name="c3" type="java.lang.String">
            <column name="C3" length="20" />
        </property>
        <property name="c4" type="java.lang.String">
            <column name="C4" length="100" />
        </property>
        <property name="c5" type="java.lang.String">
            <column name="C5" length="100" />
        </property>
        <property name="c6" type="java.lang.String">
            <column name="C6" length="100" />
        </property>
        <property name="c7" type="java.lang.String">
            <column name="C7" length="50" />
        </property>
        <property name="c8" type="java.lang.String">
            <column name="C8" length="50" />
        </property>
        <property name="c9" type="java.lang.String">
            <column name="C9" length="50" />
        </property>
        <property name="c10" type="java.lang.String">
            <column name="C10" length="50" />
        </property>
        <property name="c11" type="java.lang.String">
            <column name="C11" length="50" />
        </property>
        <property name="c12" type="java.lang.String">
            <column name="C12" length="50" />
        </property>
        <property name="model" type="java.lang.String">
            <column name="model" length="20" />
        </property>
    </class>
</hibernate-mapping>
