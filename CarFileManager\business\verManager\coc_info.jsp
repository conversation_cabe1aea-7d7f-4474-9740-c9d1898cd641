<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; /**position: relative;**/ text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var wizardModel = "two";
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	$("#print").attr("disabled", true);
	//$("#compare").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				/*if(operatePerrmission[i].flag.indexOf("compare")!=-1){
					$("#compare").attr("disabled", false);
				}*/
				if(operatePerrmission[i].flag.indexOf("print")!=-1){
					$("#print").attr("disabled", false);
					isprint = true;
				}
            }
        }
    });
	
	$("#query").click(function(){
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		if(qc1==""&&qstate==""&&qfactory==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="cocVer.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&menuid="+menuid;
		}
	});
		
	$("#compare").click(function(){
       	var dialogObj = $('#coc_compare_dialog');     	  	
       	dialogObj.data('title.dialog','COC标签版本比较').dialog('open');      	
	});
	
	$("#compare1").click(function(){
		if($('#cocVer1').val()=='' || $('#cocVer2').val()==''){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择比较的版本！');
   	   		messageObj.dialog('open');
   	   		return;
		}
		jQuery.ajax({
	        url: 'business/cocVer!findCompareCocVer.action',		           
	        data: {'cocVer1' : $('#cocVer1').val(), 'cocVer2' : $('#cocVer2').val()}, 
	     	type: 'POST',
	        beforeSend: function() {
	        },
	        error: function(request) {
	            alert("系统错误，请与管理员联系！");
	        },
	        success: function(data) {
	        	clearCompareDialog('2');
	         	var content = json2Bean(data).json;
	         	var carObj = eval("("+content.toString()+")"); 
	         	var dialogObj = $('#coc_compare_dialog');
		       	dialogObj.find("#tbl1").find("tr").each(function(i){
		       		var item = $(this).attr("name");
		       		//console.log(carObj[0][item]+"=="+$.trim(carObj[1][item]));
		       		if($.trim(carObj[0][item])!=$.trim(carObj[1][item])){//不同时
		       			$(this).attr("style","background-color: yellow;");
		       		}
		 			$(this).find("td:eq(1)").text(carObj[0][item]);      		
		 			$(this).find("td:eq(2)").text(carObj[1][item]);      		
		       	}); 
		       	//如果是电动车，则清空一些列
		       	if(carObj[0]['printtype']==3){
		       		clearColsByPureElec(1);
		       	}
		       	if(carObj[1]['printtype']==3){
		       		clearColsByPureElec(2);
		       	}
	        }
	    });       	  	       	
       	
	});	
	
	//TODO
	//纯电动车，比较的时候清空一些已经显示的列
	function clearColsByPureElec(obj){
		console.log("clearColsByPureElec")
		/*
		var cols = new Array("c71","c72","c73","c74","c75","c76","c140",
					"c141","c142","c143","c144","c145","c146",
					"c84","c85","c86","c87","c88","c89","c90");
		var dialog = $('#coc_compare_dialog');
		for(var i=0; i<cols.length; i++){
			$($(dialog).find("#tbl1 tr[name='"+cols[i]+"']").find("td").eq(obj)[0]).text("");
		}
		*/
	}
	
	function clearCompareDialog(flg){
		if(flg=='1'){
		$("#cocVer2").empty();
		$("<option value=''>请选择...</option>").appendTo("#cocVer2");	
		$("#cocVer1").get(0).selectedIndex=0;	
		}else if(flg=='2'){
        var dialogObj = $('#coc_compare_dialog');        
	       	dialogObj.find("#tbl1").find("tr").each(function(i){
	 			$(this).find("td:eq(1)").text('');      		
	 			$(this).find("td:eq(2)").text(''); 
	 			$(this).attr("style","background-color: #f1f9f3;"); 
	       	});	
       	}	
	}
	$("#coc_compare_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 800,
		height: 555,
		modal: true,
		buttons: {
			'关闭': function() {	
				$(this).dialog('close');
			}
		},
		close: function() {
			clearCompareDialog('1');			
			clearCompareDialog('2');			
		}				
	});
	

	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
      	
    	if(index==1){
        	var params = "c1="+id;
        	jQuery.ajax({
	            url: 'business/cocVer!cocVerInfo.action',		           
	            data: params,
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#public_coc_dialog');
	            	type = "add";
					setDialogValue(dialogObj,model);
					dialogObj.find('#vercode').attr('readonly',true);
					dialogObj.find('#c6').attr('readonly',true);
					$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
					dialogObj.data('title.dialog', '新增COC信息').dialog('open');
					dialogObj.find('#vercode').val('');
					getMaxVercode();
					printtypechange();
	            }
	        });
   			
   	    	
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
   	  		type = "add";
   	  		var dialogObj = $('#public_coc_dialog');
   	  		dialogObj.find('#vercode').attr('readonly',true);
   	  		dialogObj.find('#c6').attr('readonly',true);
   	  		$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
   	  		dialogObj.data('title.dialog', '新增COC数据').dialog('open');
   	   	 }
		
	});

	$("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			var params = "c1="+id;
   			jQuery.ajax({
	            url: 'business/cocVer!cocVerInfo.action',		           
	            data: params, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var content = data.json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_coc_dialog');
					if(carObj.state == null || carObj.state=="0" || carObj.state=="" ){
						setDialogValue(dialogObj,carObj);

		       	    	dialogObj.find('#c1').attr('readonly',true);
		       	    	dialogObj.find('#vercode').attr('readonly',true);
		       	    	dialogObj.find('#c6').attr('readonly',true);
		       	    	dialogObj.find('#imp').attr('disabled',true);
		       	    	$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
		       	    	printtypechange(); //2014-12-30 lmc
		       	    	dialogObj.data('title.dialog', '修改COC信息').dialog('open');
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var state = "";
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				state=$(this).parent().find("#state").val();
         		if(state=="1" || state=="9")	{
         			effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
         		}
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});
      	if(effIndex>0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效或是历史状态，不能删除！');
   	   		messageObj.dialog('open');   	   			
	   	}else if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#c1').val(id);
   	   	}
	
	});
	

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		var params = "c1="+id;
		jQuery.ajax({
            url: 'business/cocVer!cocVerInfo.action',		           
            data: params, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	        	var dialogObj = $('#public_coc_dialog');

	        	type = "look";
				setDialogValue(dialogObj,carObj);
				dialogObj.find('#imp').css('display','none');
				dialogObj.find('#tempc6').attr('size','20');
				$('input').attr("readonly", true);
				//$('.ui-dialog-buttonpane button[value="保存"]').css("display","none");
				$('.ui-dialog-buttonpane button').eq(3).css("display","none");
				if(isprint)
					$('.ui-dialog-buttonpane button[value="打印"]').css("display","");
				else
					$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
					
				printtypechange();	
				dialogObj.data('title.dialog', '查看COC信息').dialog('open');
				
	        }
	    });

		return false;
	}
	
	$('#public_coc_dialog').find('#c1').bind('keyup',function(event) { 
		if(type=="add")
			getMaxVercode();
    });
	
	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 520,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
			}
			//,
			//'下一页': function() {
			//	wizardToDisplay();
			//}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$('#public_notice_dialog').find('#c1').attr('readonly',false);

			//wizardModel = "one";
			//wizardToDisplay();
		}
	});
	
    function getMaxVercode()
    {
    	var obj = $('#public_coc_dialog').find('#c1');
		if(obj.val().gblen()==18){
			jQuery.ajax({
	            url: 'business/cocVer!getMaxCocVercode.action',		           
	            data: {"c1":obj.val()},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            		       
	            	var content = data.json;
		          	var dialogObj = $('#public_coc_dialog');
					dialogObj.find('#vercode').val(content);
	            }
	        });
		}  
    }

    $('#public_coc_dialog').find('#tempc6').bind('keyup',function(event) { 
        var value = $('#public_coc_dialog').find('#tempc6').val();
    	$('#public_coc_dialog').find('#c6').val(value);
    });
    $('#public_coc_dialog').find('#tempc6').bind('blur',function(event) { 
        var value = $('#public_coc_dialog').find('#tempc6').val();
    	$('#public_coc_dialog').find('#c6').val(value);
    });
	
    $("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 460,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var gk = $('#pubilc_notice_dialog').find('#gkxxList').val();
				if(gk!=''){
					var arr=gk.split(',');
					//alert(arr[0]);
					if(arr.length==3){
						$('#public_coc_dialog').find("#c88").val(arr[0]);
						$('#public_coc_dialog').find("#c89").val(arr[1]);
						$('#public_coc_dialog').find("#c90").val(arr[2]);
					}						
				}			
				if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
					var value = $(this).find('#pmodelList').val().split(',');
					impPubData(value[0],value[1]);
				}
				
				
				$(this).dialog('close');
			}
		}
	});
	
	$('#export').click(function() {
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		
		location.href="cocVer!exportData.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory;  
	});

	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		var params = "c1="+id;
    		jQuery.ajax({
                url: 'business/cocVer!cocVerInfo.action',		           
                data: params, 
    	        type: 'POST',
    	        beforeSend: function() {
    	        
    	        },
    	        error: function(request) {
    	            
    	        },
    	        success: function(data) {
    	            var content = json2Bean(data).json;
    	            var carObj = eval("("+content.toString()+")");

    	            if(carObj.state==null||carObj.state==""||carObj.state=="0"){
    	            	var dialogObj = $('#public_coc_dialog');
        	        	type = "effect";
        				setDialogValue(dialogObj,carObj);
        				dialogObj.find('#imp').css('display','none');
        				dialogObj.find('#tempc6').attr('size','20');
        				$('input').attr("readonly", true);
        				$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
        				$('.ui-dialog-buttonpane button').eq(3).attr('value','生效');
        				printtypechange();
        				dialogObj.data('title.dialog', '生效COC信息').dialog('open');
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
    				
    	        }
    	    });
         	
   	   	}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);

				var dlgButton = $('.ui-dialog-buttonpane button');	

				var qc1 = $('#qc1').val();
				var qstate = $('#qstate').val();
				var qfactory = $('#qfactory').val();
				var currentPage=$('#currentPage_temp').val();
				
				if(type=="delete"){	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');			
					formObj[0].action = "business/cocVer!deleteCocs.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					//判断是否为典型车型，如果是就弹出对话框让用户决定是否连同更新该典型车型下的中性车型参数。
		   			/*jQuery.ajax({
			            url: 'business/typicalityNeutral!findTypicalityCarModel.action',		           
			            data: {"dxcx":qc1}, 
				        type: 'POST',
				        dataType:'json', 
			            beforeSend: function() {
			            
			            },
			            error: function(request) {
			               
			            },
			            success: function(data) {
			            	var m_zxcx='';
			            	var content = data.json;
			            	var dialogObj = $('#public_coc_dialog');
			            	if(content!=''){		            			            	
					            var carObj = eval("("+content.toString()+")"); 
				            	if(carObj!=null && carObj.zxcx!=null){
				            		if(window.confirm("是否同时对关联的中性车型"+ carObj.zxcx + "进行相应操作！")){
				            			m_zxcx=carObj.zxcx;
				            		}
				            	}
			            	}
			            	formObj[0].action = "business/cocVer!effectCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
							formObj[0].submit();	            
			            }
			        }); 	*/		        
	            	formObj[0].action = "business/cocVer!effectCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage;
					formObj[0].submit();
							
				}else if(type=="imp"){
					$(this).dialog('close');
					//impPubData($("#public_coc_dialog").find("#tempc6").val());
					//type = "add";
				}
			}
		}
	});
	
	$("#public_coc_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 555,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'保存': function() {
				addOrUpdate(this);
			},
			'打印': function() {
				var c1 = $('#public_coc_dialog').find('#c1').val();
	   	   		var vercode = $('#public_coc_dialog').find('#vercode').val();
	   			window.document.coc.printCocVersion(c1,vercode);
			},
			'下一页': function() {
				wizard();
			}
		},
		close: function() {
			clear($(this));
			updateTips($(this).find('#validateTips'),'');	
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$(this).find('#imp').attr('disabled',false);
			wizardModel = "one";
			wizard();
		}
		
	});

	function wizard(){
		if(wizardModel=="two"){
			
			$('#public_coc_dialog').dialog('option', 'buttons', { 
				"取消": function() {
					$(this).dialog('close');
				}, 
				"保存": function() { 
					addOrUpdate();
				}, 
				'打印': function() {
					var c1 = $('#public_coc_dialog').find('#c1').val();
		   	   		var vercode = $('#public_coc_dialog').find('#vercode').val();
		   			window.document.coc.printCocVersion(c1,vercode);
				},
				"下一页": function() { 
					wizardModel = "three"; 
					wizard();
				},
				"上一页": function() {  
					wizardModel = "one";
					wizard();
				}
			} );
			$('#public_coc_dialog').find('#one').hide();
			//$('#public_coc_dialog').find('#two').show();
			showtables2();//2014-11-13 lmc
			//$('#public_coc_dialog').find('#three').hide();
			hidetables3();
			
		}else if(wizardModel=="one"){
			$('#public_coc_dialog').dialog('option', 'buttons', {
				"取消": function() { 
					$(this).dialog('close');
				}, 
				"保存": function() {
					addOrUpdate();
				}, 
				'打印': function() {
					var c1 = $('#public_coc_dialog').find('#c1').val();
		   	   		var vercode = $('#public_coc_dialog').find('#vercode').val();
		   			window.document.coc.printCocVersion(c1,vercode);
				},
				"下一页": function() {  
					wizardModel = "two";
					wizard();
				}
			} );
			$('#public_coc_dialog').find('#one').show();
			//$('#public_coc_dialog').find('#two').hide();
			hidetables2();//2014-11-13 lmc
			//$('#public_coc_dialog').find('#three').hide();
			hidetables3();
		}else if(wizardModel=="three"){
			$('#public_coc_dialog').dialog('option', 'buttons', {
				"取消": function() { 
					$(this).dialog('close');
				}, 
				"保存": function() {
					addOrUpdate();
				}, 
				'打印': function() {
					var c1 = $('#public_coc_dialog').find('#c1').val();
		   	   		var vercode = $('#public_coc_dialog').find('#vercode').val();
		   			window.document.coc.printCocVersion(c1,vercode);
				},
				"上一页": function() { 
					wizardModel = "two"; 
					wizard();
				}
			} );
			$('#public_coc_dialog').find('#one').hide();
			//$('#public_coc_dialog').find('#two').hide();
			hidetables2();//2014-11-13 lmc
			//$('#public_coc_dialog').find('#three').show();
			showtables3();

		}
		if(type=="effect")
			$('.ui-dialog-buttonpane button').eq(3).attr('value','生效');
		if(type=="look"){
			//$('.ui-dialog-buttonpane button[value="保存"]').css("display","none");
			//alert($('.ui-dialog-buttonpane button').eq(3).attr('value'));
			$('.ui-dialog-buttonpane button').eq(3).css("display","none");
			if(isprint)
				$('.ui-dialog-buttonpane button[value="打印"]').css("display","");
			else
				$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
		}else{
			$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
		}
	}
	//2014-11-13 lmc 第二页的显示效果
	function showtables2(){
		var prt = $('#public_coc_dialog').find('#printtype').val();
		$('#public_coc_dialog').find('#two').show();
		if(prt=='1'){	//		
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
			$('#public_coc_dialog').find(".qdxs").show();
		}else if(prt=='2'){
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
			$('#public_coc_dialog').find('#two_2').show();
			$('#public_coc_dialog').find('#two_3').show();
		}else if(prt=='3'){
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
			//隐藏直接喷射、气缸、排量
			$('#public_coc_dialog').find(".zjps").hide();//
			$('#public_coc_dialog').find('#two_2').show();
			$('#public_coc_dialog').find('#two_3').show();
		}else if(prt=='4'){
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
			//隐藏驱动型式、拖挂、货箱
			$('#public_coc_dialog').find(".qdxs").hide();//
		}else if(prt=='5'){
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
			$('#public_coc_dialog').find('#two_2').show();
			$('#public_coc_dialog').find('#two_3').show();
		}else if(prt=='6'){
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
		}else{
			$('#public_coc_dialog').find('#two_1').show();
			$('#public_coc_dialog').find(".zjps").show();//直接喷射、气缸、排量
			$('#public_coc_dialog').find(".qdxs").show();//驱动型式、拖挂、货箱
			//隐藏直接喷射、气缸、排量
			$('#public_coc_dialog').find(".zjps").hide();//
			$('#public_coc_dialog').find('#two_2').show();
			$('#public_coc_dialog').find('#two_4').show();
		}
		$('#public_coc_dialog').find('#two_end').show();
	}

	function hidetables2(){
		$('#public_coc_dialog').find('#two').hide();
		$('#public_coc_dialog').find('#two_1').hide();
		$('#public_coc_dialog').find('#two_2').hide();
		$('#public_coc_dialog').find('#two_3').hide();
		$('#public_coc_dialog').find('#two_4').hide();
		$('#public_coc_dialog').find('#two_end').hide();
	}
	//end 2014-11-13
	//第三页的显示效果
	function showtables3(){
		var prt = $('#public_coc_dialog').find('#printtype').val();
		$('#public_coc_dialog').find('#three').show();
		$('#public_coc_dialog').find('.fdjzs').show();
		if(prt=='1'){
			$('#public_coc_dialog').find('.fdjzs').hide();//
			
			$('#public_coc_dialog').find('#three_1').show();
			$('#public_coc_dialog').find('.yd').show();
			$('#public_coc_dialog').find('.yd').hide();//
			
			$('#public_coc_dialog').find('#three_6').show();
			$('#public_coc_dialog').find('#three_7').show();
		}else if(prt=='2'){
			$('#public_coc_dialog').find('.fdjzs').hide();//
			
			$('#public_coc_dialog').find('#three_1').show();
			$('#public_coc_dialog').find('.yd').show();
			$('#public_coc_dialog').find('.yd').hide();//
			
			$('#public_coc_dialog').find('#three_6').show();
			$('#public_coc_dialog').find('#three_7').show();
		}else if(prt=='3'){
			$('#public_coc_dialog').find('.fdjzs').hide();//
			
			$('#public_coc_dialog').find('#three_3').show();
			$('#public_coc_dialog').find('#three_4').show();

		}else if(prt=='4'){
			
			$('#public_coc_dialog').find('#three_1').show();
			$('#public_coc_dialog').find('.yd').show();
			$('#public_coc_dialog').find('#three_2').show();
			$('#public_coc_dialog').find('#three_6').show();
			$('#public_coc_dialog').find('#three_8').show();
			
		}else if(prt=='5'){
			$('#public_coc_dialog').find('.fdjzs').hide();//
			
			$('#public_coc_dialog').find('#three_1').show();
			$('#public_coc_dialog').find('.yd').show();
			$('#public_coc_dialog').find('.yd').hide();//
			
			$('#public_coc_dialog').find('#three_3').show();
			$('#public_coc_dialog').find('#three_5').show();
		}else if(prt=='6'){
			$('#public_coc_dialog').find('.fdjzs').hide();//
			
			$('#public_coc_dialog').find('#three_1').show();
			$('#public_coc_dialog').find('.yd').show();
			$('#public_coc_dialog').find('.yd').hide();//
			
			$('#public_coc_dialog').find('#three_6').show();
			$('#public_coc_dialog').find('#three_7').show();
		}else{
			$('#public_coc_dialog').find('.fdjzs').hide();//
			
			$('#public_coc_dialog').find('#three_9').show();
		}
		$('#public_coc_dialog').find('#three_end').show();
	}

	function hidetables3(){
		$('#public_coc_dialog').find('#three').hide();
		$('#public_coc_dialog').find('#three_1').hide();
		$('#public_coc_dialog').find('#three_2').hide();
		$('#public_coc_dialog').find('#three_3').hide();
		$('#public_coc_dialog').find('#three_4').hide();
		$('#public_coc_dialog').find('#three_5').hide();
		$('#public_coc_dialog').find('#three_6').hide();
		$('#public_coc_dialog').find('#three_7').hide();
		$('#public_coc_dialog').find('#three_8').hide();
		$('#public_coc_dialog').find('#three_9').hide();
		$('#public_coc_dialog').find('#three_end').hide();
	}

	function addOrUpdate(button){
		if(allFields==null){
			var parent = $('#public_coc_dialog');
			if(!parent.find("#printtype").val()){
				alert("请选择打印模板!");
				return false;
			}
			allFields = $([]).add(parent.find('#c1')).add(parent.find('#c2'))
			.add(parent.find('#vercode')).add(parent.find('#state')).add(parent.find('#c3'))
			.add(parent.find('#c4')).add(parent.find('#c5')).add(parent.find('#tempc6'))
			.add(parent.find('#c6')).add(parent.find('#c7')).add(parent.find('#c8'))
			.add(parent.find('#c9')).add(parent.find('#c10')).add(parent.find('#c11'))
			.add(parent.find('#c12')).add(parent.find('#c13')).add(parent.find('#c14'))
			.add(parent.find('#c15')).add(parent.find('#c16')).add(parent.find('#c17'))
			.add(parent.find('#c18')).add(parent.find('#c19')).add(parent.find('#c20'))
			.add(parent.find('#c21')).add(parent.find('#c22')).add(parent.find('#c24'))
			.add(parent.find('#c25')).add(parent.find('#c26')).add(parent.find('#c27'))
			.add(parent.find('#c28')).add(parent.find('#c29')).add(parent.find('#c30'))
			.add(parent.find('#printtype'))
			.add(parent.find('#c31')).add(parent.find('#c32')).add(parent.find('#c33'))
			.add(parent.find('#c34')).add(parent.find('#c35')).add(parent.find('#c36'))
			.add(parent.find('#c37')).add(parent.find('#c38')).add(parent.find('#c39'))
			.add(parent.find('#c40')).add(parent.find('#c41')).add(parent.find('#c42'))
			.add(parent.find('#c44')).add(parent.find('#c45')).add(parent.find('#c46'))
			.add(parent.find('#c47')).add(parent.find('#c48')).add(parent.find('#c49'))
			.add(parent.find('#c50')).add(parent.find('#c51')).add(parent.find('#c52'))
			.add(parent.find('#c53')).add(parent.find('#c54')).add(parent.find('#c55'))
			.add(parent.find('#c56')).add(parent.find('#c57')).add(parent.find('#c58'))
			.add(parent.find('#c59')).add(parent.find('#c61'))
			.add(parent.find('#c62')).add(parent.find('#c63')).add(parent.find('#c64'))
			.add(parent.find('#c65')).add(parent.find('#c66')).add(parent.find('#c68'))
			.add(parent.find('#c69')).add(parent.find('#c70')).add(parent.find('#c71'))
			.add(parent.find('#c72')).add(parent.find('#c73')).add(parent.find('#c74'))
			.add(parent.find('#c75')).add(parent.find('#c76')).add(parent.find('#c84'))
			.add(parent.find('#c85')).add(parent.find('#c86')).add(parent.find('#c87'))
			.add(parent.find('#c88')).add(parent.find('#c89')).add(parent.find('#c90'))
			.add(parent.find('#c94')).add(parent.find('#c95')).add(parent.find('#c96'))
			.add(parent.find('#c97')).add(parent.find('#c98')).add(parent.find('#c112'))
			.add(parent.find('#c113')).add(parent.find('#c114')).add(parent.find('#c115'))
			.add(parent.find('#c116')).add(parent.find('#c117')).add(parent.find('#c118'))
			.add(parent.find('#c119'))
			.add(parent.find('#c131')).add(parent.find('#c132')).add(parent.find('#c133'))
			.add(parent.find('#c134')).add(parent.find('#c135')).add(parent.find('#c136'))
			.add(parent.find('#c137')).add(parent.find('#c138')).add(parent.find('#c139')).add(parent.find('#c140'))
			.add(parent.find('#c141')).add(parent.find('#c142')).add(parent.find('#c143'))
			.add(parent.find('#c144')).add(parent.find('#c145')).add(parent.find('#c146'))
			.add(parent.find('#c147')).add(parent.find('#c148')).add(parent.find('#c149'))
			.add(parent.find('#c150')).add(parent.find('#c151')).add(parent.find('#c152'))
			.add(parent.find('#c153')).add(parent.find('#c154')).add(parent.find('#c155'))
			.add(parent.find('#c156')).add(parent.find('#c157'))
.add(parent.find('c194'))
.add(parent.find('c191'))
.add(parent.find('c158'))
.add(parent.find('c159'))
.add(parent.find('c160'))
.add(parent.find('c161'))
.add(parent.find('c162'))
.add(parent.find('c163'))
.add(parent.find('c164'))
.add(parent.find('c165'))
.add(parent.find('c166'))
.add(parent.find('c167'))
.add(parent.find('c168'))
.add(parent.find('c169'))
.add(parent.find('c195'))
.add(parent.find('c170'))
.add(parent.find('c171'))
.add(parent.find('c172'))
.add(parent.find('c173'))
.add(parent.find('c174'))
.add(parent.find('c175'))
.add(parent.find('c176'))
.add(parent.find('c177'))
.add(parent.find('c178'))
.add(parent.find('c179'))
.add(parent.find('c180'))
.add(parent.find('c181'))
.add(parent.find('c182'))
.add(parent.find('c183'))
.add(parent.find('c184'))
.add(parent.find('c185'))
.add(parent.find('c186'))
.add(parent.find('c187'))
.add(parent.find('c188'))
.add(parent.find('c190'))
.add(parent.find('c192'))
.add(parent.find('c193'))
.add(parent.find('c196'))
.add(parent.find('c197'))
			.add(parent.find('#factory'))
		}
		allFields.removeClass('ui-state-error');

		
		if(type=="effect"){
	        var dialogObj =$('#public_coc_dialog');
	        effectCoc(dialogObj);//生效COC
		}else if(validate('#public_coc_dialog')==true){
			var dlgButton = $('.ui-dialog-buttonpane button');//	
			dlgButton.attr('disabled', 'disabled');
	        dlgButton.addClass('ui-state-disabled');
	        
	        var dialogObj = $('#public_coc_dialog');
	        var qc1 = $('#qc1').val();
	        var m_c1=dialogObj.find('#createForm').find('#c1').val();
	        var qstate = $('#qstate').val();
	        var qfactory = $('#qfactory').val();
	        var currentPage=$('#currentPage_temp').val();
			//判断是否为典型车型，如果是就弹出对话框让用户决定是否连同更新该典型车型下的中性车型参数。
   			jQuery.ajax({
	            url: 'business/typicalityNeutral!findTypicalityCarModel.action',		           
	            data: {"dxcx":m_c1}, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var m_zxcx='';
	            	var content = json2Bean(data).json;
	            	
	            	if(content!=''){		            			            	
			            var carObj = eval("("+content.toString()+")"); 
		            	if(carObj!=null && carObj.zxcx!=null){
		            		if(window.confirm("是否同时对关联的中性车型"+ carObj.zxcx + "进行相应操作！")){
		            			m_zxcx=carObj.zxcx;
		            		}
		            	}
	            	}
	            	//alert(m_zxcx);
					if(type=="add"){
				        var dialog = $('#public_coc_dialog');
				        dialog.find('#createForm').find('#menuid').val(menuid);
						dialog.find('#createForm')[0].action="business/cocVer!addCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
						dialog.find('#createForm')[0].submit();
					}else if(type=="update"){
						var dialog = $('#public_coc_dialog');
					 	dialog.find('#createForm')[0].action="business/cocVer!updateCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
						dialog.find('#createForm')[0].submit();
					}	            
	            }
	        }); 	        
			/*if(type=="add"){
		        var dialog = $('#public_coc_dialog');
		        dialog.find('#createForm').find('#menuid').val(menuid);
				dialog.find('#createForm')[0].action="business/cocVer!addCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&currentPage="+currentPage;
				dialog.find('#createForm')[0].submit();
			}else if(type=="update"){
				var dialog = $('#public_coc_dialog');
			 	dialog.find('#createForm')[0].action="business/cocVer!updateCoc.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&currentPage="+currentPage;
				dialog.find('#createForm')[0].submit();
			}*/
		}
	}

	function effectCoc(dialogObj){
		var id = dialogObj.find("#c1").val()+","+dialogObj.find("#vercode").val();
 		var params = "c1="+id;
 		var messageObj;
	   		jQuery.ajax({
        	url: 'business/cocVer!cocVerInfo.action',		           
        	data: params, 
        	type: 'POST',
        	beforeSend: function() {
        	
        	},
        	error: function(request) {
            
        	},
        	success: function(data) {
            	var content = json2Bean(data).json;
            	var carObj = eval("("+content.toString()+")");
            	info = "生产车型:"+dialogObj.find("#c1").val()+" 版本号:"+dialogObj.find("#vercode").val();
				if(carObj.state==null||carObj.state=="0"||carObj.state==""){

					params = "c1="+$('#public_coc_dialog').find('#c6').val();			
					jQuery.ajax({
			            url: 'business/publicNoticeCarModelManager!isCarModelExistByC1.action',		           
			            data: params, 
				        type: 'POST',
			            success: function(data) {					       									
				            var dialog = $('#public_coc_dialog');
				            if(json2Bean(data).json=="false"){
								updateTips(dialog.find('#validateTips'),'车型代号:['+dialog.find('#c6').val()+'] 此公告车型不存在！');	
								//var messageObj = $('#message_dialog');
					   	   		//messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
					   	   		//messageObj.dialog('open');	
							}else{
								messageObj = $('#operate_dialog');
				   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共1条数据');
				   	   			messageObj.dialog('open');
				   	   			messageObj.find('#c1').val(id);
							}
			            }
			        });
					
					
	   	   			
				}else{
					messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
		   	   		messageObj.dialog('open');
				}
        	}
    	});
	}
	
    
	$("#imp").click(function(){//导入相关车型信息
		var parent = $('#public_coc_dialog');
		var obj = $(parent).find('#tempc6');
		updateTips($(parent).find('#validateTips'),'');
		if(!checkLength(obj,0,20)||checkLength(obj,0,0)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且最大长度为20！');			
			return false;
		}
		//type = "imp";
		var gasVer1=$('#public_coc_dialog').find('#c1').val() + ",";
		
		jQuery.ajax({
	        //url: 'business/publicNoticeCarModelManager!effectCarModelList.action?c1='+$(obj).val(),		           
	        url:'business/cocVer!findGgcxFromBv01.action?c1='+$(obj).val(),	           
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var jsonObj = eval("("+content.toString()+")"); 
	            
	            var pmodelListObj = $('#pmodelList');  
	            
	            $('#pmodelList option').remove();
	            for(var i=0;i<jsonObj.length;i++){
	    			//$(pmodelListObj).append("<option value='"+jsonObj[i].id.c1+","+jsonObj[i].id.vercode+","+jsonObj[i].flag+"'>"+jsonObj[i].id.c1+"("+jsonObj[i].flag+")</option>");   
	    			$(pmodelListObj).append("<option value='"+jsonObj[i].ggcx+","+jsonObj[i].vercode+"'>"+jsonObj[i].ggcx+"("+jsonObj[i].vercode+")</option>");   
	            }
	            
	    
				jQuery.ajax({
			        url: 'business/gasVer!findGasVer.action',		           
			        data: {"gasVer1":gasVer1}, 
			        type: 'POST',
			        beforeSend: function() {
			        
			        },
			        error: function(request) {
			            
			        },
			        success: function(data) {
			         	var content1 = json2Bean(data).json;
			         	$("#gkxxList").empty();//清空下拉框 
						$("<option value=''>请选择...</option>").appendTo("#gkxxList");			         	
			         	if(content1!=''){
				         	var carObj = eval("("+content1.toString()+")");														
							$.each( carObj, function(i, n){
								//市区 c88, 市郊c89, 综合 c90
								var tmp=n.sqgk+","+n.sjgk +","+n.zhgk;
								var stateName="市区:"+n.sqgk+",市郊:"+n.sjgk +",综合:"+n.zhgk;
								var opt="<option value='"+tmp +"'>"+stateName+"</option>";
								$(opt).appendTo("#gkxxList")//添加下拉框的option
												
							});
						}					
			        }
			    });	    
	            
	        }
	    });
		var messageObj = $('#pubilc_notice_dialog');
	   	messageObj.find('#message').text('警告:当前输入的相关车型参数可能会被覆盖，是否继续？');
   		messageObj.dialog('open');
   		
	});
	
	
    $("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 460,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var gk = $('#pubilc_notice_dialog').find('#gkxxList').val();
							
				if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
					var value = $(this).find('#pmodelList').val().split(',');
					impPubData(value[0],value[1]);
				}
				
				
				$(this).dialog('close');
			}
		}
	});		
	
	//根据车型导入相关信息
	function impPubData(slcx,vercode){
		jQuery.ajax({
            //url: 'business/cocVer!impPubData.action',
            url: 'business/cocVer!impDataFromBv01.action',		           
            data: {'tempc6' : slcx,'vercode' :vercode},                               
	        type: 'POST',
            beforeSend: function() {
            
            },
            error: function(request) {
                
            },
            success: function(data) {
	            var content = json2Bean(data).json;
	            if(content==""){
					messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:没有找到对应的公告车型记录！');
		   	   		messageObj.dialog('open');	            
	            }else{	    	
		            var carObj = eval("("+content.toString()+")");
	            	var dialogObj = $('#public_coc_dialog');
					setImpData(dialogObj, carObj);
	            }

            }			
		})	
	}

	function setImpData(dialogObj, jsonObj){
		//2012-12-6
		dialogObj.find(":text").each(function(i){
			if($(this).attr("name")!="c88" && $(this).attr("name")!="c89" && $(this).attr("name")!="c90")
				$(this).val(jsonObj[$(this).attr("name")]);
		}) ;	
				
	};

	function setDialogValue(dialogObj,jsonObj){
		var printtype = jsonObj.printtype;
		if(printtype == '4'){
			dialogObj.find("#hn").text("HC+NOx");		
		}else{
			dialogObj.find("#hn").text("NMHC");
		}
		dialogObj.find('#c1').val(jsonObj.id.c1);
		if(type=="look"||type=="update"||type=="effect"){
			dialogObj.find('#vercode').val(jsonObj.id.vercode);}
		dialogObj.find('#c2').val(jsonObj.c2);
		dialogObj.find('#c3').val(jsonObj.c3);
		dialogObj.find('#c4').val(jsonObj.c4);
		dialogObj.find('#c5').val(jsonObj.c5);
		dialogObj.find('#tempc6').val(jsonObj.c6);
		dialogObj.find('#c6').val(jsonObj.c6);
		dialogObj.find('#c7').val(jsonObj.c7);
		dialogObj.find('#c8').val(jsonObj.c8);
		dialogObj.find('#c9').val(jsonObj.c9);
		dialogObj.find('#c10').val(jsonObj.c10);
		dialogObj.find('#c11').val(jsonObj.c11);
		dialogObj.find('#c12').val(jsonObj.c12);
		dialogObj.find('#c13').val(jsonObj.c13);
		dialogObj.find('#c14').val(jsonObj.c14);
		dialogObj.find('#c15').val(jsonObj.c15);
		dialogObj.find('#c16').val(jsonObj.c16);
		dialogObj.find('#c17').val(jsonObj.c17);
		dialogObj.find('#c18').val(jsonObj.c18);
		dialogObj.find('#c19').val(jsonObj.c19);
		dialogObj.find('#c20').val(jsonObj.c20);
		dialogObj.find('#c21').val(jsonObj.c21);
		dialogObj.find('#c22').val(jsonObj.c22);
		dialogObj.find('#c24').val(jsonObj.c24);
		dialogObj.find('#c25').val(jsonObj.c25);
		dialogObj.find('#c26').val(jsonObj.c26);
		dialogObj.find('#c27').val(jsonObj.c27);
		dialogObj.find('#c28').val(jsonObj.c28);
		dialogObj.find('#c29').val(jsonObj.c29);
		dialogObj.find('#c30').val(jsonObj.c30);
		dialogObj.find('#printtype').val(jsonObj.printtype);
		dialogObj.find('#c31').val(jsonObj.c31);
		dialogObj.find('#c32').val(jsonObj.c32);
		dialogObj.find('#c33').val(jsonObj.c33);
		dialogObj.find('#c34').val(jsonObj.c34);
		dialogObj.find('#c35').val(jsonObj.c35);
		dialogObj.find('#c36').val(jsonObj.c36);
		dialogObj.find('#c37').val(jsonObj.c37);
		dialogObj.find('#c38').val(jsonObj.c38);
		dialogObj.find('#c39').val(jsonObj.c39);
		dialogObj.find('#c40').val(jsonObj.c40);
		dialogObj.find('#c41').val(jsonObj.c41);
		dialogObj.find('#c42').val(jsonObj.c42);
		dialogObj.find('#c44').val(jsonObj.c44);
		dialogObj.find('#c45').val(jsonObj.c45);
		dialogObj.find('#c46').val(jsonObj.c46);
		dialogObj.find('#c47').val(jsonObj.c47);
		dialogObj.find('#c48').val(jsonObj.c48);
		dialogObj.find('#c49').val(jsonObj.c49);
		dialogObj.find('#c50').val(jsonObj.c50);
		dialogObj.find('#c51').val(jsonObj.c51);
		dialogObj.find('#c52').val(jsonObj.c52);
		dialogObj.find('#c53').val(jsonObj.c53);
		dialogObj.find('#c54').val(jsonObj.c54);
		dialogObj.find('#c55').val(jsonObj.c55);
		dialogObj.find('#c56').val(jsonObj.c56);
		dialogObj.find('#c57').val(jsonObj.c57);
		dialogObj.find('#c58').val(jsonObj.c58);
		dialogObj.find('#c59').val(jsonObj.c59);
		//dialogObj.find('#c60').val(jsonObj.c60);
		dialogObj.find('#c61').val(jsonObj.c61);
		dialogObj.find('#c62').val(jsonObj.c62);
		dialogObj.find('#c63').val(jsonObj.c63);
		dialogObj.find('#c64').val(jsonObj.c64);
		dialogObj.find('#c65').val(jsonObj.c65);
		dialogObj.find('#c66').val(jsonObj.c66);
		dialogObj.find('#c68').val(jsonObj.c68);
		dialogObj.find('#c69').val(jsonObj.c69);
		dialogObj.find('#c70').val(jsonObj.c70);
		dialogObj.find('#c71').val(jsonObj.c71);
		dialogObj.find('#c72').val(jsonObj.c72);
		dialogObj.find('#c73').val(jsonObj.c73);
		dialogObj.find('#c74').val(jsonObj.c74);
		dialogObj.find('#c75').val(jsonObj.c75);
		dialogObj.find('#c76').val(jsonObj.c76);
		dialogObj.find('#c140').val(jsonObj.c140);
		dialogObj.find('#c141').val(jsonObj.c141);
		dialogObj.find('#c142').val(jsonObj.c142);
		dialogObj.find('#c143').val(jsonObj.c143);
		dialogObj.find('#c144').val(jsonObj.c144);
		dialogObj.find('#c145').val(jsonObj.c145);
		dialogObj.find('#c146').val(jsonObj.c146);
		dialogObj.find('#c84').val(jsonObj.c84);
		dialogObj.find('#c85').val(jsonObj.c85);
		dialogObj.find('#c86').val(jsonObj.c86);
		dialogObj.find('#c87').val(jsonObj.c87);
		dialogObj.find('#c88').val(jsonObj.c88);
		dialogObj.find('#c89').val(jsonObj.c89);
		dialogObj.find('#c90').val(jsonObj.c90);
		dialogObj.find('#c94').val(jsonObj.c94);
		dialogObj.find('#c95').val(jsonObj.c95);
		dialogObj.find('#c96').val(jsonObj.c96);
		dialogObj.find('#c97').val(jsonObj.c97);
		dialogObj.find('#c98').val(jsonObj.c98);
		dialogObj.find('#c112').val(jsonObj.c112);
		dialogObj.find('#c113').val(jsonObj.c113);
		dialogObj.find('#c114').val(jsonObj.c114);
		dialogObj.find('#c115').val(jsonObj.c115);
		dialogObj.find('#c116').val(jsonObj.c116);
		dialogObj.find('#c117').val(jsonObj.c117);
		dialogObj.find('#c118').val(jsonObj.c118);
		dialogObj.find('#c119').val(jsonObj.c119);
		//2014-11-13 lmc
		dialogObj.find('#c131').val(jsonObj.c131);
		dialogObj.find('#c132').val(jsonObj.c132);
		dialogObj.find('#c133').val(jsonObj.c133);
		dialogObj.find('#c134').val(jsonObj.c134);
		dialogObj.find('#c135').val(jsonObj.c135);
		dialogObj.find('#c136').val(jsonObj.c136);
		dialogObj.find('#c137').val(jsonObj.c137);
		dialogObj.find('#c138').val(jsonObj.c138);
		dialogObj.find('#c139').val(jsonObj.c139);
		dialogObj.find('#state').val(jsonObj.state);
		//20160610
		dialogObj.find('#factory').val(jsonObj.factory);
		//2018-11-21 新增两用燃料模板 新增钢板弹簧片数 PN N2O(国六适用)
		dialogObj.find('#c147').val(jsonObj.c147);
		dialogObj.find('#c148').val(jsonObj.c148);
		dialogObj.find('#c149').val(jsonObj.c149);
		//2018-11-22 确认coc模板 新增 新能源车 车辆注册类型 车型种类 是否带防抱死系统 动力电池额定电压（V）2018.11.26增加 整备质量
        dialogObj.find('#c150').val(jsonObj.c150);
		dialogObj.find('#c151').val(jsonObj.c151);
		dialogObj.find('#c152').val(jsonObj.c152);
		dialogObj.find('#c153').val(jsonObj.c153);
		dialogObj.find('#c154').val(jsonObj.c154);
		dialogObj.find('#c155').val(jsonObj.c155);
		dialogObj.find('#c156').val(jsonObj.c156);
		dialogObj.find('#c157').val(jsonObj.c157);
		//2023-3-10 2023年新版COC
dialogObj.find('#c194').val(jsonObj.c194);
dialogObj.find('#c191').val(jsonObj.c191);
dialogObj.find('#c158').val(jsonObj.c158);
dialogObj.find('#c159').val(jsonObj.c159);
dialogObj.find('#c160').val(jsonObj.c160);
dialogObj.find('#c161').val(jsonObj.c161);
dialogObj.find('#c162').val(jsonObj.c162);
dialogObj.find('#c163').val(jsonObj.c163);
dialogObj.find('#c164').val(jsonObj.c164);
dialogObj.find('#c165').val(jsonObj.c165);
dialogObj.find('#c166').val(jsonObj.c166);
dialogObj.find('#c167').val(jsonObj.c167);
dialogObj.find('#c168').val(jsonObj.c168);
dialogObj.find('#c169').val(jsonObj.c169);
dialogObj.find('#c195').val(jsonObj.c195);
dialogObj.find('#c170').val(jsonObj.c170);
dialogObj.find('#c171').val(jsonObj.c171);
dialogObj.find('#c172').val(jsonObj.c172);
dialogObj.find('#c173').val(jsonObj.c173);
dialogObj.find('#c174').val(jsonObj.c174);
dialogObj.find('#c175').val(jsonObj.c175);
dialogObj.find('#c176').val(jsonObj.c176);
dialogObj.find('#c177').val(jsonObj.c177);
dialogObj.find('#c178').val(jsonObj.c178);
dialogObj.find('#c179').val(jsonObj.c179);
dialogObj.find('#c180').val(jsonObj.c180);
dialogObj.find('#c181').val(jsonObj.c181);
dialogObj.find('#c182').val(jsonObj.c182);
dialogObj.find('#c183').val(jsonObj.c183);
dialogObj.find('#c184').val(jsonObj.c184);
dialogObj.find('#c185').val(jsonObj.c185);
dialogObj.find('#c186').val(jsonObj.c186);
dialogObj.find('#c187').val(jsonObj.c187);
dialogObj.find('#c188').val(jsonObj.c188);
dialogObj.find('#c190').val(jsonObj.c190);
dialogObj.find('#c192').val(jsonObj.c192);
dialogObj.find('#c193').val(jsonObj.c193);
dialogObj.find('#c196').val(jsonObj.c196);
dialogObj.find('#c197').val(jsonObj.c197);
		//
		var val = jsonObj.testtype;
		dialogObj.find('#TESTTYPE').find("option").each(function(){
			if($(this).val()==val){
				$(this).attr("selected","selected");
			}
		});
		
	}

	function clear(dialogObj){
		dialogObj.find('input').attr('value','');
		$('input').attr("readonly", false);
		dialogObj.find('#imp').css('display','');
		dialogObj.find('#tempc6').attr('size','11');
		$('.ui-dialog-buttonpane button').eq(3).attr('value','保存');
		type = null;
	}

	function isNotNull(parent,id){
		var obj = $(parent).find('#'+id);
		var value=obj.val();
		if(value!=null && value!="")
			return true;
		return false;
	}

	//获取tableid下的所有input框的id
	function FF(tableid){
			var arr=[];
		    $('#public_coc_dialog').find('#'+tableid).find("input").each(function(){
		    	var id=$(this).attr('id');
		        if(id!='undefined'){
		        arr.push(id)};
		    });
		    return arr;   
	}
	
	//获取多个tableid下的所有input框的id
	function FFS(tableids){
    	var ids=[];
    	var str= new Array();   
		str=tableids.split(",");  
		all = str.length;
		for (i=0;i<str.length ;i++ )   
		{  
        	var inputid=FF(str[i]);
            if(inputid!=''){
              ids = ids.concat(inputid)
            }
			
        }
        return ids;
	}
	
	//获取多个tableid下的所有input框的id 减去对应的多个cid下的所有input框的id
	function FFSMU(tableids,cid){
    	var allids = FFS(tableids);
		var arr=[];
		$('#public_coc_dialog').find(cid).each(function(){
			var idid=$(this).find("input").attr('id');
        	if(idid!=undefined){
				arr.push(idid);
			} 
    	});		
		
		for (var i = 0; i < arr.length; i++) {
		    for (var j = 0; j < allids.length; j++) {
		     if (allids[ j ] == arr[ i ]) {
		    	 allids.splice(j, 1);
		      j = j - 1;
		     }
		    }
		   }

		return allids;
	}	
	

	function check(id){
		//第一页begin
		if(id=="c1") return true;
		if(id=="vercode") return true;
		if(id=="tempc6") return true;
		if(id=="c2") return true;
		if(id=="c194") return true;
		if(id=="c4") return true;
		if(id=="c3") return true;
		if(id=="c4") return true;
		if(id=="c157") return true;
		if(id=="c5") return true;
		if(id=="c4") return true;
		if(id=="c98") return true;
		if(id=="c113") return true;
		if(id=="c6") return true;
		if(id=="c114") return true;
		if(id=="c191") return true;
		if(id=="c192") return true;
		if(id=="c193") return true;
		if(id=="c158") return true;
		if(id=="c150") return true;
		if(id=="c151") return true;
		if(id=="c152") return true;
		if(id=="c7") return true;
		if(id=="c95") return true;
		if(id=="c96") return true;
		if(id=="c97") return true;
		if(id=="c10") return true;
		if(id=="c12") return true;
		if(id=="c13") return true;
		if(id=="c14") return true;
		if(id=="c15") return true;
		if(id=="c16") return true;
		if(id=="c17") return true;
		if(id=="c18") return true;
		if(id=="c19") return true;
		if(id=="c22") return true;
		if(id=="c20") return true;
		if(id=="c21") return true;
		if(id=="c24") return true;
		if(id=="c25") return true;
		if(id=="c26") return true;
		if(id=="c27") return true;
		if(id=="c28") return true;
		if(id=="c29") return true;
		if(id=="c30") return true;
		if(id=="printtype") return true;
		if(id=="factory") return true;
		if(id=="c155") return true;
		if(id=="c156") return true;
		//第一页end
		//添加数组IndexOf方法
		//添加数组IndexOf方法

		if (!Array.prototype.indexOf) {
				Array.prototype.indexOf = function(elt /*, from*/) {
					var len = this.length >>> 0;
					var from = Number(arguments[1]) || 0;
					from = (from < 0) ? Math.ceil(from) : Math.floor(from);
					if (from < 0)
						from += len;
					for (; from < len; from++) {
						if (from in this && this[from] === elt)
							return from;
					}
					return -1;
				};
			}
			//第二页打印模板判断需校验的字段
			//打印模版类型，1:汽油 2：非插电混动 3：纯电动 4：两用燃料 5：插电混动 6：NGCNG 7:氢燃料电池
			var prt = $('#public_coc_dialog').find('#printtype').val();
			
			if (FFS('two').indexOf(id) != -1)
				return true;
			if (prt == '1') { //	1:汽油	
				if ($.inArray(id, FFS('two_1')) != -1)
					return true;
			} else if (prt == '2') { //2：非插电混动
				if (FFS('two_1,two_2,two_3').indexOf(id) != -1)
					return true;
			} else if (prt == '3') { // 3：纯电动
				if (FFSMU('two_1,two_2,two_3', '.zjps').indexOf(id) != -1)
					return true;
			} else if (prt == '4') {//4：两用燃料
				if (FFS('two_1').indexOf(id) != -1)
					return true;
			} else if (prt == '5') { //5：插电混动
				if (FFS('two_1,two_2,two_3').indexOf(id) != -1)
					return true;
			} else if (prt == '6') { // 6：NGCNG
				if (FFS('two_1').indexOf(id) != -1)
					return true;
			} else {//7:氢燃料电池
				if (FFSMU('two_1,two_2,two_4', '.zjps').indexOf(id) != -1)
					return true;
			}
			if (FFS('two_end').indexOf(id) != -1)
				return true;
			//第三页
			if (prt == '1') {
				if (FFSMU('three,three_1,three_6,three_7', '.fdjzs,.yd')
						.indexOf(id) != -1)
					return true;
			} else if (prt == '2') {
				if (FFSMU('three,three_1,three_6,three_7', '.fdjzs,.yd')
						.indexOf(id) != -1)
					return true;
			} else if (prt == '3') {
				if (FFSMU('three,three_3,three_4', '.fdjzs,.yd').indexOf(id) != -1)
					return true;
			} else if (prt == '4') {
				if (FFSMU('three_1,three_2,three_6,three_8', '.yd').indexOf(id) != -1)
					return true;
			} else if (prt == '5') {
				if (FFSMU('three,three_1,three_3,three_5', '.fdjzs,.yd')
						.indexOf(id) != -1)
					return true;
			} else if (prt == '6') {
				if (FFSMU('three,three_1,three_6,three_7', '.fdjzs,.yd')
						.indexOf(id) != -1)
					return true;
			} else {//7:氢燃料电池
				if (FFSMU('three,three_9', '.fdjzs').indexOf(id) != -1)
					return true;
			}
			if (FFS('three_end').indexOf(id) != -1)
				return true;
		}

		//那些字段需要校验
		function isNeedValidate(parent, id) {
			if (check(id)) {
				if (isNotNull(parent, id))
					return true;
				return false;
			}
			return false;

		}

		function validate(parent) {
			var obj = $(parent).find('#c1');
			var max = 18;
			if (isNeedValidate(parent, 'c1') && !checkLength(obj, 18, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段长度必须为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#tempc6');
			max = 20;
			if (isNeedValidate(parent, 'tempc6') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为20！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c6');
			max = 20;
			if (isNeedValidate(parent, 'c6') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为20！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c2');
			max = 40;
			if (isNeedValidate(parent, 'c2') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c3');
			max = 200;
			if (isNeedValidate(parent, 'c3') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c4');
			max = 20;
			if (isNeedValidate(parent, 'c4') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c157');
			max = 100;
			if (isNeedValidate(parent, 'c157') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c5');
			max = 10;
			if (isNeedValidate(parent, 'c5') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c112');
			max = 20;
			if (isNeedValidate(parent, 'c112') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c98');
			max = 20;
			if (isNeedValidate(parent, 'c98') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c113');
			max = 20;
			if (isNeedValidate(parent, 'c113') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c114');
			max = 20;
			if (isNeedValidate(parent, 'c114') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c191');
			max = 50;
			if (isNeedValidate(parent, 'c191') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c192');
			max = 50;
			if (isNeedValidate(parent, 'c192') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c193');
			max = 50;
			if (isNeedValidate(parent, 'c193') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c158');
			max = 10;
			if (isNeedValidate(parent, 'c158') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c150');
			max = 10;
			if (isNeedValidate(parent, 'c150') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c151');
			max = 20;
			if (isNeedValidate(parent, 'c151') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c152');
			max = 20;
			if (isNeedValidate(parent, 'c152') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c7');
			max = 20;
			if (isNeedValidate(parent, 'c7') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为10！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c95');
			max = 20;
			if (isNeedValidate(parent, 'c95') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c96');
			max = 20;
			if (isNeedValidate(parent, 'c96') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c97');
			max = 20;
			if (isNeedValidate(parent, 'c97') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c10');
			max = 200;
			if (isNeedValidate(parent, 'c10') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c12');
			max = 50;
			if (isNeedValidate(parent, 'c12') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c13');
			max = 60;
			if (isNeedValidate(parent, 'c13') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c14');
			max = 50;
			if (isNeedValidate(parent, 'c14') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c15');
			max = 20;
			if (isNeedValidate(parent, 'c15') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c16');
			max = 20;
			if (isNeedValidate(parent, 'c16') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c17');
			max = 2;
			if (isNeedValidate(parent, 'c17') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c18');
			max = 2;
			if (isNeedValidate(parent, 'c18') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c19');
			max = 10;
			if (isNeedValidate(parent, 'c19') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c20');
			max = 10;
			if (isNeedValidate(parent, 'c20') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c21');
			max = 10;
			if (isNeedValidate(parent, 'c21') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c22');
			max = 10;
			if (isNeedValidate(parent, 'c22') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c24');
			max = 10;
			if (isNeedValidate(parent, 'c24') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c25');
			max = 10;
			if (isNeedValidate(parent, 'c25') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c26');
			max = 10;
			if (isNeedValidate(parent, 'c26') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}

			obj = $(parent).find('#c27');
			max = 10;
			if (isNeedValidate(parent, 'c27') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c28');
			max = 10;
			if (isNeedValidate(parent, 'c86') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c29');
			max = 5;
			if (isNeedValidate(parent, 'c29') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c30');
			max = 5;
			if (isNeedValidate(parent, 'c30') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#printtype');
			max = 5;
			if (isNeedValidate(parent, 'printtype')
					&& !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#factory');
			max = 10;
			if (isNeedValidate(parent, 'factory') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为10！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c155');
			max = 20;
			if (isNeedValidate(parent, 'c155') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c156');
			max = 20;
			if (isNeedValidate(parent, 'c156') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c32');
			max = 5;
			if (isNeedValidate(parent, 'c32') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c33');
			max = 5;
			if (isNeedValidate(parent, 'c33') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c34');
			max = 5;
			if (isNeedValidate(parent, 'c34') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c159');
			max = 10;
			if (isNeedValidate(parent, 'c159') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c45');
			max = 20;
			if (isNeedValidate(parent, 'c45') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c115');
			max = 20;
			if (isNeedValidate(parent, 'c115') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c46');
			max = 20;
			if (isNeedValidate(parent, 'c46') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c47');
			max = 20;
			if (isNeedValidate(parent, 'c47') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c49');
			max = 20;
			if (isNeedValidate(parent, 'c49') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c166');
			max = 10;
			if (isNeedValidate(parent, 'c166') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c167');
			max = 10;
			if (isNeedValidate(parent, 'c167') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c168');
			max = 10;
			if (isNeedValidate(parent, 'c168') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c169');
			max = 10;
			if (isNeedValidate(parent, 'c169') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c195');
			max = 10;
			if (isNeedValidate(parent, 'c195') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c32');
			max = 5;
			if (isNeedValidate(parent, 'c32') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c33');
			max = 5;
			if (isNeedValidate(parent, 'c33') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c34');
			max = 5;
			if (isNeedValidate(parent, 'c34') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c159');
			max = 10;
			if (isNeedValidate(parent, 'c159') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c45');
			max = 20;
			if (isNeedValidate(parent, 'c45') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c115');
			max = 20;
			if (isNeedValidate(parent, 'c115') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c46');
			max = 20;
			if (isNeedValidate(parent, 'c46') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c47');
			max = 20;
			if (isNeedValidate(parent, 'c47') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c49');
			max = 20;
			if (isNeedValidate(parent, 'c49') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c166');
			max = 10;
			if (isNeedValidate(parent, 'c166') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c167');
			max = 10;
			if (isNeedValidate(parent, 'c167') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c168');
			max = 10;
			if (isNeedValidate(parent, 'c168') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c169');
			max = 10;
			if (isNeedValidate(parent, 'c169') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c195');
			max = 10;
			if (isNeedValidate(parent, 'c195') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c131');
			max = 50;
			if (isNeedValidate(parent, 'c131') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c132');
			max = 50;
			if (isNeedValidate(parent, 'c132') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c134');
			max = 10;
			if (isNeedValidate(parent, 'c134') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c133');
			max = 10;
			if (isNeedValidate(parent, 'c133') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c133');
			max = 10;
			if (isNeedValidate(parent, 'c133') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c136');
			max = 50;
			if (isNeedValidate(parent, 'c136') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c154');
			max = 10;
			if (isNeedValidate(parent, 'c154') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c138');
			max = 10;
			if (isNeedValidate(parent, 'c138') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c137');
			max = 50;
			if (isNeedValidate(parent, 'c137') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c160');
			max = 50;
			if (isNeedValidate(parent, 'c160') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c161');
			max = 50;
			if (isNeedValidate(parent, 'c161') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c48');
			max = 20;
			if (isNeedValidate(parent, 'c48') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c163');
			max = 10;
			if (isNeedValidate(parent, 'c163') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c164');
			max = 10;
			if (isNeedValidate(parent, 'c164') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c165');
			max = 50;
			if (isNeedValidate(parent, 'c165') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c64');
			max = 40;
			if (isNeedValidate(parent, 'c64') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c61');
			max = 20;
			if (isNeedValidate(parent, 'c61') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c116');
			max = 20;
			if (isNeedValidate(parent, 'c116') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c51');
			max = 30;
			if (isNeedValidate(parent, 'c51') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c52');
			max = 30;
			if (isNeedValidate(parent, 'c52') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c55');
			max = 20;
			if (isNeedValidate(parent, 'c55') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c56');
			max = 20;
			if (isNeedValidate(parent, 'c56') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c58');
			max = 100;
			if (isNeedValidate(parent, 'c58') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c153');
			max = 10;
			if (isNeedValidate(parent, 'c153') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c62');
			max = 20;
			if (isNeedValidate(parent, 'c62') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c147');
			max = 10;
			if (isNeedValidate(parent, 'c147') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c53');
			max = 100;
			if (isNeedValidate(parent, 'c53') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c54');
			max = 20;
			if (isNeedValidate(parent, 'c54') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c65');
			max = 100;
			if (isNeedValidate(parent, 'c65') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c66');
			max = 10;
			if (isNeedValidate(parent, 'c66') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c68');
			max = 5;
			if (isNeedValidate(parent, 'c68') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c69');
			max = 100;
			if (isNeedValidate(parent, 'c69') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c70');
			max = 20;
			if (isNeedValidate(parent, 'c70') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c140');
			max = 20;
			if (isNeedValidate(parent, 'c140') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c71');
			max = 50;
			if (isNeedValidate(parent, 'c71') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c72');
			max = 50;
			if (isNeedValidate(parent, 'c72') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c196');
			max = 20;
			if (isNeedValidate(parent, 'c196') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c73');
			max = 50;
			if (isNeedValidate(parent, 'c73') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c149');
			max = 10;
			if (isNeedValidate(parent, 'c149') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c76');
			max = 20;
			if (isNeedValidate(parent, 'c76') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c148');
			max = 50;
			if (isNeedValidate(parent, 'c148') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c74');
			max = 10;
			if (isNeedValidate(parent, 'c74') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c75');
			max = 10;
			if (isNeedValidate(parent, 'c75') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c141');
			max = 20;
			if (isNeedValidate(parent, 'c141') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c144');
			max = 20;
			if (isNeedValidate(parent, 'c144') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c143');
			max = 20;
			if (isNeedValidate(parent, 'c143') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c142');
			max = 20;
			if (isNeedValidate(parent, 'c142') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c145');
			max = 20;
			if (isNeedValidate(parent, 'c145') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c146');
			max = 20;
			if (isNeedValidate(parent, 'c146') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c170');
			max = 50;
			if (isNeedValidate(parent, 'c170') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c171');
			max = 10;
			if (isNeedValidate(parent, 'c171') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c172');
			max = 10;
			if (isNeedValidate(parent, 'c172') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c173');
			max = 10;
			if (isNeedValidate(parent, 'c173') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c174');
			max = 10;
			if (isNeedValidate(parent, 'c174') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c175');
			max = 10;
			if (isNeedValidate(parent, 'c175') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c176');
			max = 10;
			if (isNeedValidate(parent, 'c176') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c177');
			max = 10;
			if (isNeedValidate(parent, 'c177') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c178');
			max = 10;
			if (isNeedValidate(parent, 'c178') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c84');
			max = 50;
			if (isNeedValidate(parent, 'c84') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c179');
			max = 10;
			if (isNeedValidate(parent, 'c179') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c180');
			max = 10;
			if (isNeedValidate(parent, 'c180') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c181');
			max = 10;
			if (isNeedValidate(parent, 'c181') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c182');
			max = 10;
			if (isNeedValidate(parent, 'c182') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c183');
			max = 10;
			if (isNeedValidate(parent, 'c183') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c184');
			max = 10;
			if (isNeedValidate(parent, 'c184') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c185');
			max = 10;
			if (isNeedValidate(parent, 'c185') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c186');
			max = 10;
			if (isNeedValidate(parent, 'c186') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c187');
			max = 10;
			if (isNeedValidate(parent, 'c187') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c188');
			max = 10;
			if (isNeedValidate(parent, 'c188') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c85');
			max = 5;
			if (isNeedValidate(parent, 'c85') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c86');
			max = 5;
			if (isNeedValidate(parent, 'c86') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c87');
			max = 5;
			if (isNeedValidate(parent, 'c87') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c88');
			max = 5;
			if (isNeedValidate(parent, 'c88') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c89');
			max = 5;
			if (isNeedValidate(parent, 'c89') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c90');
			max = 5;
			if (isNeedValidate(parent, 'c90') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c190');
			max = 10;
			if (isNeedValidate(parent, 'c190') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}
			obj = $(parent).find('#c94');
			max = 200;
			if (isNeedValidate(parent, 'c94') && !checkLength(obj, 1, max)) {
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'), obj.parent().prev()
						.find('p').text()
						+ '字段不能为空,最大长度为' + max + '！');
				obj.focus();
				return false;
			}

			return true;
		}

		function setDialogValue2(dialogObj, jsonObj) {
			dialogObj.find('#c1').val(jsonObj.id.c1);
			dialogObj.find('#vercode').val(jsonObj.id.vercode);
			dialogObj.find('#flag').val(jsonObj.flag);
			dialogObj.find('#c2').val(jsonObj.c2);
			dialogObj.find('#c3').val(jsonObj.c3);
			dialogObj.find('#c4').val(jsonObj.c4);
			dialogObj.find('#c27').val(jsonObj.c27);
			dialogObj.find('#c28').val(jsonObj.c28);
			dialogObj.find('#c29').val(jsonObj.c29);
			dialogObj.find('#c30').val(jsonObj.c30);
			//		dialogObj.find('#c31').val(jsonObj.c31);
			//		dialogObj.find('#c32').val(jsonObj.c32);
			//		dialogObj.find('#c33').val(jsonObj.c33);
			//		dialogObj.find('#c34').val(jsonObj.c34);
			dialogObj.find('#c5').val(jsonObj.c5);
			dialogObj.find('#c6').val(jsonObj.c6);
			dialogObj.find('#c7').val(jsonObj.c7);
			dialogObj.find('#c8').val(jsonObj.c8);
			dialogObj.find('#c9').val(jsonObj.c9);
			dialogObj.find('#c10').val(jsonObj.c10);
			dialogObj.find('#c11').val(jsonObj.c11);
			//		dialogObj.find('#c18').val(jsonObj.c18);
			dialogObj.find('#c19').val(jsonObj.c19);
			//		dialogObj.find('#c20').val(jsonObj.c20);
			//		dialogObj.find('#c25').val(jsonObj.c25);
			dialogObj.find('#c26').val(jsonObj.c26);
			dialogObj.find('#c37').val(jsonObj.c37);
			dialogObj.find('#c38').val(jsonObj.c38);
			dialogObj.find('#c39').val(jsonObj.c39);
			dialogObj.find('#c40').val(jsonObj.c40);
			dialogObj.find('#c41').val(jsonObj.c41);
			dialogObj.find('#c42').val(jsonObj.c42);
			//		dialogObj.find('#c43').val(jsonObj.c43);
			//		dialogObj.find('#c44').val(jsonObj.c44);
			//		dialogObj.find('#c45').val(jsonObj.c45);
			dialogObj.find('#c46').val(jsonObj.c46);
			dialogObj.find('#c47').val(jsonObj.c47);
			//		dialogObj.find('#c48').val(jsonObj.c48);
			dialogObj.find('#c49').val(jsonObj.c49);
			dialogObj.find('#c50').val(jsonObj.c50);
			dialogObj.find('#c51').val(jsonObj.c51);
			//		dialogObj.find('#c52').val(jsonObj.c52);
			dialogObj.find('#c53').val(jsonObj.c53);
			//		dialogObj.find('#c54').val(jsonObj.c54);
			//		dialogObj.find('#c55').val(jsonObj.c55);
			//		dialogObj.find('#c56').val(jsonObj.c56);
			dialogObj.find('#c57').val(jsonObj.c57);
			//		dialogObj.find('#c58').val(jsonObj.c58);
			dialogObj.find('#c59').val(jsonObj.c59);
			dialogObj.find('#c60').val(jsonObj.c60);
			//		dialogObj.find('#c61').val(jsonObj.c61);
			dialogObj.find('#c62').val(jsonObj.c62);
			dialogObj.find('#c63').val(jsonObj.c63);
			//		dialogObj.find('#c64').val(jsonObj.c64);
			//		dialogObj.find('#c65').val(jsonObj.c65);
			//		dialogObj.find('#c66').val(jsonObj.c66);
			//		dialogObj.find('#c67').val(jsonObj.c67);
			//		dialogObj.find('#c68').val(jsonObj.c68);
			//		dialogObj.find('#c69').val(jsonObj.c69);
			//		dialogObj.find('#c70').val(jsonObj.c70);
			//		dialogObj.find('#c71').val(jsonObj.c71);
			//		dialogObj.find('#c72').val(jsonObj.c72);
			dialogObj.find('#c73').val(jsonObj.c73);
			dialogObj.find('#c86').val(jsonObj.c86);
			dialogObj.find('#c88').val(jsonObj.c88);
			dialogObj.find('#c89').val(jsonObj.c89);
			dialogObj.find('#c79').val(jsonObj.c79);
			dialogObj.find('#c77').val(jsonObj.c77);
			dialogObj.find('#c80').val(jsonObj.c80);
			dialogObj.find('#c76').val(jsonObj.c76);

			if (jsonObj.c76 == 'GV') {

			} else {

			}
			dialogObj.find('#state').val(jsonObj.state);
		}

		$('#jump').bind(
				'keyup',
				function(event) {
					var obj = $('#jump');
					if (!checkRegexp(obj, /^([0-9])+$/)) {
						obj.val("");
						return;
					}
					$(this).blur(
							function() {
								var qc1 = $('#qc1').val();
								var qstate = $('#qstate').val();
								location.href = "cocVer.action?currentPage="
										+ $('#jump').val() + "&qc1="
										+ encodeURI(encodeURI(qc1))
										+ "&qstate=" + qstate + "&qfactory="
										+ qfactory + "&menuid=" + menuid;
							});
				});

		$(".jumpPage").each(
				function(i) {
					$(this).click(
							function() {
								var qc1 = $('#qc1').val();
								var qstate = $('#qstate').val();
								var qfactory = $('#qfactory').val();

								location.href = $(this).attr('value') + "&qc1="
										+ encodeURI(encodeURI(qc1))
										+ "&qstate=" + qstate + "&qfactory="
										+ qfactory + "&menuid=" + menuid;
							});
				});

		$("#print").click(
				function() {
					var id = "";
					var index = 0;
					var messageObj = null;
					var checkedObj = $('#users-contain').find(
							"[name='checkPK'][@checked]");
					checkedObj.each(function() {
						if (this.checked == true) {
							index++;
							id = this.value;
						}
					});

					if (index == 1) {
						var c1 = id.split(",")[0];
						var vercode = id.split(",")[1];
						//alert(window.document);
						window.document.coc.printCocVersion(c1, vercode);
					} else if (index < 1) {
						messageObj = $('#message_dialog');
						messageObj.find('#message').text('警告:请选要打印的数据！');
						messageObj.dialog('open');
					} else if (index > 1) {
						messageObj = $('#message_dialog');
						messageObj.find('#message').text('警告:一次只能打印一条数据！');
						messageObj.dialog('open');
					}
				});

		$(document).ready(function() {
			$("#users").find("tr").mouseover(function() {
				$(this).addClass("over");
			}).mouseout(function() {
				//给这行添加class值为over，并且当鼠标一出该行时执行函数
				$(this).removeClass("over");
			}) //移除该行的class
		});
	});
	function printtypechange() {
		var dailog = $("#public_coc_dialog");
		var printtype = dailog.find("#printtype").val();
	}
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="6"><p>生产车型:<input type="text" id="qc1" name='qc1' class="text ui-widget-content " size="18" <s:if test="#request.qc1!=null"> value="<s:property value="#request.qc1" />"</s:if> />	
				  状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 
				  工厂:<s:select name="qfactory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.qfactory"></s:select>
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr>
				<td width="80%"></td>
			  	<td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  	<td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  	<td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
  			  	<td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
  			  	<td width="60" align="right"><button id="print" class="ui-button ui-state-default ui-corner-all">打印</button></td>
  			  	<td width="60" align="right"><button id="compare" class="ui-button ui-state-default ui-corner-all">版本比较</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="6%">选择</th> 
			    <th width="10%">生产车型</th>
			    <th width="10%">公告车型</th>	
				<th width="8%">状态</th>
				<th width="10%">版本</th>
				<th width="10%">创建人</th>
				<th width="18%">创建时间</th>
				<th width="18%">生效时间</th>
				<th width="6%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.publicNoticePageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td>
			  			<input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.c1" />,<s:property value="id.vercode" />' >
			  			<input type="hidden" name="state" id="state" value='<s:property value="state"/>'>
			  		</td>
			  		<td><s:property value="id.c1" /></td>
   		      		<td><s:property value="c6" /></td>				
			  		<td>
			  			<s:if test="state==1">生效</s:if>
			  			<s:else>
			  				<s:if test="state==9">历史</s:if>
			  				<s:else>未生效</s:else>
						</s:else>
			  		</td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="creator" /></td>		
			  		<td><s:date name="createdate" format="yyyy-MM-dd HH:mm:ss"/></td>	
			  		<td><s:property value="effecttime" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.c1" />,<s:property value="id.vercode" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.publicNoticePage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocVer.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocVer.action?currentPage=<s:property value="#request.publicNoticePage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage>=#request.publicNoticePage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocVer.action?currentPage=<s:property value="#request.publicNoticePage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="cocVer.action?currentPage=<s:property value="#request.publicNoticePage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.publicNoticePage.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.publicNoticePage.currentPage" />/总页数 <s:property value="#request.publicNoticePage.maxPage" /> 总记录数 <s:property value="#request.publicNoticePage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="public_coc_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" >
	  	<table id="one" width="100%" border="0">
	  		<tr>
				<td width="117"><label><P>生产车型</label></td>
				<td colspan="2"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="20" maxlength="18"/></td>
				<td width="90"><label><P>版本号</label></td>
				<td colspan="2"><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    		<td><label><P>车型代号</label></td>
				<td colspan="2">
					<input type="text" id="tempc6" name="tempc6" class="text ui-widget-content ui-corner-all" size="11" />&nbsp;
					<button name="imp" id="imp" class="ui-button ui-state-default ui-corner-all" style="position:static">导入</button>
				</td>
	    	</tr>
	  		<tr>		
	    		<td><label><P>车辆一致性<br>性证书编号</label></td>
				<td colspan="2"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>基本车辆制造国</label></td>
				<td colspan="2"><input type="text" id="c194" name="c194" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>最终阶段车辆制造国</label></td>
				<td colspan="2"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		</tr>
	  		<tr>			  		
	  			<td><label><P>车辆生产企业名称</label></td>
				<td colspan="2"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车辆生产企业地址</label></td>
				<td colspan="5"><input type="text" id="c157" name="c157" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		</tr>
	  		<tr>
	  			<td><label><P>车型系列<br>代号/名称</label></td>
				<td colspan="2">
					<input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="8" />
					<input type="text" id="c112" name="c112" class="text ui-widget-content ui-corner-all" size="8" />
				</td>
				<td><label><P>单元代号/名称</label></td>
				<td colspan="2">
					<input type="text" id="c98" name="c98" class="text ui-widget-content ui-corner-all" size="8" />
					<input type="text" id="c113" name="c113" class="text ui-widget-content ui-corner-all" size="8" /></td>
	  			<td><label><P>车型代号/名称</label></td>
				<td colspan="2">
					<input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="8" readonly="readonly"/>
					<input type="text" id="c114" name="c114" class="text ui-widget-content ui-corner-all" size="8" /></td>
	  		</tr>
	  		<tr>		
	    		<td><label><P>认证委托人名称</label></td>
				<td colspan="2"><input type="text" id="c191" name="c191" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>认证委托人联系方式：联系人/联系电话</label></td>
				<td colspan="2">
					<input type="text" id="c192" name="c192" class="text ui-widget-content ui-corner-all" size="7" />
					<input type="text" id="c193" name="c193" class="text ui-widget-content ui-corner-all" size="7" />
				</td>
				<td><label><P>车辆识别代号是否使用车型年份</label></td>
				<td colspan="2">
				   <select id="c158" name="c158">
				    <option value="">请选择</option>
				    <option value="是">是</option>
				    <option value="否">否</option>
			       </select></td>
	  		</tr>
	  		<tr><!-- 2018-11-22 确定COCA4纸模板 添加是否新能源车、车辆注册类型 、车型种类 -->
			    <td><label><P>新能源车</label></td>
				<td colspan="2">
				   <select id="c150" name="c150">
				    <option value="">请选择</option>
				    <option value="是">是</option>
				    <option value="否">否</option>
			       </select></td>
				<td><label><P>车辆注册类型</label></td>
				<td colspan="2"><input type="text" id="c151" name="c151" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车辆型式</label></td>
				<td colspan="2"><input type="text" id="c152" name="c152" class="text ui-widget-content ui-corner-all" size="20" /></td> 
			</tr>
	  		<tr>
	  			<td><label><P>车型名称</label></td>
				<td colspan="2"><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="20" /></td>
			
				<td><label><P>车辆中文品牌</label></td>
				<td colspan="2"><input type="text" id="c95" name="c95" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车辆英文品牌</label></td>
				<td colspan="2"><input type="text" id="c96" name="c96" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		</tr>
			<tr>
	  			<td><label><P>车辆类别</label></td>
				<td colspan="2"><input type="text" id="c97" name="c97" class="text ui-widget-content ui-corner-all" size="20" /></td>
<!-- 			
				<td><label><P>基本车辆<br>制造商名称</label></td>
				<td colspan="2"><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>基本车辆<br>制造商地址</label></td>
				<td colspan="2"><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
			 -->	
				<td><label><P>生产者（制造商）名称</label></td>
				<td colspan="2"><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<!--  
				<td><label><P>最终制造阶段<br>的制造商地址</label></td>
				<td colspan="2"><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="20" /></td>
		-->
				<td><label><P>产品标牌的位置</label></td>
				<td colspan="2"><input type="text" id="c12" name="c12" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>	
			<tr>
				<td><label><P>车辆识别代<br>号打刻位置</label></td>
				<td colspan="2"><input type="text" id="c13" name="c13" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>发动机编号在<br>发动机上的位置</label></td>
				<td colspan="2"><input type="text" id="c14" name="c14" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>CCC证书编号<br>(版本号)</label></td>
				<td colspan="2"><input type="text" id="c15" name="c15" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td><label><P>签发日期</label></td>
				<td colspan="2"><input type="text" id="c16" name="c16" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车轴数量</label></td>
				<td colspan="2"><input type="text" id="c17" name="c17" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车轮数量</label></td>
				<td colspan="2"><input type="text" id="c18" name="c18" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td><label><P>驱动轴位置</label></td>
				<td colspan="2"><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>轴距</label></td>
				<td colspan="2"><input type="text" id="c22" name="c22" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>轮距</label></td>
				<td colspan="2">
					<input type="text" id="c20" name="c20" class="text ui-widget-content ui-corner-all" size="8" />
					<input type="text" id="c21" name="c21" class="text ui-widget-content ui-corner-all" size="8" />
				</td>
			</tr>
			<tr>
				<td><label><P>外廓长度/宽度/高度(mm)</label></td>
				<td colspan="2">
					<input type="text" id="c24" name="c24" class="text ui-widget-content ui-corner-all" size="4" />
					<input type="text" id="c25" name="c25" class="text ui-widget-content ui-corner-all" size="4" />
					<input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="4" />
				</td>
				<td><label><P>前悬</label></td>
				<td colspan="2"><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>后悬</label></td>
				<td colspan="2"><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td><label><P>接近角</label></td>
				<td colspan="2"><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>离去角</label></td>
				<td colspan="2"><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>打印模板</label></td>
				<td colspan="2"><!-- 2018.10.29 将之前打印模板选择改变，取消国v，添加混合动力模板  2018.11.20 增加两用燃料模板-->
				<select id="printtype" name="printtype"  onchange="printtypechange()">
				<option value="">请选择</option>
				<option value="1">汽油</option>
				<option value="2">非插电混动</option>
				<option value="3">纯电动</option>
				<option value="4">两用燃料</option>
				<option value="5">插电混动</option>
				<option value="6">NGCNG</option>
				<option value="7">氢燃料电池</option>
				</select>
				</td>				
			</tr>
			<tr>
				<td><label><P>工厂</label></td>
				<td colspan="2"><s:select name="factory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value=""></s:select></td>
				<td><label><P>整备质量(kg)</label></td><!-- 2018-11-26 增加整备质量字段 -->
				<td colspan="2"><input type="text" id="c155" name="c155" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>转向型式</label></td>
				<td colspan="2"><input type="text" id="c156" name="c156" class="text ui-widget-content ui-corner-all" size="20" />
				</td>				
			</tr>			
		</Table>
		<table id="two" width="100%" style="display:none">
			<tr>
				<td><label><P>最大允许总质量（kg）</label></td>
				<td colspan="2"><input type="text" id="c32" name="c32" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td width="129"><label><P>最大允许总质量下的轴荷（kg）</label></td>
				<td colspan="2">
					<input type="text" id="c33" name="c33" class="text ui-widget-content ui-corner-all" size="7" />
					<input type="text" id="c34" name="c34" class="text ui-widget-content ui-corner-all" size="7" />
				</td>
				
			</tr>
			</table>
			<table id="two_1" style="display:none">
			<tr>
				<td class="qdxs"><label><P>驱动型式</label></td>
				<td colspan="2" class="qdxs"><input type="text" id="c159" name="c159" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td class="zjps"><label><P>直接喷射</label></td>
				<td colspan="2" class="zjps"><input type="text" id="c45" name="c45" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td class="zjps"><label><P>汽缸数量</label></td>
				<td colspan="2" class="zjps"><input type="text" id="c115" name="c115" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td class="zjps"><label><P>汽缸排列形式</label></td>
				<td colspan="2" class="zjps"><input type="text" id="c46" name="c46" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td class="zjps"><label><P>排量</label></td>
				<td colspan="2" class="zjps"><input type="text" id="c47" name="c47" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td class="zjps"><label><P>最大净功率</label></td>
				<td colspan="2" class="zjps"><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td class="zjps"><label><P>燃料种类</label></td>
				<td colspan="2" class="zjps"><input type="text" id="c48" name="c48" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr id=qdxs>
				<td><label><P>货箱内部长度/宽度/高度(mm)</label></td>
				<td colspan="2">
					<input type="text" id="c166" name="c166" class="text ui-widget-content ui-corner-all" size="4" />
					<input type="text" id="c167" name="c167" class="text ui-widget-content ui-corner-all" size="4" />
					<input type="text" id="c168" name="c168" class="text ui-widget-content ui-corner-all" size="4" />
				</td>
				<td><label><P>车辆是否适合拖挂/准牵引总质量</label></td>
				<td colspan="2">
					<input type="text" id="c169" name="c169" class="text ui-widget-content ui-corner-all" size="7" />
					<input type="text" id="c195" name="c195" class="text ui-widget-content ui-corner-all" size="7" />
				</td>
			</tr>
			</table>
			<table id="two_2" style="display:none">
			<tr>
				<td><label><P>驱动电机型号</label></td>
				<td colspan="2"><input type="text" id="c131" name="c131" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>驱动电机生产厂名称</label></td>
				<td colspan="2"><input type="text" id="c132" name="c132" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>驱动电机工作电压</label></td>
				<td colspan="2"><input type="text" id="c134" name="c134" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>	
				<td><label><P>驱动电机峰值功率</label></td>
				<td colspan="2"><input type="text" id="c133" name="c133" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>驱动电机编号在驱动电机上的打刻位置</label></td>
				<td colspan="2"><input type="text" id="c197" name="c197" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			</table>
			<table id="two_3" style="display:none">
			<tr>
				<td><label><P>动力蓄电池型号</label></td>
				<td colspan="2"><input type="text" id="c136" name="c136" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>动力蓄电池额定电压</label></td>
				<td colspan="2"><input type="text" id="c154" name="c154" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>动力蓄电池额定容量</label></td>
				<td colspan="2"><input type="text" id="c138" name="c138" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>	
				<td><label><P>动力蓄电池生产厂名称</label></td>
				<td colspan="2"><input type="text" id="c137" name="c137" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			</table>
			<table id="two_4" style="display:none">
			<tr>
				<td><label><P>燃料电池型号</label></td>
				<td colspan="2"><input type="text" id="c160" name="c160" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>燃料电池生产企业名称</label></td>
				<td colspan="2"><input type="text" id="c161" name="c161" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>燃料电池额定功率(kW)</label></td>
				<td colspan="2"><input type="text" id="c163" name="c163" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td><label><P>燃料电池额定电压(V)</label></td>
				<td colspan="2"><input type="text" id="c164" name="c164" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>储氢容器型号</label></td>
				<td colspan="2"><input type="text" id="c165" name="c165" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			</table>
			
			<table id="two_end" style="display:none">
			<tr>
				<td><label><P>最高车速</label></td>
				<td colspan="2"><input type="text" id="c64" name="c64" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车门数量</label></td>
				<td colspan="2"><input type="text" id="c61" name="c61" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车门结构</label></td>
				<td colspan="2"><input type="text" id="c116" name="c116" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>	
				<td><label><P>离合器型式</label></td>
				<td colspan="2"><input type="text" id="c51" name="c51" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>变速器型式</label></td>
				<td colspan="2"><input type="text" id="c52" name="c52" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>轮胎规格<br>第1轴/第2轴</label></td>
				<td colspan="2">
					<input type="text" id="c55" name="c55" class="text ui-widget-content ui-corner-all" size="8" />
					<input type="text" id="c56" name="c56" class="text ui-widget-content ui-corner-all" size="8" /></td>
			</tr>
			<tr>
				<td><label><P>制动装置简要说明</label></td>
				<td colspan="2"><input type="text" id="c58" name="c58" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>是否带防抱死系统</label></td>
				<td colspan="2">
				    <select name="c153" id="c153">
						<option value="">请选择类型</option>
						<option value="是">是</option>
						<option value="否">否</option>
					</select>
				</td>
				<td><label><P>额定载客人数</label></td>
				<td colspan="2"><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr>
				<td ><label><P>钢板弹簧片数(片)</label></td>
				<td colspan="2"><input type="text" id="c147" name="c147" class="text ui-widget-content ui-corner-all" size="20" /></td>    
				<td><label><P>速比</label></td>
				<td colspan="2"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>主传动比</label></td>
				<td colspan="2"><input type="text" id="c54" name="c54" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
		</Table>
		
		
		<table id="three" width="100%" style="display:none">
			<tr><td><label><P>声级：</label></td></tr>
			<tr>
			    <td width="129"><label><P>CCC认证引用的标准号及对应的实施阶段</label></td>
				<td colspan="2"><input type="text" id="c65" name="c65" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
	    	<tr>				
				<td width="129"><label><P>定置噪声 </label></td>
				<td colspan="2"><input type="text" id="c66" name="c66" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td class="fdjzs"><label><P>对应的发动机转速</label></td>
				<td colspan="2" class="fdjzs"><input type="text" id="c119" name="c119" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>加速行驶车外噪声</label></td>
				<td colspan="2"><input type="text" id="c68" name="c68" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>			
			</table>			
			<table id="three_1" style="display:none">
			<tr><td><label><P>排气排放物：</label></td></tr>
			<tr>				
				<td ><label><P>CCC认证引用的标准<br>号及对应的实施阶段 </label></td>
				<td colspan="2" class="nocdd"><input type="text" id="c69" name="c69" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td ><label><P>试验用液体燃料</label></td>
				<td colspan="2" class="nocdd"><input type="text" id="c70" name="c70" class="text ui-widget-content ui-corner-all" size="18" /></td>
			    <td ><label><P>试验用气体燃料</label></td>
				<td colspan="2" class="nocdd"><input type="text" id="c140" name="c140" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>			
			<tr >
				<td><label><P>CO</label></td>
				<td colspan="2"><input type="text" id="c71" name="c71" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>THC</label></td>
				<td colspan="2"><input type="text" id="c72" name="c72" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>NMHC</label></td>
				<td colspan="2"><input type="text" id="c196" name="c196" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr >	
				<td><label><P>NOx</label></td>
				<td colspan="2"><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="18" /></td>
				 <td><label><P>N2O</label></td>
				<td colspan="2"><input type="text" id="c149" name="c149" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>PM</label></td>
				<td colspan="2"><input type="text" id="c76" name="c76" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr >	
				<td><label><P>PN:个/km</label></td>
				<td colspan="2"><input type="text" id="c148" name="c148" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td class="yd"><label><P>HC+NOx</label></td>
				<td colspan="2" class="yd"><input type="text" id="c74" name="c74" class="text ui-widget-content ui-corner-all" size="18" /></td>
			  	<td class="yd"><label><P>烟度</label></td>
				<td colspan="2" class="yd"><input type="text" id="c75" name="c75" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			</table>
			<table id="three_2" style="display:none">
			<tr><td><label><P>排气排放物(气体燃料)：</label></td></tr>		
			<tr >
				<td><label><P>CO</label></td>
				<td colspan="2"><input type="text" id="c141" name="c141" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>NOx</label></td>
				<td colspan="2"><input type="text" id="c144" name="c144" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>NMHC</label></td>
				<td colspan="2"><input type="text" id="c143" name="c143" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr >		
				<td><label><P>THC</label></td>
				<td colspan="2"><input type="text" id="c142" name="c142" class="text ui-widget-content ui-corner-all" size="18" /></td>
				 <td><label><P>CH4</label></td>
				<td colspan="2"><input type="text" id="c145" name="c145" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>微粒物PM</label></td>
				<td colspan="2"><input type="text" id="c146" name="c146" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			</table>
			<table id="three_3"  style="display:none">
			<tr><td colspan="2"><label><P>能源消耗量：</label></td></tr>
			<tr>
				<td><label><P>CCC认证所依据的标准号</label></td>
				<td colspan="2"><input type="text" id="c170" name="c170" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<table id="three_4"  style="display:none">
			<tr>
				<td><label><P>综合工况电能消耗量</label></td>
				<td colspan="2"><input type="text" id="c171" name="c171" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>电能当量燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c172" name="c172" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>续驶里程</label></td>
				<td colspan="2"><input type="text" id="c173" name="c173" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<table id="three_5"  style="display:none">
			<tr>
				<td><label><P>燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c174" name="c174" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>电能消耗量</label></td>
				<td colspan="2"><input type="text" id="c175" name="c175" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>电能当量燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c176" name="c176" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr>
				<td><label><P>最低荷电状态燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c177" name="c177" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>纯电动续驶里程</label></td>
				<td colspan="2"><input type="text" id="c178" name="c178" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			</table>
			
			<table id="three_6"  style="display:none">
			<tr><td colspan="2"><label><P>CO2排放量/燃料消耗量：</label></td></tr>
			<tr>
				<td><label><P>CCC认证引用的标准号</label></td>
				<td colspan="2"><input type="text" id="c84" name="c84" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<table id="three_7"  style="display:none">
			<tr>
				<td><label><P>低速段 CO2排放量（g/km）</label></td>
				<td colspan="2"><input type="text" id="c179" name="c179" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>中速段 CO2排放量（g/km）</label></td>
				<td colspan="2"><input type="text" id="c180" name="c180" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>高速段 CO2排放量（g/km）</label></td>
				<td colspan="2"><input type="text" id="c181" name="c181" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr>	
				<td><label><P>超高速段 CO2排放量（g/km）</label></td>
				<td colspan="2"><input type="text" id="c182" name="c182" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>综合 CO2排放量（g/km）</label></td>
				<td colspan="2"><input type="text" id="c183" name="c183" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr>
				<td><label><P>低速段 燃料消耗量（L/100km）</label></td>
				<td colspan="2"><input type="text" id="c184" name="c184" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>中速段  燃料消耗量（L/100km）</label></td>
				<td colspan="2"><input type="text" id="c185" name="c185" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>高速段  燃料消耗量（L/100km）</label></td>
				<td colspan="2"><input type="text" id="c186" name="c186" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr>	
				<td><label><P>超高速段  燃料消耗量（L/100km）</label></td>
				<td colspan="2"><input type="text" id="c187" name="c187" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>综合  燃料消耗量（L/100km）</label></td>
				<td colspan="2"><input type="text" id="c188" name="c188" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			</table>
			<table id="three_8"  style="display:none">
			<tr>
				<td><label><P>市区 CO2排放量</label></td>
				<td colspan="2"><input type="text" id="c85" name="c85" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>市郊 CO2排放量</label></td>
				<td colspan="2"><input type="text" id="c86" name="c86" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>综合 CO2排放量</label></td>
				<td colspan="2"><input type="text" id="c87" name="c87" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			<tr>
				<td><label><P>市区 燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>市郊  燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="18" /></td>
				<td><label><P>综合  燃料消耗量</label></td>
				<td colspan="2"><input type="text" id="c90" name="c90" class="text ui-widget-content ui-corner-all" size="18" /></td>
			</tr>
			</table>
			<table id="three_9"  style="display:none">
			<tr>
				<td><label><P>续驶里程</label></td>
				<td colspan="2"><input type="text" id="c190" name="c190" class="text ui-widget-content ui-corner-all" size="10" /></td>
			</tr>
			</table>
			<table id="three_end"  style="display:none">
			<tr>
				<td><label><P>备注</label></td>
				<td colspan="8"><input type="text" id="c94" name="c94" class="text ui-widget-content ui-corner-all" size="70" /></td>
			</tr>
			</table>
		</table>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>


<div id="coc_compare_dialog" style="display:none;overflow-y:auto; ">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" onsubmit="return false;" >
			<table>
				<tr>
					<td ><label><P>版本号1：</label></td>
					<td name='ver1'><s:select cssClass='command' name="cocVer1" list="#request.cocVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.cocVer1" ></s:select></td>
					<td ><label><P>版本号2：</label></td>
					<td name='ver2'><s:select cssClass='command' name="cocVer2" list="#request.cocVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.cocVer1" ></s:select></td><td width="60" align="right"><button id="compare1" class="ui-button ui-state-default ui-corner-all">比较</button></td></td>
				</tr>			
			</table>		
	  	<table id="tbl1" width="100%" >
	  		<tr name="c1" style="background:#f1f9f3;">
				<td width="20%" ><label><P>生产车型</label></td><td width="40%" ></td><td width="40%" ></td>
	    	</tr>
	  		<tr name="c2" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆一致性性证书编号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c194" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>基本车辆制造国</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c4" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆制造国</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c3" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆生产企业名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c157" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆生产企业地址</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c5" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车型系列代号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c112" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车型系列名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c98" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>单元代号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c113" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>单元名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c6" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车型代号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c114" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车型名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c191" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>认证委托人名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c192" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>认证委托人联系人</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c193" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>认证委托人联系电话</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c158" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆识别代号是否使用车型年份</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c150" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>新能源车</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c151" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆注册类型</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c152" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c7" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车型名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c95" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆中文品牌</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c96" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆英文品牌</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c97" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆类别</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c10" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>生产者（制造商）名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c12" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>产品标牌的位置</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c13" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆识别代号的打刻位置</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c14" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>发动机(电动机)编号在发动机上的位置</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c15" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CCC证书编号(版本号)</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c16" style="background:#f1f9f3;">	
	  			<td width="20%" ><label><P>签发日期</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c17" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车轴数量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c18" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车轮数量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c19" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>驱动轴位置</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c22" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>轴距</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c20" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>轮距</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c21" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>轮距</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c24" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>外廓长度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c25" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>外廓宽度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c26" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>外廓高度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c27" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>前悬</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c28" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>后悬</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c29" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>接近角</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c30" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>离去角</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c155" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>整备质量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c156" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>转向型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c32" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>最大允许总质量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c33" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>该质量的轴荷分配</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c34" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>该质量的轴荷分配</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c159" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>驱动型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	    	<tr name="c45" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>直接喷射</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c115" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>汽缸数量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c46" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>汽缸排列形式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c47" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>排量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c49" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>最大净功率</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c48" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃料种类</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c166" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>货箱内长度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c167" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>货箱内宽度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c168" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>货箱内高度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c169" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆是否适合拖挂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c195" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>准牵引总质量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c131" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>驱动电机型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c132" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>驱动电机生产厂名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c134" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>驱动电机工作电压</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c133" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>驱动电机峰值功率</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c136" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>动力蓄电池型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c154" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>动力蓄电池额定电压</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c138" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>动力蓄电池额定容量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c137" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>动力蓄电池生产厂名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c160" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃料电池型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c161" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃料电池生产企业名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c163" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃料电池额定功率(kW)</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c164" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃料电池额定电压(V)</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c165" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>储氢容器型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c64" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>最高车速</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c61" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车门数量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c116" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车门结构</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c51" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>离合器型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c52" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>变速器型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c55" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>轮胎规格第1轴</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c56" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>轮胎规格第2轴</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c58" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>制动装置简要说明</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c153" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>是否带防抱死系统</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c62" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>额定载客人数)</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c147" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>钢板弹簧片数(片)</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c53" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>速比</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c54" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>主传动比</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c65" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>声级：CCC认证引用的标准号及对应的实施阶段</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c66" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>定置噪声 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c119" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>对应的发动机转速</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c68" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>加速行驶车外噪声</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c69" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CCC认证引用的标准号及对应的实施阶段</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c70" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>试验用液体燃料</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c140" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>试验用气体燃料</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>液体燃料</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c71" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CO</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c72" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>HC</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c196" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>NMHC</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c73" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>NOx</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c149" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>N2O</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c76" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>微粒度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c148" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>PN</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c75" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>烟度(吸收系数据的校正值)</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>气体燃料</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c141" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CO</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c144" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>NOx</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c143" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>NMHC</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c142" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>THC</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c145" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CH4</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c146" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>微粒度</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>能源消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c170" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CCC认证所依据的标准号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c171" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>综合工况电能消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c172" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>电能当量燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c173" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>续驶里程</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c174" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c175" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>电能消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c176" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>电能当量燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c177" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>最低荷电状态燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c178" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>纯电动续驶里程</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CO2排放量/燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c84" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>CCC认证所依据的标准号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c179" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>低速段 CO2排放量（g/km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c180" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>中速段 CO2排放量（g/km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c181" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>高速段 CO2排放量（g/km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c182" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>超高速段 CO2排放量（g/km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c183" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>综合 CO2排放量（g/km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c184" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>低速段 燃料消耗量（L/100km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c185" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>中速段  燃料消耗量（L/100km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c186" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>高速段  燃料消耗量（L/100km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c187" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>超高速段  燃料消耗量（L/100km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c188" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>综合  燃料消耗量（L/100km）</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c85" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>市区 CO2排放量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c86" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>市郊 CO2排放量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c87" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>综合 CO2排放量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c88" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>市区 燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c89" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>市郊  燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c90" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>综合  燃料消耗量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c190" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>续驶里程</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="c94" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>备注</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
		</table>
		</form>
	</fieldset>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='c1' name='c1'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>
<div id="pubilc_notice_dialog" title="操作窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
  	公告车型型号:<select id='pmodelList' name='pmodelList'><option value="">请选择...</option></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<!--button name="dis" id="dis" class="ui-button ui-state-default ui-corner-all" style="position:static">查看</button-->
  	<br>
  	<br>
  	工况信息:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<select id='gkxxList' name='gkxxList'><option value="">请选择...</option></select>
</div>
<div align="center">
	<jsp:plugin name="coc" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarVersionPrintApplet.class" codebase="." archive = "../applet/printVersion.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/com-jaspersoft-ireport.jar,../applet/Qrcode_encoder.jar,../applet/iText-2.1.0.jar,../applet/iTextAsian.jar" 
	iepluginurl="http://localhost:8980/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="0" width="0">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath%>"/>
			<jsp:param name="model" value="COCSupplement"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>

<div id="public_notice_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table id="one" width="100%">
	    		<tr>
	    		<td width="9%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>产品名称</label></td>
				<td width="17%"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>中文品牌</label></td>
				<td width="17%"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td width="7%"><label><P>英文品牌</label></td>
				<td width="16%"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业名称</label></td>
				<td><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业地址</label></td>
				<td><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>注册地址</label></td>
				<td><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>生产地址</label></td>
				<td><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>识别代号</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>前轮距</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>后轮距</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>发动机</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机企业</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机排量</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机功率</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>底盘类别</label></td>
				<td><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>批次</label></td>
				<td><input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>长</label></td>
				<td><input type="text" id="c37" name="c37" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>宽</label></td>
				<td><input type="text" id="c38"  name="c38" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>高</label></td>
				<td><input type="text" id="c39" name="c39" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>燃料种类</label></td>
				<td><input type="text" id="c40" name="c40" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>依据标准</label></td>
				<td><input type="text" id="c41" name="c41" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>转向形式</label></td>
				<td><input type="text" id="c42"  name="c42" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>		
				<td><label><P>轴数</label></td>
				<td><input type="text" id="c46"  name="c46" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>轴距</label></td>
				<td><input type="text" id="c47" name="c47" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎数</label></td>
				<td><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎规格</label></td>
				<td><input type="text" id="c50"  name="c50" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>总质量</label></td>
				<td><input type="text" id="c51" name="c51" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>			
				<td width="9%"><label><P>整备质量</label></td>
				<td width="17%"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>额定载客</label></td>
				<td><input type="text" id="c57" name="c57" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>接近离去角</label></td>
				<td><input type="text" id="c59" name="c59" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>前悬后悬</label></td>
				<td><input type="text" id="c60" name="c60" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>传动型式</label></td>
				<td><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>最高车速</label></td>
				<td><input type="text" id="c63" name="c63" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>起动方式</label></td>
				<td><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>排放水平</label></td>
				<td><input type="text" id="c86"  name="c86" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>油耗</label></td>
				<td><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轴荷</label></td>
				<td><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮数</label></td>
				<td><input type="text" id="c79" name="c79" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>	
			<tr>	
				<td><label><P>产品商标</label></td>
				<td><input type="text" id="c77" name="c77" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>联系人</label></td>
				<td><input type="text" id="c80" name="c80" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>法人代表</label></td>
				<td><input type="text" id="c76"  name="c76" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>			
		</Table>
		</form>
	</fieldset>
</div>
<script type="text/javascript">
function findOtherVer(obj){
	var val=obj.value;
	$('#cocVer2').focus();
	jQuery.ajax({
        url: 'business/cocVer!findCocVer.action',		           
        data: {'cocVer1' : val}, 
     	type: 'POST',
     	//dataType:'json',
        beforeSend: function() {
        
        },
        error: function(XmlHttpRequest,textStatus, errorThrown) {
        	//alert(XmlHttpRequest.responseText);
            alert("系统错误，请与管理员联系！");
        },
        success: function(data) {
         	var content = json2Bean(data).json;
         	var carObj = eval("("+content.toString()+")");
			$("#cocVer2").empty();//清空下拉框 
			$("<option value=''>请选择...</option>").appendTo("#cocVer2");
			$.each( carObj, function(i, n){
				var tmp=n.id.c1+","+n.id.vercode;
				var stateName='';
				if(tmp!=val){
					if(n.state=='0'){
						stateName='未生效';
					}else if(n.state=='1'){
						stateName='生效';
					}else if(n.state=='9'){
						stateName='历史';
					}
					var opt="<option value='"+tmp +"'>"+n.id.c1+"("+n.id.vercode+stateName+")</option>";
					$(opt).appendTo("#cocVer2")//添加下拉框的option
				}				
			});			     	
        }
    });	
}
function showInfo(c1,vercode,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}



</script>
</body>
</html>