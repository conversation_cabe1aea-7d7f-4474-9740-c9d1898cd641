<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
         div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
		.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#sys_operate_dialog").dialog({bgiframe: true,autoOpen: false,width: 480,height: 310,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#sys_operate_dialog');
					allFields = $([]).add(parent.find('#name')).add(parent.find('#flag')).add(parent.find('#remark'));
				}	
				allFields.removeClass('ui-state-error');
			
				if(validate('#sys_operate_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					if(type=="add"){
						jQuery.ajax({
				            url: 'system/sysOperate!isOperateExist.action',
				            data: 'name='+$(this).find('#name').val(),
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {				    									
					            var dialog = $('#sys_operate_dialog');
					            if(json2Bean(data).json=="true"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'操作名称:['+dialog.find('#name').val()+']已经存在！');		
								}else{  
									dialog.find('#createForm')[0].action="system/sysOperate!addOperate.action";
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){
						
						$(this).find('#createForm')[0].action="system/sysOperate!updateOperate.action";
						$(this).find('#createForm')[0].submit();
					}
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			$('#operate_dialog').find('#name').attr('readonly',false);
		}
		});

	
	
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#createForm');
				var dialog = $(this);
				if(type=="delete"){					
					formObj[0].action = "system/sysOperate!deleteOperate.action";
					formObj[0].submit();
				}
			}
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#create").click(function() {
		type = "add";
		$('#sys_operate_dialog').data('title.dialog', '新增操作').dialog('open');	
	});

    $("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			jQuery.ajax({
	            url: 'system/sysOperate!operateInfo.action?id='+id,		           
	            //data: $('#createForm').serialize(), 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#sys_operate_dialog');
	            	setDialogValue(dialogObj,carObj);

	       	    	dialogObj.find('#name').attr('readonly',true);
	       	    	dialogObj.data('title.dialog', '修改操作').dialog('open');
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				
				if(id==""){
					
					id = this.value;
					info = "操作名称:"+ $('#users-contain').find('#'+this.value).val();
				}else{
					id = id+"&"+ this.value; 
					info = info+"&"+"操作名称:"+ $('#users-contain').find('#'+this.value).val();
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#id').val(id);
   	   	}
	
	});
	
	function validate(parent){
		var obj = $(parent).find('#name');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'操作名称字段不能为空，最大长度为30！');	
			return false;
		}
		obj = $(parent).find('#flag');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'操作标记字段不能为空，最大长度为30！');			
			return false;
		}
		obj = $(parent).find('#remark');
		if(!checkLength(obj,0,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'备注字段不能为空，最大长度为100！');			
			return false;
		}
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#id').val(jsonObj.operateid);
		dialogObj.find('#name').val(jsonObj.name);
		dialogObj.find('#remark').val(jsonObj.remark);
		dialogObj.find('#flag').val(jsonObj.flag);
	}
	
	function clear(dialogObj){
		dialogObj.find('#name').val("");
		dialogObj.find('#remark').val("");
		dialogObj.find('#flag').val("");

		dialogObj.find('#name').attr('readonly',false);
		dialogObj.find('#remark').attr('readonly',false);
		dialogObj.find('#flag').attr('readonly',false);
		
		type = null;
	}
    
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

          //给这行添加class值为over，并且当鼠标一出该行时执行函数

          $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
  			<tr><td width="85%"></td>
			  <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  
  			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			   <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			</tr>
  </table>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="5%">选择</th> 
			    <th width="10%">操作名称</th>
			    <th width="10%">操作标记</th>
				<th width="12%">备注</th>
				<th width="10%">创建人</th>
				<th width="10%">创建时间</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.sysOperatePageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="operateid" />' ></td>
			  		<td><s:property value="name" /></td>
			  		<td><s:property value="flag" /></td>			
			  		<td><s:property value="remark" /></td>
			  		<td><s:property value="creator" /></td>
			  		<td><s:property value="time" /><input type='hidden' id='<s:property value="operateid" />' name='<s:property value="operateid" />' value="<s:property value="name" />"/></td>
			  		
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="sys_operate_dialog">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr><td>
			<label><P>操作名称*:</label>
			</td><td>
			<input type="text" id="name" name="name" class="text ui-widget-content ui-corner-all"/>
			</td></tr><tr><td>		
			<label><P>操作标记*:</label>
			</td><td>
			<input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" />
			</td></tr><tr><td>	
			<label><P>备注:</label>
			</td><td>
				<input type="text" id="remark" name="remark" class="text ui-widget-content ui-corner-all" />
			</td>
			</tr>
		 </table>
		 <input type='hidden' id='id' name='id'/>
		 <input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口">
	<form id="createForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='id' name='id'/>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>