package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;

public interface SysDataDictionaryTypeDAO {

	public void addDataDictionaryType(DataDictionaryType dictionaryType) throws DataAccessException;

	public void deleteDataDictionaryType(DataDictionaryType dictionaryType) throws DataAccessException;
	
	public void deleteDataDictionaryType(DataDictionaryType[] dictionaryType) throws DataAccessException;
	
	public void updateDataDictionaryType(DataDictionaryType dictionaryType) throws DataAccessException;
	
	public List<DataDictionaryType> findDataDictionaryType(String sql, String[] params) throws DataAccessException;
	
	public List<DataDictionaryType> findDataDictionaryType(String sql) throws DataAccessException;
	
	public DataDictionaryType loadDataDictionaryTypeObj(String id) throws DataAccessException;
	
	public List<DataDictionaryType> findAllDataDictionaryType() throws DataAccessException;
}
