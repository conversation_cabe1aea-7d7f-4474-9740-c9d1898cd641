package com.dawnpro.dfpv.carfilemanager.module.business.share.model;

/**
 * Ms05 entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class Ms05 implements java.io.Serializable {

	// Fields

	private String vin;
	private String orderNo;
	private String materialNo;
	private String factory;
	private String vehicleNo;
	private String staition;
	private String date1;
	private String time1;
	private String reqSerialNo;

	// Constructors

	/** default constructor */
	public Ms05() {
	}

	public Ms05(String vin) {
		this.vin = vin;
	}

	
	/** minimal constructor */
	public Ms05(String vin, String materialNo, String staition, String date1,String reqSerialNo) {
		this.vin = vin;
		this.materialNo = materialNo;
		this.staition = staition;
		this.date1 = date1;
	}

	/** full constructor */
	public Ms05(String vin, String orderNo, String materialNo, String factory,
			String vehicleNo, String staition, String date1, String time1) {
		this.vin = vin;
		this.orderNo = orderNo;
		this.materialNo = materialNo;
		this.factory = factory;
		this.vehicleNo = vehicleNo;
		this.staition = staition;
		this.date1 = date1;
		this.time1 = time1;
	}

	// Property accessors

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getMaterialNo() {
		return this.materialNo;
	}

	public void setMaterialNo(String materialNo) {
		this.materialNo = materialNo;
	}

	public String getFactory() {
		return this.factory;
	}

	public void setFactory(String factory) {
		this.factory = factory;
	}

	public String getVehicleNo() {
		return this.vehicleNo;
	}

	public void setVehicleNo(String vehicleNo) {
		this.vehicleNo = vehicleNo;
	}

	public String getStaition() {
		return this.staition;
	}

	public void setStaition(String staition) {
		this.staition = staition;
	}

	public String getDate1() {
		return this.date1;
	}

	public void setDate1(String date1) {
		this.date1 = date1;
	}

	public String getTime1() {
		return this.time1;
	}

	public void setTime1(String time1) {
		this.time1 = time1;
	}
	
	public String getReqSerialNo() {
		return reqSerialNo;
	}

	public void setReqSerialNo(String reqSerialNo) {
		this.reqSerialNo = reqSerialNo;
	}
}