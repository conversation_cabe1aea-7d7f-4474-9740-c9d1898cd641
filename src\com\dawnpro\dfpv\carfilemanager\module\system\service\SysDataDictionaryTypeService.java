package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;

public interface SysDataDictionaryTypeService {

	public void addDataDictionaryType(DataDictionaryType data) throws DataAccessException;
	
	public void updateDataDictionaryType(DataDictionaryType data) throws DataAccessException;
	
	public void deleteDataDictionaryType(DataDictionaryType[] data) throws DataAccessException;
	
	public List<DataDictionaryType> findDataDictionaryType(String sql) throws DataAccessException;
	
	public DataDictionaryType loadDataDictionaryType(String id) throws DataAccessException;
	
	public boolean isDataDictionaryTypeExist(String name) throws DataAccessException;
}
