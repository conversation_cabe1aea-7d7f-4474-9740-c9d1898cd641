package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

import java.util.Date;


/**
 * TypicalityNeutral generated by MyEclipse - Hibernate Tools
 */

public class TypicalityNeutralPhoto  implements java.io.Serializable {


    // Fields    

     private String id;
     private String dxcx;
     private String zxcx;
     private Date createdate;
     private Date updatetime;
     private String creator;


    // Constructors

    /** default constructor */
    public TypicalityNeutralPhoto() {
    }

	/** minimal constructor */
    public TypicalityNeutralPhoto(String id, String dxcx) {
        this.id = id;
        this.dxcx = dxcx;
    }
    
    /** full constructor */
    public TypicalityNeutralPhoto(String id, String dxcx, String zxcx, Date createdate, Date updatetime) {
        this.id = id;
        this.dxcx = dxcx;
        this.zxcx = zxcx;
        this.createdate = createdate;
        this.updatetime = updatetime;
    }

   
    // Property accessors

    public String getId() {
        return this.id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getDxcx() {
        return this.dxcx;
    }
    
    public void setDxcx(String dxcx) {
        this.dxcx = dxcx;
    }

    public String getZxcx() {
        return this.zxcx;
    }
    
    public void setZxcx(String zxcx) {
        this.zxcx = zxcx;
    }

    public Date getCreatedate() {
        return this.createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    public Date getUpdatetime() {
        return this.updatetime;
    }
    
    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}
   








}