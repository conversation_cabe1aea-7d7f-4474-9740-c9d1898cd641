package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysDataDictionaryDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryId;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryType;

/**
 *
 * @author:
 * @since: 2010-4-1
 * **/
public class SysDataDictionaryServiceImpl implements SysDataDictionaryService {
	private Logger logger = Logger.getLogger(SysDataDictionaryServiceImpl.class.getName());
	private SysDataDictionaryDAO sysDataDictionaryDAO = null;
	private PaginationService paginationService = null;
	
	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public void setSysDataDictionaryDAO(SysDataDictionaryDAO sysDataDictionaryDAO) {
		this.sysDataDictionaryDAO = sysDataDictionaryDAO;
	}
	
	public List<DataDictionary> allDataDictionary() {
		try{
			return this.sysDataDictionaryDAO.findDataDictionary("from DataDictionary d");
		}catch(DataAccessException e){
			throw e;
		}catch(RuntimeException e1){
			throw new SystemException("allDataDictionary Method Error:",e1);
		}
	}

	public void addDataDictionary(DataDictionary data)
			throws DataAccessException {
		try{
			this.sysDataDictionaryDAO.addDataDictionary(data);
		}catch(DataAccessException e){
			logger.error("addDataDictionary Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("addDataDictionary Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public void deleteDataDictionary(DataDictionary[] data)
			throws DataAccessException {
		try{
			this.sysDataDictionaryDAO.deleteDataDictionary(data);
		}catch(DataAccessException e){
			logger.error("deleteDataDictionary Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("deleteDataDictionary Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public List<DataDictionary> findDataDictionary(String type)
			throws DataAccessException {
		try{
			return this.sysDataDictionaryDAO.findDataDictionary("from DataDictionary g where g.id.type='"+type+"'");
		}catch(DataAccessException e){
			logger.error("findDataDictionary Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("findDataDictionary Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public boolean isDataDictionaryExist(DataDictionaryId id)
			throws DataAccessException {
		if(id==null) return false;
		String hql="from DataDictionary g where g.id.type='"+id.getType()+"' and g.id.name='"+id.getName()+"'";
		List<DataDictionary> result=this.sysDataDictionaryDAO.findDataDictionary(hql);
		if(result!=null && result.size()>0)
			return true;
		return false;
	}

	public DataDictionary loadDataDictionary(DataDictionaryId id)
			throws DataAccessException {
		try{
			return this.sysDataDictionaryDAO.loadDataDictionaryObj(id);
		}catch(DataAccessException e){
			logger.error("loadDataDictionary Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("loadDataDictionary Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public void updateDataDictionary(DataDictionaryId oldId,DataDictionary data)
			throws DataAccessException {
		try{
			DataDictionary dataDictionary=new DataDictionary();
			dataDictionary.setId(oldId);
			this.sysDataDictionaryDAO.deleteDataDictionary(dataDictionary);
			this.sysDataDictionaryDAO.addDataDictionary(data);
		}catch(DataAccessException e){
			logger.error("updateDataDictionary Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("updateDataDictionary Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public List<?> pagination(Page page,Object[] params) throws DataAccessException {
		List results = null;
		try{
			StringBuffer sub = new StringBuffer(30);
			String tmp = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id.name) from DataDictionary g ";
				sql = "from DataDictionary g order by g.time desc,g.id.type";
			}else{
				sub.append(" where ");
				if(params[0]!=null&&!params[0].equals("")){
					sub.append(" g.id.type like '"+String.valueOf(params[0]).trim()+"' and ");
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub.append(" g.id.name like '"+String.valueOf(params[1]).trim()+"' and ");
				}
				tmp = sub.substring(0,sub.lastIndexOf("and"));
				
				countSql = "select count(g.id.name) from DataDictionary g "+tmp;
//				sql = "from DataDictionary g "+tmp+" order g.time desc,by g.id.type";
				sql = "from DataDictionary g "+tmp+" order by g.id.type";
							}
			
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}

}
