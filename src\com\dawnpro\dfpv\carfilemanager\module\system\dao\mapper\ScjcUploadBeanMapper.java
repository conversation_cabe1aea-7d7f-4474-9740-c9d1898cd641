package com.dawnpro.dfpv.carfilemanager.module.system.dao.mapper;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelScjcUploadBean;

public class ScjcUploadBeanMapper implements RowMapper<List<FuelScjcUploadBean>> {

	public List<FuelScjcUploadBean> mapRow(ResultSet rs) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<FuelScjcUploadBean> mapRow(Collection rs) {
		List<FuelScjcUploadBean> list = new ArrayList<FuelScjcUploadBean>();
		int point = 0;
		for(Object obj : rs){
			point = 0;
			Object[] objs = (Object[])obj;
			FuelScjcUploadBean bean = new FuelScjcUploadBean();
			bean.setPmodelcode(objs[point] == null ? "" : (objs[point]+"").trim());
			bean.setVin(objs[++point] == null ? "" : (objs[point]+"").trim());
			
			bean.setXxgkhao(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setHbsb(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setScgdz(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setProddate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEngineno(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFdjcp(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFdjscdz(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setClzzname(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setRh(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEt(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAp(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTesttype(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTestNo(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTestDate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setApass(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setOpass(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setOtestdate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEpass(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCtest(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setCtestlocation(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setFinalresult(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setObd(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setOdo(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEcmmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEcmcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEcmcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTcmmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTcmcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setTcmcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEcm2moduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEcm2calid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setEcm2cvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDmcmmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDmcmcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDmcmcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setScrmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setScrcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setScrcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setHvbecmmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setHvbecmcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setHvbecmcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setBcmmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setBcmcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setBcmcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setOthmoduleid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setOthcalid(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setOthcvn(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVrhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVlhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVrco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVlco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVrnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setVlnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyManuf(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyName(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyModel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAnalyDate(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDynoModel(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setDynoManuf(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setJcuploadflg(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setAdtjc(objs[++point] == null ? "" : (objs[point]+"").trim());
			
			bean.setSecvrhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvlhc(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvrco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvlco(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvrnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			bean.setSecvlnox(objs[++point] == null ? "" : (objs[point]+"").trim());
			list.add(bean);
		}
		return list;
	}

}
