<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; /**position: relative;**/ text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.tabs.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">
var sourcerllx = '';
$(function() {
	var allFields = null;
	var type = null;
	var wizardModel = "two";	
	var impFlg = null;
	var isprint = false;
	
	var trllx ="";
	var menuid = '<%=String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%=String.valueOf(request.getAttribute("menuid"))%>&roleid=<%=String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	$("#print").attr("disabled", true);
	$("#exportfueltar").attr("disabled", true);
	//$("#compare").attr("disabled", true);
	$("#gas_info_display_dialog").tabs();
	$("#gas_info_dialog").tabs();
	$("#gas_info_effect_dialog").tabs();
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#exportfueltar").attr("disabled", false);
				}
				/*if(operatePerrmission[i].flag.indexOf("compare")!=-1){
					$("#compare").attr("disabled", false);
				}*/
				if(operatePerrmission[i].flag.indexOf("print")!=-1){
					$("#print").attr("disabled", false);
					isprint = true;
				}
            }
        }
    });

	$("#query").click(function(){
		var slcx = $('#qslcx').val();
		var state = $('#qstate').val();
		//var qfactory = $('#qfactory').val();
		location.href="gasVer.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&menuid="+menuid;
	});
	
	$("#compare").click(function(){
       	var dialogObj = $('#gas_compare_dialog');     	  	
       	dialogObj.data('title.dialog','燃油标签版本比较').dialog('open');      	
	});
	
	$("#compare1").click(function(){
		if($('#gasVer1').val()=='' || $('#gasVer2').val()==''){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择比较的版本！');
   	   		messageObj.dialog('open');
   	   		return;
		}       	         	
		jQuery.ajax({
	        url: 'business/gasVer!findCompareGasVer.action',		           
	        data: {'gasVer1' : $('#gasVer1').val(), 'gasVer2' : $('#gasVer2').val()}, 
	     	type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            alert("系统错误，请与管理员联系！");
	        },
	        success: function(data) {
	        	clearCompareDialog('2');
	         	var content = json2Bean(data).json;
	         	var carObj = eval("("+content.toString()+")"); 
	         	var dialogObj = $('#gas_compare_dialog');
		       	dialogObj.find("#tbl1").find("tr").each(function(i){
		       		var item = $(this).attr("name");
		       		if($.trim(carObj[0][item])!=$.trim(carObj[1][item])){//不同时用黄色标记
		       			$(this).attr("style","background-color: yellow;");
		       		}
		 			$(this).find("td:eq(1)").text(carObj[0][item]);      		
		 			$(this).find("td:eq(2)").text(carObj[1][item]);      		
		       	});
	
	        }
	    });       	  	       	      
	});	
	
	function clearCompareDialog(flg){
	//1:表示清除第二版本的选择数据 2：清楚版本比较数据
	if(flg=='1'){
		$("#gasVer2").empty();
		$("<option value=''>请选择...</option>").appendTo("#gasVer2");	
		$("#gasVer1").get(0).selectedIndex=0;
		}else if(flg=='2'){	
	        var dialogObj = $('#gas_compare_dialog');        
	       	dialogObj.find("#tbl1").find("tr").each(function(i){
	 			$(this).find("td:eq(1)").text('');      		
	 			$(this).find("td:eq(2)").text(''); 
	 			$(this).attr("style","background-color: #f1f9f3;"); 
	       	});	
       	}	
	}
	$("#gas_compare_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 800,
		height: 555,
		modal: true,
		buttons: {
			'关闭': function() {
				//clearCompareDialog();	
				$(this).dialog('close');
			}
		},
		close: function() {
			clearCompareDialog('1');		
			clearCompareDialog('2');		
		}				
	});
	
	
	$("#create").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
       	var slcx = "";
       	var vercode = "";
      	
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
				slcx = id.split(",")[0];
				vercode = id.split(",")[1];
             }
      	});
      	$("#imp").attr("disabled", false);
    	$('#gas_info_dialog').find('#slcx').attr('readonly',false);
    	$('#gas_info_dialog').find('#vercode').attr('readonly',true);
   		if(index==1){
   			type = "add";
   			jQuery.ajax({
	            url: 'business/gasVer!gasVerInfo.action',		           
	            data: {'slcx' : slcx,'vercode':vercode}, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
	            	//alert(data);
		            var content = json2Bean(data).json;
		            //alert("wer");

	            	var carObj = eval("("+content.toString()+")");
	            	trllx = carObj.rllx;
	              	tabshideshow(carObj.rllx);
	            	var dialogObj = $('#gas_info_dialog');
	            	setDialogValue(dialogObj,carObj);
	            	dialogObj.data('title.dialog', '新增燃油标签版本').dialog('open');
	            	dialogObj.find('#vercode').val('');
	            	getMaxVercode();
	            	
	            }
	        });
   		}else if(index<1){
   	   		type = "add";
   	   	 	$('#gas_info_dialog').data('title.dialog', '新增燃油标签版本').dialog('open');
   	   	    tabshideshow("");
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:参照新增时只能选一条记录！');
	   		messageObj.dialog('open');
   	   	 }
	});

	$("#update").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
       	var slcx = "";
       	var vercode = "";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
				slcx = id.split(",")[0];
				vercode = id.split(",")[1];
             }
      	});
   		if(index==1){
   			type = "update";
   			$("#imp").attr("disabled", true);
   			jQuery.ajax({
	            url: 'business/gasVer!gasVerInfo.action',		           
	            data: {'slcx' : slcx,'vercode' : vercode}, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")");
		            
		            sourcerllx = carObj.rllx;
		            tabshideshow(carObj.rllx);
		            
		            $("#infotableval select[id='rllx']").val(carObj.rllx);
	            	var dialogObj = $('#gas_info_dialog');
					if(carObj.state==null || carObj.state=="" || carObj.state=="0"){
						setDialogValue(dialogObj,carObj);
						
						changewltc();//触发wltc工况显示
						
		       	    	dialogObj.find('#slcx').attr('readonly',true);
		       	    	dialogObj.find('#vercode').attr('readonly',true);
		       	    	dialogObj.data('title.dialog', '修改燃油标签证书').dialog('open');
		       	    	
		       	    	
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[维护]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});

	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
       	var state="";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
         		index++;
         		state=$(this).parent().find("#state").val();
         		//state=this.parentElement.children[1].value;
         		//alert(state + ":" + state.length);
         		//alert("state:" + state);
         		if(state=="1" || state=="9")	{
         			effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
         		}else{					
					if(id==""){
						id = this.value;
						info = "id:"+this.value;
					}else{
						id = id+"&"+this.value; 
						info = info+"&"+"id:"+this.value;
					}
				}
             }
      	});
		
   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   	   		if(effIndex>0){
	   			messageObj = $('#message_dialog');
	   	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效或是历史状态，不能删除！');
	   	   		messageObj.dialog('open');   	   			
   	   		}else{
   				type = "delete";
	   	   		messageObj = $('#operate_dialog');
	   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
	   	   		messageObj.dialog('open');
	   	   		messageObj.find('#ids').val(id);
   	   		}
   	   	}
	
	});

	$('#export').click(function() {
		var slcx = $('#qslcx').val();
		var state = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		location.href="gasVer!exportGasVer.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&qfactory="+qfactory;
	});
	
	$('#exportfueltar').click(function() {
		var slcx = $('#qslcx').val();
		var state = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		location.href="gasVer!exportFueltar.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&qfactory="+qfactory;
	});
	
	$('#wltcjs').click(function() {
		var zbzl = $("#infotableval input[id='zbzl']").val();
		zbzl=zbzl.replace(new RegExp(" ","gm"), "");
		$("#infotableval input[id='zbzl']").val(zbzl)
		
		var objbsqlx = $("#gas_info_dialog").find('#bsqlx').val();//变速器类型
		var objzwps = $("#gas_info_dialog").find('#zwps').val();//座位排数
		
		if(zbzl>0){
			if(objbsqlx==''){
				alert("变速器类型必须有值");
			}else{
				if(objzwps==''){
					alert("座位排数必须有值");
				}else{
					
					jQuery.ajax({
			            url: 'business/gasVer!getWltcLpzxz.action',		           
			            data: {"zbzl":zbzl,"bsqlx":objbsqlx,"zwps":objzwps},
				        type: 'POST',
				        dataType:'json', 
			            beforeSend: function() {
		   				
			            },
			            error: function(request) {
			            },
			            success: function(data) {
			            	var content = data.json;
			            	var Objcar =content.split(",");
			            	var Objlpz = Objcar[0];
			            	var Objzx=Objcar[1]
							$("#infotableval input[id='wltclpz']").val(Objlpz);
							$("#infotableval input[id='wltclxz']").val(Objzx);
			            }
			        });
				}
			}
			
		}else{
			alert('整备质量必须有值');
		}
	});
	
	$("#imp").click(function(){//导入相关车型信息
		var parent = $('#gas_info_dialog');
		var obj = $(parent).find('#cxxh');
		updateTips($(parent).find('#validateTips'),'');
		if(!checkLength(obj,0,20)||checkLength(obj,0,0)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且最大长度为20！');			
			return false;
		}
		var cocVer1=$('#gas_info_dialog').find('#slcx').val() + ",";
		//type = "imp";
		jQuery.ajax({
	        //url: 'business/publicNoticeCarModelManager!effectCarModelList.action?c1='+$(obj).val(),		           
			url: 'business/cocVer!findGgcxFromBv01.action?c1='+$(obj).val(),	
	        data: param, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            if(content!=''){
		            var jsonObj = eval("("+content.toString()+")"); 
		            
		            var pmodelListObj = $('#pmodelList');
		            
		            $('#pmodelList option').remove();
		            for(var i=0;i<jsonObj.length;i++){
		    			//$(pmodelListObj).append("<option value='"+jsonObj[i].id.c1+","+jsonObj[i].id.vercode+","+jsonObj[i].flag+"'>"+jsonObj[i].id.c1+"("+jsonObj[i].flag+")</option>");   
		    			$(pmodelListObj).append("<option value='"+jsonObj[i].ggcx+","+jsonObj[i].vercode+","+jsonObj[i].flag+"'>"+jsonObj[i].ggcx+"("+jsonObj[i].vercode+")</option>");   
		            }
	            }
	    
				jQuery.ajax({
			        url: 'business/cocVer!findCocVer.action',		           
			        data: {"cocVer1":cocVer1}, 
			        type: 'POST',
			        beforeSend: function() {
			        
			        },
			        error: function(request) {
			            
			        },
			        success: function(data) {
			         	var content1 = json2Bean(data).json;
			         	$("#gkxxList").empty();//清空下拉框 
						$("<option value=''>请选择...</option>").appendTo("#gkxxList");			         	
			         	if(content1!=''){
				         	var carObj = eval("("+content1.toString()+")");														
							$.each( carObj, function(i, n){
								//市区 c88, 市郊c89, 综合 c90
								var tmp=n.c88+","+n.c89 +","+n.c90;
								var stateName="市区:"+n.c88+",市郊:"+n.c89 +",综合:"+n.c90;
								var opt="<option value='"+tmp +"'>"+stateName+"</option>";
								$(opt).appendTo("#gkxxList")//添加下拉框的option
												
							});
						}					
			        }
			    });	    
	            
	        }
	    });

		var messageObj = $('#pubilc_notice_dialog');
	   	messageObj.find('#message').text('警告:当前输入的相关车型参数可能会被覆盖，是否继续？');
   		messageObj.dialog('open');   		
	});

	$('#gas_info_dialog').find('#slcx').bind('keyup',function(event) {
		if(type=="add")
			getMaxVercode();
    });

    function getMaxVercode()
    {
    	var obj = $('#gas_info_dialog').find('#slcx');
		if(obj.val().gblen()==18){
			jQuery.ajax({
	            url: 'business/gasVer!getMaxGasVercode.action',		           
	            data: {"slcx":obj.val()},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            		       
	            	var content = data.json;
		          	var dialogObj = $('#gas_info_dialog');
					dialogObj.find('#vercode').val(content);
	            }
	        });
		}  
    }

	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var slcx = "";
       	var vercode = "";
       	var state = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
         		state=$(this).parent().find("#state").val();
				index++;
				if(id==""){
					id = this.value;
					info = "id:"+this.value;
					slcx=id.split(",")[0];
					vercode=id.split(",")[1];					
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"id:"+this.value;
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	}else if(state=="1"){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('提示:数据['+slcx+":"+vercode+'] 已生效！');
   	   		messageObj.dialog('open');   
   	   	 }else{
     		type = "effect";
			display(slcx, vercode,"生效燃油标签证书");
   	   	}
	});
	
	$(".display").each(function(i){
		$(this).click(function() {
			var slcx=  $(this).attr('value').split(',')[0];
			var vercode=  $(this).attr('value').split(',')[1];	  	
			display(slcx, vercode,"查看窗口");
		 });
	  });
	
	$("#gas_info_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 800,
		height: 750,
		modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				$('#gas_info_dialog').find('#qyrq').val($('#gas_info_dialog').find('#qyrq1').val());
				//alert("save rlxhlbt: " + $('#gas_info_dialog').find('#rlxhlbt').val());
				//alert("save ygnycb: " + $('#gas_info_dialog').find('#ygnycb').val());
				
				if(allFields==null){
					var parent = $('#gas_info_dialog');
					allFields = $([]).add(parent.find('#slcx')).add(parent.find('#vercode')).add(parent.find('#qebz'))
					.add(parent.find('#scqe')).add(parent.find('#cxxh')).add(parent.find('#fdjxh'))
					.add(parent.find('#rllx')).add(parent.find('#pl')).add(parent.find('#edgl')).add(parent.find('#bsqlx'))
					.add(parent.find('#qdxs')).add(parent.find('#zbzl')).add(parent.find('#jdzzl')).add(parent.find('#qtxx'))
					.add(parent.find('#sqgk')).add(parent.find('#zhgk')).add(parent.find('#sjgk')).add(parent.find('#dyxz'))
					.add(parent.find('#dexz')).add(parent.find('#qyrq')).add(parent.find('#bacode')).add(parent.find('#remark'))
					.add(parent.find('#jcjgmc')).add(parent.find('#bgbh')).add(parent.find('#jcjnjs'))
					.add(parent.find('#scqe1')).add(parent.find('#jkqcjxs'))
					.add(parent.find('#tymc')).add(parent.find('#clzl'))
					.add(parent.find('#yyc')).add(parent.find('#zgcs'))
					.add(parent.find('#edzk')).add(parent.find('#ltgg'))
					.add(parent.find('#qhlj')).add(parent.find('#zj'))
					.add(parent.find('#qgs')).add(parent.find('#zdjgl'))
					.add(parent.find('#bsqdws')).add(parent.find('#zhcopl'))
					.add(parent.find('#zwps')).add(parent.find('#chqbh'))
					.add(parent.find('#cddzgcs')).add(parent.find('#dczlzbzlb'))
					.add(parent.find('#dcbnl')).add(parent.find('#dczbcdy'))
					.add(parent.find('#dczednl')).add(parent.find('#dczzl'))
					.add(parent.find('#djedgl')).add(parent.find('#djfznj'))
					.add(parent.find('#djlx')).add(parent.find('#zhgkdnxhl'))
					.add(parent.find('#zhgkxslc')).add(parent.find('#hhdljgxs'))
					.add(parent.find('#hhdlzddglb')).add(parent.find('#xsmssdxzgn'))
					.add(parent.find('#gjbz')).add(parent.find('#dymb')).add(parent.find('#dndl')).add(parent.find('#zdhdztrlxhl'))
					.add(parent.find('#gkrzfs')).add(parent.find('#wltclpz')).add(parent.find('#wltclxz'))
					.add(parent.find('#dsgk')).add(parent.find('#zsgk')).add(parent.find('#gsgk')).add(parent.find('#cgsgk')) //20240423新增
					.add(parent.find('#rlxhlbt')).add(parent.find('#rlxhlsj')).add(parent.find('#ydzhrlxhl')).add(parent.find('#ygnycb')) //20240423新增
					.add(parent.find('#rlxhlbj1')).add(parent.find('#rlxhlbj2'))//20240423新增
					;
				}
				//alert(allFields);
				allFields.removeClass('ui-state-error');
				tabshideshow($("#infotableval select[id='rllx']").val());
				if(validate('#gas_info_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//
					
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					//判断是否为典型车型，如果是就弹出对话框让用户决定是否连同更新该典型车型下的中性车型参数。
		   			jQuery.ajax({
			            url: 'business/typicalityNeutral!findTypicalityCarModel.action',		           
			            data: {"dxcx":$('#gas_info_dialog').find('#slcx').val()}, 
				        type: 'POST',
				        dataType:'json', 
			            beforeSend: function() {
			            
			            },
			            error: function(request) {
			               
			            },
			            success: function(data) {
			            	var m_zxcx='';
			            	var content = data.json;			            	
			            	if(content!=''){		            			            	
					            var carObj = eval("("+content.toString()+")"); 
				            	if(carObj!=null && carObj.zxcx!=null){
				            		if(window.confirm("是否同时对关联的中性车型"+ carObj.zxcx + "进行相应操作！")){
				            			m_zxcx=carObj.zxcx;
				            		}
				            	}
			            	}
			            	//alert("slcx:" + $(this).find('#slcx').val());	        
							if(type=="add"){
								jQuery.ajax({
						            url: 'business/gasVer!isGasVerExist.action',
							        data: {"slcx" : $('#gas_info_dialog').find('#slcx').val(),"vercode" : $('#gas_info_dialog').find('#vercode').val()},
							        type: 'POST',
						            beforeSend: function() {
						            
						            },
						            error: function(request) {
						                
						            },
						            success: function(data) {					       									
							            var dialog = $('#gas_info_dialog');
							            
							            if(json2Bean(data).json=="true"){
							            	var dlgButton = $('.ui-dialog-buttonpane button');
											dlgButton.attr('disabled', false);
									        dlgButton.removeClass('ui-state-disabled');
											updateTips(dialog.find('#validateTips'),'车型:['+dialog.find('#slcx').val()+']和版本号:['+ dialog.find('#vercode').val() +']已经存在不能新增！');		
										}else{
											var slcx = $('#qslcx').val();
											var state = $('#qstate').val();
											//alert("slcx = " + $('#slcx').val());
											var currentPage=$('#currentPage_temp').val();
											dialog.find('#createForm')[0].action="business/gasVer!addGasVer.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
											dialog.find('#createForm')[0].submit();
										}
						            }
						        });
							}else if(type=="update"){
								var dialog = $('#gas_info_dialog');
								var slcx = $('#qslcx').val();
								var state = $('#qstate').val();
								
								var currentPage=$('#currentPage_temp').val();
								dialog.find('#createForm')[0].action="business/gasVer!updateGasVer.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
								dialog.find('#createForm')[0].submit();
							}	            
			            }
			        });

				}
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			clear($(this));
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
				
			type = null;
			//$('#gas_info_dialog').find('#vin').attr('readonly',false);
		}
	});

	$("#gas_info_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 800,height: 750,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},'打印': function() {
				var c1 = $(this).find('#slcx').val();
				var vercode = $(this).find('#vercode').val();
   				window.document.coc.printGasVersion(c1,vercode);
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
		}
	});
	
	$("#backup_print_dialog").dialog({bgiframe: true,autoOpen: false,width: 400,height: 240,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},'打印': function() {
				var c1 = $(this).find('#c1').val();
				var vercode = $(this).find('#vercode').val();
				var vinObj = $(this).find('#vin');
				var numberObj = $(this).find('#number');
				
				allFields = $([]).add($(vinObj)).add($(numberObj));
				allFields.removeClass('ui-state-error');
				
				if($(vinObj).val()==""){
					$(vinObj).addClass('ui-state-error');
					updateTips($(this).find('#validateTips'),'VIN不能为空！');	
					
					return;
				}else if($(numberObj).val()==""){
					$(vinObj).removeClass();
					$(numberObj).addClass('ui-state-error');
					updateTips($(this).find('#validateTips'),'备案号不能为空！');	
						
					return;
				}
				
				window.document.coc.printGasVersion(c1,vercode,$(vinObj).val(),$(numberObj).val());
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$(this).find('#vin').val("");
			$(this).find('#number').val("");
		}
	});
	
	$("#gas_info_effect_dialog").dialog({bgiframe: true,autoOpen: false,width: 800,height: 555,modal: true,
		buttons: {
			'关闭': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'生效': function() {
				var slcx = $(this).find('#slcx').val();
				var vercode = $(this).find('#vercode').val();
				jQuery.ajax({
	            	url: 'business/gasVer!gasVerInfo.action',		           
	            	data: {'slcx' : slcx,'vercode' : vercode},
		        	type: 'POST',
	            	beforeSend: function() {
	            	
	            	},
	            	error: function(request) {
	                
	            	},
	            	success: function(data) {
		            	var content = json2Bean(data).json;
		            	var carObj = eval("("+content.toString()+")"); 
	            		var messageObj;
						if(carObj.state==null || carObj.state=="" || carObj.state=="0"){
							params = "c1="+$('#gas_info_effect_dialog').find('#cxxh').val();			
							jQuery.ajax({
					            url: 'business/publicNoticeCarModelManager!isCarModelExistByC1.action',		           
					            data: params, 
						        type: 'POST',
					            success: function(data) {					       									
						            var dialog = $('#gas_info_effect_dialog');
						            if(json2Bean(data).json=="false"){
										updateTips(dialog.find('#validateTips'),'车型型号:['+dialog.find('#cxxh').val()+'] 此公告车型不存在！');	
										
									}else{
										messageObj = $('#operate_dialog');
						   	   			messageObj.find('#message').text('提示:确定修改【'+slcx+':'+vercode+'】为生效状态?');
						   	   			messageObj.dialog('open');
						   	   			messageObj.find('#slcx').val(slcx);
						   	   			messageObj.find('#vercode').val(vercode);
									}
					            }
					        });
						}else{
							messageObj = $('#message_dialog');
				   	   		messageObj.find('#message').text('警告:只能修改状态为[维护]的数据！');
				   	   		messageObj.dialog('open');
						}
	            	}
	        	});
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			if(type!=null){
				type = null;
			}
			
		}
	});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				var parent = $('#gas_info_dialog');
				updateTips($(parent).find('#validateTips'),'');
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var slcx = $('#qslcx').val();
				var state = $('#qstate').val();
				
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/gasVer!deleteGasVer.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					formObj[0].action = "business/gasVer!effectGasVer.action?qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="imp"){
					//$(this).dialog('close');
					//impPubData($("#gas_info_dialog").find("#cxxh").val());
					//type = "add";
				}
			}
		},
		close: function() {
			var parent = $('#gas_info_dialog');
			updateTips($(parent).find('#validateTips'),'');
		}
	});
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
    $("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 520,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
			}
			//,
			//'下一页': function() {
			//	wizardToDisplay();
			//}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$('#public_notice_dialog').find('#c1').attr('readonly',false);

			//wizardModel = "one";
			//wizardToDisplay();
		}
	});
	
    $("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 800,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var gk = $('#pubilc_notice_dialog').find('#gkxxList').val();
				if(gk!=''){
					var arr=gk.split(',');
					if(arr.length==3){
						$('#gas_info_dialog').find("#sqgk").val(arr[0]);
						$('#gas_info_dialog').find("#sjgk").val(arr[1]);
						$('#gas_info_dialog').find("#zhgk").val(arr[2]);
					}						
				}			
				if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
					var value = $(this).find('#pmodelList').val().split(',');					
					impPubData(value[0],value[1]);
				}
				
				$(this).dialog('close');
			}
		}
	});
    
    function display(slcx,vercode,title){
    	jQuery.ajax({
	        url: 'business/gasVer!gasVerInfo.action',		           
	        data: {'slcx' : slcx,'vercode' : vercode}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	        	//alert(data);
	        
	            //alert(eval("("+data.toString()+")"));
	        	var content = json2Bean(data).json;
	            
	            var carObj = eval("("+content.toString()+")");
	            //var carObj = data;
	            tabsshoworhide(carObj.rllx);
	            var dialogObj;
	            if(type=="effect"){
	            	dialogObj = $('#gas_info_effect_dialog');
	            	gtabsshoworhide(carObj.rllx);
	            }else{
	            	dialogObj = $('#gas_info_display_dialog');
	            }	
				setDialogValue(dialogObj,carObj);
				dialogObj.data('title.dialog', title).dialog('open');
				dialogObj.data('title.dialog','生效燃油标签证书').dialog('open');
				if(isprint)
					$('.ui-dialog-buttonpane button[value="打印"]').css("display","");
				else
					$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
	        }
	    });

		return false;
	}
    
    $("#dis").click(function(){
		var pmodelListObj = $('#pubilc_notice_dialog').find('#pmodelList');
		if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
			var value = $(pmodelListObj).val().split(',');
			var param = "c1="+value[0]+","+value[1];
			jQuery.ajax({
		        url: 'business/publicNoticeCarModelManager!carModelInfo.action',		           
		        data: param, 
		        type: 'POST',
		        beforeSend: function() {
		        
		        },
		        error: function(request) {
		            
		        },
		        success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
		        	var dialogObj = $('#public_notice_display_dialog');
		        
		        	setDialogValue2(dialogObj,carObj);
									
		       	   	dialogObj.dialog('open');
		        }
		    });
		}
	});

    //单个查询回填数据
    function setDialogValue(dialogObj,jsonObj){
		dialogObj.find(":text").each(function(i){
			$(this).val(jsonObj[$(this).attr("name")]);
		}) ;	
		dialogObj.find('#slcx').val(jsonObj.id.slcx);
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		
		//2013-6-13 by yj
		dialogObj.find("select").each(function(i){
			$(this).val(jsonObj[$(this).attr("name")]);
		}) ;	
		dialogObj.find('#qyrq1').val(jsonObj.qyrq);
		dialogObj.find('#qyrq').val(jsonObj.qyrq);
		
	}
    
	function clear(dialogObj){	
		dialogObj.find(":text").each(function(i){
			$(this).val("");
		}) ;
		dialogObj.find("select").each(function(i){
			$(this).val("");
		}) ;
	}

	function validate(parent){
		//return true;
		var pattern = /^(\d+)(\.)(\d{1})$/;
		var pattern2 = /^\d+\.\d{2}$/;
		var pattern1 = /^(-?\d+)(\.\d+)?/;
		
		var obj = $(parent).find('#slcx');
		if(!checkLength(obj,18,18)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型不能为空，且长度必须为18！');
			obj.focus();			
			return false;
		}
		obj = $(parent).find('#vercode');
		if(!checkLength(obj,1,21)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'版本号不能为空，且最大长度为21！');
			obj.focus();			
			return false;
		}
		obj = $(parent).find('#qebz');
		if(!checkLength(obj,1,40)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'企业标志不能为空，最大长度为40！');
			obj.focus();			
			return false;
		}
		obj = $(parent).find('#scqe');
		if(!checkLength(obj,1,200)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产企业不能为空，最大长度为200！');
			obj.focus();			
			return false;
		}
		obj = $(parent).find('#cxxh');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车型型号不能为空，最大长度为20!');
			obj.focus();			
			return false;
		}
		
		obj = $(parent).find('#rllx');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'燃料类型不能为空，最大长度为20！');
			obj.focus();			
			return false;
		}
		
		obj = $(parent).find('#gkrzfs');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'工况认证方式不能为空，最大长度为20！');
			obj.focus();			
			return false;
		}
		
		obj = $(parent).find('#wltclpz');
		if('WLTC认证'==$(parent).find('#gkrzfs').val() && 
			    ('汽油'==$(parent).find('#rllx').val() || '非插电式混合动力'==$(parent).find('#rllx').val()  ))	{	
			if(!checkLength(obj,1,5)){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'WLTC领跑值不能为空，最大长度为5！可以直接点击计算按钮进行计算');	
				obj.focus();		
				return false;
			}else if(!checkRegexp(obj,pattern2)){//保留二位小数
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'WLTC领跑值必须保留二位小数！可以直接点击计算按钮进行计算');	
				obj.focus();		
				return false;			
			}
		}
		
		obj = $(parent).find('#wltclxz');
		if('WLTC认证'==$(parent).find('#gkrzfs').val() && 
			    ('汽油'==$(parent).find('#rllx').val() || '非插电式混合动力'==$(parent).find('#rllx').val()  ))	{	
			if(!checkLength(obj,1,5)){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'WLTC限值不能为空，最大长度为5！可以直接点击计算按钮进行计算');	
				obj.focus();		
				return false;
			}else if(!checkRegexp(obj,pattern2)){//保留二位小数
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'WLTC限值必须保留二位小数！可以直接点击计算按钮进行计算');	
				obj.focus();		
				return false;			
			}
		}
		
		
		obj = $(parent).find('#qdxs');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'驱动型式不能为空，最大长度为20！');
			obj.focus();		
			return false;
		}else{
			var qdxs = $(parent).find('#qdxs').val();
			if(qdxs!='前轮驱动' && qdxs!='后轮驱动' && qdxs!='分时全轮驱动' && qdxs!='全时全轮驱动' && qdxs!='智能(适时)全轮驱动'){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'驱动型式必须填写前轮驱动、后轮驱动、分时全轮驱动、全时全轮驱动、智能(适时)全轮驱动的格式');
				obj.focus();		
				return false;
			}
		}
		obj = $(parent).find('#zbzl');
		if(!checkLength(obj,1,10)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'整车整备质量不能为空，最大长度为10，必须是整数！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#jdzzl');
		if(!checkLength(obj,1,10)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最大设计总质量不能为空，最大长度为10，必须是整数！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#dyxz');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'对应限值不能为空，最大长度为10！');	
			obj.focus();		
			return false;
		}else if(!checkRegexp(obj,pattern)){//保留一位小数 2012-12-25
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'对应限值必须保留一位小数！');	
			obj.focus();		
			return false;			
		}
		
		
		/**
		obj = $(parent).find('#dymb');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'对应限值不能为空');	
			obj.focus();		
			return false;
		}
		
		
		if($(parent).find('#dymb').val()=='1'){//使用就模板时必填 20160930
			obj = $(parent).find('#dexz');
			if(!checkLength(obj,1,10)){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'对应限值不能为空，最大长度为10！');	
				obj.focus();		
				return false;
			}else if(!checkRegexp(obj,pattern)){//保留一位小数 2012-12-25
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'对应限值必须保留一位小数！');	
				obj.focus();		
				return false;			
			}
		}**/
		
		obj = $(parent).find('#gjbz');
		if(!checkLength(obj,1,2000)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'国家标准不能为空，最大长度为2000！');	
			obj.focus();		
			return false;
		}
		
		//obj = $(parent).find('#cccgzx');
		//if(!checkLength(obj,1,100)){
		//	obj.addClass('ui-state-error');
		//	updateTips($(parent).find('#validateTips'),'coc一致性号不能为空，最大长度为100！');			
		//	return false;
		//}
		obj = $(parent).find('#qyrq');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'启用日期不能为空，最大长度为20！');
			obj.focus();			
			return false;
		}
		obj = $(parent).find('#remark');
		if(!checkLength(obj,0,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'其它说明最大长度为100！');	
			obj.focus();		
			return false;
		}

		
		//燃油消耗量上传添加的字段
		
		obj = $(parent).find('#scqe1');
		if(!checkLength(obj,1,200) ){//东风汽车公司 -》东风汽车集团有限公司  || obj.val() != '东风汽车集团有限公司'
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'生产企业（申报）必填！');	//生产企业（申报）必须是东风汽车集团有限公司
			obj.focus();		
			return false;
		}
		
		obj = $(parent).find('#jkjxs');
		if(!checkLength(obj,0,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'进口汽车经销商最大长度为100！');	
			obj.focus();		
			return false;
		}	
				
		obj = $(parent).find('#jcjgmc');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'检测机构名称不能为空，最大长度为100！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#bgbh');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'报告编号不能为空，最大长度为30！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#tymc');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'通用名称不能为空，最大长度为20！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#clzl');
		if(!checkLength(obj,1,50) || obj.val().indexOf('乘用车') < 0){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'车辆种类不能为空，最大长度为50,并且为乘用车(M1)格式!');	
			obj.focus();		
			return false;
		}		
		obj = $(parent).find('#yyc');
		if(!checkLength(obj,1,10) || (obj.val() != '是' && obj.val() != '否')){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'越野车必须填写是或否');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#zgcs');
		if(!checkLength(obj,1,50)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最高车速不能为空，最大长度为50，必须是整数！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#edzk');
		if(!checkLength(obj,1,50)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'额定载客不能为空，最大长度为50，必须是整数！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#ltgg');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轮胎规格不能为空，最大长度为50！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#qhlj');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轮距前/后不能为空，最大长度为50！');	
			obj.focus();		
			return false;
		}else if(!checkRegexp(obj,/^\d+[/]\d+$/g) || checkRegexp(obj,/[\uff00-\uffff]/g) || checkRegexp(obj,/\s+/g)){ // 2012-01-20
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'前后轮距中间用"/"隔开(半角，无空格)，并且是整数');	
			obj.focus();		
			return false;			
		}
		
		obj = $(parent).find('#zj');
		if(!checkLength(obj,1,50)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'轴距不能为空，最大长度为50，必须是整数！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#zwps');
		if(!checkLength(obj,1,3)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'座位排数不能为空，最大长度为3，必须是整数！');	
			obj.focus();		
			return false;
		}
		
		
		obj = $(parent).find('#chqbh');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'不能为空，催化器型号最大长度为100！');	
			obj.focus();		
			return false;
		}		
		
		trllx = $("#infotableval select[id='rllx']").val();
		//yj add 2013-06-14;
		if(trllx=='汽油'||trllx=='非插电式混合动力'){
			obj = $(parent).find('#bsqdws');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'变速器档位数不能为空，最大长度为10！');	
			obj.focus();		
			return false;
		}else{
			//对于CVT填写“N.A”
			var bsqlx = $(parent).find('#bsqlx').val();
			
			if(bsqlx.indexOf("CVT")>-1 || bsqlx.indexOf("cvt")>-1){
				if($(parent).find('#bsqdws').val() != 'N.A'){
					obj.addClass('ui-state-error');
					updateTips($(parent).find('#validateTips'),'变速器类型为CVT的档位数应填写N.A！');	
					obj.focus();		
					return false;
				}
			}else{
				var qdxs = $(parent).find('#bsqdws').val();
				if(qdxs!='1' && qdxs!='2' && qdxs!='3' && qdxs!='4' && qdxs!='5' && qdxs!='6' && qdxs!='7' && qdxs!='8' && qdxs!='9'){
					obj.addClass('ui-state-error');
					updateTips($(parent).find('#validateTips'),'变速器档位数必须填写1、2、3、4、5、6、7、8、9、10');
					obj.focus();		
					return false;
				}	
			}
		}
		obj = $(parent).find('#bsqlx');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'变速器类型不能为空，最大长度为10！');		
			obj.focus();	
			return false;
		}else{
			//MT、AT、AMT、CVT、DCT、其它
			var qdxs = $(parent).find('#bsqlx').val();
			if(qdxs!='MT' && qdxs!='AT' && qdxs!='AMT' && qdxs!='CVT' && qdxs!='DCT' && qdxs!='其它' && qdxs!='中央减速器'){
				obj.addClass('ui-state-error');
				updateTips($(parent).find('#validateTips'),'变速器类型必须填写MT、AT、AMT、CVT、DCT、中央减速器、其它');
				obj.focus();		
				return false;
			}			
		}
		obj = $(parent).find('#edgl');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'额定功率不能为空，最大长度为30！');	
			obj.focus();		
			return false;
		}else if(checkRegexp(obj, pattern1) && !checkRegexp(obj,pattern)){//保留一位小数 2012-12-25
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'额定功率必须保留一位小数！');	
			obj.focus();		
			return false;			
		}
		obj = $(parent).find('#fdjxh');
		if(!checkLength(obj,1,20)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'发动机型号不能为空，最大长度为20！');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#zdjgl');
		if(!checkLength(obj,0,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最大净功率最大长度为10！');	
			obj.focus();		
			return false;
		}else if(checkLengthWithoutSpace(obj,1,10) && !checkRegexp(obj,pattern)){//保留一位小数 2012-12-25
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'最大净功率必须保留一位小数！');	
			obj.focus();		
			return false;			
		}
		obj = $(parent).find('#pl');
		if(!checkLength(obj,1,10)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'排量不能为空，最大长度为10！并且必须是整数');	
			obj.focus();		
			return false;
		}
		obj = $(parent).find('#qgs');
		if(!checkLength(obj,1,10)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'气缸数不能为空，最大长度为10！');	
			obj.focus();		
			return false;
		}
		
	
		obj = $(parent).find('#qtxx');
		if(!checkLength(obj,0,200)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'其他信息最大长度为200！');	
			obj.focus();		
			return false;
		}
		
		

		

		}
		

		
		//alert("finish");
		return true;
	}

	//根据车型导入相关信息
	function impPubData(cxxh,vercode){
		jQuery.ajax({
            //url: 'business/cocVer!impPubData.action',		           
			url: 'business/gasVer!impDataFromBv01.action',
            data: {'ids' : cxxh+","+vercode},             
	        type: 'POST',
            beforeSend: function() {
            
            },
            error: function(request) {
                
            },
            success: function(data) {
	            var content = json2Bean(data).json;
	            if(content==""){
					messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:没有找到对应的公告车型记录！');
		   	   		messageObj.dialog('open');	            
	            }else{	    	
		            var carObj = eval("("+content.toString()+")");
	            	var dialogObj = $('#gas_info_dialog');
					setImpData(dialogObj, carObj);
	            }
            }			
		})	
	}

	function setImpData(dialogObj, jsonObj){
		//2012-12-6
		dialogObj.find(":text").each(function(i){
			if($(this).attr("name")!="sqgk" && $(this).attr("name")!="sjgk" && $(this).attr("name")!="zhgk")
				$(this).val(jsonObj[$(this).attr("name")]);
		}) ;	
		dialogObj.find(":select").each(function(i){
				$(this).val(jsonObj[$(this).attr("name")]);
		}) ;
		//dialogObj.find('#cxxh').val(jsonObj.c1);//车辆型号
		/*dialogObj.find('#fdjxh').val(jsonObj.c8);//发动机型号
		dialogObj.find('#rllx').val(jsonObj.c40);//燃料种类
		dialogObj.find('#pl').val(jsonObj.c10);//排量
		dialogObj.find('#edgl').val(jsonObj.c11);//额定功率
		dialogObj.find('#zbzl').val(jsonObj.c53);//整备质量
		dialogObj.find('#jdzzl').val(jsonObj.c51);//总质量*/
	};
	
	function setDialogValue2(dialogObj,jsonObj){
		dialogObj.find('#c1').val(jsonObj.id.c1);
		dialogObj.find('#vercode').val(jsonObj.id.vercode);
		dialogObj.find('#flag').val(jsonObj.flag);
		dialogObj.find('#c2').val(jsonObj.c2);
		dialogObj.find('#c3').val(jsonObj.c3);
		dialogObj.find('#c4').val(jsonObj.c4);
		dialogObj.find('#c27').val(jsonObj.c27);
		dialogObj.find('#c28').val(jsonObj.c28);
		dialogObj.find('#c29').val(jsonObj.c29);
		dialogObj.find('#c30').val(jsonObj.c30);
//		dialogObj.find('#c31').val(jsonObj.c31);
//		dialogObj.find('#c32').val(jsonObj.c32);
//		dialogObj.find('#c33').val(jsonObj.c33);
//		dialogObj.find('#c34').val(jsonObj.c34);
		dialogObj.find('#c5').val(jsonObj.c5);
		dialogObj.find('#c6').val(jsonObj.c6);
		dialogObj.find('#c7').val(jsonObj.c7);
		dialogObj.find('#c8').val(jsonObj.c8);
		dialogObj.find('#c9').val(jsonObj.c9);
		dialogObj.find('#c10').val(jsonObj.c10);
		dialogObj.find('#c11').val(jsonObj.c11);
//		dialogObj.find('#c18').val(jsonObj.c18);
		dialogObj.find('#c19').val(jsonObj.c19);
//		dialogObj.find('#c20').val(jsonObj.c20);
//		dialogObj.find('#c25').val(jsonObj.c25);
		dialogObj.find('#c26').val(jsonObj.c26);
		dialogObj.find('#c37').val(jsonObj.c37);
		dialogObj.find('#c38').val(jsonObj.c38);
		dialogObj.find('#c39').val(jsonObj.c39);
		dialogObj.find('#c40').val(jsonObj.c40);
		dialogObj.find('#c41').val(jsonObj.c41);
		dialogObj.find('#c42').val(jsonObj.c42);
//		dialogObj.find('#c43').val(jsonObj.c43);
//		dialogObj.find('#c44').val(jsonObj.c44);
//		dialogObj.find('#c45').val(jsonObj.c45);
		dialogObj.find('#c46').val(jsonObj.c46);
		dialogObj.find('#c47').val(jsonObj.c47);
//		dialogObj.find('#c48').val(jsonObj.c48);
		dialogObj.find('#c49').val(jsonObj.c49);
		dialogObj.find('#c50').val(jsonObj.c50);
		dialogObj.find('#c51').val(jsonObj.c51);
//		dialogObj.find('#c52').val(jsonObj.c52);
		dialogObj.find('#c53').val(jsonObj.c53);
//		dialogObj.find('#c54').val(jsonObj.c54);
//		dialogObj.find('#c55').val(jsonObj.c55);
//		dialogObj.find('#c56').val(jsonObj.c56);
		dialogObj.find('#c57').val(jsonObj.c57);
//		dialogObj.find('#c58').val(jsonObj.c58);
		dialogObj.find('#c59').val(jsonObj.c59);
		dialogObj.find('#c60').val(jsonObj.c60);
//		dialogObj.find('#c61').val(jsonObj.c61);
		dialogObj.find('#c62').val(jsonObj.c62);
		dialogObj.find('#c63').val(jsonObj.c63);
//		dialogObj.find('#c64').val(jsonObj.c64);
//		dialogObj.find('#c65').val(jsonObj.c65);
//		dialogObj.find('#c66').val(jsonObj.c66);
//		dialogObj.find('#c67').val(jsonObj.c67);
//		dialogObj.find('#c68').val(jsonObj.c68);
//		dialogObj.find('#c69').val(jsonObj.c69);
//		dialogObj.find('#c70').val(jsonObj.c70);
//		dialogObj.find('#c71').val(jsonObj.c71);
//		dialogObj.find('#c72').val(jsonObj.c72);
		dialogObj.find('#c73').val(jsonObj.c73);
		dialogObj.find('#c86').val(jsonObj.c86);
		dialogObj.find('#c88').val(jsonObj.c88);
		dialogObj.find('#c89').val(jsonObj.c89);
		dialogObj.find('#c79').val(jsonObj.c79);
		dialogObj.find('#c77').val(jsonObj.c77);
		dialogObj.find('#c80').val(jsonObj.c80);
		dialogObj.find('#c76').val(jsonObj.c76);
		
			
		dialogObj.find('#state').val(jsonObj.state);
	}
	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var slcx = $('#qslcx').val();
			var state = $('#qstate').val();
			
			location.href="gasVer.action?currentPage="+$('#jump').val()+"&qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&menuid="+menuid;
   		}   
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var slcx = $('#qslcx').val();
				var state = $('#qstate').val();
				
				location.href=$(this).attr('value')+"&qslcx="+encodeURI(encodeURI(slcx))+"&qstate="+state+"&menuid="+menuid;
		 });
	  });

	$("#print").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   	   		var c1 = id.split(",")[0];
   	   		var vercode = id.split(",")[1];
   			window.document.coc.printGasVersion(c1,vercode);
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要打印的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能打印一条数据！');
	   		messageObj.dialog('open');
   	   	 }
	});
    
	$("#backupprint").click(function() {
		var id = "";
		var index=0;
	 	var messageObj = null;
		var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
	      	checkedObj.each(function(){
	         	if(this.checked==true){
					index++;
					id = this.value; 
	             }
	      	});
		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要打印的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能打印一条数据！');
	   		messageObj.dialog('open');
   	   	}else if(index==1){
   	   		var c1 = id.split(",")[0];
	   		var vercode = id.split(",")[1];
   	   		var messageObj = $('#backup_print_dialog');
   	   		messageObj.dialog('open'); 
   	   		$(messageObj).find('#c1').val(c1);
   	   		$(messageObj).find('#vercode').val(vercode);
   	   	}
	});
	
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif',
  	   		buttonImageOnly: true}, $.datepicker.regional['zh']));
	$('#gas_info_dialog').find('#qyrq1').datepicker($.datepicker.regional['zh']);
	$('#gas_info_dialog').find('#qyrq1').change(function() {
		var rq = $('#gas_info_dialog').find('#qyrq1').val().split("-");
		//alert(rq);
		$('#gas_info_dialog').find('#qyrq1').val(rq[0]+'年'+rq[1]+'月'+rq[2]+'日');
		$('#gas_info_dialog').find('#qyrq').val(rq[0]+'年'+rq[1]+'月'+rq[2]+'日');
	});
});
$(document).ready(function() {
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class


});

function createrllxchange(){
	//var dialogObj = $('#gas_info_dialog');    
	trllx = $("#infotableval select[id='rllx']").val();
	if(sourcerllx=='汽油'||sourcerllx=='柴油'||sourcerllx=='两用燃料'||sourcerllx=='双燃料'){
		if(trllx=='纯电动'||trllx=='非插电式混合动力'||trllx=='插电式混合动力'){
			cleartabs();
		}
	}
	else if(sourcerllx=='纯电动'){
		if(trllx!='纯电动'){
			cleartabs();
		}
	}
	else if(sourcerllx=='非插电式混合动力'){
		if(trllx!='非插电式混合动力'){
			cleartabs();
		}
	}else if(sourcerllx=='插电式混合动力'){
		if(trllx!='插电式混合动力'){
			cleartabs();
		}
	}
	tabshideshow(trllx);

}

function changewltc(){
	var gkrzfs = $("#infotableval select[id='gkrzfs']").val();
	trllx = $("#infotableval select[id='rllx']").val();
	if(gkrzfs=='WLTC认证' && trllx!='纯电动' && trllx!=''){//wltc认证：
		$("#infotableval tr[id='wltclpxz']").show();
		$("#infotableval button[id='wltcjs']").show();
	}else{
		$("#infotableval input[id='wltclpz']").val('');
		$("#infotableval input[id='wltclxz']").val('');
		$("#infotableval tr[id='wltclpxz']").hide();
		$("#infotableval button[id='wltcjs']").hide();
	}
}

//新增修改
function tabshideshow(str){
	
	changewltc();
	
	var trllx = str;
	if(trllx=='汽油'||trllx=='非插电式混合动力'){
		$("#infotabs1table").show(); //变速器相关（汽油、非插电混）、插电混
		
		$("#infotabs2table").show(); // 可变参数
		$("#infotabs2table tr[id='trtabs2_qy_ch']").show();
		$("#infotabs2table tr[id='trtabs2_cd_ch']").hide();
		
		$("#infotabs3table").show(); // 汽油非插电混相关
		
		$("#infotabs4table").hide();//cleantable("infotabs2table");  //插电混相关
		
	   	$("#infotabs5table").hide();//cleantable("infotabs5table"); //纯电
	   	
	   	$("#infotabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	   	$("#infotabs6table tr[id='trtabs6_qy_ch']").show();
	   	$("#infotabs6table tr[id='trtabs6_cd_ch']").hide();
	   	//$("#infotabs6table p[id='labelzhgkxslc']").html('续航里程');
	   	$("#infotabs6table tr[id='trtabs6_qy_cd']").show();
	}
	else if(trllx=='纯电动'){
		$("#infotabs1table").hide();//cleantable("infotabs1table"); //变速器相关（汽油、非插电混）
		$("#infotabs2table").show(); // 可变参数
		$("#infotabs2table tr[id='trtabs2_qy_ch']").hide();
		$("#infotabs2table tr[id='trtabs2_cd_ch']").show();
		$("#infotabs2table p[id='gktjxbglhdl']").html('电能消耗量(kW·h/100km)');
		
		$("#infotabs3table").hide();//cleantable("infotabs3table"); // 汽油非插电混相关
		
		$("#infotabs4table").hide();//cleantable("infotabs4table");  //插电混相关
		
	   	$("#infotabs5table").show(); //纯电
	   	
	   	$("#infotabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	   	$("#infotabs6table tr[id='trtabs6_qy_ch']").hide();
	   	$("#infotabs6table tr[id='trtabs6_cd_ch']").show();
	   	$("#infotabs6table p[id='labelzhgkxslc']").html('续航里程(km)');
	   	$("#infotabs6table tr[id='trtabs6_qy_cd']").show();	
	
	}
	/*
	else if(trllx=='非插电式混合动力'){
		$("#infotabs1table").show();
	   	$("#infotabs2table").show();
	   	$("#infotabs3table").show();
	   	$("#infotabs4table").hide();cleantable("infotabs4table");
	   		   	
	   	$("#infotabs2table p[id='labelcddzgcs']").html('纯电动模式下1km最高车速(km/h)');
	  
	   	$("#infotabs2table p[id='labelzhgkxslc']").html('纯电动模式下综合工况续驶里程(km)');
	    
	    $("#infotabs1table p[id='tjasydnxhl']").html('市郊工况(L/100km)');
	   
	   	$("#infotabs1table p[id='tjbsyrlxhl']").html('市区工况(L/100km)');
	   
   	    $("#infotabs1table p[id='rlxhl']").html('综合工况(L/100km)');
	   	
	}
	*/
	else if(trllx=='插电式混合动力'){
		$("#infotabs1table").show();//cleantable("infotabs1table"); //变速器相关（汽油、非插电混）、插电混
		$("#infotabs2table").show(); // 可变参数
		$("#infotabs2table tr[id='trtabs2_qy_ch']").show();
		$("#infotabs2table tr[id='trtabs2_cd_ch']").show();
		$("#infotabs2table p[id='gktjxbglhdl']").html('综合电能消耗量(kW·h/100km)');
		
		$("#infotabs3table").hide();//cleantable("infotabs3table"); // 汽油非插电混相关
		
		$("#infotabs4table").show();//cleantable("infotabs4table");  //插电混相关
		
	   	$("#infotabs5table").show();//cleantable("infotabs5table"); //纯电
	   	
	   	$("#infotabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	   	$("#infotabs6table tr[id='trtabs6_qy_ch']").show();
	   	$("#infotabs6table tr[id='trtabs6_cd_ch']").show();
	   	$("#infotabs6table p[id='labelzhgkxslc']").html('电动续航里程(km)');
	   	$("#infotabs6table tr[id='trtabs6_qy_cd']").show();	
		
	}
	else{
		$("#infotabs1table").hide();cleantable("infotabs1table");
	   	$("#infotabs2table").hide();cleantable("infotabs2table");
	   	$("#infotabs3table").hide();cleantable("infotabs3table");
	   	$("#infotabs4table").hide();cleantable("infotabs4table");
	   	$("#infotabs5table").hide();cleantable("infotabs5table");
	   	$("#infotabs6table").hide();cleantable("infotabs6table");
	}
}

function cleartabs(){
	$('#infotabs-2').find(":text").each(function(i){
			$(this).val('');
		}) ;
	$('#infotabs-2').find("select").each(function(i){
			$(this).val('');
		}) ;
}

function cleantable(tabid){
	var tbid = "#"+tabid;
		$(tbid).find(":text").each(function(i){
			$(this).val('');
		}) ;
	$(tbid).find("select").each(function(i){
			$(this).val('');
		}) ;
}

//查看界面
function tabsshoworhide(rllx){
	var showrllx =  rllx;
	if(showrllx=='汽油'||showrllx=='非插电式混合动力'){
			$("#tabs1table").show(); //变速器相关（汽油、非插电混）、插电混
		
			$("#tabs2table").show(); // 可变参数
			$("#tabs2table tr[id='trtabs2_qy_ch']").show();
			$("#tabs2table tr[id='trtabs2_cd_ch']").hide();
		
			$("#tabs3table").show(); // 汽油非插电混相关
		
			$("#tabs4table").hide();//cleantable("infotabs2table");  //插电混相关
		
	   		$("#tabs5table").hide(); //纯电
	   	
	   		$("#tabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	   		$("#tabs6table tr[id='trtabs6_qy_ch']").show();
	   		$("#tabs6table tr[id='trtabs6_cd_ch']").hide();
	   		$("#tabs6table tr[id='trtabs6_qy_cd']").show();
	   			}	
	/*
	           else if(showrllx=='非插电式混合动力'){
	            	$("#tabs1table").show();
	            	$("#tabs2table").show();
	            	$("#tabs3table").show();
	            	$("#tabs4table").hide(); cleantable("tabs4table");
	            	
	   					   	$("#tabs2table p[id='labelcddzgcs']").empty();
	   					   	$("#tabs2table p[id='labelcddzgcs']").html('纯电动模式下1km最高车速(km/h)');
	   					   	$("#tabs2table p[id='labelzhgkxslc']").empty();
	   					   	$("#tabs2table p[id='labelzhgkxslc']").html('纯电动模式下综合工况续驶里程(km)');
	   					$("#tabs1table p[id='tjasydnxhl']").empty();
	   				    $("#tabs1table p[id='tjasydnxhl']").html('市郊工况(L/100km)');
	   				   	$("#tabs1table p[id='tjbsyrlxhl']").empty();
	   				   	$("#tabs1table p[id='tjbsyrlxhl']").html('市区工况(L/100km)');
	   				    $("#tabs1table p[id='rlxhl']").empty();
	   			   	    $("#tabs1table p[id='rlxhl']").html('综合工况(L/100km)');
	            	}
	*/
	           
	      else if(showrllx=='纯电动'){
	        	$("#tabs1table").hide();//cleantable("tabs1table"); //变速器相关（汽油、非插电混）、插电混
	       		$("#tabs2table").show(); // 可变参数
	       		$("#tabs2table tr[id='trtabs2_qy_ch']").hide();
	       		$("#tabs2table tr[id='trtabs2_cd_ch']").show();
	       		$("#tabs2table p[id='gktjxbglhdl']").html('电能消耗量(kW·h/100km)');
	       		
	       		$("#tabs3table").hide();//cleantable("tabs3table"); // 汽油非插电混相关
	       		
	       		$("#tabs4table").hide();//cleantable("tabs4table");  //插电混相关
	       		
	       	   	$("#tabs5table").show(); //纯电
	       	   	
	       	   	$("#tabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	       	   	$("#tabs6table tr[id='trtabs6_qy_ch']").hide();
	       	   	$("#tabs6table tr[id='trtabs6_cd_ch']").show();
	       	   	$("#tabs6table p[id='labelzhgkxslc']").html('续航里程(km)');
	       	   	$("#tabs6table tr[id='trtabs6_qy_cd']").show();
	      }
	   	  else if(showrllx=='插电式混合动力'){
	   		$("#tabs1table").show();//cleantable("tabs1table"); //变速器相关（汽油、非插电混）、插电混
			$("#tabs2table").show(); // 可变参数
			$("#tabs2table tr[id='trtabs2_qy_ch']").show();
			$("#tabs2table tr[id='trtabs2_cd_ch']").show();
			$("#tabs2table p[id='gktjxbglhdl']").html('综合电能消耗量(kW·h/100km)');
			
			$("#tabs3table").hide();//cleantable("tabs3table"); // 汽油非插电混相关
			
			$("#tabs4table").show();//cleantable("tabs4table");  //插电混相关
			
		   	$("#tabs5table").show();//cleantable("tabs5table"); //纯电
		   	
		   	$("#tabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
		   	$("#tabs6table tr[id='trtabs6_qy_ch']").show();
		   	$("#tabs6table tr[id='trtabs6_cd_ch']").show();
		   	$("#tabs6table p[id='labelzhgkxslc']").html('电动续航里程(km)');
		   	$("#tabs6table tr[id='trtabs6_qy_cd']").show();
		  }else{
					$("#tabs1table").hide(); cleantable("tabs1table");
				   	$("#tabs2table").hide(); cleantable("tabs2table");
				   	$("#tabs3table").hide(); cleantable("tabs3table");
				   	$("#tabs4table").hide(); cleantable("tabs4table");
				   	$("#tabs5table").hide(); cleantable("tabs5table");
				   	$("#tabs6table").hide(); cleantable("tabs6table");
	}
}

//生效页面
function gtabsshoworhide(rllx){
	var showrllx =  rllx;
	if(showrllx=='汽油'||showrllx=='柴油'||showrllx=='两用燃料'||showrllx=='双燃料'||showrllx=='非插电式混合动力'){
				$("#gtabs1table").show(); //变速器相关（汽油、非插电混）、插电混
		
				$("#gtabs2table").show(); // 可变参数
				$("#gtabs2table tr[id='trtabs2_qy_ch']").show();
				$("#gtabs2table tr[id='trtabs2_cd_ch']").hide();
		
				$("#gtabs3table").show(); // 汽油非插电混相关
		
				$("#gtabs4table").hide();//cleantable("gtabs2table");  //插电混相关
		
	   			$("#gtabs5table").hide(); //纯电
	   	
	   			$("#gtabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	   			$("#gtabs6table tr[id='trtabs6_qy_ch']").show();
	   			$("#gtabs6table tr[id='trtabs6_cd_ch']").hide();
	   			//$("#gtabs6table p[id='labelzhgkxslc']").html('续航里程');
	   			$("#gtabs6table tr[id='trtabs6_qy_cd']").show();
	   		}	
			else if(showrllx=='纯电动'){
				$("#gtabs1table").hide();//cleantable("gtabs1table"); //变速器相关（汽油、非插电混）、插电混
				$("#gtabs2table").show(); // 可变参数
				$("#gtabs2table tr[id='trtabs2_qy_ch']").hide();
				$("#gtabs2table tr[id='trtabs2_cd_ch']").show();
				$("#gtabs2table p[id='gktjxbglhdl']").html('电能消耗量(kW·h/100km)');
				
				$("#gtabs3table").hide();//cleantable("gtabs3table"); // 汽油非插电混相关
				
				$("#gtabs4table").hide();//cleantable("gtabs4table");  //插电混相关
				
			   	$("#gtabs5table").show(); //纯电
			   	
			   	$("#gtabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
			   	$("#gtabs6table tr[id='trtabs6_qy_ch']").hide();
			   	$("#gtabs6table tr[id='trtabs6_cd_ch']").show();
			   	$("#gtabs6table p[id='labelzhgkxslc']").html('续航里程(km)');
			   	$("#gtabs6table tr[id='trtabs6_qy_cd']").show();
    		}
	        else if(showrllx=='插电式混合动力'){
	        	$("#gtabs1table").show();//cleantable("gtabs1table"); //变速器相关（汽油、非插电混）、插电混
	    		$("#gtabs2table").show(); // 可变参数
	    		$("#gtabs2table tr[id='trtabs2_qy_ch']").show();
	    		$("#gtabs2table tr[id='trtabs2_cd_ch']").show();
	    		$("#gtabs2table p[id='gktjxbglhdl']").html('综合电能消耗量(kW·h/100km)');
	    		
	    		$("#gtabs3table").hide();//cleantable("gtabs3table"); // 汽油非插电混相关
	    		
	    		$("#gtabs4table").show();//cleantable("gtabs4table");  //插电混相关
	    		
	    	   	$("#gtabs5table").show();//cleantable("gtabs5table"); //纯电
	    	   	
	    	   	$("#gtabs6table").show(); //全部 + 可变参数（汽油、插混、纯电）
	    	   	$("#gtabs6table tr[id='trtabs6_qy_ch']").show();
	    	   	$("#gtabs6table tr[id='trtabs6_cd_ch']").show();
	    	   	$("#gtabs6table p[id='labelzhgkxslc']").html('电动续航里程(km)');
	    	   	$("#gtabs6table tr[id='trtabs6_qy_cd']").show();	
	            	
	         }else{
					$("#gtabs1table").hide(); cleantable("gtabs1table");
				   	$("#gtabs2table").hide(); cleantable("gtabs2table");
				   	$("#gtabs3table").hide(); cleantable("gtabs3table");
				   	$("#gtabs4table").hide(); cleantable("gtabs4table");
				   	$("#gtabs5table").hide(); cleantable("gtabs5table");
				   	$("#gtabs6table").hide(); cleantable("gtabs6table");
	}
}
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan="10"><p>生产车型:<input type="text" id="qslcx" name="qslcx" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.qslcx!=null"> value="<s:property value="#request.qslcx" />"</s:if>/>  			    
				 状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 				
				<!--  工厂:<s:select name="qfactory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.qfactory"></s:select>-->
				</td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			
			<tr>
			   <td width="80%"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
  			   <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			   <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			   <td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
			   <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			   <td width="60" align="right"><button id="print" class="ui-button ui-state-default ui-corner-all">打印</button></td>
			   <td width="60" align="right"><button id="backupprint" class="ui-button ui-state-default ui-corner-all">备案打印</button></td>
			   <td width="60" align="right"><button id="compare" class="ui-button ui-state-default ui-corner-all">版本比较</button></td>
			 <!--  <td width="60" align="right"><button id="exportfueltar" class="ui-button ui-state-default ui-corner-all">导出目标值</button></td> --> 
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="6%">生产车型</th>
			    <th width="7%">公告车型</th>
			    <th width="5%">状态</th>
			    <th width="10%">版本</th>
				<th width="8%">创建人</th>
				<th width="13%">创建时间</th>
				<th width="13%">生效时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.gasVerPageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.slcx" />,<s:property value="id.vercode" />' ><input type="hidden" name="state" id="state" value='<s:property value="state" />'></td>
			  		<td><s:property value="id.slcx" /></td>
			  		<td><s:property value="cxxh" /></td>
			  		<td>
			  			<s:if test="state==1">生效</s:if>
			  			<s:else>
				  			<s:if test="state==9">历史</s:if>
				  			<s:else>未生效</s:else>
			  			</s:else>
			  		</td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="creator" /></td>	
			  		<td><s:property value="time" /></td>
			  		<td><s:property value="effectTime" /></td>
			  		
			  		<td><a class='display' onclick="return false;" href='#' value='<s:property value="id.slcx" />,<s:property value="id.vercode" />'>查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="gasVer.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="gasVer.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="gasVer.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="gasVer.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<!--<td width="7%" align="right"><button id="template" class="ui-button ui-state-default ui-corner-all">模板</button></td>           	  
			  	<td width="7%" align="right"><button id="import" class="ui-button ui-state-default ui-corner-all">导入</button></td>              
			  	<td width="7%" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>           
				-->
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>

<div id="gas_info_dialog" style="display:none">
	<ul>
		<li><a href="#infotabs-1">查看列表</a></li>
		<li><a href="#infotabs-2">燃油目标量</a></li>
	</ul>
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	<div id="infotabs-1">
	  	  <table id="infotableval" width="100%">
	    	<tr>
				<td><label><P>生产车型</label></td>
				<td><input type="text" id="slcx" name="slcx" class="text ui-widget-content ui-corner-all" size="20" maxlength="18"/>
					</td>
				<td><label><P>版本号</label></td>
				<td><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="25" /></td>
	    	</tr>
	    	<tr>
				<td><label><P>车型型号</label></td>
				<td>
					<input type="text" id="cxxh" name="cxxh" class="text ui-widget-content ui-corner-all" size="11"  />&nbsp;
					<button name="imp" id="imp" class="ui-button ui-state-default ui-corner-all" style="position:static">导入</button>
				</td>
				<td><label><P>生产企业（印刷）</label></td>
				<td><input type="text" id="scqe" name="scqe" class="text ui-widget-content ui-corner-all" size="20"  /></td>
			</tr>
			<tr>	
				<td><label><P>企业标志</label></td>
				<td><input type="text" id="qebz" name="qebz" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>燃料类型</label></td>
				<td>
				<select id="rllx" name="rllx" onchange="createrllxchange()">
				<option value="">请选择</option>
				<option value="汽油">汽油</option>
				<!--  <option value="柴油">柴油</option> -->
				<!--  <option value="两用燃料">两用燃料</option> -->
				<!--  <option value="双燃料">双燃料</option> -->
				<option value="非插电式混合动力">非插电式混合动力</option>
				<option value="插电式混合动力">插电式混合动力</option>
				<option value="纯电动">纯电动</option>
				</select>
				</td>
			</tr>
			
			<tr>
				<td><label><p>工况认证方式</label></td>
				<td>
				<select id="gkrzfs" name="gkrzfs" onchange="changewltc()">
				<option value="">请选择</option>
				<option value="NEDC认证">NEDC认证</option>
				<option value="WLTC认证">WLTC认证</option>
				</select>
				</td>
			</tr>
			<tr id="wltclpxz">				
				<td><label><p>WLTC领跑值(l/100km)</label></td>
				<td><input type="text" id="wltclpz" name="wltclpz" class="text ui-widget-content ui-corner-all" size="20" /></td>																																				
				<td><label><p>WLTC限值(l/100km)</label></td>
				<td><input type="text" id="wltclxz" name="wltclxz" class="text ui-widget-content ui-corner-all" size="20" /></td>	
			</tr>
			
			<tr>	
				<td><label><p>驱动型式</label></td>
				<td>
			<!-- <input type="text" id="qdxs" name="qdxs" class="text ui-widget-content ui-corner-all" size="20" /> -->	
				
				<select id="qdxs" name="qdxs">
				<option value="">请选择</option>
				<option value="前轮驱动">前轮驱动</option>
				<option value="后轮驱动">后轮驱动</option>
				<option value="分时全轮驱动">分时全轮驱动</option>
				<option value="全时全轮驱动">全时全轮驱动</option>
				<option value="智能(适时)全轮驱动">智能(适时)全轮驱动</option>
				</select>
				</td>
				<td><label><p>整车整备质量(kg)</label></td>
				<td><input type="text" id="zbzl" name="zbzl" class="text ui-widget-content ui-corner-all" size="20" />
				<button name="wltcjs" id="wltcjs" class="ui-button ui-state-default ui-corner-all" style="position:static">计算</button>
				</td>
			</tr>
			<tr>				
				<td><label><p>最大设计总质量(kg)</label></td>
				<td><input type="text" id="jdzzl" name="jdzzl" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>启用日期</label></td>
				<td><input type="text" id="qyrq1" name="qyrq1" class="text ui-widget-content ui-corner-all" size="20"  /></td>
			</tr>
			<tr>  
				<td><label><p>备案号</label></td>
				<td><input type="text" id="bacode" name="bacode" class="text ui-widget-content ui-corner-all" size="20"  /></td>
				</tr>
			<tr>				
				<td><label><p>对应限值为(l/100km)</label></td>
				<td><input type="text" id="dyxz" name="dyxz" class="text ui-widget-content ui-corner-all" size="20" /></td>																																				
				<td><label><p>对应限值为(l/100km)</label></td>
				<td><input type="text" id="dexz" name="dexz" class="text ui-widget-content ui-corner-all" size="20" /></td>	
			</tr>
			<tr>
				<td><label><p>其它说明</label></td>
				<td colspan="3"><input type="text" id="remark" name="remark" class="text ui-widget-content ui-corner-all" size="69" /></td>
			</tr>
		<!--/Table>
	  	<table id="two" width="100%"-->
	    	<tr>
				<td><label><P>生产企业（申报）</label></td>
				<td><input type="text" id="scqe1" name="scqe1" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>进口汽车经销商</label></td>
				<td><input type="text" id="jkjxs" name="jkjxs" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>	  	
	    	<tr>
				<td><label><P>检测机构名称</label></td>
				<td><input type="text" id="jcjgmc" name="jcjgmc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>报告编号</label></td>
				<td><input type="text" id="bgbh" name="bgbh" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>	  	  
	    	<tr>
				<td><label><P>通用名称</label></td>
				<td><input type="text" id="tymc" name="tymc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>车辆种类</label></td>
				<td><input type="text" id="clzl" name="clzl" class="text ui-widget-content ui-corner-all" size="20" value="乘用车（M1类）"/></td>
	    	</tr>	  	  
	    	<tr>
				<td><label><p>越野车（G类）</label></td>
				<td>
				<!-- <input type="text" id="yyc" name="yyc" class="text ui-widget-content ui-corner-all" size="20" /> -->
				<select id="yyc" name="yyc">
				<option value="">请选择</option>
				<option value="是">是</option>
				<option value="否">否</option>
				
				</select>
				</td>
				<td><label><p>最高车速(km/h)</label></td>
				<td><input type="text" id="zgcs" name="zgcs" class="text ui-widget-content ui-corner-all" size="20"  /></td>
		    </tr>
		    <tr>	
				<td><label><p>额定载客</label></td>
				<td><input type="text" id="edzk" name="edzk" class="text ui-widget-content ui-corner-all" size="20"  /></td>
				<td><label><p>轮胎规格</label></td>
				<td><input type="text" id="ltgg" name="ltgg" class="text ui-widget-content ui-corner-all" size="20"  /></td>	
		    </tr>
		    <tr>				
				<td><label><p>轮距前/后(mm)</label></td>
				<td><input type="text" id="qhlj" name="qhlj" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>轴距(mm)</label></td>
				<td><input type="text" id="zj" name="zj" class="text ui-widget-content ui-corner-all" size="20" /></td>
		    </tr>
		    <tr>				
				<td><label><p>座位排数</label></td>
				<td><input type="text" id="zwps" name="zwps" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>催化器编号</label></td>
				<td><input type="text" id="chqbh" name="chqbh" class="text ui-widget-content ui-corner-all" size="20" /></td>
		    </tr>
	    	<tr>
		    	<td><label><p>国家标准</label></td>
		    	<td colspan="3"><input type="text" id="gjbz" name="gjbz" class="text ui-widget-content ui-corner-all" size="69" /></td>
		    	
		    </tr>
		    <!--  
			<tr>
				<td><label><P>工厂</label></td>
				<td ><s:select name="factory" list="#request.factorytype"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" ></s:select></td>
				<td><label><P></label></td>
				<td ></td>							
			</tr>		    
		   --> 
		</Table>	
		</div>
			
		<input type='hidden' id='time' name='time'/>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='qyrq' name='qyrq'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	<div id="infotabs-2">
  <table width="100%" id="infotabs1table">  <!-- 汽油和混动 -->	  
   	<tr>
		<td width="20%"><label><P>变速器档位数</label></td>
		<td width="20%">
		<!--<input type="text" id="bsqdws" name="bsqdws" class="text ui-widget-content ui-corner-all" size="20" />  -->
			<select id="bsqdws" name="bsqdws">
				<option value="">请选择</option>
				<option value="1">1</option>
				<option value="2">2</option>
				<option value="3">3</option>
				<option value="4">4</option>
				<option value="5">5</option>
				<option value="6">6</option>
				<option value="7">7</option>
				<option value="8">8</option>
				<option value="9">9</option>
				<option value="10">10</option>
				<option value="N.A">N.A</option>
			</select>
				</td>
		<td width="20%"><label><P>变速器型式</label></td>
		<td width="20%">
		<!--  <input type="text" id="bsqlx" name="bsqlx" class="text ui-widget-content ui-corner-all" size="20" /> -->
		<select  id="bsqlx" name="bsqlx">
		<option value="">请选择</option>
		<option value="MT">MT</option>
		<option value="AT">AT</option>
		<option value="AMT">AMT</option>
		<option value="CVT">CVT</option>
		<option value="DCT">DCT</option>
		<option value="中央减速器">中央减速器</option>
		<option value="其它">其它</option>
		</select>
		</td>
   	
   	</tr>
   	<tr>
		<td><label><P>额定功率(kW)</label></td>
		<td><input type="text" id="edgl" name="edgl" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>发动机型号</label></td>
		<td><input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
   	</tr>
   	<tr>
		<td><label><P>最大净功率(kW)</label></td>
		<td><input type="text" id="zdjgl" name="zdjgl" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>排量</label></td>
		<td><input type="text" id="pl" name="pl" class="text ui-widget-content ui-corner-all" size="20" /></td>
   	</tr>
   	<tr>
		<td><label><P>汽车节能技术</label></td>
		<td><input type="text" id="jcjnjs" name="jcjnjs" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>气缸数</label></td>
		<td><input type="text" id="qgs" name="qgs" class="text ui-widget-content ui-corner-all" size="20" /></td>
   	</tr>
  </table>
	<table width="100%" id="infotabs2table">
	  <tr id="trtabs2_qy_ch"><!-- 汽油:综合燃料消耗量   插电混：综合燃料消耗量 -->
	    <td><label><P>综合燃料消耗量(L/100km)</label></td>
		<td><input type="text" id="zhgk" name="zhgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  	<td></td>
	  	<td></td>
	  </tr>
	  <tr id="trtabs2_cd_ch">
	    <!-- 纯电:电能消耗量   插电混：综合电能消耗量 -->
	    <td ><label><P id="gktjxbglhdl">电能消耗量(kW·h/100km)</label></td>
		<td><input type="text" id="zhgkdnxhl" name="zhgkdnxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	   	<td><label><P>驱动电机峰值功率(kW)</label></td><!-- lable 驱动电机额定功率  改为  驱动电机峰值功率 ，数据字段不变 -->
		<td><input type="text" id="djedgl" name="djedgl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  </tr>
	</table>
	
	<table width="100%" id="infotabs3table"><!-- 汽油与非插电混 -->
	<tr>
		<td><label><P>低速(L/100km)</label></td>
		<td><input type="text" id="dsgk" name="dsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>中速(L/100km)</label></td>
		<td><input type="text" id="zsgk" name="zsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr>	
		<td><label><P>高速(L/100km)</label></td>
		<td><input type="text" id="gsgk" name="gsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P >超高速(L/100km)</label></td>
		<td><input type="text" id="cgsgk" name="cgsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr>
		<td><label><P>白底自编辑标题</label></td>
		<td><input type="text" id="rlxhlbt" name="rlxhlbt" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>白底自编辑数据</label></td>
		<td><input type="text" id="rlxhlsj" name="rlxhlsj" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	</table>
   	
   <table width="100%" id="infotabs4table">  <!-- 插电混 -->
   	<tr>
	    <td><label><P >油电综合折算燃料消耗量(L/100km)</label></td>
		<td><input type="text" id="ydzhrlxhl" name="ydzhrlxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  	<td width="20%"><label><P id="zdhdztrlxhl">亏电电状态燃料消耗量(L/100km)</P></label></td>
		<td width="20%"><input type="text" id="zdhdztrlxhl" name="zdhdztrlxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	 </tr>
 	</table>
 
   
   <table width="100%" id="infotabs5table"> <!-- 纯电 -->    	
	<tr > 
		<td><label><P>电能当量燃料消耗量(L/100km)</label></td>
   		<td><input type="text" id="dndl" name="dndl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
	<tr>
		<td><label><P>白底自编辑1</label></td>
		<td><input type="text" id="rlxhlbj1" name="rlxhlbj1" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
	<tr>
		<td><label><P>白底自编辑2</label></td>
		<td><input type="text" id="rlxhlbj2" name="rlxhlbj2" class="text ui-widget-content ui-corner-all" size="20" /></td>
	 </tr>
   </table>
   
   <table width="100%" id="infotabs6table"> 
   <tr id="trtabs6_qy_ch"><!-- 燃油:CO2排放量 插电混:CO2排放量-->
		<td><label><P >CO2排放量(g/km)</label></td>
		<td><input type="text" id="zhcopl" name="zhcopl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   
   <tr id="trtabs6_cd_ch"><!-- 纯电:续航里程 插电混:电动续航里程-->
   		<td><label><P  id="labelzhgkxslc">续航里程(km)</P></label></td>
		<td><input type="text" id="zhgkxslc" name="zhgkxslc" class="text ui-widget-content ui-corner-all" size="20" /></td>    	
   </tr>
   <tr id="trtabs6_qy_cd"><!-- 汽油:预估能源成本   纯电：预估能源成本 -->
   <td><label><P >预估能源成本(元/100km)</label></td>
		<td><input type="text" id="ygnycb" name="ygnycb" class="text ui-widget-content ui-corner-all" size="20" /></td>
   </tr>
		
   	<tr >
   		<td><label><P>其他信息</label></td>
		<td><input type="text" id="qtxx" name="qtxx" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
	</tr>
   </table>
    
       	</div>
		</form>
	</fieldset>
</div>

<div id="gas_info_display_dialog" title="查看窗口" style="display:none">
	<ul>
		<li><a href="#tabs-1">查看列表</a></li>
		<li><a href="#tabs-2">燃油目标量</a></li>
	</ul>
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	<div id="tabs-1">
	  	  <table width="100%">  	  
	    	<tr>
				<td><label><P>生产车型</label></td>
				<td><input type="text" id="slcx" name="slcx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><P>版本号</label></td>
				<td><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
	    	</tr>	  	  
	    	<tr>
				<td><label><p>企业标志</label></td>
				<td><input type="text" id="qebz" name="qebz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>生产企业（印刷）</label></td>
				<td><input type="text" id="scqe" name="scqe" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
		    </tr>
		    <tr>	
				<td><label><p>车型型号</label></td>
				<td><input type="text" id="cxxh" name="cxxh" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
				<td><label><p>燃料类型</label></td>
				<td><input type="text" id="rllx" name="rllx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
			  </tr>
			  
			   <tr>				
				<td><label><p>工况认证方式</label></td>
				<td><input type="text" id="gkrzfs" name="gkrzfs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    
		    <tr>				
				<td><label><p>WLTC领跑值(l/100km)</label></td>
				<td><input type="text" id="wltclpz" name="wltclpz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>WLTC限值(l/100km)</label></td>
				<td><input type="text" id="wltclxz" name="wltclxz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
			  
		    <tr>				
				<td><label><p>驱动型式</label></td>
				<td><input type="text" id="qdxs" name="qdxs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>整车整备质量(kg)</label></td>
				<td><input type="text" id="zbzl" name="zbzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>最大设计总质量(kg)</label></td>
				<td><input type="text" id="jdzzl" name="jdzzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>启用日期</label></td>
				<td><input type="text" id="qyrq" name="qyrq" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>																																		
			</tr>
			<tr>  	
			  	<td><label><p>备案号</label></td>
				<td><input type="text" id="bacode" name="bacode" class="text ui-widget-content ui-corner-all" size="20"  readonly="true"/></td>
				</tr>
			  </tr>
				 <tr>				
				<td><label><p>对应限值为(l/100km)</label></td>
				<td><input type="text" id="dyxz" name="dyxz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>																																				
				<td><label><p>对应限值为(l/100km)</label></td>
				<td><input type="text" id="dexz" name="dexz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>																																				
			</tr>
		   <tr>
				<td><label><p>其它说明</label></td>
				<td colspan="3"><input type="text" id="remark" name="remark" class="text ui-widget-content ui-corner-all" size="68" readonly="true" /></td>
			</tr>
		
	    	<tr>
				<td><label><P>生产企业（申报）</label></td>
				<td><input type="text" id="scqe1" name="scqe1" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>进口汽车经销商</label></td>
				<td><input type="text" id="jkjxs" name="jkjxs" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>	  	
	    	<tr>
				<td><label><P>检测机构名称</label></td>
				<td><input type="text" id="jcjgmc" name="jcjgmc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>报告编号</label></td>
				<td><input type="text" id="bgbh" name="bgbh" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>	  	
	    	<tr>
				<td><label><P>通用名称</label></td>
				<td><input type="text" id="tymc" name="tymc" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><P>车辆种类</label></td>
				<td><input type="text" id="clzl" name="clzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
	    	</tr>	  	  
	    	<tr>
				<td><label><p>越野车（G类）</label></td>
				<td><input type="text" id="yyc" name="yyc" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>最高车速(km/h)</label></td>
				<td><input type="text" id="zgcs" name="zgcs" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
		    </tr>
		    <tr>	
				<td><label><p>额定载客(人)</label></td>
				<td><input type="text" id="edzk" name="edzk" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
				<td><label><p>轮胎规格</label></td>
				<td><input type="text" id="ltgg" name="ltgg" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>	
		    </tr>
		    <tr>				
				<td><label><p>轮距前/后(mm)</label></td>
				<td><input type="text" id="qhlj" name="qhlj" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>轴距(mm)</label></td>
				<td><input type="text" id="zj" name="zj" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>座位排数</label></td>
				<td><input type="text" id="zwps" name="zwps" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>催化器编号</label></td>
				<td><input type="text" id="chqbh" name="chqbh" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>				
		    </tr>
		   	<tr>
		    	<td><label><p>国家标准</label></td>
		    	<td colspan="3"><input type="text" id="gjbz" name="gjbz" class="text ui-widget-content ui-corner-all" size="69" /></td>

		    </tr>
	    
		    		    
		</Table>	
		</div>
	<div id="tabs-2">
  <table width="100%" id="tabs1table">  	  
   	<tr>
		<td width="20%"><label><P>变速器档位数</label></td>
		<td width="20%"><input type="text" id="bsqdws" name="bsqdws" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td width="20%"><label><P>变速器型式</label></td>
		<td width="20%"><input type="text" id="bsqlx" name="bsqlx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>额定功率(kW)</label></td>
		<td><input type="text" id="edgl" name="edgl" class="text ui-widget-content ui-corner-all" size="30" readonly="true"/></td>
		<td><label><P>发动机型号</label></td>
		<td><input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>最大净功率(kW)</label></td>
		<td><input type="text" id="zdjgl" name="zdjgl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>排量</label></td>
		<td><input type="text" id="pl" name="pl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>汽车节能技术</label></td>
		<td><input type="text" id="jcjnjs" name="jcjnjs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>气缸数</label></td>
		<td><input type="text" id="qgs" name="qgs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   </table>
   <table width="100%" id="tabs2table">
   	  <tr id="trtabs2_qy_ch"><!-- 汽油:综合燃料消耗量   插电混：综合燃料消耗量 -->
	    <td><label><P>综合燃料消耗量(L/100km)</label></td>
		<td><input type="text" id="zhgk" name="zhgk" class="text ui-widget-content ui-corner-all" size="20"  readonly="true"/></td>
	  	<td></td>
	  	<td></td>
	  </tr>
	  <tr id="trtabs2_cd_ch"><!-- 纯电:电能消耗量   插电混：综合电能消耗量 -->
	  	<td ><label><P id="gktjxbglhdl">电能消耗量(kW·h/100km)</label></td>
		<td><input type="text" id="zhgkdnxhl" name="zhgkdnxhl" class="text ui-widget-content ui-corner-all" size="20"  readonly="true"/></td>
		<td><label><P>驱动电机峰值功率(kW)</label></td><!-- lable 驱动电机额定功率  改为  驱动电机峰值功率 ，数据字段不变 -->
		<td><input type="text" id="djedgl" name="djedgl" class="text ui-widget-content ui-corner-all" size="20"  readonly="true"/></td>
	  </tr>
	</table>
   	
   	
   <table width="100%" id="tabs3table">  
   	<tr>
		<td><label><P>低速(L/100km)</label></td>
		<td><input type="text" id="dsgk" name="dsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>中速(L/100km)</label></td>
		<td><input type="text" id="zsgk" name="zsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr>	
		<td><label><P>高速(L/100km)</label></td>
		<td><input type="text" id="gsgk" name="gsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P >超高速(L/100km)</label></td>
		<td><input type="text" id="cgsgk" name="cgsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr>
		<td><label><P>白底自编辑标题</label></td>
		<td><input type="text" id="rlxhlbt" name="rlxhlbt" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>白底自编辑数据</label></td>
		<td><input type="text" id="rlxhlsj" name="rlxhlsj" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr> 
   </table>
   
<table width="100%" id="tabs4table">  
   	<tr>
	    <td><label><P >油电综合折算燃料消耗量(L/100km)</label></td>
		<td><input type="text" id="ydzhrlxhl" name="ydzhrlxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  	<td width="20%"><label><P id="zdhdztrlxhl">亏电电状态燃料消耗量(L/100km)</P></label></td>
		<td width="20%"><input type="text" id="zdhdztrlxhl" name="zdhdztrlxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	 </tr>
   </table>    
      
<table width="100%" id="tabs5table"> 
	<tr > 
		<td><label><P>电能当量燃料消耗量(L/100km)</label></td>
   		<td><input type="text" id="dndl" name="dndl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
	<tr>
		<td><label><P>白底自编辑1</label></td>
		<td><input type="text" id="rlxhlbj1" name="rlxhlbj1" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
	<tr>
		<td><label><P>白底自编辑2</label></td>
		<td><input type="text" id="rlxhlbj2" name="rlxhlbj2" class="text ui-widget-content ui-corner-all" size="20" /></td>
	 </tr>
   </table>
   
<table width="100%" id="tabs6table"> 
   <tr id="trtabs6_qy_ch"><!-- 燃油:CO2排放量 插电混:CO2排放量-->
		<td><label><P >CO2排放量(g/km)</label></td>
		<td><input type="text" id="zhcopl" name="zhcopl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   
   <tr id="trtabs6_cd_ch"><!-- 纯电:续航里程 插电混:电动续航里程-->
   		<td><label><P  id="labelzhgkxslc">续航里程(km)</P></label></td>
		<td><input type="text" id="zhgkxslc" name="zhgkxslc" class="text ui-widget-content ui-corner-all" size="20" /></td>    	
   </tr>
   <tr id="trtabs6_qy_cd"><!-- 汽油:预估能源成本   纯电：预估能源成本 -->
   <td><label><P >预估能源成本(元/100km)</label></td>
		<td><input type="text" id="ygnycb" name="ygnycb" class="text ui-widget-content ui-corner-all" size="20" /></td>
   </tr> 
</table> 
	    	</div>			
		<input type='hidden' id='time' name='time'/>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="gas_info_effect_dialog" title="生效窗口" style="display:none">
	<ul>
		<li><a href="#tabs-g1">查看列表</a></li>
		<li><a href="#tabs-g2">燃油目标量</a></li>
	</ul>
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	<div id="tabs-g1">
	  	  <table width="100%">
	    	<tr>
				<td><label><P>生产车型</label></td>
				<td><input type="text" id="slcx" name="slcx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><P>版本号</label></td>
				<td><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
	    	</tr>	  	  
	    	<tr>
				<td><label><p>企业标志</label></td>
				<td><input type="text" id="qebz" name="qebz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>生产企业（印刷）</label></td>
				<td><input type="text" id="scqe" name="scqe" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
		    </tr>
		    <tr>	
				<td><label><p>车型型号</label></td>
				<td><input type="text" id="cxxh" name="cxxh" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
				<td><label><p>发动机型号</label></td>
				<td><input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>	
		    </tr>
		    <tr>				
				<td><label><p>燃料类型</label></td>
				<td><input type="text" id="rllx" name="rllx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>排量（ml)</label></td>
				<td><input type="text" id="pl" name="pl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    
		    <tr>				
				<td><label><p>工况认证方式</label></td>
				<td><input type="text" id="gkrzfs" name="gkrzfs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    
		    <tr>				
				<td><label><p>WLTC领跑值(l/100km)</label></td>
				<td><input type="text" id="wltclpz" name="wltclpz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>WLTC限值(l/100km)</label></td>
				<td><input type="text" id="wltclxz" name="wltclxz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    
		    <tr>				
				<td><label><p>额定功率(kw)</label></td>
				<td><input type="text" id="edgl" name="edgl" class="text ui-widget-content ui-corner-all" size="30" readonly="true"/></td>
				<td><label><p>变速器类型</label></td>
				<td><input type="text" id="bsqlx" name="bsqlx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>驱动型式</label></td>
				<td><input type="text" id="qdxs" name="qdxs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>整车整备质量(kg)</label></td>
				<td><input type="text" id="zbzl" name="zbzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>最大设计总质量(kg)</label></td>
				<td><input type="text" id="jdzzl" name="jdzzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>其他信息</label></td>
				<td><input type="text" id="qtxx" name="qtxx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>市区工况(L/100km)</label></td>
				<td><input type="text" id="sqgk" name="sqgk" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>综合工况(L/100km)</label></td>
				<td><input type="text" id="zhgk" name="zhgk" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>																																				
		    </tr>
		    <tr>				
				<td><label><p>市郊工况(L/100km)</label></td>
				<td><input type="text" id="sjgk" name="sjgk" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>																																				
				<td><label><p>对应限值为(L/100km)</label></td>
				<td><input type="text" id="dyxz" name="dyxz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>																																				
		    </tr>
		    <tr>				
				<td><label><p>对应限值为(l/100km)</label></td>
				<td><input type="text" id="dexz" name="dexz" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>																																				
				<td><label><p>启用日期</label></td>
				<td><input type="text" id="qyrq" name="qyrq" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>	
			</tr>
			<tr>  	
				<td><label><p>备案号</label></td>
				<td><input type="text" id="bacode" name="bacode" class="text ui-widget-content ui-corner-all" size="20"  readonly="true"/></td>																																
		    </tr>
		    <tr>
				
				<td><label><p>其它说明</label></td>
				<td colspan="3"><input type="text" id="remark" name="remark" class="text ui-widget-content ui-corner-all" size="68" readonly="true" /></td>
			</tr>
	    	<tr>
				<td><label><P>生产企业（申报）</label></td>
				<td><input type="text" id="scqe1" name="scqe1" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>进口汽车经销商</label></td>
				<td><input type="text" id="jkjxs" name="jkjxs" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>	  	
	    	<tr>
				<td><label><P>检测机构名称</label></td>
				<td><input type="text" id="jcjgmc" name="jcjgmc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>报告编号</label></td>
				<td><input type="text" id="bgbh" name="bgbh" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>	  	
	    	<tr>
				<td><label><P>通用名称</label></td>
				<td><input type="text" id="tymc" name="tymc" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><P>车辆种类</label></td>
				<td><input type="text" id="clzl" name="clzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
	    	</tr>	  	  
	    	<tr>
				<td><label><p>越野车（G类）</label></td>
				<td><input type="text" id="yyc" name="yyc" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>最高车速(km/h)</label></td>
				<td><input type="text" id="zgcs" name="zgcs" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
		    </tr>
		    <tr>	
				<td><label><p>额定载客(人)</label></td>
				<td><input type="text" id="edzk" name="edzk" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>
				<td><label><p>轮胎规格</label></td>
				<td><input type="text" id="ltgg" name="ltgg" class="text ui-widget-content ui-corner-all" size="20" readonly="true" /></td>	
		    </tr>
		    <tr>				
				<td><label><p>轮距前/后(mm)</label></td>
				<td><input type="text" id="qhlj" name="qhlj" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>轴距(mm)</label></td>
				<td><input type="text" id="zj" name="zj" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>气缸数</label></td>
				<td><input type="text" id="qgs" name="qgs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
				<td><label><p>最大净功率(kW)</label></td>
				<td><input type="text" id="zdjgl" name="zdjgl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		    </tr>
		    <tr>				
				<td><label><p>变速器档位数</label></td>
				<td colspan=""><input type="text" id="bsqdws" name="bsqdws" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>综合工况CO2排放量(g/km)</label></td>
				<td colspan=""><input type="text" id="zhcopl" name="zhcopl" class="text ui-widget-content ui-corner-all" size="20" /></td>
		    </tr>	
		    <tr>				
				<td><label><p>座位排数</label></td>
				<td><input type="text" id="zwps" name="zwps" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>催化器编号</label></td>
				<td><input type="text" id="chqbh" name="chqbh" class="text ui-widget-content ui-corner-all" size="20" /></td>
		    </tr>
		    <tr>
		    	<td><label><p>国家标准</label></td>
		    	<td colspan="3"><input type="text" id="gjbz" name="gjbz" class="text ui-widget-content ui-corner-all" size="69" /></td>

		    </tr>
		    <%-- 20160610 lmc 工厂--%>		    
		</Table>
	</div>
	<div id="tabs-g2">
		<table width="100%" id="gtabs1table">  	  
   	<tr>
		<td width="20%"><label><P>变速器档位数</label></td>
		<td width="20%"><input type="text" id="bsqdws" name="bsqdws" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td width="20%"><label><P>变速器型式</label></td>
		<td width="20%"><input type="text" id="bsqlx" name="bsqlx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>额定功率(kW)</label></td>
		<td><input type="text" id="edgl" name="edgl" class="text ui-widget-content ui-corner-all" size="30" readonly="true"/></td>
		<td><label><P>发动机型号</label></td>
		<td><input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>最大净功率(kW)</label></td>
		<td><input type="text" id="zdjgl" name="zdjgl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>排量</label></td>
		<td><input type="text" id="pl" name="pl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>汽车节能技术</label></td>
		<td><input type="text" id="jcjnjs" name="jcjnjs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>气缸数</label></td>
		<td><input type="text" id="qgs" name="qgs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	</table>
   	
   <table width="100%" id="gtabs2table">  
   	<tr id="trtabs2_qy_ch"><!-- 汽油:综合燃料消耗量   插电混：综合燃料消耗量 -->
	    <td><label><P>综合燃料消耗量(L/100km)</label></td>
		<td><input type="text" id="zhgk" name="zhgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  	<td></td>
	  	<td></td>
	  </tr>
	  <tr id="trtabs2_cd_ch"><!-- 纯电:电能消耗量   插电混：综合电能消耗量 -->
	  	<td ><label><P id="gktjxbglhdl">电能消耗量(kW·h/100km)</label></td>
		<td><input type="text" id="zhgkdnxhl" name="zhgkdnxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>驱动电机峰值功率(kW)</label></td><!-- lable 驱动电机额定功率  改为  驱动电机峰值功率 ，数据字段不变 -->
		<td><input type="text" id="djedgl" name="djedgl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  </tr>
   </table>
   <table width="100%" id="gtabs3table">  
   	<tr>
		<td><label><P>低速(L/100km)</label></td>
		<td><input type="text" id="dsgk" name="dsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>中速(L/100km)</label></td>
		<td><input type="text" id="zsgk" name="zsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr>	
		<td><label><P>高速(L/100km)</label></td>
		<td><input type="text" id="gsgk" name="gsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P >超高速(L/100km)</label></td>
		<td><input type="text" id="cgsgk" name="cgsgk" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr>
		<td><label><P>白底自编辑标题</label></td>
		<td><input type="text" id="rlxhlbt" name="rlxhlbt" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td><label><P>白底自编辑数据</label></td>
		<td><input type="text" id="rlxhlsj" name="rlxhlsj" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   </table> 
   
<table width="100%" id="gtabs4table">  
   	<tr>
	    <td><label><P >油电综合折算燃料消耗量(L/100km)</label></td>
		<td><input type="text" id="ydzhrlxhl" name="ydzhrlxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  	<td width="20%"><label><P id="zdhdztrlxhl">亏电电状态燃料消耗量(L/100km)</P></label></td>
		<td width="20%"><input type="text" id="zdhdztrlxhl" name="zdhdztrlxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	 </tr>	 
</table>
<table width="100%" id="gtabs5table"> 
    <tr > 
		<td><label><P>电能当量燃料消耗量(L/100km)</label></td>
   		<td><input type="text" id="dndl" name="dndl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr> 
	<tr>
		<td><label><P>白底自编辑1</label></td>
		<td><input type="text" id="rlxhlbj1" name="rlxhlbj1" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
	<tr>	
		<td><label><P>白底自编辑2</label></td>
		<td><input type="text" id="rlxhlbj2" name="rlxhlbj2" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
</table>
<table width="100%" id="gtabs6table"> 
    <tr id="trtabs6_qy_ch"><!-- 燃油:CO2排放量 插电混:CO2排放量-->
		<td><label><P >CO2排放量(g/km)</label></td>
		<td><input type="text" id="zhcopl" name="zhcopl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   
   <tr id="trtabs6_cd_ch"><!-- 纯电:续航里程 插电混:电动续航里程-->
   		<td><label><P  id="labelzhgkxslc">续航里程(km)</P></label></td>
		<td><input type="text" id="zhgkxslc" name="zhgkxslc" class="text ui-widget-content ui-corner-all" size="20" /></td>    	
   </tr>
   <tr id="trtabs6_qy_cd"><!-- 汽油:预估能源成本   纯电：预估能源成本 -->
   <td><label><P >预估能源成本(元/100km)</label></td>
		<td><input type="text" id="ygnycb" name="ygnycb" class="text ui-widget-content ui-corner-all" size="20" /></td>
   </tr>
		
   	<tr >
   		<td><label><P>其他信息</label></td>
		<td><input type="text" id="qtxx" name="qtxx" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
	</tr>
</table>
	</div>		
		<input type='hidden' id='time' name='time'/>
		<input type='hidden' id='creator' name='creator'/>
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='slcx' name='slcx'>
	<input type='hidden' id='vercode' name='vercode'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div id="public_notice_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table id="one" width="100%">
	    		<tr>
	    		<td width="9%"><label><P>产品型号</label></td>
				<td width="17%"><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>识别码</label></td>
				<td width="17%"><input type="text" id="flag" name="flag" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="9%"><label><P>产品名称</label></td>
				<td width="17%"><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td width="8%"><label><P>中文品牌</label></td>
				<td width="17%"><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td width="7%"><label><P>英文品牌</label></td>
				<td width="16%"><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业名称</label></td>
				<td><input type="text" id="c27" name="c27" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>企业地址</label></td>
				<td><input type="text" id="c28" name="c28" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>注册地址</label></td>
				<td><input type="text" id="c29" name="c29" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>生产地址</label></td>
				<td><input type="text" id="c30" name="c30" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>识别代号</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>前轮距</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>后轮距</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>发动机</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机企业</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机排量</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>发动机功率</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>底盘类别</label></td>
				<td><input type="text" id="c19" name="c19" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>批次</label></td>
				<td><input type="text" id="c26" name="c26" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>长</label></td>
				<td><input type="text" id="c37" name="c37" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>宽</label></td>
				<td><input type="text" id="c38"  name="c38" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>			
				<td><label><P>高</label></td>
				<td><input type="text" id="c39" name="c39" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>燃料种类</label></td>
				<td><input type="text" id="c40" name="c40" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>依据标准</label></td>
				<td><input type="text" id="c41" name="c41" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>转向形式</label></td>
				<td><input type="text" id="c42"  name="c42" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>		
				<td><label><P>轴数</label></td>
				<td><input type="text" id="c46"  name="c46" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>	
				<td><label><P>轴距(mm)</label></td>
				<td><input type="text" id="c47" name="c47" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎数</label></td>
				<td><input type="text" id="c49" name="c49" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮胎规格</label></td>
				<td><input type="text" id="c50"  name="c50" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>
				<td><label><P>总质量</label></td>
				<td><input type="text" id="c51" name="c51" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>			
				<td width="9%"><label><P>整备质量</label></td>
				<td width="17%"><input type="text" id="c53" name="c53" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>额定载客(人)</label></td>
				<td><input type="text" id="c57" name="c57" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>接近离去角</label></td>
				<td><input type="text" id="c59" name="c59" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>前悬后悬</label></td>
				<td><input type="text" id="c60" name="c60" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>传动型式</label></td>
				<td><input type="text" id="c62" name="c62" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>最高车速(km/h)</label></td>
				<td><input type="text" id="c63" name="c63" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>起动方式</label></td>
				<td><input type="text" id="c73" name="c73" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>排放水平</label></td>
				<td><input type="text" id="c86"  name="c86" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>油耗</label></td>
				<td><input type="text" id="c88" name="c88" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轴荷</label></td>
				<td><input type="text" id="c89" name="c89" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>轮数</label></td>
				<td><input type="text" id="c79" name="c79" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>	
			<tr>	
				<td><label><P>产品商标</label></td>
				<td><input type="text" id="c77" name="c77" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>		
				<td><label><P>联系人</label></td>
				<td><input type="text" id="c80" name="c80" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
				<td><label><P>法人代表</label></td>
				<td><input type="text" id="c76"  name="c76" class="text ui-widget-content ui-corner-all" size="14" readonly/></td>
			</tr>			
		</Table>
		</form>
	</fieldset>
</div>
<div id="pubilc_notice_dialog" title="操作窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
  	公告车型型号:<select id='pmodelList' name='pmodelList'><option value="">请选择...</option></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<!--  button name="dis" id="dis" class="ui-button ui-state-default ui-corner-all" style="position:static">查看</button>-->
  	<br>
  	<br>
  	工况信息:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<select id='gkxxList' name='gkxxList'><option value="">请选择...</option></select>
</div>
<div id="backup_print_dialog" style="display:none" title="备案打印">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  <table id="one" width="100%">
	    	<tr>
	    		<td width="9%"><label><P>VIN</label></td>
				<td width="17%" align="left"><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="30" /></td>
			</tr>	
			<tr>
	    		<td width="9%"><label><P>备案号</label></td>
				<td width="17%" align="left"><input type="text" id="number" name="numbers" class="text ui-widget-content ui-corner-all" size="30" /></td>
			</tr>	
			<input type='hidden' id='c1' name='c1'/>
			<input type='hidden' id='vercode' name='vercode'/>	
		</Table>
		</form>
	</fieldset>
</div>


<!-- 比较界面 -->
<div id="gas_compare_dialog" style="display:none;overflow-y:auto" >
	<p id="validateTips"></p>
	
		<form id="createForm" method="post" >
			<table>
				<tr>
					<td ><label><P>版本号1：</label></td>
					<td name='ver1'><s:select cssClass='command' name="gasVer1" list="#request.gasVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.gasVer1" ></s:select></td>
					<td ><label><P>版本号2：</label></td>
					<td name='ver2'><s:select cssClass='command' name="gasVer2" list="#request.gasVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.gasVer1" ></s:select></td>
					<td width="60" align="right"><button id="compare1" type="button" class="ui-button ui-state-default ui-corner-all">比较</button></td></td>
				</tr>			
			</table>
			<table id="tbl1" width="100%" >
				<tr name="qebz" style="background:#f1f9f3;">
					<td width="10%" ><label><P>企业标志</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="scqe" style="background:#f1f9f3">
					<td width="10%" ><label><P>生产企业（印刷）</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="cxxh" style="background:#f1f9f3">
					<td width="10%" ><label><P>车型型号</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="fdjxh" style="background:#f1f9f3">
					<td width="10%" ><label><P>发动机型号</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="rllx" style="background:#f1f9f3">
					<td width="10%" ><label><P>燃料类型</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="pl" style="background:#f1f9f3">
					<td width="10%" ><label><P>排量（ml)</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="edgl" style="background:#f1f9f3">
					<td width="10%" ><label><P>额定功率(kw)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="bsqlx" style="background:#f1f9f3">
					<td width="10%" ><label><P>变速器类型</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="qdxs" style="background:#f1f9f3">
					<td width="10%" ><label><P>驱动型式</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="zbzl" style="background:#f1f9f3">
					<td width="10%" ><label><P>整车整备质量(kg)</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="jdzzl" style="background:#f1f9f3">
					<td width="10%" ><label><P>最大设计总质量(kg)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="qtxx" style="background:#f1f9f3">
					<td width="10%" ><label><P>其他信息</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="sqgk" style="background:#f1f9f3">
					<td width="10%" ><label><P>市区工况(L/100km)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="zhgk" style="background:#f1f9f3">
					<td width="10%" ><label><P>综合工况(L/100km)</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="sjgk" style="background:#f1f9f3">
					<td width="10%" ><label><P>市郊工况(L/100km)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="dyxz" style="background:#f1f9f3">
					<td width="10%" ><label><P>对应限值为(L/100km)</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="qyrq" style="background:#f1f9f3">
					<td width="10%" ><label><P>启用日期</label></td><td width="45%" ></td><td width="45%" ></td>										
				</tr>
				<tr name="remark" style="background:#f1f9f3">
					<td width="10%" ><label><P>其它说明</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="scqe1" style="background:#f1f9f3">
					<td width="10%" ><label><P>生产企业（申报）</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="jkjxs" style="background:#f1f9f3">
					<td width="10%" ><label><P>进口汽车经销商</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="jcjgmc" style="background:#f1f9f3">
					<td width="10%" ><label><P>检测机构名称</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="bgbh" style="background:#f1f9f3">
					<td width="10%" ><label><P>报告编号</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="tymc" style="background:#f1f9f3">
					<td width="10%" ><label><P>通用名称</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="clzl" style="background:#f1f9f3">
					<td width="10%" ><label><P>车辆种类</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="yyc" style="background:#f1f9f3">
					<td width="10%" ><label><P>越野车（G类）</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="zgcs" style="background:#f1f9f3">
					<td width="10%" ><label><P>最高车速(km/h)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="edzk" style="background:#f1f9f3">
					<td width="10%" ><label><P>额定载客(人)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="ltgg" style="background:#f1f9f3">
					<td width="10%" ><label><P>轮胎规格</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="qhlj" style="background:#f1f9f3">
					<td width="10%" ><label><P>轮距前/后(mm)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="zj" style="background:#f1f9f3">
					<td width="10%" ><label><P>轴距(mm)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="qgs" style="background:#f1f9f3">
					<td width="10%" ><label><P>气缸数</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="zdjgl" style="background:#f1f9f3">
					<td width="10%" ><label><P>最大净功率(kW)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="bsqdws" style="background:#f1f9f3">
					<td width="10%" ><label><P>变速器档位数</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="zhcopl" style="background:#f1f9f3">
					<td width="10%" ><label><P>综合工况CO2排放量(g/km)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>				
				<tr name="zwps" style="background:#f1f9f3">
					<td width="10%" ><label><P>座位排数</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>	

				<tr name="chqbh" style="background:#f1f9f3">
					<td width="10%" ><label><P>催化器编号</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>	
				
				
				<tr name="bsqlx" style="background:#f1f9f3">
					<td width="10%" ><label><P>变速器型式</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				<tr name="jcjnjs" style="background:#f1f9f3">
					<td width="10%" ><label><P>汽车节能技术</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				<tr name="dcbnl" style="background:#f1f9f3">
					<td width="10%" ><label><P>动力蓄电池组比能量</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				<tr name="dczbcdy" style="background:#f1f9f3">
					<td width="10%" ><label><P>动力蓄电池组标称电压(V)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				<tr name="dczednl" style="background:#f1f9f3">
					<td width="10%" ><label><P>动力蓄电池组总能量(kWh)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				<tr name="dczzl" style="background:#f1f9f3">
					<td width="10%" ><label><P>动力蓄电池组种类</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				
				<tr name="djedgl" style="background:#f1f9f3">
					<td width="10%" ><label><P>驱动电机峰值功率(kW)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="djfznj" style="background:#f1f9f3">
					<td width="10%" ><label><P>驱动电机峰值扭矩(N•m)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				<tr name="djlx" style="background:#f1f9f3">
					<td width="10%" ><label><P>驱动电机类型</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
				
				
					<tr name="zhgkxslc" style="background:#f1f9f3">
					<td width="10%" ><label><P>纯电动模式下综合工况续驶里程(km)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
					<tr name="hhdljgxs" style="background:#f1f9f3">
					<td width="10%" ><label><P>混合动力结构型式</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
					<tr name="hhdlzddglb" style="background:#f1f9f3">
					<td width="10%" ><label><P>混合动力最大电功率比(%)</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
					<tr name="xsmssdxzgn" style="background:#f1f9f3">
					<td width="10%" ><label><P>是否具有行驶模式手动选择功能</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
					
				<tr name="dndl" style="background:#f1f9f3">
					<td width="10%" ><label><P>电能当量燃料消耗量</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>	
				<tr name="zdhdztrlxhl" style="background:#f1f9f3">
					<td width="10%" ><label><P>最低荷电状态燃料消耗量</label></td><td width="45%" ></td><td width="45%" ></td>
				</tr>
							
			</table>
		</form>
	
</div>

<div align="center"> 
	<jsp:plugin name="coc" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarVersionPrintApplet.class" codebase="." archive = "../applet/printVersion.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/com-jaspersoft-ireport.jar,../applet/Qrcode_encoder.jar,../applet/iText-2.1.0.jar,../applet/iTextAsian.jar" 
	iepluginurl="http://********/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="0" width="0">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="COCSupplement"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>
<script type="text/javascript">
function findOtherVer(obj){
	var val=obj.value;
	$('#gasVer2').focus();
	jQuery.ajax({
        url: 'business/gasVer!findGasVer.action',		           
        data: {'gasVer1' : val}, 
     	type: 'POST',
     	//dataType:'json',
        beforeSend: function() {
        
        },
        error: function(XmlHttpRequest,textStatus, errorThrown) {
        	//alert(XmlHttpRequest.responseText);
            alert("系统错误，请与管理员联系！");
        },
        success: function(data) {
         	var content = json2Bean(data).json;
         	var carObj = eval("("+content.toString()+")");
			$("#gasVer2").empty();//清空下拉框 
			$("<option value=''>请选择...</option>").appendTo("#gasVer2");
			$.each( carObj, function(i, n){
				var tmp=n.id.slcx+","+n.id.vercode;
				var stateName='';
				if(tmp!=val){
					if(n.state=='0'){
						stateName='未生效';
					}else if(n.state=='1'){
						stateName='生效';
					}else if(n.state=='9'){
						stateName='历史';
					}
					var opt="<option value='"+tmp +"'>"+n.id.slcx+"("+n.id.vercode+stateName+")</option>";
					$(opt).appendTo("#gasVer2")//添加下拉框的option
				}				
			});			     	
        }
    });	
}


function showInfo(c1,vercode,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}
</script>
</body>
</html>