package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao;

import java.io.Serializable;
import java.util.List;

import org.hibernate.ObjectNotFoundException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FilterModel;

public class FilterModelDAOImpl extends GenericHibernateDAOImpl<FilterModel> implements FilterModelDAO {

	public void addFilterModel(FilterModel obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCarColor Method Error:",e);
		}
	}

	public void deleteFilterModel(Serializable id) {
		try{
			this.delete(this.loadFilterModel(id));
		}catch(Exception e){
			throw new DataAccessException("deleteFilterModel Method Error:",e);
		}

	}

	public void deleteFilterModel(Serializable[] id) {
		try{
			for(int i  = 0; i < id.length; i++){
				this.deleteFilterModel(id[i]);
			}
		}catch(Exception e){
			throw new DataAccessException("deleteFilterModel Method Error:",e);
		}

	}

	public List<FilterModel> findFilterModel(String sql) {
		List<FilterModel> list = null;
		try{
			list = this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findFilterModel Method Error:",e);
		}
		return list;
	}

	public List<FilterModel> findFilterModel(String sql, Object[] params) {
		List<FilterModel> list = null;
		try{
			list = this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findFilterModel Method Error:",e);
		}
		return list;
	}

	public void updateFilterModel(FilterModel obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateFilterModel Method Error:",e);
		}
	}

	public FilterModel loadFilterModel(Serializable id) {
		FilterModel obj = null;
		try{
			obj = (FilterModel)this.load(FilterModel.class, id);
		}catch(ObjectNotFoundException e){
			
		}catch(Exception e){
			
		}
		return obj;
	}

}
