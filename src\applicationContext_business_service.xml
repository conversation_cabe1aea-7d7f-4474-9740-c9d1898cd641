<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="autodetect" xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xmlns:context="http://www.springframework.org/schema/context"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
				http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	
	<bean id="certificateService" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.service.CertificateServiceImpl" lazy-init="true" scope="request">
		<property name="certificateDAO">
			<ref bean="certificateDAO" />
		</property>
		<property name="systemConfigParamsService">
			<ref bean="systemConfigParamsService" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
		<property name="sysDataDictionaryService">
			<ref bean="sysDataDictionaryService" />
		</property>
		<property name="hwCOCYearSeqService">
			<ref bean="hwCOCYearSeqService" />
		</property>
	</bean>
	
	<bean id="interfaceLogService" class="com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceLogServiceImpl" lazy-init="true" scope="request">
		<property name="interfaceLogDAO">
			<ref bean="interfaceLogDAO" />
		</property>
		<property name="certificateService" ref="certificateService"/>
		<property name="paginationService" ref="paginationService"/>
		<property name="cocPhotoService" ref="cocPhotoService"/>
	</bean>
	
	<bean id="publicNoticeCarModelService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.PublicNoticeCarModelServiceImpl" lazy-init="true" scope="request">
		<property name="publicNoticeCarModelDAO">
			<ref bean="publicNoticeCarModelDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="carRecallService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CarRecallServiceImpl" lazy-init="true" scope="request">
		<property name="dao">
			<ref bean="carRecallDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="carInfoService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CarInfoServiceImpl" lazy-init="true" scope="request">
		<property name="dao">
			<ref bean="carInfoDAO" />
		</property>
		<property name="carInfoVoidDao">
			<ref bean="carInfoVoidDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
		<property name="interfaceLogDAO">
			<ref bean="interfaceLogDAO" />
		</property>
		<property name="cocPhotoService" ref="cocPhotoService"/>
	</bean>
	
	<bean id="carvercodeservice" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CarVerCodeServiceImpl" lazy-init="true" scope="request">
		<property name="dao">
			<ref bean="carInfoDAO" />
		</property>
		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="gasVerService" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.GasVerServiceImpl" lazy-init="true" scope="request">
		<property name="gasVerDAO">
			<ref bean="gasVerDao" />
		</property>
		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="cocVerService" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.CocVerServiceImpl" lazy-init="true" scope="request">
		<property name="cocVerDAO">
			<ref bean="cocVerDao" />
		</property>
		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="hwCocVerService" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.HwCocVerServiceImpl" lazy-init="true" scope="request">
		<property name="hwCocVerDAO">
			<ref bean="hwCocVerDao" />
		</property>
		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
		<bean id="proenvService" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.ProenvServiceImpl" lazy-init="true" scope="request">
		<property name="proenvDAO">
			<ref bean="proenvDao" />
		</property>
		
		<property name="scjcPrintDAO">
			<ref bean="scjcPrintDAO" />
		</property>
		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	
	
	
	<bean id="modelverService" class="com.dawnpro.dfpv.carfilemanager.module.business.modelver.service.impl.ModelverServiceImpl" lazy-init="true" scope="request">
		<property name="modelverDAO">
			<ref bean="modelverDao" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="productionCarModelService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.ProductionCarModelServiceImpl" lazy-init="true" scope="request">
		<property name="productionCarModelDAO">
			<ref bean="productionCarModelDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="cocPhotoService" class="com.dawnpro.dfpv.carfilemanager.module.business.verManager.service.impl.COCPhotoServiceImpl" lazy-init="true" scope="request">
		<property name="cocPhotoDAO">
			<ref bean="cocPhotoDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="carColorService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CarColorServiceImpl" lazy-init="true" scope="request">
		<property name="carColorDAO">
			<ref bean="carColorDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	
	<bean id="scjcNeutralService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.ScjcNeutralServiceImpl" lazy-init="true" scope="request">
		<property name="scjcNeutralDAO">
			<ref bean="scjcNeutralDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="lqzNeutralService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.LqzNeutralServiceImpl" lazy-init="true" scope="request">
		<property name="lqzNeutralDAO">
			<ref bean="lqzNeutralDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="gasPrinterLogService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.GASPrinterLogServiceImpl" lazy-init="true" scope="request">
		<property name="gasPrinterLogDAO">
			<ref bean="gasPrinterLogDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="cocModelTypeService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.COCModelTypeServiceImpl" lazy-init="true" scope="request">
		<property name="cocModelTypeDAO">
			<ref bean="cocModelTypeDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="yearCodeService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.YearCodeServiceImpl" lazy-init="true" scope="request">
		<property name="yearCodeDAO">
			<ref bean="yearCodeDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	<bean id="hwCOCYearSeqService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.HWCOCYearSeqServiceImpl" lazy-init="true" scope="request">
		<property name="hwCocYearSeqDAO">
			<ref bean="hwCOCYearSeqDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>
	
	
	<bean id="typicalityNeutralService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.TypicalityNeutralServiceImpl" lazy-init="true" scope="request">
		<property name="typicalityNeutralDAO">
			<ref bean="typicalityNeutralDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	
	<bean id="typicalityNeutralPhotoService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.TypicalityNeutralPhotoServiceImpl" lazy-init="true" scope="request">
		<property name="typicalityNeutralPhotoDAO">
			<ref bean="typicalityNeutralPhotoDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	<!-- fuelupload start-->
	<bean id="fuelUploadService" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.FuelUploadServiceImpl" lazy-init="true" scope="request">
		<property name="fuelUploadDAO">
			<ref bean="fuelUploadDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>
	</bean>	
	<bean id="uploadUserService" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.UploadUserServiceImpl" lazy-init="true" scope="request">
		<property name="uploadUserDAO">
			<ref bean="uploadUserDAO" />
		</property>
	</bean>	
	<bean id="holidayService" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.HolidayServiceImpl" lazy-init="true" scope="request">
		<property name="holidayDAO">
			<ref bean="holidayDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>		
	</bean>	
	<bean id="filterModelService" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.FilterModelServiceImpl" lazy-init="true" scope="request">
		<property name="filterModelDAO">
			<ref bean="filterModelDAO" />
		</property>
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>		
	</bean>	
	<bean id="uploadStateManagerService" class="com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.UploadStateManagerServiceImpl" lazy-init="true" scope="request">
		<property name="carInfoDAO">
			<ref bean="carInfoDAO" />
		</property>		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>		
	</bean>	
	<bean id="carPublicModelIconService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CarPublicModelIconServiceImpl" lazy-init="true" scope="request">
		<property name="carPublicModelIconDAO">
			<ref bean="carPublicModelIconDAO" />
		</property>		
		<property name="paginationService">
			<ref bean="paginationService" />
		</property>		
		<property name="publicNoticeCarModelService">
			<ref bean="publicNoticeCarModelService" />
		</property>		
	</bean>	
	<!-- fuelupload end -->	
	
	<bean id="certificateSupplementInfoService" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.service.CertificateSupplementInfoServiceImpl" lazy-init="true" scope="request">
		<property name="certificateSupplementInfoDAO">
			<ref bean="certificateSupplementInfoDAO" />
		</property>
	</bean>	
	<!-- 
	<bean id="interfaceDataProcessService" class="com.dawnpro.dfpv.carfilemanager.module.business.share.service.impl.InterfaceDataProcessServiceImpl" lazy-init="true" scope="request">
		<property name="interfaceLogDAO">
			<ref bean="interfaceLogDAO"/>
		</property>
		<property name="certificateService">
			<ref bean="certificateService"/>
		</property>
		
		<property name="cocPhotoService">
			<ref bean="cocPhotoService"/>
		</property>
	</bean>
	 -->
</beans>
