<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
	body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
	p {font-family:"宋体";font-size: 10pt;font-weight:bold;}
	input.text { width:12; padding: .4em; }
	input.text1 { width:12; padding: .4em; background:#bcd4ec;}
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align: center; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/jquery-cookie.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	$("#dialog").dialog({
		autoOpen: false,
		bgiframe: false,
		height: 400,
		weight:40,
		modal: false
	});
	
	var printers = window.document.gas.allPrintService(); 

	$('#vin').bind('keyup',function(event) {  
     	if(event.keyCode==13){
     		
     		var gas_qy_printer = $.cookie("gas_qy_printer");
			var gas_fcdhh_printer = $.cookie("gas_fcdhh_printer");
			var gas_cdhh_printer = $.cookie("gas_cdhh_printer");
			var gas_cdd_printer = $.cookie("gas_cdd_printer");
			var messageObj = $('#message_dialog');
   	   		
			if(gas_qy_printer==null){
				$("#print_info_div").hide();
				messageObj.find('#message').text('警告:请设置燃料标识打印机-汽油！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}
			
			if(gas_fcdhh_printer==null){
				$("#print_info_div").hide();
				messageObj.find('#message').text('警告:请设置燃料标识打印机-非插电式混合动力！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}
			
			if(gas_cdhh_printer==null){
				$("#print_info_div").hide();
				messageObj.find('#message').text('警告:请设置燃料标识打印机-插电式混合动力！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}			
			if(gas_cdd_printer==null){
				$("#print_info_div").hide();
				messageObj.find('#message').text('警告:请设置燃料标识打印机-纯电动！');
	   	   		messageObj.dialog('open');
	   	   		
				return ;
			}
     		
			vprint(messageObj); 
   		}   

    });   
    
	$("#search").click(function() {
		var gas_qy_printer = $.cookie("gas_qy_printer");
		var gas_fcdhh_printer = $.cookie("gas_fcdhh_printer");
		var gas_cdhh_printer = $.cookie("gas_cdhh_printer");
		var gas_cdd_printer = $.cookie("gas_cdd_printer");
		var messageObj = $('#message_dialog');
	   		
		if(gas_qy_printer==null){
			$("#print_info_div").hide();
			messageObj.find('#message').text('警告:请设置燃料标识打印机-汽油！');
   	   		messageObj.dialog('open');
   	   		
			return ;
		}
		
		if(gas_fcdhh_printer==null){
			$("#print_info_div").hide();
			messageObj.find('#message').text('警告:请设置燃料标识打印机-非插电式混合动力！');
   	   		messageObj.dialog('open');
   	   		
			return ;
		}
		
		if(gas_cdhh_printer==null){
			$("#print_info_div").hide();
			messageObj.find('#message').text('警告:请设置燃料标识打印机-插电式混合动力！');
   	   		messageObj.dialog('open');
   	   		
			return ;
		}			
		if(gas_cdd_printer==null){
			$("#print_info_div").hide();
			messageObj.find('#message').text('警告:请设置燃料标识打印机-纯电动！');
   	   		messageObj.dialog('open');
   	   		
			return ;
		}
		
		vprint(messageObj);
		});
	
	
	function vprint(messageObj){
		window.document.gas.printGAS($("#vin")[0].value);  
	 }
	
	$("#printer").click(function() {
		$("#print_info_div").hide();
		var printerList = printers.split("@");
		var gasQYPrinterObl = $("#gas_qy_printer");
		var gasFCDHHPrinterObl = $("#gas_fcdhh_printer");
		var gasCDHHPrinterObl = $("#gas_cdhh_printer");
		var gasCDDPrinterObl = $("#gas_cdd_printer");
		if($(gasQYPrinterObl).find("option").length==0){
			$(gasQYPrinterObl).append("<option value=''>请选择</option>");   
			$(gasFCDHHPrinterObl).append("<option value=''>请选择</option>"); 
			$(gasCDHHPrinterObl).append("<option value=''>请选择</option>");
			$(gasCDDPrinterObl).append("<option value=''>请选择</option>");
			for(var i=0;i<printerList.length;i++){
				$(gasQYPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");   
				$(gasFCDHHPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");  
				$(gasCDHHPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");
				$(gasCDDPrinterObl).append("<option value='"+printerList[i]+"'>"+printerList[i]+"</option>");
			}
		}
		
		var gas_qy_printer = $.cookie("gas_qy_printer");
		var gas_fcdhh_printer = $.cookie("gas_fcdhh_printer");
		var gas_cdhh_printer = $.cookie("gas_cdhh_printer");
		var gas_cdd_printer = $.cookie("gas_cdd_printer");
		if(gas_qy_printer!=null){
			$(gasQYPrinterObl).val(gas_qy_printer);	
		}
		if(gas_fcdhh_printer!=null){
			$(gasFCDHHPrinterObl).val(gas_fcdhh_printer);	
		}
		if(gas_cdhh_printer!=null){
			$(gasCDHHPrinterObl).val(gas_cdhh_printer);	
		}		
		if(gas_cdd_printer!=null){
			$(gasCDDPrinterObl).val(gas_cdd_printer);	
		}		
		
		$("#printer_setting_dialog").dialog('open');	
	});
	
	$("#printer_setting_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 650,
		height:340,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var gas_qy = $("#gas_qy_printer").val();
				var gas_fcdhh = $("#gas_fcdhh_printer").val();
				var gas_cdhh = $("#gas_cdhh_printer").val();
				var gas_cdd = $("#gas_cdd_printer").val();
				if(gas_qy==""){
					updateTips($(this).find('#validateTips'),'请选择燃料标识打印机-汽油！');		
					
					return ;
				}
				if(gas_fcdhh==""){
					updateTips($(this).find('#validateTips'),'请选择燃料标识打印机-非插电式混合动力！');		
					
					return ;
				}
				if(gas_cdhh==""){
					updateTips($(this).find('#validateTips'),'请选择燃料标识打印机-插电式混合动力！');		
					
					return ;
				}				
				if(gas_cdd==""){
					updateTips($(this).find('#validateTips'),'请选择燃料标识打印机-纯电动！');		
					
					return ;
				}				
            
				$.cookie("gas_qy_printer",$("#gas_qy_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				$.cookie("gas_fcdhh_printer",$("#gas_fcdhh_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				$.cookie("gas_cdhh_printer",$("#gas_cdhh_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				$.cookie("gas_cdd_printer",$("#gas_cdd_printer").val(),{expires: 180,path:'/CarFileManager/business/'});
				
				gasPrinterLog("gas_qy_printer",$("#gas_qy_printer").val());
				gasPrinterLog("gas_fcdhh_printer",$("#gas_fcdhh_printer").val());
				gasPrinterLog("gas_cdhh_printer",$("#gas_cdhh_printer").val());
				gasPrinterLog("gas_cdd_printer",$("#gas_cdd_printer").val());
				
				$(this).dialog('close');
			}
		},
		close: function() {
			$("#print_info_div").show();
			$("#gas_qy_printer").val("请选择");
			$("#gas_fcdhh_printer").val("请选择");
			$("#gas_cdhh_printer").val("请选择");
			$("#gas_cdd_printer").val("请选择");
		}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {},
		close: function() {
			$("#print_info_div").show();
		}
	});
	
				
}
);

function vinFocus(){
	$("#vin")[0].focus(); 
	$("#vin").addClass("text ui-widget-content ui-corner-all");
}

function showInfo(enterprise,model,engineModel,fuelType,range,ratingPower){
	$("#enterprise")[0].value = enterprise;
	$("#model")[0].value = model;
	$("#engineModel")[0].value = engineModel;
	$("#fuelType")[0].value = fuelType;
	$("#range")[0].value = range;
	$("#ratingPower")[0].value = ratingPower;

	$("#vin")[0].select();
}

function readPrinterCookie(){
	var cookie = new Array(8);
	cookie[0] = $.cookie("cocprinter");
	cookie[1] = $.cookie("photoprinter");
	cookie[2] = $.cookie("hbprinter");
	cookie[3] = $.cookie("hbviprinter");
	cookie[4] = $.cookie("gas_qy_printer");
	cookie[5] = $.cookie("gas_fcdhh_printer");
	cookie[6] = $.cookie("gas_cdhh_printer");
	cookie[7] = $.cookie("gas_cdd_printer");
	
	return cookie;
}

function gasPrinterLog(printpoint,pinrter){
	jQuery.ajax({
        url: 'business/cocModelTypeAction!addGASPrinterLog.action',	
        //model 打印点名称     modelType 打印机
        data: {'model' : printpoint,'modelType' : pinrter}, 
        type: 'POST',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {
           
        }
    });

	return false;
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<!-- 
<p>VIN:<input type="text" id="vin" class="text ui-widget-content ui-corner-all"/>&nbsp;&nbsp;<button id="create-user" class="ui-button ui-state-default ui-corner-all">查询</button></p>
 -->
<div align="center"> 
	<jsp:plugin name="gas" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarCertificatePrintApplet.class" codebase="." archive = "../../applet/com.dawnpro.dfpv.carfilemanager.print.applet.jar,../../applet/jasperreports-3.6.1.jar,../../applet/commons-logging-1.1.1.jar,../../applet/commons-collections-3.2.jar,../../applet/commons-digester-1.7.jar,../../applet/Qrcode_encoder.jar" 
	iepluginurl="http://********/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="120" width="800">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="GAS"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>

<div align="center">
<table width="750" height="63" border="0">
	<tr>
		<td width="200" height="19" align="left"  style="font-family:arial;font-size: 24pt; ">VIN:</td>
		<td colspan="3" align="left">
			<table cellpadding="0" border="0" cellspacing="0">
				<tr>
					<td><input id="vin" type="text" width="100" class="text ui-widget-content ui-corner-all"  style="font-family:arial;font-size: 24pt; "/></td>
					<td>&nbsp;&nbsp;<button id="search" class="ui-button ui-state-default ui-corner-all">查询</button></td>
					<td>&nbsp;&nbsp;<button id="printer" class="ui-button ui-state-default ui-corner-all">打印机设置</button></td>
				</tr>
			</table>
		</td>
	</tr>
	<tr>
		<td align="left" style="font-family:arial;font-size: 24pt; ">车辆型号:</td>
		<td colspan="3" width="157"><input id="model" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; " readOnly/></td>
	
	</tr>
	<tr>
		<td height="18" align="left" style="font-family:arial;font-size: 24pt; ">发动机型号:</td>
		<td colspan="3" width="130"><input id="engineModel" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; " readOnly/></td>
	</tr>
	<tr>
		<td align="left" style="font-family:arial;font-size: 24pt; ">生产企业:</td>
		<td align="left"><input id="enterprise" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; width:200px;" readOnly></td>
		<td align="left" width="170" style="font-family:arial;font-size: 24pt; ">燃料类型:</td>
		<td align="left"><input id="fuelType" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; width:100px;" readOnly/></td>
		
	</tr>
	<tr>
		<td height="18" align="left" style="font-family:arial;font-size: 24pt; ">排量:</td>
		<td align="left"><input id="range" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; width:200px;" readOnly></td>
		<td align="left" style="font-family:arial;font-size: 24pt; ">额定功率:</td>
		<td align="left" width="157"><input id="ratingPower" type="text" width="100" class="text1 ui-widget-content ui-corner-all" style="font-family:arial;font-size: 24pt; width:100px;" readOnly></td>
		
	</tr>
</table>
</div>


<div id="printer_setting_dialog" style="display:none" title="打印机设置">
	<p id="validateTips"></p>
	<fieldset>
		<table id="one" width="100%" border="0">
	  		<tr>
	  			<td><label>燃料标识打印机-汽油</label></td>
				<td><select id="gas_qy_printer"></select></td>
	  		</tr>
	  		<tr>
	  			<td><label>燃料标识打印机-非插电式混合动力</label></td>
				<td><select id="gas_fcdhh_printer"></select></td>
	  		</tr>
	  		<tr>
	  			<td><label>燃料标识打印机-插电式混合动力</label></td>
				<td><select id="gas_cdhh_printer"></select></td>
	  		</tr>	  		
	  		<tr>
	  			<td><label>燃料标识打印机-纯电动</label></td>
				<td><select id="gas_cdd_printer"></select></td>
	  		</tr>	  		
		</Table>
	</fieldset>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>