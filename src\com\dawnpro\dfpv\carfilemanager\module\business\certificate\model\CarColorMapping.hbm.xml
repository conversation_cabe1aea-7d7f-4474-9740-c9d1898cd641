<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarColorMapping" table="CAR_COLOR_MAPPING" >
        <id name="code" type="java.lang.String">
            <column name="CODE" length="4" />
            <generator class="assigned" />
        </id>
        <property name="color" type="java.lang.String">
            <column name="COLOR" length="20" />
        </property>
        <property name="basecolor" type="java.lang.String">
            <column name="BASECOLOR" length="4" />
        </property>
        <property name="encolor" type="java.lang.String">
            <column name="ENCOLOR" length="20" />
        </property>
    </class>
</hibernate-mapping>
