<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link href="js/jquery/uploadify/css/uploadify.css" rel="stylesheet" type="text/css" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .4em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript">	
$(document).ready(function() { 
	$("#uploadFile").uploadify({
		'uploader'       : 'js/jquery/uploadify/uploadify.swf',
		'script'         : 'business/cocVer!importData.action',
		'cancelImg'      : 'js/jquery/uploadify/images/cancel.png',
		'fileDataName'	 : 'uploadFile',
		'queueID'        : 'fileQueue',
		'simUploadLimit' : 1,
		'queueSizeLimit' : 1,
		'auto'           : false,
		'multi'          : false,
		'fileDesc' 		 : 'xls' ,
		'fileExt'        : '*.xls',
		'onComplete'     : function(event,queueId,fileObj,response,data){
			alert(eval("("+response+")").json);
			self.opener.location.reload();
			parent.window.close();   
		}
	});
});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div id="fileQueue">
	<input type="file" name="uploadFile" id="uploadFile" />
</div>
<a href="javascript:$('#uploadFile').uploadifyUpload();">上传</>&nbsp;&nbsp;&nbsp;<a href="javascript:$('#uploadFile').uploadifyClearQueue();">取消</>
</body>
</html>