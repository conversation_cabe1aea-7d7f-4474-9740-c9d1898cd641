package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysLogDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysLog;

public class SysLogServiceImpl implements SysLogService{
	private Logger logger = Logger.getLogger(SysLogServiceImpl.class.getName());
	private SysLogDAO dao;
	private PaginationService paginationService = null;
	
	public void setDao(SysLogDAO dao) {
		this.dao = dao;
	}
	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	public void addLog(SysLog log) throws DataAccessException {
		try{
			this.dao.addLog(log);
		}catch(DataAccessException e){
			logger.error("addLog Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("addLog Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}
	public List<?> pagination(Page page,Object[] params) throws DataAccessException {
		List results = null;
		try{
			String sub = "";
			String countSql = null;
			String sql = null;
			
			if(params==null){
				countSql = "select count(g.id) from SysLog g order by g.depdatetime desc";
				sql = "from SysLog g order by g.depdatetime desc";
			}else{
				if(params[0]!=null&&!params[0].equals("")){
					sub = " g.depdatetime > '"+String.valueOf(params[0])+"' and ";
				}
				if(params[1]!=null&&!params[1].equals("")){
					sub += " g.depdatetime < '"+params[1]+"' and ";
				}
				if(params[2]!=null&&!params[2].equals("")){
					sub += " g.loginname like '%"+params[2]+"%' and ";
				}
				if(params[3]!=null&&!params[3].equals("")){
					sub += " g.descript like '%"+params[3]+"%' and ";
				}
				sub = sub.substring(0,sub.lastIndexOf("and"));
				
				countSql = "select count(g.id) from SysLog g where "+sub+"order by g.depdatetime desc";
				sql = "from SysLog g where "+sub+" order by g.depdatetime desc";
			}
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum(countSql);
			results = this.paginationService.pagination(sql);
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}
}
