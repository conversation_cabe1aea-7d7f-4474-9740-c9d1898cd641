package com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarInfo;

public interface CarInfoDAO {

	public void addCarModel(CarInfo obj);
	
	public void updateCarModel(CarInfo obj);
	
	public void deleteCarModel(CarInfo obj);
	
	public void deleteCarModels(CarInfo[] obj);
	
	public CarInfo loadCarModelObj(String vin);
	
	public List<CarInfo> findCarModel(String sql);
	
	public List<CarInfo> findCarModel(String sql,Object[] params);
	
	public int updateCarModel(String sql,Object[] value);
}
