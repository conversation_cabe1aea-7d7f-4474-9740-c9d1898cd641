package com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.dao.mapper.RowMapper;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.COCReportBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuelScjcUploadBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProeHBBean;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Proenvironment;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.ProenvironmentId;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.CocVerDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.ProenvDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysMenu;

public class ProenvDAOImpl extends GenericHibernateDAOImpl<Proenvironment> 
	implements ProenvDAO {

	public void addCocVer(Proenvironment obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addHBVer Method Error:",e);
		}
	}

	public void deleteCocVer(Proenvironment[] id) {
		try{
			this.deleteBatch(id);
		}catch(Exception e){
			throw new DataAccessException("deleteHBVer Method Error:",e);
		}
	}

	public Proenvironment loadHBVer(ProenvironmentId id) {
		Proenvironment obj = null;
		try{
			obj = (Proenvironment)this.load(Proenvironment.class,id);
			if(obj != null)
			{
				obj.getJybgno();
			}
		}catch(Exception e){
			throw new DataAccessException("loadHBVer Method Error:",e);
		}
		return obj;
	}

	public void updateCocVer(Proenvironment obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateHBVer Method Error:",e);
		}
	}

	public Proenvironment findMaxCocVerBySccx(String c1) {
		Proenvironment obj = null;
		try{
			List<Proenvironment> results = null;
			String sql = "from Proenvironment t where t.id.slcx=? order by substr(t.id.vercode,length(t.id.vercode)-1,2) desc";
			Object[] params = new Object[]{c1};
			results = this.find(sql,params);
			if(results != null && results.size()>0)
				obj = results.get(0);
		}catch(Exception e){
			throw new DataAccessException("findMaxHBVerBySccx Method Error:",e);
		}
		return obj;
	}

	public List<Proenvironment> findCocVer(String hql) {
		List<Proenvironment> list = null;
		try{
			list = this.find(hql);
		}catch(Exception e){
			throw new DataAccessException("findHBVer Method Error:",e);
		}
		return list;
	}

	public void addCocVer(Proenvironment[] obj) {
		if(obj == null ) return;
		try{
			this.addBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("addHBVer Method Error:",e);
		}
	}

	public void updateCocVer(Proenvironment[] obj) {
		if(obj == null ) return;
		try{
			this.updateBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("updateHBVer Method Error:",e);
		}
	}

	public ProeHBBean findHBCertificate(String sql, Object[] params) {
		ProeHBBean hb = null;
		try{
			List results = this.find(sql, params);
			if(results!=null&&results.size()>0){
				hb = (ProeHBBean)results.get(0);
				hb.setResult(true);
			}else{
				hb = new ProeHBBean();
				hb.setResult(false);
			}
		}catch(Exception e){
			throw new DataAccessException("findHBCertificate Method Error:",e);
		}
		
		return hb;
	}
	
	public FuelScjcUploadBean findScjcCertificate(String sql, Object[] params) {
		FuelScjcUploadBean hb = null;
		try{
			List results = this.find(sql, params);
			if(results!=null&&results.size()>0){
				hb = (FuelScjcUploadBean)results.get(0);
				hb.setResult(true);
			}else{
				hb = new FuelScjcUploadBean();
				hb.setResult(false);
			}
		}catch(Exception e){
			throw new DataAccessException("findScjcCertificate Method Error:",e);
		}
		
		return hb;
	}
	
	

}
