<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CertificateSupplementInfo" table="CERTIFICATE_SUPPLEMENT_INFO" >
       <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CertificateSupplementInfoId">
            <key-property name="vin" type="java.lang.String">
                <column name="VIN" length="20" />
            </key-property>
            <key-property name="printtime" type="java.lang.String">
                <column name="PRINT_TIME" length="19" />
            </key-property>
        </composite-id>
        <property name="cocnum" type="java.lang.String">
            <column name="COC_NUM" length="20" />
        </property>
        <property name="type" type="java.lang.String">
            <column name="TYPE" length="10" />
        </property>
        <property name="reason" type="java.lang.String">
            <column name="REASON" length="20" />
        </property>
         <property name="description" type="java.lang.String">
            <column name="DESCRIPTION" length="100" />
        </property>
    </class>
</hibernate-mapping>
