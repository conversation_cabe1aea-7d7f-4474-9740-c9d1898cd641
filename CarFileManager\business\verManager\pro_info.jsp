<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.dawnpro.dfpv.carfilemanager.base.action.*" %>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String imgpath = Action.PHOTO_MODEL_HB.substring(1,Action.PHOTO_MODEL_HB.length());
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
    
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="<%=basePath%>"/>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; /**position: relative;**/ text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
#preview{
	position:absolute;
	border:1px solid #ccc;
	background:#333;
	padding:5px;
	display:none;
	color:#fff;
	width:470px;
	height:310px;
	z-index: 3000;
	
}
#imgview{
width:400px;
height:300px;
}	
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	


$(function() {
	//设置图片位置
	//$("#preview").css({'top':'380px'});
	
	
	var allFields = null;
	var type = null;
	var wizardModel = "two";
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);
	$("#print").attr("disabled", true);
	//$("#compare").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				/*if(operatePerrmission[i].flag.indexOf("compare")!=-1){
					$("#compare").attr("disabled", false);
				}*/
				if(operatePerrmission[i].flag.indexOf("print")!=-1){
					$("#print").attr("disabled", false);
					isprint = true;
				}
            }
        }
    });
	
	$("#query").click(function(){
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		if(qc1==""&&qstate==""&&qfactory==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			location.href="proenv.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&menuid="+menuid;
		}
	});
		
	$("#compare").click(function(){
       	var dialogObj = $('#coc_compare_dialog');     	  	
       	dialogObj.data('title.dialog','Proenv版本比较').dialog('open');      	
	});
	
	$("#compare1").click(function(){
		if($('#cocVer1').val()=='' || $('#cocVer2').val()==''){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择比较的版本！');
   	   		messageObj.dialog('open');
   	   		return;
		}
		jQuery.ajax({
	        url: 'business/proenv!findCompareCocVer.action',		           
	        data: {'cocVer1' : $('#cocVer1').val(), 'cocVer2' : $('#cocVer2').val()}, 
	     	type: 'POST',
	        beforeSend: function() {
	        },
	        error: function(request) {
	            alert("系统错误，请与管理员联系！");
	        },
	        success: function(data) {
	        	clearCompareDialog('2');
	         	var content = json2Bean(data).json;
	         	var carObj = eval("("+content.toString()+")"); 
	         	var dialogObj = $('#coc_compare_dialog');
	         	
	         
	         	
		       	dialogObj.find("#tbl1").find("tr").each(function(i){
		       		var item = $(this).attr("name");
		       		//console.log(carObj[0][item]+"=="+$.trim(carObj[1][item]));
		       		if($.trim(carObj[0][item])!=$.trim(carObj[1][item])){//不同时
		       			$(this).attr("style","background-color: yellow;");
		       		}
		 			$(this).find("td:eq(1)").text(carObj[0][item]);      		
		 			$(this).find("td:eq(2)").text(carObj[1][item]); 
		 			
		 			
		 			if(i==0){
		 				var bslcx0= carObj[0].id.slcx;
			        	var bslcx1 = carObj[1].id.slcx;
			        	if($.trim(bslcx0)!=$.trim(bslcx1)){//不同时
			       			$(this).attr("style","background-color: yellow;");
			       		}
			        	$(this).find("td:eq(1)").text(bslcx0);      		
		 				$(this).find("td:eq(2)").text(bslcx1); 
		 			}
		 			if(i==1){
			       	 	var bvercode0 = carObj[0].id.vercode;
			        	var bvercode1 = carObj[1].id.vercode;
			        	if($.trim(bvercode0)!=$.trim(bvercode1)){//不同时
			       			$(this).attr("style","background-color: yellow;");
			       		}
			        	$(this).find("td:eq(1)").text(bvercode0);      		
		 				$(this).find("td:eq(2)").text(bvercode1); 
		       		}
		 			
		       	}); 
		       	
	        }
	    });       	  	       	
       	
	});	
	
	
	
	function clearCompareDialog(flg){
		if(flg=='1'){
		$("#cocVer2").empty();
		$("<option value=''>请选择...</option>").appendTo("#cocVer2");	
		$("#cocVer1").get(0).selectedIndex=0;	
		}else if(flg=='2'){
        var dialogObj = $('#coc_compare_dialog');        
	       	dialogObj.find("#tbl1").find("tr").each(function(i){
	 			$(this).find("td:eq(1)").text('');      		
	 			$(this).find("td:eq(2)").text(''); 
	 			$(this).attr("style","background-color: #f1f9f3;"); 
	       	});	
       	}	
	}
	$("#coc_compare_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 1100,
		height: 555,
		modal: true,
		buttons: {
			'关闭': function() {	
				$(this).dialog('close');
			}
		},
		close: function() {
			clearCompareDialog('1');			
			clearCompareDialog('2');			
		}				
	});
	 
 
	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK']:checked"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
      	
      	$("#rllx").val(1).attr("SELECTED","SELECTED");
    	if(index==1){
        	var params = "slcx="+id;
        	jQuery.ajax({
	            url: 'business/proenv!HBVerInfo.action',		           
	            data: params,
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {	       
	            	var content = data.json;
		            var model = eval("("+content.toString()+")");    
		          	var dialogObj = $('#public_coc_dialog');
		          	dialogObj.parent().find("button").each(function() {
				        if( $(this).text() == '保存' || $(this).text() == '上传' || $(this).text() == '导入') {
				            $(this).css("display","block");
				        }
	    			});	    
	            	type = "add";
					//setDialogValue(dialogObj,model);
					dialogObj.find('#vercode').attr('readonly',true);
					dialogObj.find('#c6').attr('readonly',true);
					dialogObj.find('#jzzl').attr('readonly',true);
					dialogObj.dialog('option','title', '新增PROENV信息').dialog('open');
					dialogObj.find('#vercode').val('');
					getMaxVercode();
					dialogObj.find('#jzzl').val('');
					//getMaxZbzl(); setDialogValue在后面会覆盖jzzl的值，如果生产车型改变，修改后回车可带出新车型的基准质量的值，此处可以不调用 20191129
					createrllxchange();
					setDialogValue(dialogObj,model);
					
	            }
	        });
   			
   	    	
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能参考一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
   	  		type = "add";
   	  		var dialogObj = $('#public_coc_dialog');
   	  	    dialogObj.parent().find("button").each(function() {
	          if( $(this).text() == '保存' || $(this).text() == '上传' || $(this).text() == '导入') {
	              $(this).css("display","block");
	           }
		    });	     
   	  		dialogObj.find('#vercode').attr('readonly',true);
   	  		dialogObj.find('#c6').attr('readonly',true);
   	  		dialogObj.find('#rllx').attr("disabled",false);
   	  	    dialogObj.find('#pfbz').attr("disabled",false);
   	  	    dialogObj.find('#jzzl').attr('readonly',true);
   	  		dialogObj.dialog('option','title', '新增PROENV数据').dialog('open');
   	  	    createrllxchange();
   	   	 }
		
	});

	$("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK']:checked"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   			type = "update";
   			var params = "slcx="+id;
   			jQuery.ajax({
	            url: 'business/proenv!HBVerInfo.action',		           
	            data: params, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var content = data.json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#public_coc_dialog');
					dialogObj.parent().find("button").each(function() {
				        if( $(this).text() == '保存' || $(this).text() == '上传' || $(this).text() == '导入') {
				            $(this).css("display","block");
				        }
	    			});	            	
					if(carObj.state == null || carObj.state=="0" || carObj.state=="" ){
						//setDialogValue(dialogObj,carObj);

		       	    	dialogObj.find('#slcx').attr('readonly',true);
		       	    	dialogObj.find('#vercode').attr('readonly',true);
		       	    	dialogObj.find('#c6').attr('readonly',true);
		       	    	dialogObj.find('#imp').attr('disabled',true);
		       	    	dialogObj.find('#jzzl').attr('readonly',true);
		       	    	
		       	    	dialogObj.find('#rllx').attr("disabled",false);
		       	    	dialogObj.find('#pfbz').attr("disabled",false);
		       	    	dialogObj.dialog('option','title', '修改PROENV信息').dialog('open');
		       	    	//getMaxZbzl(); setDialogValue在后面会覆盖jzzl的值，如果生产车型改变，修改后回车可带出新车型的基准质量的值，此处可以不调用 20191129
		       	    	createrllxchange();
						setDialogValue(dialogObj,carObj);
						
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});
	
	$("#delete").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var state = "";
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK']:checked"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				state=$(this).parent().find("#state").val();
         		if(state=="1" || state=="9")	{
         			effIndex++;	
         			if(effId==""){
         				effId = this.value;
         			}else{
         				effId = effId + "&" + this.value;
         			}
         		}
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});
      	if(effIndex>0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('提示:数据['+effId+'] 共'+effIndex+'条已生效或是历史状态，不能删除！');
   	   		messageObj.dialog('open');   	   			
	   	}else if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
   			type = "delete";
   	   		messageObj = $('#operate_dialog');
   	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
   	   		messageObj.dialog('open');
   	   		messageObj.find('#c1').val(id);
   	   	}
	
	});
	

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		var params = "slcx="+id;
		jQuery.ajax({
            url: 'business/proenv!HBVerInfo.action',		           
            data: params, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content =  json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	        	var dialogObj = $('#public_coc_dialog');

	        	type = "look";
				setDialogValue(dialogObj,carObj);
				$('input').attr("readonly", true);
				dialogObj.parent().find("button").each(function() {
			        if( $(this).text() == '保存' || $(this).text() == '上传' || $(this).text() == '导入') {
			            $(this).css("display","none");
			        }
    			});
					
				dialogObj.dialog('option','title', '查看PROENV信息').dialog('open');
				
	        }
	    });

		return false;
	}
	
	
	
	$("#public_notice_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 520,modal: true,
		buttons: {
	
			'取消': function() {
				$(this).dialog('close');
				$("#rllx").attr("disabled",false);
				$("#pfbz").attr("disabled",false);
				if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
				}
				$('#public_notice_dialog').find('#slcx').attr('readonly',false);
				wizardModel = "one";
			}
			//,
			//'下一页': function() {
			//	wizardToDisplay();
			//}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
			$('#public_notice_dialog').find('#slcx').attr('readonly',false);

			wizardModel = "one";
			$("#rllx").attr("disabled",false);
			$("#pfbz").attr("disabled",false);
			//wizardToDisplay();
		}
	});
	
	
	$('#public_coc_dialog').find('#slcx').bind('keyup',function(event) { 
		if(type=="add")
			getMaxVercode();
		    getMaxZbzl();
    });
	
    function getMaxVercode()
    {
    	
    	var obj = $('#public_coc_dialog').find('#slcx');
		if(obj.val().gblen()==18){
			jQuery.ajax({
	            url: 'business/proenv!getMaxCocVercode.action',		           
	            data: {"slcx":obj.val()},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            		       
	            	var content = data.json;
		          	var dialogObj = $('#public_coc_dialog');
					dialogObj.find('#vercode').val(content);
	            }
	        });
		}  
    }
   
    function getMaxZbzl(){
    	var obj = $('#public_coc_dialog').find('#slcx');
		if(obj.val().gblen()==18){
			jQuery.ajax({
	            url: 'business/gasVer!getMaxZbzl.action',		           
	            data: {"slcx":obj.val()},
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            		       
	            	var content = data.json;
		          	var dialogObj = $('#public_coc_dialog');
					dialogObj.find('#jzzl').val(content);
	            }
	        });
		}  
    }
    
    $('#public_coc_dialog').find('#tempc6').bind('keyup',function(event) { 
        var value = $('#public_coc_dialog').find('#tempc6').val();
    	$('#public_coc_dialog').find('#c6').val(value);
    });
    $('#public_coc_dialog').find('#tempc6').bind('blur',function(event) { 
        var value = $('#public_coc_dialog').find('#tempc6').val();
    	$('#public_coc_dialog').find('#c6').val(value);
    });
	
    $("#pubilc_notice_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 460,
		height:280,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var gk = $('#pubilc_notice_dialog').find('#gkxxList').val();
				if(gk!=''){
					var arr=gk.split(',');
					//alert(arr[0]);
					if(arr.length==3){
						$('#public_coc_dialog').find("#c88").val(arr[0]);
						$('#public_coc_dialog').find("#c89").val(arr[1]);
						$('#public_coc_dialog').find("#c90").val(arr[2]);
					}						
				}			
				if($('#pubilc_notice_dialog').find('#pmodelList option').length>0){
					var value = $(this).find('#pmodelList').val().split(',');
					impPubData(value[0],value[1]);
				}
				
				
				$(this).dialog('close');
			}
		}
	});
	



	$('#export').click(function() {
		var qc1 = $('#qc1').val();
		var qstate = $('#qstate').val();
		var qfactory = $('#qfactory').val();
		
		location.href="proenv!exportData.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory;  
	});

	$("#effect").click(function() {
		var id = "";
		var info = "";
		var tmp = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK']:checked"); 
      
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				tmp = this.value.split(',');
				if(id==""){
					id = this.value;
					info = "生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+"生产车型:"+tmp[0]+" 版本号:"+tmp[1];
				}
             }
      	});

   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要生效的数据！');
   	   		messageObj.dialog('open');
   	   	}else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能生效一条数据！');
	   		messageObj.dialog('open');
   	   	 }else{
     		var params = "slcx="+id;
     		
    		jQuery.ajax({
                url: 'business/proenv!HBVerInfo.action',		           
                data: params, 
    	        type: 'POST',
    	        beforeSend: function() {
    	        
    	        },
    	        error: function(request) {
    	            
    	        },
    	        success: function(data) {
    	            var content =  json2Bean(data).json;
    	            var carObj = eval("("+content.toString()+")");
    	            if(carObj.state==null||carObj.state==""||carObj.state=="0"){
    	            	var dialogObj = $('#public_coc_dialog');
    	            	dialogObj.parent().find("button").each(function() {
    				        if( $(this).text() == '保存' || $(this).text() == '上传' || $(this).text() == '导入') {
    				            $(this).css("display","block");
    				        }
    	    			});	    
        	        	type = "effect";
        				//setDialogValue(dialogObj,carObj);
        				dialogObj.find('#imp').css('display','none');
        				dialogObj.find('#tempc6').attr('size','20');
        				$('input').attr("readonly", true);
        				$('.ui-dialog-buttonpane button[value="打印"]').css("display","none");
        				$('.ui-dialog-buttonpane button').eq(3).attr('value','生效');
        				dialogObj.data('title.dialog', '生效PROENV信息').dialog('open');
        				createrllxchange();
						setDialogValue(dialogObj,carObj);
					}else{
						messageObj = $('#message_dialog');
			   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
			   	   		messageObj.dialog('open');
					}
    				
    	        }
    	    });
         	
   	   	}
	});
	
	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);

				var dlgButton = $('.ui-dialog-buttonpane button');	

				var qc1 = $('#qc1').val();
				var qstate = $('#qstate').val();
				var qfactory = $('#qfactory').val();
				var currentPage=$('#currentPage_temp').val();
				
				if(type=="delete"){	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');			
					formObj[0].action = "business/proenv!deleteHB.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage;
					formObj[0].submit();
				}else if(type=="effect"){
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
	            	formObj[0].action = "business/proenv!effectHB.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage;
					formObj[0].submit();
							
				}else if(type=="imp"){
					$(this).dialog('close');
				}
			}
		}
	});
	
	$("#public_coc_dialog").dialog({bgiframe: true,autoOpen: false,width: 980,height: 505,modal: true,
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'保存': function() {
				addOrUpdate(this);
			}
			//,
			//'打印': function() {
			//	var c1 = $('#public_coc_dialog').find('#c1').val();
	   	   	//	var vercode = $('#public_coc_dialog').find('#vercode').val();
	   			//window.document.coc.printCocVersion(c1,vercode);
			//}
			//,
			//'下一页': function() {
			//	wizard();
			//}
		},
		close: function() {
			clear($(this));
			updateTips($(this).find('#validateTips'),'');	
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			
		//  $(this).find('#imp').css("display","");
		//	$(this).find('a').show();

			$("#rllx").attr("disabled",false);
			$("#pfbz").attr("disabled",false);
			wizardModel = "one";
			//wizard();
		/*	$('#public_coc_dialog').parent().find("button").each(function() {
			     if( $(this).text() == '保存' || $(this).text() == '上传' || $(this).text() == '导入') {
			            $(this).css("display","");
			     }
    		}); */
		}
		
	});

	function addOrUpdate(button){
		
		//alert($('#ccjyjg').val().length);
		//alert($('#ccjyjg2').val());
		if(allFields==null){
			var parent = $('#public_coc_dialog');
			//if(!parent.find("#printtype").val()){
			//	alert("请选择打印模板!");
			//	return false;
			//}
			allFields = $([]).add(parent.find('#slcx'))
			.add(parent.find('#vercode')).add(parent.find('#state'))
			.add(parent.find('#pzextno')).add(parent.find('#jybgno')).add(parent.find('#jyjgmc'))
			.add(parent.find('#ccjyjg')).add(parent.find('#fdjxh')).add(parent.find('#fdjgc'))
			.add(parent.find('#chzhq')).add(parent.find('#chzhqgc')).add(parent.find('#ryzfkzqxh'))
			.add(parent.find('#ryzfkzqgc')).add(parent.find('#egrxh')).add(parent.find('#egrgc'))
			.add(parent.find('#obdxh')).add(parent.find('#obdgc')).add(parent.find('#iuprnox'))
			.add(parent.find('#ecuxh')).add(parent.find('#ecugc')).add(parent.find('#bsqxs'))
			.add(parent.find('#bsqdws')).add(parent.find('#xsqxh')).add(parent.find('#xsqgc'))
			.add(parent.find('#zyqxh')).add(parent.find('#zyqgc')).add(parent.find('#zlqxs'))
			.add(parent.find('#fdjbs')).add(parent.find('#fdjpath')).add(parent.find('#chzhqbs'))
			.add(parent.find('#chzhqpath'))
			.add(parent.find('#ycgqxh')).add(parent.find('#ycgqgc')).add(parent.find('#qzspfxh'))
			.add(parent.find('#qzspfgc'))
			.add(parent.find('#ryzfkzqbs')).add(parent.find('#ryzfkzqpath')).add(parent.find('#ycgqbs'))
			.add(parent.find('#ycgqpath')).add(parent.find('#egrbs')).add(parent.find('#egrpath'))
			.add(parent.find('#ecubs')).add(parent.find('#ecupath')).add(parent.find('#xsqbs'))
			.add(parent.find('#xsqpath')).add(parent.find('#zyqbs')).add(parent.find('#zyqpath'))
			.add(parent.find('#mark'))
			.add(parent.find('#jzzl')).add(parent.find('#jcjr5'))
			.add(parent.find('#jyjg5')).add(parent.find('#hbtc2')).add(parent.find('#hbzt2')).add(parent.find('#hbfzscc2'))
			.add(parent.find('#pfbz')).add(parent.find('#klbjqxh')).add(parent.find('#klbjqscc')).add(parent.find('#tgxh'))
			.add(parent.find('#tgscc')).add(parent.find('#fdjcp')).add(parent.find('#fdjscdz')).add(parent.find('#rygjfs')).add(parent.find('#obdplace'))
			.add(parent.find('#dpxh')).add(parent.find('#dpscc')).add(parent.find('#ktzljzl'))
			.add(parent.find('#ktzljjzl')).add(parent.find('#xxjyyj')).add(parent.find('#xxjyjl'))
		}
		allFields.removeClass('ui-state-error');

		
		if(type=="effect"){
	        var dialogObj =$('#public_coc_dialog');
	        effectHB(dialogObj);//生效COC
		}else if(validate('#public_coc_dialog')==true){
			var dlgButton = $('.ui-dialog-buttonpane button');//	
			dlgButton.attr('disabled', 'disabled');
	        dlgButton.addClass('ui-state-disabled');
	        
	        var dialogObj = $('#public_coc_dialog');
	        var qc1 = $('#qc1').val();
	        var m_c1=dialogObj.find('#createForm').find('#slcx').val();
	        var qstate = $('#qstate').val();
	        var qfactory = $('#qfactory').val();
	        var currentPage=$('#currentPage_temp').val();
			//判断是否为典型车型，如果是就弹出对话框让用户决定是否连同更新该典型车型下的中性车型参数。
   			jQuery.ajax({
	            url: 'business/typicalityNeutral!findTypicalityCarModel.action',		           
	            data: {"dxcx":m_c1}, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	               
	            },
	            success: function(data) {
	            	var m_zxcx='';
	            	var content = data.json;
	            	
	            	if(content!=''){		            			            	
			            var carObj = eval("("+content.toString()+")"); 
		            	if(carObj!=null && carObj.zxcx!=null){
		            		if(window.confirm("是否同时对关联的中性车型"+ carObj.zxcx + "进行相应操作！")){
		            			m_zxcx=carObj.zxcx;
		            		}
		            	}
	            	}
	            	//alert(m_zxcx);
					if(type=="add"){
				        var dialog = $('#public_coc_dialog');
				        dialog.find('#createForm').find('#menuid').val(menuid);
						dialog.find('#createForm')[0].action="business/proenv!addHB.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
						dialog.find('#createForm')[0].submit();
					}else if(type=="update"){
						var dialog = $('#public_coc_dialog');
					 	dialog.find('#createForm')[0].action="business/proenv!updateHB.action?qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&currentPage="+currentPage+"&zxcx="+m_zxcx;
						dialog.find('#createForm')[0].submit();
					}	            
	            }
	        }); 	        
			
		}
	}

	function effectHB(dialogObj){
		var id = dialogObj.find("#slcx").val()+","+dialogObj.find("#vercode").val();
 		var params = "slcx="+id;
 		var messageObj;
	   		jQuery.ajax({
        	url: 'business/proenv!HBVerInfo.action',		           
        	data: params, 
        	type: 'POST',
        	beforeSend: function() {
        	
        	},
        	error: function(request) {
            
        	},
        	success: function(data) {
        		
            	var content =  json2Bean(data).json;
    	        var carObj = eval("("+content.toString()+")");
    	            
            	info = "生产车型:"+dialogObj.find("#ggcx").val()+" 版本号:"+dialogObj.find("#vercode").val();
				if(carObj.state==null||carObj.state=="0"||carObj.state==""){

					params = "c1="+$('#public_coc_dialog').find('#ggcx').val();			
					jQuery.ajax({
			            url: 'business/publicNoticeCarModelManager!isCarModelExistByC1.action',		           
			            data: params, 
				        type: 'POST',
			            success: function(data) {					       									
				            var dialog = $('#public_coc_dialog');
				            if(data.json=="false"){
								updateTips(dialog.find('#validateTips'),'车型代号:['+dialog.find('#ggcx').val()+'] 此公告车型不存在！');	
								//var messageObj = $('#message_dialog');
					   	   		//messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
					   	   		//messageObj.dialog('open');	
							}else{
								messageObj = $('#operate_dialog');
				   	   			messageObj.find('#message').text('提示:确定修改【'+info+'】为生效 状态！ 共1条数据');
				   	   			messageObj.dialog('open');
				   	   			messageObj.find('#c1').val(id);
							}
			            }
			        });
					
					
	   	   			
				}else{
					messageObj = $('#message_dialog');
		   	   		messageObj.find('#message').text('警告:只能修改状态为[未生效]的数据！');
		   	   		messageObj.dialog('open');
				}
        	}
    	});
	}

	function setDialogValue(dialogObj,jsonObj){
		gbtable(jsonObj.rllx);
		var printtype = jsonObj.printtype;
		//alert("jsonObj.ccjyjg =" + jsonObj.ccjyjg);
		//alert("jsonObj.ccjyjg2 =" + jsonObj.ccjyjg2);
		dialogObj.find('#slcx').val(jsonObj.id.slcx);
		if(type=="look"||type=="update"||type=="effect"){
			dialogObj.find('#vercode').val(jsonObj.id.vercode);}
		dialogObj.find('#ggcx').val(jsonObj.ggcx);
		
		dialogObj.find('#pzextno').val(jsonObj.pzextno);
		dialogObj.find('#jybgno').val(jsonObj.jybgno);
		dialogObj.find('#jyjgmc').val(jsonObj.jyjgmc);
		dialogObj.find('#ccjyjg').val(jsonObj.ccjyjg);
		//dialogObj.find('#ccjyjg1').val(jsonObj.ccjyjg);
		dialogObj.find('#ccjyjg2').val(jsonObj.ccjyjg2);
		dialogObj.find('#fdjxh').val(jsonObj.fdjxh);
		dialogObj.find('#fdjgc').val(jsonObj.fdjgc);
		dialogObj.find('#chzhq').val(jsonObj.chzhq);
		dialogObj.find('#chzhqgc').val(jsonObj.chzhqgc);
		dialogObj.find('#ryzfkzqxh').val(jsonObj.ryzfkzqxh);
		dialogObj.find('#ryzfkzqgc').val(jsonObj.ryzfkzqgc);
		dialogObj.find('#egrxh').val(jsonObj.egrxh);
		dialogObj.find('#egrgc').val(jsonObj.egrgc);
		dialogObj.find('#obdxh').val(jsonObj.obdxh);
		dialogObj.find('#obdgc').val(jsonObj.obdgc);
		dialogObj.find('#iuprnox').val(jsonObj.iuprnox);
		dialogObj.find('#ecuxh').val(jsonObj.ecuxh);
		dialogObj.find('#ecugc').val(jsonObj.ecugc);
		dialogObj.find('#bsqxs').val(jsonObj.bsqxs);
		dialogObj.find('#bsqdws').val(jsonObj.bsqdws);
		dialogObj.find('#xsqxh').val(jsonObj.xsqxh);
		dialogObj.find('#xsqgc').val(jsonObj.xsqgc);
		dialogObj.find('#zyqxh').val(jsonObj.zyqxh);
		dialogObj.find('#zyqgc').val(jsonObj.zyqgc);
		dialogObj.find('#zlqxs').val(jsonObj.zlqxs);
		
		
		dialogObj.find('#jzzl').val(jsonObj.jzzl);
		
		dialogObj.find('#ycgqxh').val(jsonObj.ycgqxh);
		dialogObj.find('#ycgqgc').val(jsonObj.ycgqgc);
		dialogObj.find('#qzspfxh').val(jsonObj.qzspfxh);
		dialogObj.find('#qzspfgc').val(jsonObj.qzspfgc);
		
		dialogObj.find('#jyjg1').val(jsonObj.jyjg1);
		dialogObj.find('#jyjg2').val(jsonObj.jyjg2);
		dialogObj.find('#jyjg3').val(jsonObj.jyjg3);
		dialogObj.find('#jyjg4').val(jsonObj.jyjg4);
		dialogObj.find('#jyjg5').val(jsonObj.jyjg5);
		
		dialogObj.find('#jcjr1').val(jsonObj.jcjr1);
		dialogObj.find('#jcjr2').val(jsonObj.jcjr2);
		dialogObj.find('#jcjr3').val(jsonObj.jcjr3);
		dialogObj.find('#jcjr4').val(jsonObj.jcjr4);
		dialogObj.find('#jcjr5').val(jsonObj.jcjr5);
		
		dialogObj.find('#hbtc').val(jsonObj.hbtc);
		dialogObj.find('#hbzt').val(jsonObj.hbzt);
		dialogObj.find('#hbfzscc').val(jsonObj.hbfzscc);
		dialogObj.find('#frdb').val(jsonObj.frdb);
		dialogObj.find('#dz').val(jsonObj.dz);
		dialogObj.find('#tel').val(jsonObj.tel);
		dialogObj.find('#hbdws').val(jsonObj.hbdws);
		dialogObj.find('#ecubb').val(jsonObj.ecubb);
		
		dialogObj.find('#rqhhq').val(jsonObj.rqhhq);
		dialogObj.find('#rqhhqgc').val(jsonObj.rqhhqgc);
		dialogObj.find('#rqpsdy').val(jsonObj.rqpsdy);
		dialogObj.find('#rqpsdygc').val(jsonObj.rqpsdygc);
		dialogObj.find('#hbdws').val(jsonObj.hbdws);
		
		dialogObj.find('#cnzl').val(jsonObj.cnzl);
		dialogObj.find('#cnzlgc').val(jsonObj.cnzlgc);
		dialogObj.find('#dcrl').val(jsonObj.dcrl);
		dialogObj.find('#dhlc').val(jsonObj.dhlc);
		dialogObj.find('#ddjxh').val(jsonObj.ddjxh);
		dialogObj.find('#ddjgc').val(jsonObj.ddjgc);
		dialogObj.find('#ddjecu').val(jsonObj.ddjecu);
		dialogObj.find('#ddjecubbh').val(jsonObj.ddjecubbh);
		dialogObj.find('#ddjecuscc').val(jsonObj.ddjecuscc);
		dialogObj.find('#rllx').val(jsonObj.rllx)
		
		dialogObj.find('#hbtc2').val(jsonObj.hbtc2);
		dialogObj.find('#hbzt2').val(jsonObj.hbzt2);
		dialogObj.find('#hbfzscc2').val(jsonObj.hbfzscc2);
		dialogObj.find('#klbjqxh').val(jsonObj.klbjqxh);
		dialogObj.find('#klbjqscc').val(jsonObj.klbjqscc);
		dialogObj.find('#tgxh').val(jsonObj.tgxh);
		dialogObj.find('#tgscc').val(jsonObj.tgscc);
		
		dialogObj.find('#xxgkhao').val(jsonObj.xxgkhao);
		dialogObj.find('#clxh').val(jsonObj.clxh);
		dialogObj.find('#hbsb').val(jsonObj.hbsb);
		dialogObj.find('#qcfl').val(jsonObj.qcfl);
		dialogObj.find('#cxsb').val(jsonObj.cxsb);
		dialogObj.find('#clzzname').val(jsonObj.clzzname);
		dialogObj.find('#scgdz').val(jsonObj.scgdz);
		dialogObj.find('#pfjd').val(jsonObj.pfjd);
		dialogObj.find('#pfbz').val(jsonObj.pfbz);
		dialogObj.find('#dbz').val(jsonObj.dbz);		
	    //gbtable(jsonObj.rllx,jsonObj.pfbz);
	    //20191021
	    dialogObj.find('#fdjcp').val(jsonObj.fdjcp);
	    dialogObj.find('#fdjscdz').val(jsonObj.fdjscdz);
	    dialogObj.find('#rygjfs').val(jsonObj.rygjfs);
	    dialogObj.find('#obdplcae').val(jsonObj.obdplcae);
	    
	    dialogObj.find('#dpxh').val(jsonObj.dpxh);
	    dialogObj.find('#dpscc').val(jsonObj.dpscc);
	    dialogObj.find('#ktzljzl').val(jsonObj.ktzljzl);
	    dialogObj.find('#ktzljjzl').val(jsonObj.ktzljjzl);
	    dialogObj.find('#xxjyyj').val(jsonObj.xxjyyj);
	    dialogObj.find('#xxjyjl').val(jsonObj.xxjyjl);
	    
		//设置图片位置
		//$("#preview").css({'top':'180px'});
		
	}

	function clear(dialogObj){
		dialogObj.find('input').attr('value','');
		dialogObj.find('a').hide();
		
		$('input').attr("readonly", false);
		dialogObj.find('#imp').css('display','');
		dialogObj.find('#tempc6').attr('size','11');
		$('.ui-dialog-buttonpane button').eq(3).attr('value','保存');
		type = null;
	}

	function isNotNull(parent,id){
		var obj = $(parent).find('#'+id);
		var value=obj.val();
		if(value!=null && value!="")
			return true;
		return false;
	}
	function getPageNum(id){

		return 0;
	}
	
	
	$('#public_coc_dialog').find('#slcx').bind('keyup',function(event) { 
		if(type=="add")
			getMaxVercode();
    });
	
	function validate(parent){
		//pageNeedValidateCache=getPageNeedValidate(parent);
		var obj = $(parent).find('#slcx');
		var max = 18;
		if(!checkLength(obj,18,max)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),obj.parent().prev().find('p').text()+'字段长度必须为'+max+'！');			
			obj.focus();
			return false;
		}
		
		return true;
	}
	
	$('#jump').bind('keyup',function(event) {
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		$(this).blur(function(){
			var qc1 = $('#qc1').val();
			var qstate = $('#qstate').val();
			location.href="cocVer.action?currentPage="+$('#jump').val()+"&qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&menuid="+menuid;
		});
    });

	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var qc1 = $('#qc1').val();
				var qstate = $('#qstate').val();
				var qfactory = $('#qfactory').val();
				
				location.href=$(this).attr('value')+"&qc1="+encodeURI(encodeURI(qc1))+"&qstate="+qstate+"&qfactory="+qfactory+"&menuid="+menuid;
		 });
	});

	
	$("#print").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK']:checked"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});

   		if(index==1){
   	   		var c1 = id.split(",")[0];
   	   		var vercode = id.split(",")[1];
   	   		
   			window.document.coc.printHBCX(c1,vercode);
   	   	
   	   		
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要打印的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能打印一条数据！');
	   		messageObj.dialog('open');
   	   	 }
	});

	$(document).ready(function(){
	 	$("#users").find("tr").mouseover(function(){  
		$(this).addClass("over");}).mouseout(function(){ 
        //给这行添加class值为over，并且当鼠标一出该行时执行函数
        $(this).removeClass("over");})    //移除该行的class
	});
});


//打印显示
function showInfo(c1,vercode,info){
	var messageObj = $('#message_dialog');
 	messageObj.find('#message').text(info);
	messageObj.dialog('open');
}


	function createrllxchange(){
		var trllx = $("select[id='rllx']").val();
		//alert(trllx);
			
		gbtable(trllx);
	}
	
	function pfbzchange(){
		var trllx = $("select[id='rllx']").val();
		//alert(trllx);
			
		gbtable(trllx);
	}
	
	function gbtable(trllx){
		//
		//var td_obj ;
		//汽油
		if(trllx=="1"){ 
			$.each($("#one tr"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			}); 
			$.each($("#one td"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			});
			    $("#ddc6").hide();			
				$("#ddj2").hide();			
				$("#ddc7").hide();
				$("#ddc81").hide();
				$("#ddc82").hide();
				$("#ddc91").hide();
				$("#ddc92").hide();
		}
		//非插电混动
		if(trllx=="4"){
			$.each($("#one tr"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			}); 
			$.each($("#one td"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			});
				$("#ddj2").hide();
				$("#ddc91").val("纯电续航里程");
				//$("#ddc16").hide();
		}
		//插电混动
		if(trllx=="5"){
			$.each($("#one tr"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			}); 
			$.each($("#one td"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			});
				$("#ddj2").hide();
				$("#ddc91").val("纯电续航里程");
				//$("#ddc16").hide();
		}
		//电动
		if(trllx=="3"){
			$.each($("#one tr"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			}); 
			$.each($("#one td"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			});
				$("#pfjd1").hide();
				$("#pfjd2").hide();
				$("#ddc1").hide();
				$("#ddc3").hide();
				$("#ddc4").hide();
				$("#ddc5").hide();
				$("#ddc91").text("纯电续航里程及对应工况");
				$("#ddc10").hide();
				$("#ddc11").hide();
				$("#ddc12").hide();
				$("#ddc13").hide();
				$("#ddc14").hide();
				$("#ddc15").hide();
				$("#ddc16").hide();
				$("#ddc17").hide();
				$("#ddc18").hide();
				$("#ddc19").hide();
		
		}
		//燃料电池
		if(trllx=="7"){
			$.each($("#one tr"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			}); 
			$.each($("#one td"), function(i){				
				//console.log(i);
				if(i>=1){//从出厂检验项目及结论以后，顺序号12.
					//this.style.display = ''; 					
					//this.style.display = 'block';
					//$(this).css('display', 'block');
					$(this).show();
		        } 
			});
			$("#pfjd1").hide();
			$("#pfjd2").hide();
			$("#ddc1").hide();
			$("#ddc3").hide();
			$("#ddc4").hide();
			$("#ddc5").hide();
			$("#ddc91").text("纯电续航里程及对应工况");
			$("#ddc10").hide();
			$("#ddc11").hide();
			$("#ddc12").hide();
			$("#ddc13").hide();
			$("#ddc14").hide();
			$("#ddc15").hide();
			$("#ddc16").hide();
			$("#ddc17").hide();
			$("#ddc18").hide();
			$("#ddc19").hide();
		
		}
		
	}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="7"><p>生产车型:<input type="text" id="qc1" name='qc1' class="text ui-widget-content " size="18" <s:if test="#request.qc1!=null"> value="<s:property value="#request.qc1" />"</s:if> />	
				  状态:<s:select name="qstate" list="#request.stateMap"  listKey="value" listValue="key" headerKey="" headerValue="请选择" theme="simple" value="#request.qstate"></s:select> 
  			    </td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr>
				<td width="80%"></td>
			  	<td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  	<td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  	<td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
  			  	<td width="60" align="right"><button id="effect" class="ui-button ui-state-default ui-corner-all">生效</button></td>
  				<td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			    <td width="60" align="right"><button id="print" class="ui-button ui-state-default ui-corner-all">打印</button></td>
			    <td width="60" align="right"><button id="compare" class="ui-button ui-state-default ui-corner-all">版本比较</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr>
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="6%">选择</th> 
			    <th width="10%">生产车型</th>
			    <th width="10%">公告车型</th>	
				<th width="8%">状态</th>
				<th width="7%">类型</th>
				<th width="10%">版本</th>
				<th width="9%">创建人</th>
				<th width="17%">创建时间</th>
				<th width="17%">生效时间</th>
				<th width="6%">操作</th>
				
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.publicNoticePageData" status="obj" > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td>
			  			<input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="id.slcx" />,<s:property value="id.vercode" />' >
			  			<input type="hidden" name="state" id="state" value='<s:property value="state"/>'>
			  		</td>
			  		<td><s:property value="id.slcx" /></td>
   		      		<td><s:property value="ggcx" /></td>				
			  		<td>
			  			<s:if test="state==1">生效</s:if>
			  			<s:else>
			  				<s:if test="state==9">历史</s:if>
			  				<s:else>未生效</s:else>
						</s:else>
			  		</td>
			  		<td>
						<s:if test="rllx==1">汽油</s:if>
			  			<s:else>
			  				     <s:if test="rllx==3">纯电动</s:if>
			  				     <s:else>
			  				          <s:if test="rllx==4">非插电混动</s:if>
			  				          <s:else>
			  				               <s:if test="rllx==5">插电混动</s:if>  
			  				               <s:else>
			  				                 <s:if test="rllx==7">燃料电池</s:if>      
			  				     	  	   </s:else>     
			  				     	  </s:else>   
			  				     </s:else>
						</s:else>
					</td>
			  		<td><s:property value="id.vercode" /></td>
			  		<td><s:property value="creator" /></td>		
			  		<td><s:date name="datadate" format="yyyy-MM-dd HH:mm:ss"/></td>	
			  		<td><s:property value="effectTime" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.slcx" />,<s:property value="id.vercode" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.publicNoticePage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="proenv.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="proenv.action?currentPage=<s:property value="#request.publicNoticePage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage>=#request.publicNoticePage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="proenv.action?currentPage=<s:property value="#request.publicNoticePage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.publicNoticePage.currentPage==#request.publicNoticePage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="proenv.action?currentPage=<s:property value="#request.publicNoticePage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.publicNoticePage.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.publicNoticePage.currentPage" />/总页数 <s:property value="#request.publicNoticePage.maxPage" /> 总记录数 <s:property value="#request.publicNoticePage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			  	<td width="7%" align="right"><td width="7%" align="right">
			  	</td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="public_coc_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" >
	  	<table id="one" name="one" width="100%" border="0">
	  		<tr><!-- 1 187-->
	  			<td width="13%"><label><p>声明信息</p></label></td>
				<td colspan="8" width="87%">
					<input type="text" id="dbz" name="dbz" class="text ui-widget-content ui-corner-all" size="80" /></td>
		
	  		</tr>
	  		<tr><!-- 2 117 90-->
				<td width="13%"><label><P>生产车型</label></td>
				<td colspan="2" width="20%">
					<input type="text" id="slcx" name="slcx" class="text ui-widget-content ui-corner-all" size="20" maxlength="20"/></td>
				<td width="13%"><label><P>版本号</label></td>
				<td colspan="2" width="20%">
					<input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    		<td width="13%"><label><P>公告车型</label></td>
				<td colspan="2" width="20%">
					<input type="text" id="ggcx" name="ggcx" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>
	    	
	    	<tr>
	    		<td><label><p>环保类型</p></label></td>
				<td colspan="2">
					<select name="rllx" id="rllx" onchange="createrllxchange()">
						<option value="1">汽油</option>
						<option value="3">纯电动</option>
						<option value="4">非插电混动</option>
						<option value="5">插电混动</option>
						<option value="7">燃料电池</option>
					</select>
				</td>
				<td><label><P>法人代表</P></label></td>
				<td colspan="2">
					<input type="text" id="frdb" name="frdb" class="text ui-widget-content ui-corner-all" size="20" /></td>
				
				<td><label><P>联系电话</P></label></td>
				<td colspan="2">
					<input type="text" id="tel" name="tel" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			
			<tr>
				<td><label><P>地址</label></td>
				<td colspan="8">
				<input type="text" id="dz" name="dz" class="text ui-widget-content ui-corner-all" size="50"/></td>					
			</tr>
			<tr>
	    	 	<td ><label><P>信息公开编号</label></td>
				<td colspan="8">
				<input type="text" id="xxgkhao" name="xxgkhao" class="text ui-widget-content ui-corner-all" size="20" maxlength="35"/></td>    		
			</tr>
	    	
	    	<tr>
				<td width="13%"><label><P>车辆型号</label></td>
				<td colspan="2" width="20%">
					<input type="text" id="clxh" name="clxh" class="text ui-widget-content ui-corner-all" size="20" maxlength="20"/></td>
				<td width="13%"><label><P>商    标</label></td>
				<td colspan="2" width="20%">
					<input type="text" id="hbsb" name="hbsb" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    		<td width="13%"><label><P>汽车分类</label></td>
				<td colspan="2" width="20%">
					<input type="text" id="qcfl" name="qcfl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>
	    	<tr>
	    	    <td id="pfjd1"><label><P>排放阶段</td>
				<td id="pfjd2" colspan="2">
					<input type="text" id="pfjd" name="pfjd" class="text ui-widget-content ui-corner-all" size="20" /></td>				
	    		<td ><label><P>车辆制造商名称</label></td>
				<td colspan="2">
					<input type="text" id="clzzname" name="clzzname" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    		<td><label><P>生产厂地址</label></td>
				<td colspan="2">
					<input type="text" id="scgdz" name="scgdz" class="text ui-widget-content ui-corner-all" size="20" /></td>
	    	</tr>
	    	<tr id="ddc1">
	    	   <td ><label><P>基准质量</label></td>
			   <td colspan="2">
					<input type="text" id="jzzl" name="jzzl" class="text ui-widget-content ui-corner-all" size="20"/></td>
	   		   <td ><label><P>车型的识别方法和位置</label></td>
			   <td colspan="2">
					<input type="text" id="cxsb" name="cxsb" class="text ui-widget-content ui-corner-all" size="20" maxlength="20"/></td>
	    	</tr>
	    	<tr>
	    	    <td ><label><P>底盘型号</td>
				<td colspan="2">
					<input type="text" id="dpxh" name="dpxh" class="text ui-widget-content ui-corner-all" size="20" /></td>				
	    		<td ><label><P>底盘生产企业</label></td>
				<td colspan="2">
					<input type="text" id="dpscc" name="dpscc" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    	</tr>
	    	<tr>
	    	    <td ><label><P>车用空调制冷剂种类</td>
				<td colspan="2">
					<input type="text" id="ktzljzl" name="ktzljzl" class="text ui-widget-content ui-corner-all" size="20" /></td>				
	    		<td ><label><P>车用空调制冷剂加注量</label></td>
				<td colspan="2">
					<input type="text" id="ktzljjzl" name="ktzljjzl" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    	</tr>
	    	<tr id="ddc3" name="ddc3" ><!-- 9 2018-10-16 国六车型标准-->
	  			<td><label><p>依据的标准	</p></label></td>
				<td colspan="2">GB 18352.6-2016</td>
				<td><label><p>检测机构</p></label></td>
				<td colspan="2">
					<input type="text" id="jyjg5" name="jyjg5" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>检测结论	</p></label></td>
				<td colspan="2">
					<input type="text" id="jcjr5" name="jcjr5" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		</tr>
	  		<tr ><!-- 12 -->
	  			<td><label><p>依据的标准 </p></label></td>
				<td colspan="2">GB1495-2002</td>
				<td><label><p>检测机构</p></label></td>
				<td colspan="2">
					<input type="text" id="jyjg3" name="jyjg3" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><p>检测结论	</p></label></td>
				<td colspan="2">
					<input type="text" id="jcjr3" name="jcjr3" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		</tr>
	    	<tr  id="ddc4" name="ddc4" >
	    	    <td ><label><P>下线检验信息依据</td>
				<td colspan="2">
					<input type="text" id="xxjyyj" name="xxjyyj" class="text ui-widget-content ui-corner-all" size="20" /></td>				
	    		<td ><label><P>下线检验信息结论</label></td>
				<td colspan="2">
					<input type="text" id="xxjyjl" name="xxjyjl" class="text ui-widget-content ui-corner-all" size="20"/></td>
	    	</tr>
	    	<tr   id="ddc5" name="ddc5" >
			<td><label><P>发动机型号</label></td>
			<td colspan="2">
				<input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
  			<td><label><P>发动机生产厂</label></td>
			<td colspan="2">
				<input type="text" id="fdjgc" name="fdjgc" class="text ui-widget-content ui-corner-all" size="20" /></td>	  		
			</tr>
			
			<tr id="ddc6" name="ddc6" style="display: none;">
			<td><label><P>电动机型号</td>
			<td colspan="2">
				<input type="text" id="ddjxh" name="ddjxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
  			<td><label><P>电动机生产厂</label></td>
			<td colspan="2">
				<input type="text" id="ddjgc" name="ddjgc" class="text ui-widget-content ui-corner-all" size="20" /></td>	  		
		  	</tr>
			
			<tr id="ddc7" name="ddc7" style="display: none;">
		  	<td><label><P>储能装置型号</td>
			<td colspan="2">
				<input type="text" id="cnzl" name="cnzl" class="text ui-widget-content ui-corner-all" size="20"/></td>
			<td><label><P>储能装置生产厂</td>
			<td colspan="2">
				<input type="text" id="cnzlgc" name="cnzlgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
  			</tr>
  			
			<tr >
  			<td id="ddc81" name="ddc81" ><label><P>电池容量</td>
			<td id="ddc82" name="ddc82"  colspan="2">
				<input type="text" id="dcrl" name="dcrl" class="text ui-widget-content ui-corner-all" size="20" />	 </td> 		
			<td id="ddc91" name="ddc91" ><label>纯电续航里程</label></td>
			<td id="ddc92" name="ddc92"  colspan="2">
				<input type="text" id="dhlc" name="dhlc" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			
			<tr id="ddj2" name="ddj2" style="display: none;">
			<td><label><P>整车控制器型号</td>
			<td colspan="2">
				<input type="text" id="ddjecu" name="ddjecu" class="text ui-widget-content ui-corner-all" size="20" /></td>
			<td><label><P>整车控制器版本号</label></td>
			<td colspan="2">
				<input type="text" id="ddjecubbh" name="ddjecubbh" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		<td><label><P>整车控制器生产厂</td>
			<td colspan="2">
				<input type="text" id="ddjecuscc" name="ddjecuscc" class="text ui-widget-content ui-corner-all" size="20" /></td>	
			</tr>
			<tr id="ddc10" name="ddc10">
	  			<td><label><P>催化转换器</label></td>
				<td colspan="2"><input type="text" id="chzhq" name="chzhq" class="text ui-widget-content ui-corner-all" size="20" /></td>
			
				<td><label><P>催化转化器生产厂</label></td>
				<td colspan="2"><input type="text" id="chzhqgc" name="chzhqgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
	  		</tr>
	  		<tr id="ddc11" name="ddc11">
	  			<td><label><P>涂层</label></td>
	  			<td colspan="2">
				<input type="text" id="hbtc" name="hbtc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>载体</td>
				<td colspan="2"><input type="text" id="hbzt" name="hbzt" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>封装生产企业</td>
				<td colspan="2"><input type="text" id="hbfzscc" name="hbfzscc" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr id="ddc12" name="ddc12" style="display: none;"><!-- 31 -->
		       <td><label><P>颗粒捕集器型号</label></td>
				<td colspan="2"><input type="text" id="klbjqxh" name="klbjqxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>颗粒捕集器生产企业</label></td>
	    		<td colspan="2"><input type="text" id="klbjqscc" name="klbjqscc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td>&nbsp;</td>
				<td colspan="2">&nbsp;</td>
              </tr>
			<tr id="ddc13" name="ddc13" style="display: none;"> 
				<td><label><P>涂层</label></td>
				<td colspan="2"><input type="text" id="hbtc2" name="hbtc2" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>载体</label></td>
				<td colspan="2"><input type="text" id="hbzt2" name="hbzt2" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>封装生产企业</label></td>
				<td colspan="2"><input type="text" id="hbfzscc2" name="hbfzscc2" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr id="ddc14" name="ddc14" style="display: none;"><!-- 32 -->
				<td><label><P>炭罐型号</label></td>
				<td colspan="2"><input type="text" id="tgxh" name="tgxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>炭罐生产企业</label></td>
				<td colspan="2"><input type="text" id="tgscc" name="tgscc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>氧传感器型号</label></td>
				<td colspan="2"><input type="text" id="ycgqxh" name="ycgqxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr id="ddc15" name="ddc15"  style="display: none;">
				<td><label><P>氧传感器生产厂</label></td>
				<td colspan="2"><input type="text" id="ycgqgc" name="ycgqgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>增压器型号</label></td>
				<td colspan="2"><input type="text" id="zyqxh" name="zyqxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>增压器生产厂</label></td>
				<td colspan="2"><input type="text" id="zyqgc" name="zyqgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr id="ddc16" name="ddc16"  style="display: none;">
				<td><label><P>EGR型号</label></td>
				<td colspan="2"><input type="text" id="egrxh" name="egrxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>EGR生产厂</label></td>
				<td colspan="2"><input type="text" id="egrgc" name="egrgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr  id="ddc17" name="ddc17"  style="display: none;"><!-- 22 -->
				<td><label><P>OBD型号</label></td>
				<td colspan="2"><input type="text" id="obdxh" name="obdxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
			
				<td><label><P>OBD生产厂</label></td>
				<td colspan="2"><input type="text" id="obdgc" name="obdgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>变速器型式</label></td>
				<td colspan="2"><input type="text" id="bsqxs" name="bsqxs" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>	
			<tr  id="ddc18" name="ddc18"  style="display: none;"><!-- 23 -->
				<td><label><P>ECU型号</label></td>
				<td colspan="2"><input type="text" id="ecuxh" name="ecuxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>ECU生产厂</label></td>
				<td colspan="2"><input type="text" id="ecugc" name="ecugc" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>档位数</P></label></td>
				<td colspan="2"><input type="text" id="bsqdws" name="bsqdws" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			<tr  id="ddc19" name="ddc19"  style="display: none;">
				<td><label><P>ECU版本号</P></label></td>
				<td colspan="2"><input type="text" id="ecubb" name="ecubb" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>消声器型号</label></td>
				<td colspan="2"><input type="text" id="xsqxh" name="xsqxh" class="text ui-widget-content ui-corner-all" size="20" /></td>
				<td><label><P>消声器生产厂</label></td>
				<td colspan="2"><input type="text" id="xsqgc" name="xsqgc" class="text ui-widget-content ui-corner-all" size="20" /></td>
			</tr>
			
	   
		</Table>
		
		<input type='hidden' id='state' name='state'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>


<div id="coc_compare_dialog" style="display:none;overflow-y:auto; ">
	<p id="validateTips"></p>
	<fieldset>
		<form id="createForm" method="post" onSubmit="return false;" >
			<table>
				<tr>
					<td ><label><P>版本号1：</label></td>
					<td name='ver1'><s:select cssClass='command' name="cocVer1" list="#request.cocVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.cocVer1" ></s:select></td>
					<td ><label><P>版本号2：</label></td>
					<td name='ver2'><s:select cssClass='command' name="cocVer2" list="#request.cocVerMap"  listKey="key" listValue="value" headerKey="" headerValue="请选择" theme="simple" value="#request.cocVer1" ></s:select></td><td width="60" align="right">
					<button id="compare1" class="ui-button ui-state-default ui-corner-all">比较</button></td></td>
				</tr>			
			</table>		
	  	<table id="tbl1" width="100%" >
	  		<tr name="slcx" style="background:#f1f9f3;">
				<td width="20%" ><label><P>生产车型</label></td><td width="40%" ></td><td width="40%" ></td>
	    	</tr>
	  		<tr name="vercode" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>版本号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="hbsb" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>商 标</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="qcfl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>汽车分类</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ggcx" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>公告车型</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="clxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
	  		<tr name="cxsb" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车型的识别方法和位置</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="clzzname" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车辆制造商名称</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="scgdz" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>生产厂地址</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="xxgkhao" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>信息公开编号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jzzl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>基准质量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="pfjd" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>排放阶段</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="frdb" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>法人代表</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="tel" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>联系电话</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="rllx" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>环保类型</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="dz" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>地址</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
	  		<tr name="fdjxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>发动机型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="fdjgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>发动机生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="chzhq" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>催化转换器</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="chzhqgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>催化转换器生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="klbjqxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>颗粒捕集器型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="klbjqscc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>颗粒捕集器生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="tgxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>炭罐型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="tgscc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>炭罐生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="ryzfkzqxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃油装置控制型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ryzfkzqgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃油装置控制生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  			<tr name="ycgqxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>氧传感器型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  			<tr name="ycgqgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>氧传感器生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  			<tr name="qzspfxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>曲轴箱排放控制装置型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  			<tr name="qzspfgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>曲轴箱排放控制装置生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
	  		<tr name="egrxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>EGR型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
	  		<tr name="egrgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>EGR生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="obdxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>OBD型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="obdgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>OBD生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="iuprnox" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>IUPRX检测功能</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ecuxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>ECU型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ecugc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>ECU生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
	  		
	  		
	  		
	  		
	  		<tr name="bsqxs" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>变速器型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="xsqxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>消声器型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="xsqgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>消声器生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="zyqxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>增压器型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="zyqgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>增压器生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="zlqxs" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>中冷器型式</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
	  		
	  		
	  		
	  		<tr name="jyjg1" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 18352.5-2013 检测机构 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jyjg2" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 18285-2005 检测机构 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jyjg3" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 1495-2002 检测机构 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="jyjg4" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB/T27630-2011 检测机构 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jyjg5" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 18352.6-2016 检测机构 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="jcjr1" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 18352.5-2013 检测结论 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jcjr2" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 18285-2005 检测结论 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jcjr3" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 1495-2002 检测结论 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jcjr4" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB/T27630-2011 检测结论 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="jcjr5" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>依据的标准  GB 18352.6-2016 检测结论 </label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="ddjxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>电动机型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ddjgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>电动机生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="cnzl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>储能装置型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="cnzlgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>储能装置生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="dcrl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>电池容量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="dhlc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>续航里程</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="rqhhq" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃气混合器型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="rqhhqgc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃气混合器生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="rqpsdy" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃气喷射单元型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="rqpsdygc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>燃气喷射单元生产厂</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		<tr name="dpxh" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>底盘型号</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="dpscc" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>底盘生产企业</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ktzljzl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车用空调制冷剂种类</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="ktzljjzl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>车用空调制冷剂加注量</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="xxjyyj" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>下线检验信息依据</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		<tr name="xxjyjl" style="background:#f1f9f3;">		
	    		<td width="20%" ><label><P>下线检验信息结论</label></td><td width="40%" ></td><td width="40%" ></td>
	  		</tr>
	  		
	  		
		</table>
		</form>
	</fieldset>
</div>


<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='c1' name='c1'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>
<div id="pubilc_notice_dialog" title="操作窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
  	公告车型型号:<select id='pmodelList' name='pmodelList'><option value="">请选择...</option></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<!--button name="dis" id="dis" class="ui-button ui-state-default ui-corner-all" style="position:static">查看</button-->
  	<br>
  	<br>
  	工况信息:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<select id='gkxxList' name='gkxxList'><option value="">请选择...</option></select>
</div>
<div align="center"> 
	<jsp:plugin name="coc" type="applet" code="com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.applet.CarVersionPrintApplet.class" codebase="." archive = "../applet/printVersion.jar,../applet/jasperreports-3.6.1.jar,../applet/commons-logging-1.1.1.jar,../applet/commons-collections-3.2.jar,../applet/commons-digester-1.7.jar,../applet/com-jaspersoft-ireport.jar,../applet/Qrcode_encoder.jar,../applet/iText-2.1.0.jar,../applet/iTextAsian.jar,../applet/barcode4j.jar,../applet/groovy-all-1.5.5.jar" 
	iepluginurl="http://********:8080/CarFileManager/tools/jre-6u17-windows-i586-s.exe" height="0" width="0">
		<jsp:params>
			<jsp:param name="url" value="<%=basePath %>"/>
			<jsp:param name="model" value="PRO"/>
			<jsp:param name="printPoint" value="G95"/>
		</jsp:params>
		<jsp:fallback>客户端打印控件加载失败！</jsp:fallback>
	</jsp:plugin>
</div>

<script type="text/javascript">
function findOtherVer(obj){
	var val=obj.value;
	$('#cocVer2').focus();
	jQuery.ajax({
        url: 'business/cocVer!findCocVer.action',		           
        data: {'cocVer1' : val}, 
     	type: 'POST',
     	//dataType:'json',
        beforeSend: function() {
        
        },
        error: function(XmlHttpRequest,textStatus, errorThrown) {
        	//alert(XmlHttpRequest.responseText);
            alert("系统错误，请与管理员联系！");
        },
        success: function(data) {
         	var content = json2Bean(data).json;
         	var carObj = eval("("+content.toString()+")");
			$("#cocVer2").empty();//清空下拉框 
			$("<option value=''>请选择...</option>").appendTo("#cocVer2");
			$.each( carObj, function(i, n){
				var tmp=n.id.c1+","+n.id.vercode;
				var stateName='';
				if(tmp!=val){
					if(n.state=='0'){
						stateName='未生效';
					}else if(n.state=='1'){
						stateName='生效';
					}else if(n.state=='9'){
						stateName='历史';
					}
					var opt="<option value='"+tmp +"'>"+n.id.c1+"("+n.id.vercode+stateName+")</option>";
					$(opt).appendTo("#cocVer2")//添加下拉框的option
				}				
			});			     	
        }
    });	
}





</script>
</body>
</html>