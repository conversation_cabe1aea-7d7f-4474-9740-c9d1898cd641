package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysLog;

public class SysLogDAOImpl extends GenericHibernateDAOImpl<SysLog> implements SysLogDAO{

	public void addLog(SysLog data) throws DataAccessException {
		try{
			this.add(data);
		}catch(Exception e){
			throw new DataAccessException("findLog Method Error:",e);
		}
	}

	public List<SysLog> findLog(String sql, String[] params) throws DataAccessException {
		try{
			return this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findLog Method Error:",e);
		}
	}

	public List<SysLog> findLog(String sql) throws DataAccessException {
		try{
			return this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findLog Method Error:",e);
		}
	}

}
