package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.model.FilterModel;

public interface FilterModelService {
	public void addFilterModel(FilterModel obj) throws DataAccessException;
	public void updateFilterModel(FilterModel obj) throws DataAccessException;
	public void deleteFilterModel(Serializable id) throws DataAccessException;
	public void deleteFilterModel(Serializable[] id) throws DataAccessException;
	public List<FilterModel> findFilterModel(String slcx) throws DataAccessException;
	public FilterModel loadFilterModel(Serializable id) throws DataAccessException;
	public List<?> pagination(Page page, Map<String,String> params) throws DataAccessException;
}
