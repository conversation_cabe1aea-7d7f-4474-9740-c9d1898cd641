<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.GASPrinterLog" table="GAS_PRINTER_LOG" lazy="false">
       
       <id name="id" type="java.lang.String">
            <column name="ID" length="50" />
            <generator class="assigned" />
        </id>
        
        <property name="printpoint" type="java.lang.String">
            <column name="PRINTPOINT" length="20">
            </column>
        </property>
        
        <property name="printer" type="java.lang.String">
            <column name="PRINTER" length="10">
            </column>
        </property>
        
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20">
                <comment>创建人</comment>
            </column>
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7">
                <comment>时间</comment>
            </column>
        </property>
    </class>
</hibernate-mapping>
