<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcs" table="T_HW_BASE_COCCS">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.HwBaseCOCcsId">
            <key-property name="slcx" type="java.lang.String">
                <column name="SLCX" length="20" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="30" />
            </key-property>
        </composite-id>
        
        <property name="ggcx" type="java.lang.String">
            <column name="GGCX" length="20" />
        </property>
        
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7" />
        </property>
        
        <property name="state" type="java.lang.String">
            <column name="STATE" length="1" />
        </property>
        <property name="factory" type="java.lang.String">
            <column name="FACTORY" length="10" />
        </property>
        <property name="effecttime" type="java.lang.String">
            <column name="EFFECT_TIME" length="19" />
        </property>
        <property name="published" type="java.lang.String">
            <column name="PUBLISHED" length="1" />
        </property>
        <property name="remark" type="java.lang.String">
            <column name="REMARK" length="100" />
        </property>
        <property name="printtype" type="java.lang.String">
            <column name="PRINTTYPE" length="2" />
        </property>
         <property name="printmodel" type="java.lang.String">
            <column name="PRINTMODEL" length="2" />
        </property>
         
        
        <property name="c1" type="java.lang.String"><column name="C1" length="500" /></property>
<property name="c2" type="java.lang.String"><column name="C2" length="500" /></property>
<property name="c3" type="java.lang.String"><column name="C3" length="500" /></property>
<property name="c4" type="java.lang.String"><column name="C4" length="500" /></property>
<property name="c5" type="java.lang.String"><column name="C5" length="500" /></property>
<property name="c6" type="java.lang.String"><column name="C6" length="500" /></property>
<property name="c7" type="java.lang.String"><column name="C7" length="500" /></property>
<property name="c8" type="java.lang.String"><column name="C8" length="500" /></property>
<property name="c9" type="java.lang.String"><column name="C9" length="500" /></property>
<property name="c10" type="java.lang.String"><column name="C10" length="500" /></property>
<property name="c11" type="java.lang.String"><column name="C11" length="500" /></property>
<property name="c12" type="java.lang.String"><column name="C12" length="500" /></property>
<property name="c13" type="java.lang.String"><column name="C13" length="500" /></property>
<property name="c14" type="java.lang.String"><column name="C14" length="500" /></property>
<property name="c15" type="java.lang.String"><column name="C15" length="500" /></property>
<property name="c16" type="java.lang.String"><column name="C16" length="500" /></property>
<property name="c17" type="java.lang.String"><column name="C17" length="500" /></property>
<property name="c18" type="java.lang.String"><column name="C18" length="500" /></property>
<property name="c19" type="java.lang.String"><column name="C19" length="500" /></property>
<property name="c20" type="java.lang.String"><column name="C20" length="500" /></property>
<property name="c21" type="java.lang.String"><column name="C21" length="500" /></property>
<property name="c22" type="java.lang.String"><column name="C22" length="500" /></property>
<property name="c23" type="java.lang.String"><column name="C23" length="500" /></property>
<property name="c24" type="java.lang.String"><column name="C24" length="500" /></property>
<property name="c25" type="java.lang.String"><column name="C25" length="500" /></property>
<property name="c26" type="java.lang.String"><column name="C26" length="500" /></property>
<property name="c27" type="java.lang.String"><column name="C27" length="500" /></property>
<property name="c28" type="java.lang.String"><column name="C28" length="500" /></property>
<property name="c29" type="java.lang.String"><column name="C29" length="500" /></property>
<property name="c30" type="java.lang.String"><column name="C30" length="500" /></property>
<property name="c31" type="java.lang.String"><column name="C31" length="500" /></property>
<property name="c32" type="java.lang.String"><column name="C32" length="500" /></property>
<property name="c33" type="java.lang.String"><column name="C33" length="500" /></property>
<property name="c34" type="java.lang.String"><column name="C34" length="500" /></property>
<property name="c35" type="java.lang.String"><column name="C35" length="500" /></property>
<property name="c36" type="java.lang.String"><column name="C36" length="500" /></property>
<property name="c37" type="java.lang.String"><column name="C37" length="500" /></property>
<property name="c38" type="java.lang.String"><column name="C38" length="500" /></property>
<property name="c39" type="java.lang.String"><column name="C39" length="500" /></property>
<property name="c40" type="java.lang.String"><column name="C40" length="500" /></property>
<property name="c41" type="java.lang.String"><column name="C41" length="500" /></property>
<property name="c42" type="java.lang.String"><column name="C42" length="500" /></property>
<property name="c43" type="java.lang.String"><column name="C43" length="500" /></property>
<property name="c44" type="java.lang.String"><column name="C44" length="500" /></property>
<property name="c45" type="java.lang.String"><column name="C45" length="500" /></property>
<property name="c46" type="java.lang.String"><column name="C46" length="500" /></property>
<property name="c47" type="java.lang.String"><column name="C47" length="500" /></property>
<property name="c48" type="java.lang.String"><column name="C48" length="500" /></property>
<property name="c49" type="java.lang.String"><column name="C49" length="500" /></property>
<property name="c50" type="java.lang.String"><column name="C50" length="500" /></property>
<property name="c51" type="java.lang.String"><column name="C51" length="500" /></property>
<property name="c52" type="java.lang.String"><column name="C52" length="500" /></property>
<property name="c53" type="java.lang.String"><column name="C53" length="500" /></property>
<property name="c54" type="java.lang.String"><column name="C54" length="500" /></property>
<property name="c55" type="java.lang.String"><column name="C55" length="500" /></property>
<property name="c56" type="java.lang.String"><column name="C56" length="500" /></property>
<property name="c57" type="java.lang.String"><column name="C57" length="500" /></property>
<property name="c58" type="java.lang.String"><column name="C58" length="500" /></property>
<property name="c59" type="java.lang.String"><column name="C59" length="500" /></property>
<property name="c60" type="java.lang.String"><column name="C60" length="500" /></property>
<property name="c61" type="java.lang.String"><column name="C61" length="500" /></property>
<property name="c62" type="java.lang.String"><column name="C62" length="500" /></property>
<property name="c63" type="java.lang.String"><column name="C63" length="500" /></property>
<property name="c64" type="java.lang.String"><column name="C64" length="500" /></property>
<property name="c65" type="java.lang.String"><column name="C65" length="500" /></property>
<property name="c66" type="java.lang.String"><column name="C66" length="500" /></property>
<property name="c67" type="java.lang.String"><column name="C67" length="500" /></property>
<property name="c68" type="java.lang.String"><column name="C68" length="500" /></property>
<property name="c69" type="java.lang.String"><column name="C69" length="500" /></property>
<property name="c70" type="java.lang.String"><column name="C70" length="500" /></property>
<property name="c71" type="java.lang.String"><column name="C71" length="500" /></property>
<property name="c72" type="java.lang.String"><column name="C72" length="500" /></property>
<property name="c73" type="java.lang.String"><column name="C73" length="500" /></property>
<property name="c74" type="java.lang.String"><column name="C74" length="500" /></property>
<property name="c75" type="java.lang.String"><column name="C75" length="500" /></property>
<property name="c76" type="java.lang.String"><column name="C76" length="500" /></property>
<property name="c77" type="java.lang.String"><column name="C77" length="500" /></property>
<property name="c78" type="java.lang.String"><column name="C78" length="500" /></property>
<property name="c79" type="java.lang.String"><column name="C79" length="500" /></property>
<property name="c80" type="java.lang.String"><column name="C80" length="500" /></property>
<property name="c81" type="java.lang.String"><column name="C81" length="500" /></property>
<property name="c82" type="java.lang.String"><column name="C82" length="500" /></property>
<property name="c83" type="java.lang.String"><column name="C83" length="500" /></property>
<property name="c84" type="java.lang.String"><column name="C84" length="500" /></property>
<property name="c85" type="java.lang.String"><column name="C85" length="500" /></property>
<property name="c86" type="java.lang.String"><column name="C86" length="500" /></property>
<property name="c87" type="java.lang.String"><column name="C87" length="500" /></property>
<property name="c88" type="java.lang.String"><column name="C88" length="500" /></property>
<property name="c89" type="java.lang.String"><column name="C89" length="500" /></property>
<property name="c90" type="java.lang.String"><column name="C90" length="500" /></property>
<property name="c91" type="java.lang.String"><column name="C91" length="500" /></property>
<property name="c92" type="java.lang.String"><column name="C92" length="500" /></property>
<property name="c93" type="java.lang.String"><column name="C93" length="500" /></property>
<property name="c94" type="java.lang.String"><column name="C94" length="500" /></property>
<property name="c95" type="java.lang.String"><column name="C95" length="500" /></property>
<property name="c96" type="java.lang.String"><column name="C96" length="500" /></property>
<property name="c97" type="java.lang.String"><column name="C97" length="500" /></property>
<property name="c98" type="java.lang.String"><column name="C98" length="500" /></property>
<property name="c99" type="java.lang.String"><column name="C99" length="500" /></property>
<property name="c100" type="java.lang.String"><column name="C100" length="500" /></property>
<property name="c101" type="java.lang.String"><column name="C101" length="500" /></property>
<property name="c102" type="java.lang.String"><column name="C102" length="500" /></property>
<property name="c103" type="java.lang.String"><column name="C103" length="500" /></property>
<property name="c104" type="java.lang.String"><column name="C104" length="500" /></property>
<property name="c105" type="java.lang.String"><column name="C105" length="500" /></property>
<property name="c106" type="java.lang.String"><column name="C106" length="500" /></property>
<property name="c107" type="java.lang.String"><column name="C107" length="500" /></property>
<property name="c108" type="java.lang.String"><column name="C108" length="500" /></property>
<property name="c17u01" type="java.lang.String"><column name="C17U01" length="100" /></property>
<property name="c17u02" type="java.lang.String"><column name="C17U02" length="100" /></property>
<property name="c17u03" type="java.lang.String"><column name="C17U03" length="100" /></property>
<property name="c17u04" type="java.lang.String"><column name="C17U04" length="100" /></property>
<property name="c17u05" type="java.lang.String"><column name="C17U05" length="100" /></property>
<property name="c17u06" type="java.lang.String"><column name="C17U06" length="100" /></property>
<property name="c18u01" type="java.lang.String"><column name="C18U01" length="100" /></property>
<property name="c18u02" type="java.lang.String"><column name="C18U02" length="100" /></property>
<property name="c18u03" type="java.lang.String"><column name="C18U03" length="100" /></property>
<property name="c18u04" type="java.lang.String"><column name="C18U04" length="100" /></property>
<property name="c18u05" type="java.lang.String"><column name="C18U05" length="100" /></property>
<property name="c19u01" type="java.lang.String"><column name="C19U01" length="100" /></property>
<property name="c19u02" type="java.lang.String"><column name="C19U02" length="100" /></property>
<property name="c19u03" type="java.lang.String"><column name="C19U03" length="100" /></property>
<property name="c19u04" type="java.lang.String"><column name="C19U04" length="100" /></property>
<property name="c20u01" type="java.lang.String"><column name="C20U01" length="100" /></property>
<property name="c20u02" type="java.lang.String"><column name="C20U02" length="100" /></property>
<property name="c20u03" type="java.lang.String"><column name="C20U03" length="100" /></property>
<property name="c20u04" type="java.lang.String"><column name="C20U04" length="100" /></property>
<property name="c20u05" type="java.lang.String"><column name="C20U05" length="100" /></property>
<property name="c21u01" type="java.lang.String"><column name="C21U01" length="100" /></property>
<property name="c21u02" type="java.lang.String"><column name="C21U02" length="100" /></property>
<property name="c22u01" type="java.lang.String"><column name="C22U01" length="100" /></property>
<property name="c22u02" type="java.lang.String"><column name="C22U02" length="100" /></property>
<property name="c24u01" type="java.lang.String"><column name="C24U01" length="100" /></property>
<property name="c24u02" type="java.lang.String"><column name="C24U02" length="100" /></property>
<property name="c25u01" type="java.lang.String"><column name="C25U01" length="100" /></property>
<property name="c25u02" type="java.lang.String"><column name="C25U02" length="100" /></property>
<property name="c25u03" type="java.lang.String"><column name="C25U03" length="100" /></property>
<property name="c25u04" type="java.lang.String"><column name="C25U04" length="100" /></property>
<property name="c26u01" type="java.lang.String"><column name="C26U01" length="100" /></property>
<property name="c27u01" type="java.lang.String"><column name="C27U01" length="100" /></property>
<property name="c28u01" type="java.lang.String"><column name="C28U01" length="100" /></property>
<property name="c29u01" type="java.lang.String"><column name="C29U01" length="100" /></property>
<property name="c30u01" type="java.lang.String"><column name="C30U01" length="100" /></property>
<property name="c31u01" type="java.lang.String"><column name="C31U01" length="100" /></property>
<property name="c32u01" type="java.lang.String"><column name="C32U01" length="100" /></property>
<property name="c33u01" type="java.lang.String"><column name="C33U01" length="100" /></property>
<property name="c35u01" type="java.lang.String"><column name="C35U01" length="100" /></property>
<property name="c36u01" type="java.lang.String"><column name="C36U01" length="100" /></property>
<property name="c36u02" type="java.lang.String"><column name="C36U02" length="100" /></property>
<property name="c37u01" type="java.lang.String"><column name="C37U01" length="100" /></property>
<property name="c40u01" type="java.lang.String"><column name="C40U01" length="100" /></property>
<property name="c41u01" type="java.lang.String"><column name="C41U01" length="100" /></property>
<property name="c42u01" type="java.lang.String"><column name="C42U01" length="100" /></property>
<property name="c43u01" type="java.lang.String"><column name="C43U01" length="100" /></property>
<property name="c44u01" type="java.lang.String"><column name="C44U01" length="100" /></property>
<property name="c45u01" type="java.lang.String"><column name="C45U01" length="100" /></property>
<property name="c46u01" type="java.lang.String"><column name="C46U01" length="100" /></property>
<property name="c48u01" type="java.lang.String"><column name="C48U01" length="100" /></property>
<property name="c48u02" type="java.lang.String"><column name="C48U02" length="100" /></property>
<property name="c49u01" type="java.lang.String"><column name="C49U01" length="100" /></property>
<property name="c50u01" type="java.lang.String"><column name="C50U01" length="100" /></property>
<property name="c50c1" type="java.lang.String"><column name="C50C1" length="100" /></property>
<property name="c50c1u01" type="java.lang.String"><column name="C50C1U01" length="100" /></property>
<property name="c52u01" type="java.lang.String"><column name="C52U01" length="100" /></property>
<property name="c52u02" type="java.lang.String"><column name="C52U02" length="100" /></property>
<property name="c53u01" type="java.lang.String"><column name="C53U01" length="100" /></property>
<property name="c53u02" type="java.lang.String"><column name="C53U02" length="100" /></property>
<property name="c54u01" type="java.lang.String"><column name="C54U01" length="100" /></property>
<property name="c54u02" type="java.lang.String"><column name="C54U02" length="100" /></property>
<property name="c55u01" type="java.lang.String"><column name="C55U01" length="100" /></property>
<property name="c56u01" type="java.lang.String"><column name="C56U01" length="100" /></property>
<property name="c56u02" type="java.lang.String"><column name="C56U02" length="100" /></property>
<property name="c59u01" type="java.lang.String"><column name="C59U01" length="100" /></property>
<property name="c60u01" type="java.lang.String"><column name="C60U01" length="100" /></property>
<property name="c60u02" type="java.lang.String"><column name="C60U02" length="100" /></property>
<property name="c61u01" type="java.lang.String"><column name="C61U01" length="100" /></property>
<property name="c61u02" type="java.lang.String"><column name="C61U02" length="100" /></property>
<property name="c61u03" type="java.lang.String"><column name="C61U03" length="100" /></property>
<property name="c61u04" type="java.lang.String"><column name="C61U04" length="100" /></property>
<property name="c61u05" type="java.lang.String"><column name="C61U05" length="100" /></property>
<property name="c61u06" type="java.lang.String"><column name="C61U06" length="100" /></property>
<property name="c61u07" type="java.lang.String"><column name="C61U07" length="100" /></property>
<property name="c61u08" type="java.lang.String"><column name="C61U08" length="100" /></property>
<property name="c61u11" type="java.lang.String"><column name="C61U11" length="100" /></property>
<property name="c61u12" type="java.lang.String"><column name="C61U12" length="100" /></property>
<property name="c61u13" type="java.lang.String"><column name="C61U13" length="100" /></property>
<property name="c61u14" type="java.lang.String"><column name="C61U14" length="100" /></property>
<property name="c61u15" type="java.lang.String"><column name="C61U15" length="100" /></property>
<property name="c61u16" type="java.lang.String"><column name="C61U16" length="100" /></property>
<property name="c61u17" type="java.lang.String"><column name="C61U17" length="100" /></property>
<property name="c61u18" type="java.lang.String"><column name="C61U18" length="100" /></property>
<property name="c65u01" type="java.lang.String"><column name="C65U01" length="100" /></property>
<property name="c65u02" type="java.lang.String"><column name="C65U02" length="100" /></property>
<property name="c65u03" type="java.lang.String"><column name="C65U03" length="100" /></property>
<property name="c65u04" type="java.lang.String"><column name="C65U04" length="100" /></property>
<property name="c65u05" type="java.lang.String"><column name="C65U05" length="100" /></property>
<property name="c69u01" type="java.lang.String"><column name="C69U01" length="100" /></property>
<property name="c70u01" type="java.lang.String"><column name="C70U01" length="100" /></property>
<property name="c71u01" type="java.lang.String"><column name="C71U01" length="100" /></property>
<property name="c72u01" type="java.lang.String"><column name="C72U01" length="100" /></property>
<property name="c80u01" type="java.lang.String"><column name="C80U01" length="100" /></property>
<property name="c81c1" type="java.lang.String"><column name="C81C1" length="100" /></property>
<property name="c81c2" type="java.lang.String"><column name="C81C2" length="100" /></property>
<property name="c81c3" type="java.lang.String"><column name="C81C3" length="100" /></property>
<property name="c81c4" type="java.lang.String"><column name="C81C4" length="100" /></property>
<property name="c81c3u01" type="java.lang.String"><column name="C81C3U01" length="100" /></property>
<property name="c81c3u02" type="java.lang.String"><column name="C81C3U02" length="100" /></property>
<property name="c81c3u03" type="java.lang.String"><column name="C81C3U03" length="100" /></property>
<property name="c81c3u04" type="java.lang.String"><column name="C81C3U04" length="100" /></property>
<property name="c81c3u05" type="java.lang.String"><column name="C81C3U05" length="100" /></property>
<property name="c81c3u06" type="java.lang.String"><column name="C81C3U06" length="100" /></property>
<property name="c81c3u07" type="java.lang.String"><column name="C81C3U07" length="100" /></property>
<property name="c81c3u08" type="java.lang.String"><column name="C81C3U08" length="100" /></property>
<property name="c81c3u09" type="java.lang.String"><column name="C81C3U09" length="100" /></property>
<property name="c81c5" type="java.lang.String"><column name="C81C5" length="100" /></property>
<property name="c81c5u01" type="java.lang.String"><column name="C81C5U01" length="100" /></property>
<property name="c81c5u02" type="java.lang.String"><column name="C81C5U02" length="100" /></property>
<property name="c81c5u03" type="java.lang.String"><column name="C81C5U03" length="100" /></property>
<property name="c81c6" type="java.lang.String"><column name="C81C6" length="100" /></property>
<property name="c81c6u01" type="java.lang.String"><column name="C81C6U01" length="100" /></property>
<property name="c81c6u02" type="java.lang.String"><column name="C81C6U02" length="100" /></property>
<property name="c81c6u03" type="java.lang.String"><column name="C81C6U03" length="100" /></property>
<property name="c81c7" type="java.lang.String"><column name="C81C7" length="100" /></property>
<property name="c82u01" type="java.lang.String"><column name="C82U01" length="100" /></property>
<property name="c83u01" type="java.lang.String"><column name="C83U01" length="100" /></property>
<property name="c84u01" type="java.lang.String"><column name="C84U01" length="100" /></property>
<property name="c85u01" type="java.lang.String"><column name="C85U01" length="100" /></property>
<property name="c86u01" type="java.lang.String"><column name="C86U01" length="100" /></property>
<property name="c87u01" type="java.lang.String"><column name="C87U01" length="100" /></property>
<property name="c88u01" type="java.lang.String"><column name="C88U01" length="100" /></property>
<property name="c89u01" type="java.lang.String"><column name="C89U01" length="100" /></property>
<property name="c90u01" type="java.lang.String"><column name="C90U01" length="100" /></property>
<property name="c91u01" type="java.lang.String"><column name="C91U01" length="100" /></property>
<property name="c92u01" type="java.lang.String"><column name="C92U01" length="100" /></property>
<property name="c93u01" type="java.lang.String"><column name="C93U01" length="100" /></property>
<property name="c94u01" type="java.lang.String"><column name="C94U01" length="100" /></property>
<property name="c95u01" type="java.lang.String"><column name="C95U01" length="100" /></property>
<property name="c96u01" type="java.lang.String"><column name="C96U01" length="100" /></property>
<property name="c98u01" type="java.lang.String"><column name="C98U01" length="100" /></property>
<property name="c99u01" type="java.lang.String"><column name="C99U01" length="100" /></property>
<property name="c100u01" type="java.lang.String"><column name="C100U01" length="100" /></property>
<property name="c101c1" type="java.lang.String"><column name="C101C1" length="100" /></property>
<property name="c101c2" type="java.lang.String"><column name="C101C2" length="100" /></property>
<property name="c101c3" type="java.lang.String"><column name="C101C3" length="100" /></property>
<property name="c101c4" type="java.lang.String"><column name="C101C4" length="100" /></property>
<property name="c101c5" type="java.lang.String"><column name="C101C5" length="100" /></property>
<property name="c101c6" type="java.lang.String"><column name="C101C6" length="100" /></property>
<property name="c101c7" type="java.lang.String"><column name="C101C7" length="100" /></property>
<property name="c101c8" type="java.lang.String"><column name="C101C8" length="100" /></property>
<property name="c101c9" type="java.lang.String"><column name="C101C9" length="100" /></property>
<property name="c101c10" type="java.lang.String"><column name="C101C10" length="100" /></property>
<property name="c101c11" type="java.lang.String"><column name="C101C11" length="100" /></property>
<property name="c101c12" type="java.lang.String"><column name="C101C12" length="100" /></property>
<property name="c101c13" type="java.lang.String"><column name="C101C13" length="100" /></property>
<property name="c101c14" type="java.lang.String"><column name="C101C14" length="100" /></property>
<property name="c101c15" type="java.lang.String"><column name="C101C15" length="100" /></property>
<property name="c101c16" type="java.lang.String"><column name="C101C16" length="100" /></property>
<property name="c101c17" type="java.lang.String"><column name="C101C17" length="100" /></property>
<property name="c101c18" type="java.lang.String"><column name="C101C18" length="100" /></property>
<property name="c101c19" type="java.lang.String"><column name="C101C19" length="100" /></property>
<property name="c101c20" type="java.lang.String"><column name="C101C20" length="100" /></property>
<property name="c101c21" type="java.lang.String"><column name="C101C21" length="100" /></property>
<property name="c101u01" type="java.lang.String"><column name="C101U01" length="100" /></property>
<property name="c101u02" type="java.lang.String"><column name="C101U02" length="100" /></property>
<property name="c101u03" type="java.lang.String"><column name="C101U03" length="100" /></property>
<property name="c101u04" type="java.lang.String"><column name="C101U04" length="100" /></property>
<property name="c101u05" type="java.lang.String"><column name="C101U05" length="100" /></property>
<property name="c101u06" type="java.lang.String"><column name="C101U06" length="100" /></property>
<property name="c101u07" type="java.lang.String"><column name="C101U07" length="100" /></property>
<property name="c101u08" type="java.lang.String"><column name="C101U08" length="100" /></property>
<property name="c101u09" type="java.lang.String"><column name="C101U09" length="100" /></property>
<property name="c101u10" type="java.lang.String"><column name="C101U10" length="100" /></property>
<property name="c101u11" type="java.lang.String"><column name="C101U11" length="100" /></property>
<property name="c101u12" type="java.lang.String"><column name="C101U12" length="100" /></property>
<property name="c101u13" type="java.lang.String"><column name="C101U13" length="100" /></property>
<property name="c101u14" type="java.lang.String"><column name="C101U14" length="100" /></property>
<property name="c101u15" type="java.lang.String"><column name="C101U15" length="100" /></property>
<property name="c101u16" type="java.lang.String"><column name="C101U16" length="100" /></property>
<property name="c101u17" type="java.lang.String"><column name="C101U17" length="100" /></property>
<property name="c101u18" type="java.lang.String"><column name="C101U18" length="100" /></property>
<property name="c101u19" type="java.lang.String"><column name="C101U19" length="100" /></property>
<property name="c101u20" type="java.lang.String"><column name="C101U20" length="100" /></property>
<property name="c101u21" type="java.lang.String"><column name="C101U21" length="100" /></property>
<property name="c102c1" type="java.lang.String"><column name="C102C1" length="100" /></property>
<property name="c102c2" type="java.lang.String"><column name="C102C2" length="100" /></property>
<property name="c102c3" type="java.lang.String"><column name="C102C3" length="100" /></property>
<property name="c102c4" type="java.lang.String"><column name="C102C4" length="100" /></property>
<property name="c102u01" type="java.lang.String"><column name="C102U01" length="100" /></property>
<property name="c102u02" type="java.lang.String"><column name="C102U02" length="100" /></property>
<property name="c102u03" type="java.lang.String"><column name="C102U03" length="100" /></property>
<property name="c102u04" type="java.lang.String"><column name="C102U04" length="100" /></property>
<property name="c107u01" type="java.lang.String"><column name="C107U01" length="100" /></property>
<property name="c108u01" type="java.lang.String"><column name="C108U01" length="100" /></property>
<property name="c109u02" type="java.lang.String"><column name="C109U02" length="100" /></property>
<property name="c109u05" type="java.lang.String"><column name="C109U05" length="100" /></property>
<property name="c109u06" type="java.lang.String"><column name="C109U06" length="100" /></property>
<property name="c109u07" type="java.lang.String"><column name="C109U07" length="100" /></property>
<property name="c109u08" type="java.lang.String"><column name="C109U08" length="100" /></property>
<property name="c109u09" type="java.lang.String"><column name="C109U09" length="100" /></property>
<property name="c109u10" type="java.lang.String"><column name="C109U10" length="100" /></property>
        
    </class>
</hibernate-mapping>
