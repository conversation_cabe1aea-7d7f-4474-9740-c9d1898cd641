package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service;

import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Holiday;

public interface HolidayService {
	public void addHoliday(Holiday obj)throws DataAccessException;
	public void updateHoliday(Holiday obj)throws DataAccessException;
	public List<?> pagination(Page page,Map<String,String> params) throws DataAccessException;

}
