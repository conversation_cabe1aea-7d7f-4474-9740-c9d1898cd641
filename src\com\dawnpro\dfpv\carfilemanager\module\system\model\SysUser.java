package com.dawnpro.dfpv.carfilemanager.module.system.model;
/**
 * SysUser entity. <AUTHOR> Persistence Tools
 */

public class SysUser  implements java.io.Serializable {


    // Fields    

     private String loginname;
     private String password;
     private String username;
     private String state;
     private String roleid;
     private String rolename;
     private String remark;
     private String creator;
     private String time;


    // Constructors

    /** default constructor */
    public SysUser() {
    }

	/** minimal constructor */
    public SysUser(String loginname, String password, String state, String roleid, String creator, String time) {
        this.loginname = loginname;
        this.password = password;
        this.state = state;
        this.roleid = roleid;
        this.creator = creator;
        this.time = time;
    }
    
    /** full constructor */
    public SysUser(String loginname, String password, String username, String state, String roleid, String remark, String creator, String time) {
        this.loginname = loginname;
        this.password = password;
        this.username = username;
        this.state = state;
        this.roleid = roleid;
        this.remark = remark;
        this.creator = creator;
        this.time = time;
    }
    
    public String getRolename() {
		return rolename;
	}

	public void setRolename(String rolename) {
		this.rolename = rolename;
	}
   
    // Property accessors

    public String getLoginname() {
        return this.loginname;
    }
    
    public void setLoginname(String loginname) {
        this.loginname = loginname;
    }

    public String getPassword() {
        return this.password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return this.username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }

    public String getState() {
        return this.state;
    }
    
    public void setState(String state) {
        this.state = state;
    }

    public String getRoleid() {
		return roleid;
	}

	public void setRoleid(String roleid) {
		this.roleid = roleid;
	}

    public String getRemark() {
        return this.remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return this.creator;
    }
    
    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getTime() {
        return this.time;
    }
    
    public void setTime(String time) {
        this.time = time;
    }
   








}