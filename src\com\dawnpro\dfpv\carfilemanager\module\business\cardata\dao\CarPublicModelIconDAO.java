package com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao;

import java.io.Serializable;
import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarPublicModelIcon;

public interface CarPublicModelIconDAO {
	public void addCarPublicModelIcon(CarPublicModelIcon obj);
	public void updateCarPublicModelIcon(CarPublicModelIcon obj);
	public void deleteCarPublicModelIcon(Serializable id);
	public void deleteCarPublicModelIcon(Serializable[] obj);
	public CarPublicModelIcon loadCarPublicModelIcon(Serializable id);
	public List<CarPublicModelIcon> findCarPublicModelIcon(String sql);
	public List<CarPublicModelIcon> findCarPublicModelIcon(String sql, Object[] params);
}
