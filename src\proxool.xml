<?xml version="1.0" encoding="UTF-8"?>
<something-else-entirely>  
    <proxool>
        <alias>DBPool</alias>   
      <!--  <driver-url>***************************************</driver-url>  -->
      <!--  <driver-url>*****************************************</driver-url>-->
       <!--driver-url>**************************************</driver-url-->
       <driver-url>*********************************************</driver-url><!--  ************* --> 
        <!--<driver-url>*******************************************</driver-url>  --> 
        <driver-class>oracle.jdbc.driver.OracleDriver</driver-class> 
        <!-- <driver-properties>  
            <property name="user" value="vfile" />
            <property name="password" value="admin" />
        </driver-properties>   -->
         
        <driver-properties>  
            <property name="user" value="v_file" />  
            <property name="password" value="EbzZaiJ3rRE8Ji" /> 
        </driver-properties><!--本地虚机 -->  
                 
		<!--140 vfile数据库 
		<driver-properties>  
            <property name="user" value="v_file" />
            <property name="password" value="v_file20091210" />
        </driver-properties>    -->   
        <maximum-connection-count>50</maximum-connection-count>    
        <minimum-connection-count>15</minimum-connection-count>    
        <maximum-new-connections>30</maximum-new-connections>    
        <prototype-count>10</prototype-count>
        <prototype-count>5</prototype-count>
        <house-keeping-sleep-time>90000</house-keeping-sleep-time>  
        <maximum-connection-lifetime>18000000</maximum-connection-lifetime>    
        <test-before-use>true</test-before-use>  
        <house-keeping-test-sql>select sysdate from dual</house-keeping-test-sql>  
        <statistics>1m,15m,1d</statistics>
		<statistics-log-level>INFO</statistics-log-level>
		<fatal-sql-exception>Connection is closed,SQLSTATE=08003,Error opening socket. SQLSTATE=08S01,SQLSTATE=08S01</fatal-sql-exception>
		<fatal-sql-exception-wrapper-class>org.logicalcobwebs.proxool.FatalRuntimeException</fatal-sql-exception-wrapper-class>
    </proxool>  
</something-else-entirely>  