package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

/**
 * CarProductionModel entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class CarProductionModel implements java.io.Serializable {

	// Fields

	private CarProductionModelId id;
	private String modelName;
	private String pmodelCode;
	private String brandCode;
	private String brandName;
	private String decorationStaCode;
	private String decorationStaName;
	private String emissionStaCode;
	private String emissionStaName;
	private String engineTypeCode;
	private String engineTypeName;
	private String isSunRoof;
	private String isSunRoofName;
	private String modelKindCode;
	private String modelKindName;
	private String modelTypeCode;
	private String modelTypeName;
	private String safetyRestraintCode;
	private String safetyRestraintName;
	private String transmissionCode;
	private String transmissionName;
	private String state;
	private String creator;
	private String time;
	private String deliveryCapacity;
	private String flag;
	
	private String mpflag;
	
	private String activetime;
	private String publishtime;

	// Constructors

	/** default constructor */
	public CarProductionModel() {
	}

	/** minimal constructor */
	public CarProductionModel(CarProductionModelId id) {
		this.id = id;
	}

	/** full constructor */
	public CarProductionModel(CarProductionModelId id, String modelName,
			String pmodelCode, String brandCode, String brandName,
			String decorationStaCode, String decorationStaName,
			String emissionStaCode, String emissionStaName,
			String engineTypeCode, String engineTypeName, String isSunRoof,
			String modelKindCode, String modelKindName, String modelTypeCode,
			String modelTypeName, String safetyRestraintCode,
			String safetyRestraintName, String transmissionCode,
			String transmissionName, String state, String creator, String time,String deliveryCapacity,String flag) {
		this.id = id;
		this.modelName = modelName;
		this.pmodelCode = pmodelCode;
		this.brandCode = brandCode;
		this.brandName = brandName;
		this.decorationStaCode = decorationStaCode;
		this.decorationStaName = decorationStaName;
		this.emissionStaCode = emissionStaCode;
		this.emissionStaName = emissionStaName;
		this.engineTypeCode = engineTypeCode;
		this.engineTypeName = engineTypeName;
		this.isSunRoof = isSunRoof;
		this.modelKindCode = modelKindCode;
		this.modelKindName = modelKindName;
		this.modelTypeCode = modelTypeCode;
		this.modelTypeName = modelTypeName;
		this.safetyRestraintCode = safetyRestraintCode;
		this.safetyRestraintName = safetyRestraintName;
		this.transmissionCode = transmissionCode;
		this.transmissionName = transmissionName;
		this.state = state;
		this.creator = creator;
		this.time = time;
		this.deliveryCapacity = deliveryCapacity;
		this.flag=flag;
	}

	// Property accessors

	public CarProductionModelId getId() {
		return this.id;
	}

	public void setId(CarProductionModelId id) {
		this.id = id;
	}

	public String getModelName() {
		return this.modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public String getPmodelCode() {
		return this.pmodelCode;
	}

	public void setPmodelCode(String pmodelCode) {
		this.pmodelCode = pmodelCode;
	}

	public String getBrandCode() {
		return this.brandCode;
	}

	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}

	public String getBrandName() {
		return this.brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getDecorationStaCode() {
		return this.decorationStaCode;
	}

	public void setDecorationStaCode(String decorationStaCode) {
		this.decorationStaCode = decorationStaCode;
	}

	public String getDecorationStaName() {
		return this.decorationStaName;
	}

	public void setDecorationStaName(String decorationStaName) {
		this.decorationStaName = decorationStaName;
	}

	public String getEmissionStaCode() {
		return this.emissionStaCode;
	}

	public void setEmissionStaCode(String emissionStaCode) {
		this.emissionStaCode = emissionStaCode;
	}

	public String getEmissionStaName() {
		return this.emissionStaName;
	}

	public void setEmissionStaName(String emissionStaName) {
		this.emissionStaName = emissionStaName;
	}

	public String getEngineTypeCode() {
		return this.engineTypeCode;
	}

	public void setEngineTypeCode(String engineTypeCode) {
		this.engineTypeCode = engineTypeCode;
	}

	public String getEngineTypeName() {
		return this.engineTypeName;
	}

	public void setEngineTypeName(String engineTypeName) {
		this.engineTypeName = engineTypeName;
	}

	public String getIsSunRoof() {
		return this.isSunRoof;
	}

	public void setIsSunRoof(String isSunRoof) {
		this.isSunRoof = isSunRoof;
	}

	public String getModelKindCode() {
		return this.modelKindCode;
	}

	public void setModelKindCode(String modelKindCode) {
		this.modelKindCode = modelKindCode;
	}

	public String getModelKindName() {
		return this.modelKindName;
	}

	public void setModelKindName(String modelKindName) {
		this.modelKindName = modelKindName;
	}

	public String getModelTypeCode() {
		return this.modelTypeCode;
	}

	public void setModelTypeCode(String modelTypeCode) {
		this.modelTypeCode = modelTypeCode;
	}

	public String getModelTypeName() {
		return this.modelTypeName;
	}

	public void setModelTypeName(String modelTypeName) {
		this.modelTypeName = modelTypeName;
	}

	public String getSafetyRestraintCode() {
		return this.safetyRestraintCode;
	}

	public void setSafetyRestraintCode(String safetyRestraintCode) {
		this.safetyRestraintCode = safetyRestraintCode;
	}

	public String getSafetyRestraintName() {
		return this.safetyRestraintName;
	}

	public void setSafetyRestraintName(String safetyRestraintName) {
		this.safetyRestraintName = safetyRestraintName;
	}

	public String getTransmissionCode() {
		return this.transmissionCode;
	}

	public void setTransmissionCode(String transmissionCode) {
		this.transmissionCode = transmissionCode;
	}

	public String getTransmissionName() {
		return this.transmissionName;
	}

	public void setTransmissionName(String transmissionName) {
		this.transmissionName = transmissionName;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getIsSunRoofName() {
		return isSunRoofName;
	}

	public void setIsSunRoofName(String isSunRoofName) {
		this.isSunRoofName = isSunRoofName;
	}
	
	public String getDeliveryCapacity() {
		return deliveryCapacity;
	}

	public void setDeliveryCapacity(String deliveryCapacity) {
		this.deliveryCapacity = deliveryCapacity;
	}
	
	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	/**
	 * @return the mpflag
	 */
	public String getMpflag() {
		return mpflag;
	}

	/**
	 * @param mpflag the mpflag to set
	 */
	public void setMpflag(String mpflag) {
		this.mpflag = mpflag;
	}

	public String getActivetime() {
		return activetime;
	}

	public void setActivetime(String activetime) {
		this.activetime = activetime;
	}

	public String getPublishtime() {
		return publishtime;
	}

	public void setPublishtime(String publishtime) {
		this.publishtime = publishtime;
	}
	
	
}