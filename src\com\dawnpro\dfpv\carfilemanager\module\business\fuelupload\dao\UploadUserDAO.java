package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Uploaduser;

public interface UploadUserDAO {
	public void addUploadUser(Uploaduser obj);
	public void updateUploadUser(Uploaduser obj);
	public void deleteUploadUser(Uploaduser obj);
	public List<Uploaduser> findUploadUser(String sql);
}
