<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="EclipseModuleManager">
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/Qrcode_encoder.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/antlr-2.7.6.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/asm-2.2.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/asm-commons-2.2.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/asm-util-2.2.3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/aspectjrt.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/aspectjweaver.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/avalon-framework-4.2.0.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/barcode4j.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/cglib-nodep-2.1_3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/classes12.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/com.ibm.mq.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/com.ibm.mqjms.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-beanutils.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-collections-3.2.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-compress-1.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-fileupload-1.2.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-io-1.4.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-lang-2.4.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/connector.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/dhbcore.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/dom4j-1.6.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/ehcache-1.3.0.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/ezmorph-1.0.4.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/freemarker-2.3.8.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/geronimo-j2ee-management_1.0_spec-1.0.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/geronimo-jms_1.1_spec-1.0.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/groovy-all-1.5.5.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/hibernate3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jasperreports-3.6.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jaxen-1.1-beta-7.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jcommon-1.0.13.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/json-lib-2.2.1-jdk15.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jsonplugin-0.32.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jta.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jxl.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/log4j-1.2.14.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/metadata-extractor-2.3.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/ognl-2.6.11.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/plugin.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/proxool-0.9.0RC3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/rt.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/servlet-api.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-aop.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-beans.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-context.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-core.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-hibernate3.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-jdbc.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-tx.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-web.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/struts2-core-********.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/struts2-spring-plugin-********.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/xwork-2.0.4.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/quartz-2.0.2.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-digester.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-logging-1.1.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/iText-2.1.0.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/iTextAsian.jar!/" />
    <libelement value="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/quartz-1.6.5.jar!/" />
    <conelement value="com.genuitec.eclipse.j2eedt.core.J2EE14_CONTAINER" />
    <conelement value="org.eclipse.jst.j2ee.internal.web.container" />
    <conelement value="org.eclipse.jst.j2ee.internal.module.container" />
    <conelement value="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v7.0" />
    <src_description expected_position="0">
      <src_folder value="file://$MODULE_DIR$/src" expected_position="0" />
    </src_description>
  </component>
  <component name="NewModuleRootManager">
    <output url="file://$MODULE_DIR$/CarFileManager/WEB-INF/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
    </content>
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="library" name="com.genuitec.eclipse.j2eedt.core.J2EE14_CONTAINER" level="application" />
    <orderEntry type="module-library">
      <library name="Qrcode_encoder.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/Qrcode_encoder.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="antlr-2.7.6.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/antlr-2.7.6.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="asm-2.2.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/asm-2.2.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="asm-commons-2.2.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/asm-commons-2.2.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="asm-util-2.2.3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/asm-util-2.2.3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="aspectjrt.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/aspectjrt.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="aspectjweaver.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/aspectjweaver.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="avalon-framework-4.2.0.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/avalon-framework-4.2.0.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="barcode4j.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/barcode4j.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="cglib-nodep-2.1_3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/cglib-nodep-2.1_3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="classes12.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/classes12.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="com.ibm.mq.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/com.ibm.mq.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="com.ibm.mqjms.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/com.ibm.mqjms.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-beanutils.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-beanutils.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-collections-3.2.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-collections-3.2.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-compress-1.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-compress-1.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-fileupload-1.2.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-fileupload-1.2.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-io-1.4.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-io-1.4.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-lang-2.4.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-lang-2.4.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="connector.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/connector.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="dhbcore.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/dhbcore.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="dom4j-1.6.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/dom4j-1.6.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="ehcache-1.3.0.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/ehcache-1.3.0.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="ezmorph-1.0.4.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/ezmorph-1.0.4.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="freemarker-2.3.8.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/freemarker-2.3.8.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="geronimo-j2ee-management_1.0_spec-1.0.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/geronimo-j2ee-management_1.0_spec-1.0.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="geronimo-jms_1.1_spec-1.0.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/geronimo-jms_1.1_spec-1.0.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="groovy-all-1.5.5.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/groovy-all-1.5.5.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="hibernate3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/hibernate3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="jasperreports-3.6.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jasperreports-3.6.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="jaxen-1.1-beta-7.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jaxen-1.1-beta-7.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="jcommon-1.0.13.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jcommon-1.0.13.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="json-lib-2.2.1-jdk15.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/json-lib-2.2.1-jdk15.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="jsonplugin-0.32.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jsonplugin-0.32.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="jta.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jta.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="jxl.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/jxl.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="log4j-1.2.14.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/log4j-1.2.14.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES>
          <root url="file://C:/Users/<USER>/AppData/Local/Temp/.org.sf.feeling.decompiler1754617619736/source/log4j-1.2.14-sources.jar" />
        </SOURCES>
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="metadata-extractor-2.3.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/metadata-extractor-2.3.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="ognl-2.6.11.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/ognl-2.6.11.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="plugin.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/plugin.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="proxool-0.9.0RC3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/proxool-0.9.0RC3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="rt.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/rt.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="servlet-api.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/servlet-api.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-aop.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-aop.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-beans.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-beans.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-context.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-context.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-core.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-core.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-hibernate3.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-hibernate3.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-jdbc.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-jdbc.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-tx.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-tx.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring-web.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring-web.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES>
          <root url="file://F:/study/source/spring-framework-3.2/spring-context/src" />
        </SOURCES>
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="spring.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/spring.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="struts2-core-********.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/struts2-core-********.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES>
          <root url="file://F:/study/source/struts-2.5.20-src.zip" />
        </SOURCES>
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="struts2-spring-plugin-********.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/struts2-spring-plugin-********.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="xwork-2.0.4.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/xwork-2.0.4.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="library" name="org.eclipse.jst.j2ee.internal.web.container" level="application" />
    <orderEntry type="library" name="org.eclipse.jst.j2ee.internal.module.container" level="application" />
    <orderEntry type="module-library">
      <library name="quartz-2.0.2.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/quartz-2.0.2.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="library" name="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v7.0" level="application" />
    <orderEntry type="module-library">
      <library name="commons-digester.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-digester.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="commons-logging-1.1.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/commons-logging-1.1.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="iText-2.1.0.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/iText-2.1.0.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="iTextAsian.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/iTextAsian.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module-library">
      <library name="quartz-1.6.5.jar">
        <CLASSES>
          <root url="jar://$MODULE_DIR$/CarFileManager/WEB-INF/lib/quartz-1.6.5.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="library" name="lib" level="project" />
  </component>
</module>