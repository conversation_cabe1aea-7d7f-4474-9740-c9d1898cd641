package com.dawnpro.dfpv.carfilemanager.module.business.verManager.service;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.FuellabeltemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceData;

public interface GasVerService {
	public void addGasVer(Fuellabeltemplate obj) throws DataAccessException;
	public void addGasVer(Fuellabeltemplate[] objs) throws DataAccessException;
	public void updateGasVer(Fuellabeltemplate obj) throws DataAccessException;
	public void updateGasVer(Fuellabeltemplate[] objs) throws DataAccessException;
	public void deleteGasVer(Serializable id) throws DataAccessException;
	public void deleteGasVer(Fuellabeltemplate[] id) throws DataAccessException;
	public Fuellabeltemplate loadGasVer(Serializable id) throws DataAccessException;
	public List<?> pagination(Page page,Map<String, String> params) throws DataAccessException;
	public List<Fuellabeltemplate> findGasVer(String hql, Object[] params)throws DataAccessException;
	public List<Fuellabeltemplate> findGasVer(String hql)throws DataAccessException;
	/**
	 *
	 * @param id
	 * @return
	 */
	public Fuellabeltemplate findMaxGasVerBySccx(String c1)throws DataAccessException;
	public List<?> paginationfuel(Page page,Map<String, String> params) throws DataAccessException;
	
	public String getfxjkList(Map<String, String> params,InterfaceData log)throws DataAccessException;
	public String getCAFCList(Map<String, String> params,InterfaceData log)throws DataAccessException;
}
