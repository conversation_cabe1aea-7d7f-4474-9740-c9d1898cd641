package com.dawnpro.dfpv.carfilemanager.barcode;

import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.drew.imaging.jpeg.JpegMetadataReader;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.drew.metadata.exif.ExifDirectory;

import jxl.Cell;
import jxl.CellType;
import jxl.Sheet;
import jxl.Workbook;
 
public class Test {

	public static void main(String[] args){
		try{
			 File jpegFile = new File("E:\\B01-001-A1AA300.jpg");  
	         Metadata metadata = JpegMetadataReader.readMetadata(jpegFile);  
	         String info = null;
	         Directory exif = metadata.getDirectory(ExifDirectory.class);  
//	         info = exif.getString(ExifDirectory.TAG_EXIF_IMAGE_WIDTH);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_EXIF_IMAGE_HEIGHT);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_X_RESOLUTION);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_Y_RESOLUTION);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_MAKE);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_MODEL);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_COLOR_SPACE);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_FOCAL_LENGTH);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_FNUMBER);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_EXPOSURE_TIME);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_ISO_EQUIVALENT);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_METERING_MODE);
//	         System.out.println(info);
	         info = exif.getString(ExifDirectory.TAG_LIGHT_SOURCE);
	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_EXPOSURE_PROGRAM);
//	         System.out.println(info);
//	         info = exif.getString(ExifDirectory.TAG_EXPOSURE_BIAS);
	 
//	         System.out.println("a:"+exif.getTagName(ExifDirectory.TAG_COLOR_SPACE));
//	         Iterator tags = exif.getTagIterator();  
//	         while (tags.hasNext()) {  
//	             Tag tag = (Tag)tags.next();  
//	            System.out.println(tag.getTagName()+":"+tag.getDescription());
//	         }  
		
		}catch(Exception e){
			e.printStackTrace();
		}
	
	}
	
}
