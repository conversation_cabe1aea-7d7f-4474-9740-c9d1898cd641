<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#exportfueltar").attr("disabled", false);
				}
            }
        }
    });

	$("#export").click(function(){
		var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		location.href="uploadStateManager!export.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&menuid="+menuid;
	});
	
	
	
 

	$("#query").click(function(){
		//var frm = $("#qfrm");
		//frm.action = "business/uploadStateManager.action";
		//frm.submit();
		var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
		location.href="uploadStateManager.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
	});

	$("#update").click(function(){
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = id + this.value + ","; 
             }
      	});
      	if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要修改上传的数据！');
   	   		messageObj.dialog('open');      
   	   		return false;		
      	}else{
      		type='update';
      		$('#uploadStateManager_dialog').find("#ids").val(id);
      		$('#uploadStateManager_dialog').data('title.dialog', '修改上传').dialog('open');	
      		return false;	
      	}
		
	});
	
	
	$("#delete").click(function(){
       	var id = "";
       	var index = 0;
       	var messageObj = null;
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = id + this.value + ","; 
             }
      	});
      	if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要上传撤销的数据！');
   	   		messageObj.dialog('open');      
   	   		return false;		
      	}else{
      		type='delete';
      		$('#uploadStateManager_dialog').find("#ids").val(id);
      		$('#uploadStateManager_dialog').data('title.dialog', '上传撤销').dialog('open');	
      		return false;	
      	}
		
	});
	
	
	$('#fxjk').click(function() {
		$('#fxjk_dialog').data('title.dialog', '年份').dialog('open');	
      	return false;	
	});
	$("#fxjk_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 300,
		height: 200,  
		modal: true,
		buttons: {
			'取消': function() {
				//clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
		'保存': function() {
				var fasyears = $('#fasyears').val();
				var falfyear = $('#falfyear').val();
				
				jQuery.ajax({
				           // url: jurl,
					       url: 'business/gasVer!fxjk.action?fasyears='+fasyears+'&falfyear='+falfyear,
				           data: {'ids' : $(this).find('#ids').val()},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#fxjk_dialog');
					            var json = json2Bean(data).json;
					            if(json=="ok"){
					            	alert(json);
					            	//var dlgButton = $('.ui-dialog-buttonpane button');
									//dlgButton.attr('disabled', false);
							        //dlgButton.removeClass('ui-state-disabled');
									//updateTips(dialog.find('#validateTips'),'请选择上传成功的记录!');		
								}else{
									//var currentPage=$('#currentPage_temp').val();
									//dialog.find('#createForm')[0].action="business/uploadStateManager!updateState.action?type=3&currentPage="+currentPage;
									//dialog.find('#createForm')[0].submit();
								}
				            }
				        });	
					
				//location.href="gasVer!fxjk.action?fasyears="+fasyears+"&falfyear="+falfyear;
				$(this).dialog('close');
				//}
		}
			},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			//clear($(this));
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
				
			type = null;
		}
		
	});
	
	
	
	
	
	$('#exportfueltar').click(function() {
		$('#gasinfo_dialog').data('title.dialog', '年份').dialog('open');	
      	return false;	
		//location.href="gasVer!exportFueltar.action";
	});
	
	
	
	
	$("#gasinfo_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 300,
		height: 200,  
		modal: true,
		buttons: {
			'取消': function() {
				//clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
		'保存': function() {
				var gasyears = $('#gasyears').val();
				var halfyear = $('#halfyear').val();
				if(validate1('#gasinfo_dialog')==true){

				location.href="gasVer!exportFueltar.action?gasyears="+gasyears+"&halfyear="+halfyear;
				$(this).dialog('close');
				}
		}
			},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			//clear($(this));
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
				
			type = null;
		}
		
	});
	
	$(".display").each(function(i){
		  $(this).click(function() {			  
			//var vin=  $(this).val();
			var vin=  $(this).attr("value");			
			display(vin,"查看窗口");
		 });
	  });
	
    function display(vin,title){
		jQuery.ajax({
	        url: 'business/uploadStateManager!uploaDisplay.action',		           
	        data: {'vin' : vin}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	           var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	            var dialogObj;
	            	dialogObj = $('#uploadStateManager_display_dialog');
	            	tabsshoworhide(carObj.rllx);
				setDialogValue(dialogObj,carObj);
				dialogObj.data('title.dialog', title).dialog('open');
	        }
	    });

		return false;
	}
  
    function setDialogValue(dialogObj,jsonObj){
//		dialogObj.find(":text").each(function(i){
//			$(this).val(jsonObj[i]==null?"":jsonObj[i]);
//		}) ;
		dialogObj.find(":text").each(function(i){
			$(this).val(jsonObj[$(this).attr("name")]);
		}) ;

	}
    
        $("#uploadStateManager_display_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 1000,
		height: 550,
		modal: true,        
		buttons: {  
        	'取消': function() {
				$("#uploadStateManager_display_dialog").dialog('close');
			}
		}                            
	});
			
	$("#uploadStateManager_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 300,
		height: 200,
		modal: true,
		buttons: {
			'取消': function() {
				//clear($(this));
				//alert($(this).find("#ids").val());
				$(this).find("#reason").val('');
				$(this).find("#ids").val('');
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				if(allFields==null){
					var parent = $('#uploadStateManager_dialog');
					allFields = $([]).add(parent.find('#reason'));
				}
				allFields.removeClass('ui-state-error');
				
				if(validate('#uploadStateManager_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//	
					dlgButton.attr('disabled', 'disabled');
			        dlgButton.addClass('ui-state-disabled');
					if(type=="update"){
						jQuery.ajax({
				            url: 'business/uploadStateManager!isSuccessState.action',
					        data: {'ids' : $(this).find('#ids').val()},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#uploadStateManager_dialog');
					            if(json2Bean(data).json=="false"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'请选择上传成功的记录!');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].action="business/uploadStateManager!updateState.action?type=3&currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="delete"){
						jQuery.ajax({
				            url: 'business/uploadStateManager!isSuccessState.action',
					        data: {'ids' : $(this).find('#ids').val(),'type':'4'},
					        type: 'POST',
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#uploadStateManager_dialog');
					            if(json2Bean(data).json=="false"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'请选择上传成功的记录!');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].type.value='4';
									dialog.find('#createForm')[0].action="business/uploadStateManager!updateState.action?currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });					

					}
				}
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			//clear($(this));
			$(this).find("#reason").val('');
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			type = null;
			//$('#car_color_mapping_dialog').find('#vin').attr('readonly',false);
		}
	});
	
	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});


	function validate(parent){
		//return true;
		var obj = $(parent).find('#reason');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过100！');
			obj.focus();			
			return false;
		}

		return true;
	}
	
	function validate1(parent){
		//return true;
		var obj = $(parent).find('#gasyears');
		if(!checkLength(obj,1,4)||!checkRegexp(obj,/^(\d+)$/)){
			obj.addClass('ui-state-error');
			updateTips($("#gasinfo_dialog").find('#validateTips'),'请输入年份，最大长度为4且必须是整数！');	
			obj.focus();			
			return false;
		}

		return true;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var vin = $('#vin').val();
			var pmodel = $('#pmodel').val();
			var uploadType = $('#uploadType').val();
			var uploadState = $('#uploadState').val();
			var beginDate = $('#beginDate').val();
			var endDate = $('#endDate').val();
			if(beginDate!='' && isDate(beginDate)){
				alert("请按yyyy-MM-dd格式填写日期");
				return;
			}
			if(endDate!='' && isDate(endDate)){
				alert("请按yyyy-MM-dd格式填写日期");
				return;			
			}
			
			location.href="uploadStateManager.action?currentPage="+$('#jump').val()+
			"&vin="+encodeURI(encodeURI(vin))+
			"&pmodel="+encodeURI(encodeURI(pmodel))+
			"&uploadType="+encodeURI(encodeURI(uploadType))+
			"&uploadState="+encodeURI(encodeURI(uploadState))+
			"&beginDate="+encodeURI(encodeURI(beginDate))+
			"&endDate="+encodeURI(encodeURI(endDate))+
			"&csbeginDate="+encodeURI(encodeURI(csbeginDate))+
			"&csendDate="+encodeURI(encodeURI(csendDate))+			
			"&menuid="+menuid;
   		}     
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var vin = $('#vin').val();
				var pmodel = $('#pmodel').val();
				var uploadType = $('#uploadType').val();
				var uploadState = $('#uploadState').val();
				var beginDate = $('#beginDate').val();
				var endDate = $('#endDate').val();
				var csbeginDate = $('#csbeginDate').val();
				var csendDate = $('#csendDate').val();
				if(beginDate!='' && !isDate(beginDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;
				}
				if(endDate!='' && !isDate(endDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;			
				}				
				if(csbeginDate!='' && !isDate(csbeginDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;
				}
				if(csendDate!='' && !isDate(csendDate)){
					alert("请按yyyy-MM-dd格式填写日期");
					return;			
				}				
				location.href=$(this).attr('value')+
				"&vin="+encodeURI(encodeURI(vin))+
				"&pmodel="+encodeURI(encodeURI(pmodel))+
				"&uploadType="+encodeURI(encodeURI(uploadType))+
				"&uploadState="+encodeURI(encodeURI(uploadState))+
				"&beginDate="+encodeURI(encodeURI(beginDate))+
				"&endDate="+encodeURI(encodeURI(endDate))+
				"&csbeginDate="+encodeURI(encodeURI(csbeginDate))+
				"&csendDate="+encodeURI(encodeURI(csendDate))+
				"&menuid="+menuid;
		 });
	  });
	  
	
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif',
  	   		buttonImageOnly: true}, $.datepicker.regional['zh']));
	$('#beginDate').datepicker($.datepicker.regional['zh']); 
	$('#endDate').datepicker($.datepicker.regional['zh']); 
	$('#csbeginDate').datepicker($.datepicker.regional['zh']); 
	$('#csendDate').datepicker($.datepicker.regional['zh']); 
});
$(document).ready(function() {
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class


});

function tabsshoworhide(rllx){
	var showrllx =  rllx;
	if(showrllx=='汽油'||showrllx=='柴油'||showrllx=='两用燃料'||showrllx=='双燃料'){
	            	$("#tabs1table").show();
	            	$("#tabs2table").hide();
	            	$("#tabs3table").hide();
	             	$("#tabs4table").show();
				   	$("#tabs4table tr[id='trtabs3']").hide();
				   	$("#tabs4table input[id='dczlzbzlb']").val('');
				   	$("#tabs4table input[id='zhgkdnxhl']").val('');
				   	$("#tabs4table td[id='tdqtxx']").show();
	   			}	
	           else if(showrllx=='非插电式混合动力'){
	            	$("#tabs1table").show();
	            	$("#tabs2table").show();
	            	$("#tabs3table").show();
	            	$("#tabs4table").hide();
	            	$("#tabs1table input[id='qtxx']").hide();
	   						$("#tabs1table input[id='qtxx']").val('');
	   						$("#tabs2table p[id='labelcddzgcs']").html('纯电动模式下1km最高车速(km/h)');
	   						$("#tabs2table p[id='labelzhgkxslc']").html('纯电动模式下综合工况续驶里程(km)');
				   	$("#tabs2table input[id='dczlzbzlb']").hide();
				   	$("#tabs2table input[id='dczlzbzlb']").val('');
				   	$("#tabs2table input[id='zhgkdnxhl']").hide();
				   	$("#tabs2table input[id='zhgkdnxhl']").val('');
	            	}
	           else if(showrllx=='纯电动'){
	            	$("#tabs1table").hide();
	            	$("#tabs2table").show();
	            	$("#tabs3table").hide();
	            	$("#tabs4table").show();
						  	$("#tabs4table tr[id='trtabs3']").show();
						   	$("#tabs4table td[id='tdqtxx']").hide();
						   	$("#tabs4table td[id='tdqtxx']").val('');
	            	}else{
					$("#tabs1table").hide();
				   	$("#tabs2table").hide();
				   	$("#tabs3table").hide();
				   	$("#tabs4table").hide();
	}
}

</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
<!--form id="qfrm" name="qfrm"-->
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan=""><p>VIN:<input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" <s:if test="#request.vin!=null"> value="<s:property value="#request.vin" />"</s:if> size="10" /> 
				车型:<input type="text" id="pmodel" name="pmodel" class="text ui-widget-content ui-corner-all"  size="10" <s:if test="#request.pmodel!=null"> value="<s:property value="#request.pmodel" />"</s:if>/> 
				上传方式：<s:select name="uploadType" list="#{'1':'上传','2':'补传','3':'修改','4':'撤销','5':'未上传'}"  listKey="key" listValue="value" headerKey="" headerValue="全部" theme="simple"></s:select>				
				上传状态：<s:select name="uploadState" list="#{'T':'成功','F':'失败','I':'处理中'}"  listKey="key" listValue="value" headerKey="" headerValue="全部" theme="simple" ></s:select>
				</td>
				<td align="right" colspan='3'></td>
			</tr>
			<tr>
				<td align="left" colspan="">上传时间：<input type="text" id="beginDate" name="beginDate" class="text ui-widget-content ui-corner-all"  size="8" <s:if test="#request.beginDate!=null"> value="<s:property value="#request.beginDate" />"</s:if>/>  -  <input type="text" id="endDate" name="endDate" class="text ui-widget-content ui-corner-all"  size="8" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />"</s:if>/>
				生产时间：<input type="text" id="csbeginDate" name="csbeginDate" class="text ui-widget-content ui-corner-all"  size="8" <s:if test="#request.csbeginDate!=null"> value="<s:property value="#request.csbeginDate" />"</s:if>/>  -  <input type="text" id="csendDate" name="csendDate" class="text ui-widget-content ui-corner-all"  size="8" <s:if test="#request.csendDate!=null"> value="<s:property value="#request.csendDate" />"</s:if>/>				
				</td>
				<td align="right" colspan='3'></td>
			</tr>
			
			<tr>
			   <td width="80%"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="7%" align="right"></td>
			   <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all"/>导出</button></td>
			   <td width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			   <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all"/>修改</button></td>
			   <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all"/>撤销</button></td>
			   <td width="60" align="right"><button id="exportfueltar" class="ui-button ui-state-default ui-corner-all">导出目标值</button></td>
			   <td width="60" align="right"><button id="fxjk" class="ui-button ui-state-default ui-corner-all">发送接口</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="6%">vin</th>
			    <th width="6%">公告车型</th>
			    <th width="6%">生产日期</th>
			    <th width="6%">上传日期</th>
			    
			    <th width="6%">上传方式</th>
			    <th width="6%">上传状态</th>
			    <th width="6%">反馈信息</th>
			    <th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator id="item" value="#request.uploadStateManagerPageData" status="obj"  > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
				
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="cxxh" /></td>
			  		<td><s:property value="proddate" /></td>
			  		<td><s:date name="adtfuel" format="yyyy-MM-dd"/></td>
			  		<td>
					<s:if test='fuelupload in {"T", "I", "F"}'>
			  			上传
			  		</s:if><s:elseif test='fueldelay in {"T", "I", "F"}'>
			  			补传
			  		</s:elseif><s:elseif test='fuelmodified in {"T", "I", "F"}'>
			  			修改上传
			  		</s:elseif><s:elseif test='fueldel in {"T", "I", "F"}'>
			  			撤销
			  		</s:elseif><s:else>
						未上传
					</s:else>
			  		</td>
			  		<td>
					<s:if test='fuelupload=="T"'>
			  			上传成功
			  		</s:if><s:elseif test='fuelupload=="F"'>
			  			上传失败
			  		</s:elseif><s:elseif test='fuelupload=="I"'>
			  			正在上传
			  		</s:elseif><s:elseif test='fueldelay=="T"'>
			  			补传成功
			  		</s:elseif><s:elseif test='fueldelay=="F"'>
			  			补传失败
			  		</s:elseif><s:elseif test='fueldelay=="I"'>
			  			正在补传
			  		</s:elseif><s:elseif test='fuelmodified=="T"'>
			  			修改成功
			  		</s:elseif><s:elseif test='fuelmodified=="F"'>
			  			修改失败
			  		</s:elseif><s:elseif test='fuelmodified=="I"'>
			  			正在修改
			  		</s:elseif><s:elseif test='fueldel=="T"'>
			  			撤销成功
			  		</s:elseif><s:elseif test='fueldel=="F"'>
			  			撤销失败
			  		</s:elseif><s:elseif test='fueldel=="I"'>
			  			正在撤销
			  		</s:elseif><s:else>
						未上传
					</s:else>			  				  		
					</td>
			  		<td><s:property value="fuelresponse" /></td>
	  			  	<td><a class='display' onclick="return false;" href='#'  value='<s:property value="vin" />'>查看</a></td>
			  		
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadStateManager.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadStateManager.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadStateManager.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadStateManager.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
<!--/form-->
</div>

<div id="uploadStateManager_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	    	<tr>
				<td align="left"><label><P>原因:</label></td>
				<td><input type="text" id="reason" name="reason"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20"/>
					</td>
	    	</tr>			
		</Table>
		<input type='hidden' id='ids' name='ids'/>
		<input type='hidden' id='type' name='type'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div id="uploadStateManager_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm3" method="post" > 
	  	  <table id = "tuest" width="100%">
	    	<tr>
				<td align="left"><label><P>VIN:</label></td>
				<td><input type="text" id="vin" name="vin"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>车型型号:</label></td>
				<td><input type="text" id="cxxh" name="cxxh"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>车辆种类:</label></td>
				<td><input type="text" id="clzl" name="clzl"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	<tr>
				<td align="left"><label><P>生产日期:</label></td>
				<td><input type="text" id="proddate" name="proddate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>额定载客:</label></td>
				<td><input type="text" id="edzk" name="edzk"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>进口汽车经销商:</label></td>
				<td><input type="text" id="jkjxs" name="jkjxs"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>		    				
	    	<tr>
				<td align="left"><label><P>报告编号:</label></td>
				<td><input type="text" id="bgbh" name="bgbh"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>检测机构名称:</label></td>
				<td><input type="text" id="jcjgmc" name="jcjgmc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>轮距前/后:</label></td>
				<td><input type="text" id="qhlj" name="qhlj"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>		    				
	    	<tr>
				<td align="left"><label><P>轮胎规格:</label></td>
				<td><input type="text" id="ltgg" name="ltgg"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>生产企业（印刷）:</label></td>
				<td><input type="text" id="scqe1" name="scqe1"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>驱动型式:</label></td>
				<td><input type="text" id="qdxs" name="qdxs"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>		    				
	    	<tr>
				<td align="left"><label><P>燃料类型:</label></td>
				<td><input type="text" id="rllx" name="rllx"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>通用名称:</label></td>
				<td><input type="text" id="tymc" name="tymc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>越野车（G类）:</label></td>
				<td><input type="text" id="yyc" name="yyc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>		    				
	    	<tr>
				<td align="left"><label><P>整车整备质量(kg):</label></td>
				<td><input type="text" id="zbzl" name="zbzl"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>最大设计总质量(kg):</label></td>
				<td><input type="text" id="jdzzl" name="jdzzl"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>最高车速:</label></td>
				<td><input type="text" id="zgcs" name="zgcs"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>		    				
	    	<tr>
				<td align="left"><label><P>轴距:</label></td>
				<td><input type="text" id="zj" name="zj"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>座位排数:</label></td>
				<td><input type="text" id="zwps" name="zwps"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
			</tr>		    				
	    		<div id="tabs-2">
  <div width="100%" id="tabs1table">  	  
   	<tr>
		<td width="20%"><label><P>变速器档位数</label></td>
		<td width="20%"><input type="text" id="bsqdws" name="bsqdws" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td width="20%"><label><P>变速器型式</label></td>
		<td width="20%"><input type="text" id="bsqlx" name="bsqlx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   		<td width="20%"><label><P>额定功率(kW)</label></td>
		<td width="20%"><input type="text" id="edgl" name="edgl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		</tr>
   	<tr>
		<td><label><P>发动机型号</label></td>
		<td><input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>最大净功率(kW)</label></td>
		<td><input type="text" id="zdjgl" name="zdjgl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>排量</label></td>
		<td><input type="text" id="pl" name="pl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>汽车节能技术</label></td>
		<td><input type="text" id="jcjnjs" name="jcjnjs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>气缸数</label></td>
		<td><input type="text" id="qgs" name="qgs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>市郊工况(L/100km)</label></td>
		<td><input type="text" id="sjgk" name="sjgk" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
   		<td><label><P>市区工况(L/100km)</label></td>
		<td><input type="text" id="sqgk" name="sqgk" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>综合工况CO2排放量(g/km)</label></td>
		<td><input type="text" id="zhcopl" name="zhcopl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   		<td><label><P>综合工况(L/100km)</label></td>
		<td><input type="text" id="zhgk" name="zhgk" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
	</tr>
   	</div>
   	
   <div width="100%" id="tabs2table">  
   	<tr>
		<td width="20%"><label><P id="labelcddzgcs">电动汽车30分钟最高车速</label></td>
		<td width="20%"><input type="text" id="cddzgcs" name="cddzgcs" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td width="20%"><label><P>动力蓄电池组比能量(Wh/kg)</label></td>
		<td width="20%"><input type="text" id="dcbnl" name="dcbnl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td width="20%"><label><P>动力蓄电池组标称电压(V)</label></td>
		<td width="20%"><input type="text" id="dczbcdy" name="dczbcdy" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>
		<td><label><P>动力蓄电池组总能量(kWh)</label></td>
		<td><input type="text" id="dczednl" name="dczednl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>动力蓄电池组种类</label></td>
		<td><input type="text" id="dczzl" name="dczzl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>驱动电机额定功率(kW)</label></td>
		<td><input type="text" id="djedgl" name="djedgl" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
   	</tr>
   	<tr>		
		<td><label><P>驱动电机峰值扭矩(N•m)</label></td>
		<td><input type="text" id="djfznj" name="djfznj" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P>驱动电机类型</label></td>
		<td><input type="text" id="djlx" name="djlx" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		<td><label><P id="labelzhgkxslc">综合工况续驶里程</label></td>
		<td><input type="text" id="zhgkxslc" name="zhgkxslc" class="text ui-widget-content ui-corner-all" size="20" readonly="true"/></td>
		</tr> 
   </div>
   <div width="100%" id="tabs3table">  
   	<tr>
		<td width="20%"><label><P>混合动力结构型式</label></td>
		<td width="20%"><input type="text" id="hhdljgxs" name="hhdljgxs" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td width="20%"><label><P>混合动力最大电功率比(%)</label></td>
		<td width="20%"><input type="text" id="hhdlzddglb" name="hhdlzddglb" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td width="20%"><label><P>是否具有行驶模式手动选择功能</label></td>
		<td width="20%"><input type="text" id="xsmssdxzgn" name="xsmssdxzgn" class="text ui-widget-content ui-corner-all" size="20" /></td>
   	</tr>
   </div> 
   
<div width="100%" id="tabs4table">  
   	<tr id="trtabs3">
		<td width="20%"><label><P>动力蓄电池总质量与整车整备质量的比值(%)</label></td>
		<td width="20%"><input type="text" id="dczlzbzlb" name="dczlzbzlb" class="text ui-widget-content ui-corner-all" size="20" /></td>
	   	<td width="20%"><label><P>综合工况电能消耗量</label></td>
		<td width="20%"><input type="text" id="zhgkdnxhl" name="zhgkdnxhl" class="text ui-widget-content ui-corner-all" size="20" /></td>
	</tr>
   	<tr id="tdqtxx">
   		<td><label><P>其他信息</label></td>
		<td><input type="text" id="qtxx" name="qtxx" class="text ui-widget-content ui-corner-all" size="20" /></td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		</tr>
   </div>       
   
	    	</div>	    				
	    	<tr>
			</tr>    				
		</Table>
		</form>
	</fieldset>
</div>

<div id="gasinfo_dialog" title="输入年份" style="display:none">
<p id="validateTips"></p>
<form id="createForm" method="post" > 
<table width="100%"> 
<tr>
				<td><label><P>年份</label></td>
				<td><input type="text" id="gasyears" name="gasyears" class="text ui-widget-content ui-corner-all" size="20"/></td>
</tr>
<tr>
		<td><label><P>月份</label></td>
		<td >		
		<select id="halfyear" name="halfyear" style="width:200px">
		<!--
		<option value="1">上半年</option>
		<option value="2">下半年</option>
		-->
		<option value="01">1月</option>
		<option value="02">2月</option>
		<option value="03">3月</option>
		<option value="04">4月</option>
		<option value="05">5月</option>
		<option value="06">6月</option>
		<option value="07">7月</option>
		<option value="08">8月</option>
		<option value="09">9月</option>
		<option value="10">10月</option>
		<option value="11">11月</option>
		<option value="12">12月</option>		
		</select>
		</td>
</tr>
</table>
</form>
</div>


<div id="fxjk_dialog" title="输入年份" style="display:none">
<p id="validateTips"></p>
<form id="createForm" method="post" > 
<table width="100%"> 
<tr>
				<td><label><P>年份</label></td>
				<td><input type="text" id="fasyears" name="fasyears" class="text ui-widget-content ui-corner-all" size="20"/></td>
</tr>
<tr>
		<td><label><P>月份</label></td>
		<td >		
		<select id="falfyear" name="falfyear" style="width:200px">
		<option value="01">1月</option>
		<option value="02">2月</option>
		<option value="03">3月</option>
		<option value="04">4月</option>
		<option value="05">5月</option>
		<option value="06">6月</option>
		<option value="07">7月</option>
		<option value="08">8月</option>
		<option value="09">9月</option>
		<option value="10">10月</option>
		<option value="11">11月</option>
		<option value="12">12月</option>
		
		</select>
		</td>
</tr>
</table>
</form>
</div>

</body>
</html>