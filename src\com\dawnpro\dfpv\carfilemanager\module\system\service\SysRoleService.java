package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;

public interface SysRoleService {
	
	public void addRole(SysRole role) throws DataAccessException;
	
	public void updateRole(SysRole role) throws DataAccessException;
	
	public void deleteRole(SysRole[] role) throws DataAccessException;
	
	public List<SysRole> findRole(String sql) throws DataAccessException;
	
	public List<?> pagination(Page page) throws DataAccessException;
	
	public SysRole loadRole(String roleId) throws DataAccessException;
	
	public boolean isRoleExist(String name) throws DataAccessException;
}
