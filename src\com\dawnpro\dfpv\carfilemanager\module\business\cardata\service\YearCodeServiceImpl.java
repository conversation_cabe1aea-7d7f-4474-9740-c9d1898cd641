package com.dawnpro.dfpv.carfilemanager.module.business.cardata.service;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao.YearCodeDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.YearCode;



public class YearCodeServiceImpl implements YearCodeService {
	private YearCodeDAO YearCodeDAO = null;

	private PaginationService paginationService = null;

	
	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}
	
	public YearCodeDAO getYearCodeDAO() {
		return YearCodeDAO;
	}

	public void setYearCodeDAO(YearCodeDAO YearCodeDAO) {
		this.YearCodeDAO = YearCodeDAO;
	}

	public void addYearCode(YearCode obj)
			throws DataAccessException {
		try{
			this.YearCodeDAO.addYearCode(obj);
		}catch (DataAccessException e) {
			throw new SystemException("addYearCode Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("addYearCode Method Error:", e);
		}
	}

	public void deleteYearCode(Serializable id)
			throws DataAccessException {
		try{
			this.YearCodeDAO.deleteYearCode(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteYearCode Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("deleteYearCode Method Error:", e);
		}
	}

	public void deleteYearCode(Serializable[] id)
			throws DataAccessException {
		try{
			this.YearCodeDAO.deleteYearCode(id);
		}catch (DataAccessException e) {
			throw new SystemException("deleteYearCode Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("delete YearCode Method Error:", e);
		}
	}

	public boolean isYearCodeExist(String year)
			throws DataAccessException {
		try{
			List list = YearCodeDAO.findYearCode(" from YearCode g where g.year=? ", new Object[]{ year});
			if(list != null && list.size() > 0){
				return true;
			}
		}catch (DataAccessException e) {
			throw new SystemException("isYearCodeExist Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("isYearCodeExist Method Error:", e);
		}
		return false;

	}

	public YearCode loadYearCode(String id)
			throws DataAccessException {
		try{
			return this.YearCodeDAO.loadYearCode(id);
		}catch (DataAccessException e) {
			throw new SystemException("loadYearCode Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("loadYearCode Method Error:", e);
		}

	}

	public List<?> pagination(Page page, Map<String, String> params)
			throws DataAccessException {
		List results = null;
		try{
			String year = params.get("year") == null ? "" : params.get("year");
			StringBuffer sub = new StringBuffer(30);
			StringBuffer countBuf = new StringBuffer(30);
			StringBuffer sqlBuf = new StringBuffer(30);
			
			countBuf.append("select count(*) from YearCode g ");
			sqlBuf.append("from YearCode g ");
				
			sub.append(" where 1 = 1 ");
			if(!year.equals("")){
				sub.append(" and g.year like '%").append(year.toUpperCase()).append("%' ");
			}

			countBuf.append(sub);
			sqlBuf.append(sub+" order by g.createdate desc");
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.getPage().setPageSize(page.getPageSize());
			this.paginationService.countPageSum(countBuf.toString());
			results = this.paginationService.pagination(sqlBuf.toString());
			
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;

	}

	public void updateYearCode(YearCode obj)
			throws DataAccessException {
		try{
//			YearCode tmp = this.loadYearCode(obj.getId());
//			tmp.setLpz(obj.getLpz());
//			tmp.setCyear(obj.getCyear());
//			tmp.setXs(obj.getXs());
			
			this.YearCodeDAO.updateYearCode(obj);
		}catch (DataAccessException e) {
			throw new SystemException("updateYearCode Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("updateYearCode Method Error:", e);
		}
	}

	public YearCode findYearCode(String id) throws DataAccessException {
		try{
			List<YearCode> list = YearCodeDAO.findYearCode(" from YearCode g where g.id=?", new Object[]{id});
			if(list != null && list.size() > 0){
				return list.get(0);
			}
		}catch (DataAccessException e) {
			throw new SystemException("isYearCodeExist Method Error:", e);
		} catch (RuntimeException e) {
			throw new SystemException("isYearCodeExist Method Error:", e);
		}
		return null;
		
	}

}
