package com.dawnpro.dfpv.carfilemanager.barcode;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperPrintManager;

import com.dawnpro.dfpv.carfilemanager.module.business.certificate.print.jasperreports.provider.CarModelPhotoCertificateBeanDataSource;
import com.sun.image.codec.jpeg.JPEGCodec;
import com.sun.image.codec.jpeg.JPEGImageEncoder;

public class AA {
	public static void reduceImg(String imgsrc, String imgdist, int widthdist,   
	        int heightdist) {   
	    try {   
	        File srcfile = new File(imgsrc);   
	        if (!srcfile.exists()) {   
	            return;   
	        }   
	        Image src = javax.imageio.ImageIO.read(srcfile);   
	  
	        BufferedImage tag= new BufferedImage((int) widthdist, (int) heightdist,   
	                BufferedImage.TYPE_INT_RGB);   
	  
	        tag.getGraphics().drawImage(src.getScaledInstance(widthdist, heightdist,  Image.SCALE_SMOOTH), 0, 0,  null);    
	           
	        FileOutputStream out = new FileOutputStream(imgdist);   
	        JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);   
	        encoder.encode(tag);   
	        out.close();   
	  
	    } catch (IOException ex) {   
	        ex.printStackTrace();   
	    }   
	}  
	
	public static void main(String[] args){
		try{//reduceImg("e:/1.jpg","e:/a.jpg",332,226);
			String photoUrl = "http://localhost:80/CarFileManager/photo/modelphoto/B01-001/1ba5f5c2-2da2-47f7-aeaa-d7bc035b5e7f.jpeg";
			URL photo = new URL(photoUrl); 
		    HttpURLConnection conn = (HttpURLConnection) photo.openConnection(); 
		    
			Map params = new HashMap();
		    params.put("carmodelphoto", conn.getInputStream());
		    //params.put("test", "12345");
			JasperPrint jpt =  JasperFillManager.fillReport(AA.class.getResourceAsStream("report12.jasper"),params,new CarModelPhotoCertificateBeanDataSource());
			
			JasperPrintManager.printReport(jpt, false); 
			
		}catch(Exception e){
			e.printStackTrace();
		}
		
	}
}
