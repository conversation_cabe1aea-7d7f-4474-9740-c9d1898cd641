<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="GASCDHH"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="504"
		 pageHeight="648"
		 columnWidth="458"
		 columnSpacing="0"
		 leftMargin="23"
		 rightMargin="23"
		 topMargin="14"
		 bottomMargin="0"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="yjdimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="dianimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="upimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="downimage" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G1image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G2image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="G3image" isForPrompting="true" class="java.lang.String"/>
	<parameter name="dcximage" isForPrompting="true" class="java.lang.String"/>

	<field name="carConsistencyNumber" class="java.lang.String">
		<fieldDescription><![CDATA[carConsistencyNumber]]></fieldDescription>
	</field>
	<field name="cityStatus" class="java.lang.String">
		<fieldDescription><![CDATA[cityStatus]]></fieldDescription>
	</field>
	<field name="class" class="java.lang.Class">
		<fieldDescription><![CDATA[class]]></fieldDescription>
	</field>
	<field name="colligateStatus" class="java.lang.String">
		<fieldDescription><![CDATA[colligateStatus]]></fieldDescription>
	</field>
	<field name="date" class="java.lang.String">
		<fieldDescription><![CDATA[date]]></fieldDescription>
	</field>
	<field name="derailleurType" class="java.lang.String">
		<fieldDescription><![CDATA[derailleurType]]></fieldDescription>
	</field>
	<field name="driveType" class="java.lang.String">
		<fieldDescription><![CDATA[driveType]]></fieldDescription>
	</field>
	<field name="engineModel" class="java.lang.String">
		<fieldDescription><![CDATA[engineModel]]></fieldDescription>
	</field>
	<field name="enterprise" class="java.lang.String">
		<fieldDescription><![CDATA[enterprise]]></fieldDescription>
	</field>
	<field name="environsStatus" class="java.lang.String">
		<fieldDescription><![CDATA[environsStatus]]></fieldDescription>
	</field>
	<field name="fuelType" class="java.lang.String">
		<fieldDescription><![CDATA[fuelType]]></fieldDescription>
	</field>
	<field name="limitOne" class="java.lang.String">
		<fieldDescription><![CDATA[limitOne]]></fieldDescription>
	</field>
	<field name="limitTwo" class="java.lang.String">
		<fieldDescription><![CDATA[limitTwo]]></fieldDescription>
	</field>
	<field name="maxDesignSumQuality" class="java.lang.String">
		<fieldDescription><![CDATA[maxDesignSumQuality]]></fieldDescription>
	</field>
	<field name="model" class="java.lang.String">
		<fieldDescription><![CDATA[model]]></fieldDescription>
	</field>
	<field name="quality" class="java.lang.String">
		<fieldDescription><![CDATA[quality]]></fieldDescription>
	</field>
	<field name="range" class="java.lang.String">
		<fieldDescription><![CDATA[range]]></fieldDescription>
	</field>
	<field name="ratingPower" class="java.lang.String">
		<fieldDescription><![CDATA[ratingPower]]></fieldDescription>
	</field>
	<field name="remark" class="java.lang.String">
		<fieldDescription><![CDATA[remark]]></fieldDescription>
	</field>
	<field name="result" class="java.lang.Boolean">
		<fieldDescription><![CDATA[result]]></fieldDescription>
	</field>
	<field name="vin" class="java.lang.String">
		<fieldDescription><![CDATA[vin]]></fieldDescription>
	</field>
	<field name="qebz" class="java.lang.String"/>
	<field name="gjbz" class="java.lang.String"/>
	<field name="dymb" class="java.lang.String"/>
	<field name="zhgkdnxhl" class="java.lang.String"/>
	<field name="zhgkxslc" class="java.lang.String"/>
	<field name="zhgk" class="java.lang.String"/>
	<field name="dndl" class="java.lang.String"/>
	<field name="zdjgl" class="java.lang.String"/>
	<field name="zdhdztrlxhl" class="java.lang.String"/>
	<field name="djedgl" class="java.lang.String"/>
	<field name="zhcopl" class="java.lang.String"/>
	<field name="ygnycb" class="java.lang.String"/>
	<field name="bacode" class="java.lang.String"/>
	<field name="ydzhrlxhl" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="57"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="18"
						y="4"
						width="105"
						height="42"
						key="textField"/>
					<box></box>
					<textElement textAlignment="Left" verticalAlignment="Top">
						<font fontName="黑体" size="23" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{qebz}]]></textFieldExpression>
				</textField>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="55"
						width="375"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="3.0"/>
</graphicElement>
				</line>
			</band>
		</title>
		<pageHeader>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageHeader>
		<columnHeader>
			<band height="517"  isSplitAllowed="true" >
				<image  scaleImage="FillFrame" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="9"
						y="245"
						width="357"
						height="36"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{dcximage}]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="276"
						y="492"
						width="99"
						height="18"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{date}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="91"
						y="56"
						width="98"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{quality}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="56"
						width="91"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[整车整备质量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="104"
						y="70"
						width="74"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{maxDesignSumQuality}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="70"
						width="104"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大设计总质量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="206"
						y="56"
						width="116"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[驱动电机峰值功率：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="322"
						y="56"
						width="31"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{djedgl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="84"
						width="65"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[其他信息：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="42"
						y="105"
						width="141"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[综合燃料消耗量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="42"
						y="161"
						width="159"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[油电综合折算燃料消耗量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="166"
						y="244"
						width="104"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[电动续驶里程：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="201"
						y="105"
						width="103"
						height="18"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{colligateStatus}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="42"
						width="78"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[发动机型号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="79"
						y="42"
						width="100"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{engineModel}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="206"
						y="28"
						width="78"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[变速器类型：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="206"
						y="42"
						width="39"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[排量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="285"
						y="28"
						width="93"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{derailleurType}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="264"
						y="42"
						width="80"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{range}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="42"
						y="123"
						width="141"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[综合电能消耗量：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="42"
						y="179"
						width="161"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[亏电状态燃料消耗量：]]></text>
				</staticText>
				<image  evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="335"
						y="362"
						width="39"
						height="58"
						key="image"/>
					<box></box>
					<graphicElement stretchType="NoStretch"/>
					<imageExpression class="java.lang.String"><![CDATA[$P{G3image}]]></imageExpression>
				</image>
				<line direction="TopDown">
					<reportElement
						x="0"
						y="485"
						width="375"
						height="1"
						key="line"/>
					<graphicElement stretchType="NoStretch">
					<pen lineWidth="1.0"/>
</graphicElement>
				</line>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="65"
						y="346"
						width="265"
						height="139"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["    "+$F{gjbz}.replaceAll("\\\\n","\\\n    ")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="259"
						y="229"
						width="87"
						height="36"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="28" isBold="true" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zhgkxslc}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="201"
						y="123"
						width="77"
						height="18"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" size="12" isBold="false"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zhgkdnxhl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="201"
						y="142"
						width="102"
						height="36"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" size="28" isBold="true"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ydzhrlxhl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="181"
						y="56"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="181"
						y="70"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kg]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="206"
						y="70"
						width="77"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[最大净功率：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="353"
						y="42"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[mL]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="353"
						y="56"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kW]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="353"
						y="70"
						width="23"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kW]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="283"
						y="70"
						width="70"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zdjgl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="304"
						y="105"
						width="59"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="275"
						y="123"
						width="88"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[kW·h/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="304"
						y="161"
						width="59"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="304"
						y="179"
						width="59"
						height="19"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[L/100 km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="47"
						y="492"
						width="154"
						height="20"
						key="textField"/>
					<box></box>
					<textElement verticalAlignment="Top">
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{bacode}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="340"
						y="244"
						width="23"
						height="18"
						key="staticText"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="宋体" pdfFontName="STSong-Light" size="12" isBold="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[km]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="67"
						y="28"
						width="100"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{model}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="28"
						width="65"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[车辆型号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="202"
						y="179"
						width="97"
						height="18"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" size="12" isBold="false"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zdhdztrlxhl}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="67"
						y="0"
						width="267"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{enterprise}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="0"
						width="65"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[生产企业：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="14"
						width="65"
						height="14"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[能源种类：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="67"
						y="14"
						width="267"
						height="14"
						key="textField"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA["可外接充电式混合动力（汽油/电）"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="218"
						y="492"
						width="55"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" isBold="true"/>
					</textElement>
				<text><![CDATA[启用日期：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="1"
						y="492"
						width="46"
						height="20"
						key="staticText"/>
					<box></box>
					<textElement>
						<font fontName="黑体" isBold="true"/>
					</textElement>
				<text><![CDATA[备案号：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="65"
						y="84"
						width="310"
						height="20"
						key="textField-1"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="10" isBold="false" isPdfEmbedded ="true" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{remark}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="43"
						y="197"
						width="159"
						height="18"
						key="staticText-1"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[预估能源成本：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="202"
						y="197"
						width="97"
						height="18"
						key="textField-2"/>
					<box></box>
					<textElement>
						<font fontName="黑体" size="12" isBold="false"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{ygnycb}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="297"
						y="197"
						width="66"
						height="18"
						key="staticText-2"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[元/100 km]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="43"
						y="215"
						width="159"
						height="18"
						key="staticText-3"/>
					<box></box>
					<textElement>
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[CO2排放量：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="202"
						y="215"
						width="97"
						height="18"
						key="textField-3"/>
					<box></box>
					<textElement>
						<font fontName="黑体" size="12" isBold="false"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{zhcopl}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="304"
						y="215"
						width="59"
						height="18"
						key="staticText-4"/>
					<box></box>
					<textElement textAlignment="Right">
						<font fontName="黑体" pdfFontName="STSong-Light" size="12" isBold="false" pdfEncoding ="UniGB-UCS2-H"/>
					</textElement>
				<text><![CDATA[g/km]]></text>
				</staticText>
			</band>
		</columnHeader>
		<detail>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</summary>
</jasperReport>
