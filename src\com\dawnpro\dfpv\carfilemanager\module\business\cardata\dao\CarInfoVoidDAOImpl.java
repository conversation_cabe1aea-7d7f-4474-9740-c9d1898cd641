package com.dawnpro.dfpv.carfilemanager.module.business.cardata.dao;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarInfoVoid;

public class CarInfoVoidDAOImpl extends GenericHibernateDAOImpl<CarInfoVoid> implements CarInfoVoidDAO  {

	public void addCarModel(CarInfoVoid car) {
		try{
			this.add(car);
		}catch(Exception e){
			throw new DataAccessException("addCarModel Method Error:",e);
		}
	}

}
