<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="2.4"
	xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
	<display-name>DFPV_CarFileManager</display-name>
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
	</welcome-file-list>

	<resource-env-ref>
		<resource-env-ref-name>jms/queueConnectionFactory</resource-env-ref-name>
		<resource-env-ref-type>javax.jms.QueueConnectionFactory</resource-env-ref-type>
	</resource-env-ref>

	<resource-env-ref>
		<resource-env-ref-name>jms/MES.CFM.SERVICE.RECEIVEQ1</resource-env-ref-name>
		<resource-env-ref-type>javax.jms.Queue</resource-env-ref-type>
	</resource-env-ref>

	<servlet>
		<servlet-name>BarcodeServlet</servlet-name>
		<servlet-class>com.dawnpro.dfpv.carfilemanager.barcode.BarcodeServlet</servlet-class>
	</servlet>

	<servlet-mapping>
		<servlet-name>BarcodeServlet</servlet-name>
		<url-pattern>/barcode</url-pattern>
	</servlet-mapping>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>/WEB-INF/classes/applicationContext*.xml</param-value>
	</context-param>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>

	<listener>
		<listener-class>
			org.springframework.web.context.request.RequestContextListener</listener-class>
	</listener>

	<filter>
		<filter-name>Struts2</filter-name>
		<filter-class>org.apache.struts2.dispatcher.FilterDispatcher</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>Struts2</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>struts-cleanup</filter-name>
		<filter-class>org.apache.struts2.dispatcher.ActionContextCleanUp</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>struts-cleanup</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<session-config>
		<session-timeout>30</session-timeout>
	</session-config>

</web-app>
