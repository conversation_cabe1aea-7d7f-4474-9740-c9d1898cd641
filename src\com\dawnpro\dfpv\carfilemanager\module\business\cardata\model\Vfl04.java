package com.dawnpro.dfpv.carfilemanager.module.business.cardata.model;

// default package

/**
 * Vfl04 entity. <AUTHOR> Persistence Tools
 */

public class Vfl04 implements java.io.Serializable {

	// Fields

	private String vin;
	private String vehiclemodel;
	private String obd;
	private Double odo;
	private String checker;
	private String testno;
	private String ecmmodulename;
	private String ecmmoduleid;
	private String ecmcalid;
	private String ecmcvn;
	private String tcmmodulename;
	private String tcmmoduleid;
	private String tcmcalid;
	private String tcmcvn;
	private String ecm2modulename;
	private String ecm2moduleid;
	private String ecm2calid;
	private String ecm2cvn;
	private String dmcmmodulename;
	private String dmcmmoduleid;
	private String dmcmcalid;
	private String dmcmcvn;
	private String scrmodulename;
	private String scrmoduleid;
	private String scrcalid;
	private String scrcvn;
	private String hvbecmmodulename;
	private String hvbecmmoduleid;
	private String hvbecmcalid;
	private String hvbecmcvn;
	private String bcmmodulename;
	private String serialno;
	private String uuid;
	private String cratedate;
	private String bcmmoduleid;
	private String bcmcalid;
	private String bcmcvn;
	private String othmodulename;
	private String othmoduleid;
	private String othcalid;
	private String othcvn;
	private String apass;
	private String ocommunchk;
	private String opass;
	private String testdate;
	private String ctest;
	private String ctestlocation;

	// Constructors

	/** default constructor */
	public Vfl04() {
	}

	/** minimal constructor */
	public Vfl04(String vin) {
		this.vin = vin;
	}

	/** full constructor */
	public Vfl04(String vin, String vehiclemodel, String obd, Double odo,
			String checker, String testno, String ecmmodulename,
			String ecmmoduleid, String ecmcalid, String ecmcvn,
			String tcmmodulename, String tcmmoduleid, String tcmcalid,
			String tcmcvn, String ecm2modulename, String ecm2moduleid,
			String ecm2calid, String ecm2cvn, String dmcmmodulename,
			String dmcmmoduleid, String dmcmcalid, String dmcmcvn,
			String scrmodulename, String scrmoduleid, String scrcalid,
			String scrcvn, String hvbecmmodulename, String hvbecmmoduleid,
			String hvbecmcalid, String hvbecmcvn, String bcmmodulename,
			String serialno, String uuid, String cratedate, String bcmmoduleid,
			String bcmcalid, String bcmcvn, String othmodulename,
			String othmoduleid, String othcalid, String othcvn, String apass,
			String ocommunchk, String opass, String testdate, String ctest,
			String ctestlocation) {
		this.vin = vin;
		this.vehiclemodel = vehiclemodel;
		this.obd = obd;
		this.odo = odo;
		this.checker = checker;
		this.testno = testno;
		this.ecmmodulename = ecmmodulename;
		this.ecmmoduleid = ecmmoduleid;
		this.ecmcalid = ecmcalid;
		this.ecmcvn = ecmcvn;
		this.tcmmodulename = tcmmodulename;
		this.tcmmoduleid = tcmmoduleid;
		this.tcmcalid = tcmcalid;
		this.tcmcvn = tcmcvn;
		this.ecm2modulename = ecm2modulename;
		this.ecm2moduleid = ecm2moduleid;
		this.ecm2calid = ecm2calid;
		this.ecm2cvn = ecm2cvn;
		this.dmcmmodulename = dmcmmodulename;
		this.dmcmmoduleid = dmcmmoduleid;
		this.dmcmcalid = dmcmcalid;
		this.dmcmcvn = dmcmcvn;
		this.scrmodulename = scrmodulename;
		this.scrmoduleid = scrmoduleid;
		this.scrcalid = scrcalid;
		this.scrcvn = scrcvn;
		this.hvbecmmodulename = hvbecmmodulename;
		this.hvbecmmoduleid = hvbecmmoduleid;
		this.hvbecmcalid = hvbecmcalid;
		this.hvbecmcvn = hvbecmcvn;
		this.bcmmodulename = bcmmodulename;
		this.serialno = serialno;
		this.uuid = uuid;
		this.cratedate = cratedate;
		this.bcmmoduleid = bcmmoduleid;
		this.bcmcalid = bcmcalid;
		this.bcmcvn = bcmcvn;
		this.othmodulename = othmodulename;
		this.othmoduleid = othmoduleid;
		this.othcalid = othcalid;
		this.othcvn = othcvn;
		this.apass = apass;
		this.ocommunchk = ocommunchk;
		this.opass = opass;
		this.testdate = testdate;
		this.ctest = ctest;
		this.ctestlocation = ctestlocation;
	}

	// Property accessors

	public String getVin() {
		return this.vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public String getVehiclemodel() {
		return this.vehiclemodel;
	}

	public void setVehiclemodel(String vehiclemodel) {
		this.vehiclemodel = vehiclemodel;
	}

	public String getObd() {
		return this.obd;
	}

	public void setObd(String obd) {
		this.obd = obd;
	}

	public Double getOdo() {
		return this.odo;
	}

	public void setOdo(Double odo) {
		this.odo = odo;
	}

	public String getChecker() {
		return this.checker;
	}

	public void setChecker(String checker) {
		this.checker = checker;
	}

	public String getTestno() {
		return this.testno;
	}

	public void setTestno(String testno) {
		this.testno = testno;
	}

	public String getEcmmodulename() {
		return this.ecmmodulename;
	}

	public void setEcmmodulename(String ecmmodulename) {
		this.ecmmodulename = ecmmodulename;
	}

	public String getEcmmoduleid() {
		return this.ecmmoduleid;
	}

	public void setEcmmoduleid(String ecmmoduleid) {
		this.ecmmoduleid = ecmmoduleid;
	}

	public String getEcmcalid() {
		return this.ecmcalid;
	}

	public void setEcmcalid(String ecmcalid) {
		this.ecmcalid = ecmcalid;
	}

	public String getEcmcvn() {
		return this.ecmcvn;
	}

	public void setEcmcvn(String ecmcvn) {
		this.ecmcvn = ecmcvn;
	}

	public String getTcmmodulename() {
		return this.tcmmodulename;
	}

	public void setTcmmodulename(String tcmmodulename) {
		this.tcmmodulename = tcmmodulename;
	}

	public String getTcmmoduleid() {
		return this.tcmmoduleid;
	}

	public void setTcmmoduleid(String tcmmoduleid) {
		this.tcmmoduleid = tcmmoduleid;
	}

	public String getTcmcalid() {
		return this.tcmcalid;
	}

	public void setTcmcalid(String tcmcalid) {
		this.tcmcalid = tcmcalid;
	}

	public String getTcmcvn() {
		return this.tcmcvn;
	}

	public void setTcmcvn(String tcmcvn) {
		this.tcmcvn = tcmcvn;
	}

	public String getEcm2modulename() {
		return this.ecm2modulename;
	}

	public void setEcm2modulename(String ecm2modulename) {
		this.ecm2modulename = ecm2modulename;
	}

	public String getEcm2moduleid() {
		return this.ecm2moduleid;
	}

	public void setEcm2moduleid(String ecm2moduleid) {
		this.ecm2moduleid = ecm2moduleid;
	}

	public String getEcm2calid() {
		return this.ecm2calid;
	}

	public void setEcm2calid(String ecm2calid) {
		this.ecm2calid = ecm2calid;
	}

	public String getEcm2cvn() {
		return this.ecm2cvn;
	}

	public void setEcm2cvn(String ecm2cvn) {
		this.ecm2cvn = ecm2cvn;
	}

	public String getDmcmmodulename() {
		return this.dmcmmodulename;
	}

	public void setDmcmmodulename(String dmcmmodulename) {
		this.dmcmmodulename = dmcmmodulename;
	}

	public String getDmcmmoduleid() {
		return this.dmcmmoduleid;
	}

	public void setDmcmmoduleid(String dmcmmoduleid) {
		this.dmcmmoduleid = dmcmmoduleid;
	}

	public String getDmcmcalid() {
		return this.dmcmcalid;
	}

	public void setDmcmcalid(String dmcmcalid) {
		this.dmcmcalid = dmcmcalid;
	}

	public String getDmcmcvn() {
		return this.dmcmcvn;
	}

	public void setDmcmcvn(String dmcmcvn) {
		this.dmcmcvn = dmcmcvn;
	}

	public String getScrmodulename() {
		return this.scrmodulename;
	}

	public void setScrmodulename(String scrmodulename) {
		this.scrmodulename = scrmodulename;
	}

	public String getScrmoduleid() {
		return this.scrmoduleid;
	}

	public void setScrmoduleid(String scrmoduleid) {
		this.scrmoduleid = scrmoduleid;
	}

	public String getScrcalid() {
		return this.scrcalid;
	}

	public void setScrcalid(String scrcalid) {
		this.scrcalid = scrcalid;
	}

	public String getScrcvn() {
		return this.scrcvn;
	}

	public void setScrcvn(String scrcvn) {
		this.scrcvn = scrcvn;
	}

	public String getHvbecmmodulename() {
		return this.hvbecmmodulename;
	}

	public void setHvbecmmodulename(String hvbecmmodulename) {
		this.hvbecmmodulename = hvbecmmodulename;
	}

	public String getHvbecmmoduleid() {
		return this.hvbecmmoduleid;
	}

	public void setHvbecmmoduleid(String hvbecmmoduleid) {
		this.hvbecmmoduleid = hvbecmmoduleid;
	}

	public String getHvbecmcalid() {
		return this.hvbecmcalid;
	}

	public void setHvbecmcalid(String hvbecmcalid) {
		this.hvbecmcalid = hvbecmcalid;
	}

	public String getHvbecmcvn() {
		return this.hvbecmcvn;
	}

	public void setHvbecmcvn(String hvbecmcvn) {
		this.hvbecmcvn = hvbecmcvn;
	}

	public String getBcmmodulename() {
		return this.bcmmodulename;
	}

	public void setBcmmodulename(String bcmmodulename) {
		this.bcmmodulename = bcmmodulename;
	}

	public String getSerialno() {
		return this.serialno;
	}

	public void setSerialno(String serialno) {
		this.serialno = serialno;
	}

	public String getUuid() {
		return this.uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getCratedate() {
		return this.cratedate;
	}

	public void setCratedate(String cratedate) {
		this.cratedate = cratedate;
	}

	public String getBcmmoduleid() {
		return this.bcmmoduleid;
	}

	public void setBcmmoduleid(String bcmmoduleid) {
		this.bcmmoduleid = bcmmoduleid;
	}

	public String getBcmcalid() {
		return this.bcmcalid;
	}

	public void setBcmcalid(String bcmcalid) {
		this.bcmcalid = bcmcalid;
	}

	public String getBcmcvn() {
		return this.bcmcvn;
	}

	public void setBcmcvn(String bcmcvn) {
		this.bcmcvn = bcmcvn;
	}

	public String getOthmodulename() {
		return this.othmodulename;
	}

	public void setOthmodulename(String othmodulename) {
		this.othmodulename = othmodulename;
	}

	public String getOthmoduleid() {
		return this.othmoduleid;
	}

	public void setOthmoduleid(String othmoduleid) {
		this.othmoduleid = othmoduleid;
	}

	public String getOthcalid() {
		return this.othcalid;
	}

	public void setOthcalid(String othcalid) {
		this.othcalid = othcalid;
	}

	public String getOthcvn() {
		return this.othcvn;
	}

	public void setOthcvn(String othcvn) {
		this.othcvn = othcvn;
	}

	public String getApass() {
		return this.apass;
	}

	public void setApass(String apass) {
		this.apass = apass;
	}

	public String getOcommunchk() {
		return this.ocommunchk;
	}

	public void setOcommunchk(String ocommunchk) {
		this.ocommunchk = ocommunchk;
	}

	public String getOpass() {
		return this.opass;
	}

	public void setOpass(String opass) {
		this.opass = opass;
	}

	public String getTestdate() {
		return this.testdate;
	}

	public void setTestdate(String testdate) {
		this.testdate = testdate;
	}

	public String getCtest() {
		return this.ctest;
	}

	public void setCtest(String ctest) {
		this.ctest = ctest;
	}

	public String getCtestlocation() {
		return this.ctestlocation;
	}

	public void setCtestlocation(String ctestlocation) {
		this.ctestlocation = ctestlocation;
	}

}