<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt;  background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript">	
$(function() {

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	$("#create").attr("disabled", true);
	$("#update").attr("disabled", true);
	$("#delete").attr("disabled", true);
	$("#effect").attr("disabled", true);
	$("#published").attr("disabled", true);
	$("#import").attr("disabled", true);
	$("#export").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("update")!=-1){
					$("#update").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("delete")!=-1){
					$("#delete").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("effect")!=-1){
					$("#effect").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("published")!=-1){
					$("#published").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("import")!=-1){
					$("#import").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#query").click(function(){
		var startProdDate = $('#startProdDate').val();
		var endProdDate = $('#endProdDate').val();
		var vin = $('#vin').val();
		var clxh = $('#clxh').val();
		var fdjxh = $('#fdjxh').val();
		if(startProdDate==""&&endProdDate==""&&vin==""&&clxh==""&&fdjxh==""){
		 	var messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请输入查询条件！');
   	   		messageObj.dialog('open');
		}else{
			var obj = $('#startProdDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			obj = $('#endProdDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			location.href="carRecallQuery.action?startProdDate="+startProdDate+"&endProdDate="+endProdDate
				+"&vin="+encodeURI(encodeURI(vin))+"&clxh="+encodeURI(encodeURI(clxh))+"&fdjxh="+encodeURI(encodeURI(fdjxh))+"&menuid="+menuid;
		}
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });
	
	function display(id){
		jQuery.ajax({
        url: 'business/carRecallQuery!carModelInfo.action',		           
        data: {'c1':id.split(",")[0],'vercode':id.split(",")[1]}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carObj = eval("("+content.toString()+")");
	        	var dialogObj = $('#car_display_dialog');
				setDialogValue(dialogObj,carObj);
				$('input').attr("readonly", true);
				dialogObj.data('title.dialog', '查看召回备案信息').dialog('open');
				
	        }
	    });

		return false;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#c1').val(jsonObj[0].id.c1);
		dialogObj.find('#vercode').val(jsonObj[0].id.vercode);
		dialogObj.find('#vin').val(jsonObj[1].vin);

		dialogObj.find('#engineNo').val(jsonObj[1].engineNo);
		dialogObj.find('#c2').val(jsonObj[0].c2);
		dialogObj.find('#prodDate').val(jsonObj[1].prodDate);
		dialogObj.find('#engineType').val(jsonObj[1].engineType);
		dialogObj.find('#c3').val(jsonObj[0].c3);
		dialogObj.find('#c4').val(jsonObj[0].c4);
		dialogObj.find('#c5').val(jsonObj[0].c5);
		dialogObj.find('#c6').val(jsonObj[0].c6);
		dialogObj.find('#c7').val(jsonObj[0].c7);
		dialogObj.find('#c8').val(jsonObj[0].c8);
		dialogObj.find('#c9').val(jsonObj[0].c9);
		dialogObj.find('#c10').val(jsonObj[0].c10);
		dialogObj.find('#c11').val(jsonObj[0].c11);
		dialogObj.find('#c12').val(jsonObj[0].c12);
	}

	$("#car_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 700,height: 440,modal: true,
		buttons: {
			'关闭': function() {
				$(this).dialog('close');
			}
		},
		close: function() {
			$(this).find('input').attr('value','');
		}
	});

	$('#export').click(function() {
		var startProdDate = $('#startProdDate').val();
		var endProdDate = $('#endProdDate').val();
		var vin = $('#vin').val();
		var clxh = $('#clxh').val();
		var fdjxh = $('#fdjxh').val();
		location.href="business/carRecallQuery!exportData.action?startProdDate="+startProdDate+"&endProdDate="+endProdDate
		+"&vin="+encodeURI(encodeURI(vin))+"&clxh="+encodeURI(encodeURI(clxh))+"&fdjxh="+encodeURI(encodeURI(fdjxh));
	});

	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var obj = $('#startProdDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			obj = $('#endProdDate');
			if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				return ;
			}
			var startProdDate = $('#startProdDate').val();
			var endProdDate = $('#endProdDate').val();
			var vin = $('#vin').val();
			var clxh = $('#clxh').val();
			var fdjxh = $('#fdjxh').val();
			location.href="carRecallQuery.action?currentPage="+$('#jump').val()+"&startProdDate="+startProdDate+"&endProdDate="+endProdDate
				+"&vin="+encodeURI(encodeURI(vin))+"&clxh="+encodeURI(encodeURI(clxh))+"&fdjxh="+encodeURI(encodeURI(fdjxh))+"&menuid="+menuid;
   		}
   		
    });
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var obj = $('#startProdDate');
				if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
					obj.focus();
					return ;
				}
				obj = $('#endProdDate');
				if(obj.val()!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
					alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
					obj.focus();
					return ;
				}
			  	var startProdDate = $('#startProdDate').val();
				var endProdDate = $('#endProdDate').val();
				var vin = $('#vin').val();
				var clxh = $('#clxh').val();
				var fdjxh = $('#fdjxh').val();
				location.href=$(this).attr('value')+"&startProdDate="+startProdDate+"&endProdDate="+endProdDate
					+"&vin="+encodeURI(encodeURI(vin))+"&clxh="+encodeURI(encodeURI(clxh))+"&fdjxh="+encodeURI(encodeURI(fdjxh))+"&menuid="+menuid;
		 });
	});

	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif',
	   		buttonImageOnly: true}, $.datepicker.regional['zh']));
	var date = new Date();
	$('#startProdDate').datepicker($.datepicker.regional['zh']);
	$('#endProdDate').datepicker($.datepicker.regional['zh']);
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

           //给这行添加class值为over，并且当鼠标一出该行时执行函数

           $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" width="250">
					VIN:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.vin!=null"> value="<s:property value="#request.vin" />"</s:if>/>
				</td>
				<td align="left" width="470">	
					生产日期：&nbsp;&nbsp;<input type="text" id="startProdDate" name="startProdDate" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.startProdDate!=null"> value="<s:property value="#request.startProdDate" />"</s:if>/>至
					<input type="text" id="endProdDate" name="endProdDate" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.endProdDate!=null"> value="<s:property value="#request.endProdDate" />"</s:if>/>
				</td>
			</tr>
			<tr>
				<td align="left">
					生产车型：<input type="text" id="clxh" name="clxh" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.clxh!=null"> value="<s:property value="#request.clxh" />"</s:if>/>
				</td>
				<td align="left">	
					发动机型号：<input type="text" id="fdjxh" name="fdjxh" class="text ui-widget-content ui-corner-all"  size="20" <s:if test="#request.fdjxh!=null"> value="<s:property value="#request.fdjxh" />"</s:if>/>
  			    </td>
				<td>&nbsp;</td>
			</tr>
  </table>
  <table width="100%">
			<tr>
			  	<td align="right">
			  		<button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button>&nbsp;
			  		<button id="export" class="ui-button ui-state-default ui-corner-all">导出</button>
			  	</td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
			    <th width="10%">VIN</th>
			    <th width="10%">发动机型号</th>
			    <th width="10%">发动机号</th>
				<th width="10%">生产日期</th>
			    <th width="10%">公告车型</th>
			    <th width="10%">版本号</th>
			    <th width="6%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.pageData" status="obj" id="object"> 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><s:property value="#object[1].vin" /></td>
			  		<td><s:property value="#object[1].engineType" /></td>
			  		<td><s:property value="#object[1].engineNo" /></td>
			  		<td><s:property value="#object[1].prodDate" /></td>
			  		<td><s:property value="#object[0].model" /></td>
			  		<td><s:property value="#object[0].id.vercode" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value='<s:property value="#object[0].id.c1" />,<s:property value="#object[0].id.vercode" />'>查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecallQuery.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecallQuery.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecallQuery.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="carRecallQuery.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="car_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
	  	  <table width="100%">
	  	  	<tr>
				<td><label><P>VIN</label></td>
				<td><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="18" maxlength="7"/></td>
			</tr>
	    	<tr>
				<td><label><P>生产车型</label></td>
				<td><input type="text" id="c1" name="c1" class="text ui-widget-content ui-corner-all" size="20" maxlength="18"/></td>
				<td><label><P>版本号</label></td>
				<td><input type="text" id="vercode" name="vercode" class="text ui-widget-content ui-corner-all" size="25" readonly=true/></td>
			</tr>
			<tr>
				<td><label><P>发动机号</label></td>
				<td><input type="text" id="engineNo" name="engineNo" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>生产日期</label></td>
				<td><input type="text" id="prodDate" name="prodDate" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>
				<td><label><P>车身型式</label></td>
				<td><input type="text" id="c2" name="c2" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>发动机型号</label></td>
				<td><input type="text" id="engineType" name="engineType" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>	
				<td><label><P>变速器型号</label></td>
				<td><input type="text" id="c3" name="c3" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>安全带装备情况</label></td>
				<td><input type="text" id="c4" name="c4" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>	
				<td><label><P>安全气囊装备情况</label></td>
				<td><input type="text" id="c5" name="c5" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>制动系统装备情况<br>（是否装备ABS）</label></td>
				<td><input type="text" id="c6" name="c6" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>	
				<td><label><P>其它系统配置1</label></td>
				<td><input type="text" id="c7" name="c7" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>其它系统配置2</label></td>
				<td><input type="text" id="c8" name="c8" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>
				<td><label><P>其它系统配置3</label></td>
				<td><input type="text" id="c9" name="c9" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>其它系统配置4</label></td>
				<td><input type="text" id="c10" name="c10" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
			<tr>
				<td><label><P>其它系统配置5</label></td>
				<td><input type="text" id="c11" name="c11" class="text ui-widget-content ui-corner-all" size="18"/></td>
				<td><label><P>其它系统配置6</label></td>
				<td><input type="text" id="c12" name="c12" class="text ui-widget-content ui-corner-all" size="18"/></td>
			</tr>
		</Table>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>
<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>