package com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.action;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.business.fuelupload.service.FuelUploadService;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

public class FuelUploadStateAction extends BaseSupportAction{

	private Logger logger = Logger.getLogger(FuelUploadStateAction.class.getName());
	private FuelUploadService fuelUploadService;
	private String currentPage = null;
	public FuelUploadService getFuelUploadService() {
		return fuelUploadService;
	}
	public void setFuelUploadService(FuelUploadService fuelUploadService) {
		this.fuelUploadService = fuelUploadService;
	}
	public String getCurrentPage() {
		return currentPage;
	}
	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}
	public String execute(){
		
		try {
			SysUser user = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", user.getRoleid());

			Page page = new Page();
			List results = null;

			if (this.currentPage != null && !this.currentPage.trim().equals("")) {
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			} else {
				page.setCurrentPage(1);
			}
			Map<String, String> params = new HashMap<String, String>();
			
			results = fuelUploadService.findFuelUploadStateModify(page, params);
			
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("fuelUploadStatePageData", results);
		}catch (DataAccessException e) {
			logger.error("Action DataAccessException Error:", e);
			throw e;
		} catch (SystemException e) {
			logger.error("Action SystemException Error:", e);
			throw e;
		} catch (RuntimeException e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		} catch (Exception e) {
			logger.error("Action Runtime Error:", e);
			throw new SystemException("Action Runtime Error:", e);
		}

		return SUCCESS;
	}
}
