<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .4em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">	
$(function() {
	var allFields = null;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';
	$("#export").attr("disabled", true);
	$("#cancel").attr("disabled", true);

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("cancel")!=-1){
					$("#cancel").attr("disabled", false);
				}
				if(operatePerrmission[i].flag.indexOf("export")!=-1){
					$("#export").attr("disabled", false);
				}
            }
        }
    });
	
	$("#coc_cancel_dialog").dialog({bgiframe: true,autoOpen: false,width: 480,height: 240,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'作废': function() {
				if(allFields==null){
					var parent = $('#coc_cancel_dialog');
					allFields = $([]).add(parent.find('#vin'));
				}
				allFields.removeClass('ui-state-error');
				if(validate('#coc_cancel_dialog')==true){
					var dlgButton = $('.ui-dialog-buttonpane button');//
					dlgButton.attr('disabled', 'disabled');
			    	dlgButton.addClass('ui-state-disabled');
			    	
					jQuery.ajax({
			            url: 'business/cocCancel!isCOCPrint.action?vin='+$(this).find('#vin').val(),		           
			            //data: $('#createForm').serialize(), 
				        type: 'POST',
			            beforeSend: function() {
			            
			            },
			            error: function(request) {
			                
			            },
			            success: function(data) {					       									
				            if(json2Bean(data).json=="false"){
				            	var dlgButton = $('.ui-dialog-buttonpane button');
								dlgButton.attr('disabled', false);
						        dlgButton.removeClass('ui-state-disabled');
				            	updateTips($('#coc_cancel_dialog').find('#validateTips'),'VIN号为['+$('#coc_cancel_dialog').find('#vin').val()+']的COC证书未打印过或者不存在，不能作废！');		
							}else{
								var messageObj = $('#operate_dialog');
								var dlgButton = $('.ui-dialog-buttonpane button');
								dlgButton.attr('disabled', false);
						        dlgButton.removeClass('ui-state-disabled');
					   	   		messageObj.find('#message').text('提示:确定作废VIN号为['+$('#coc_cancel_dialog').find('#vin').val()+']的COC证书？');
					   	   		messageObj.data('title.dialog', '确认窗口').dialog('open');	
					   	   		messageObj.find('#vin').val($('#coc_cancel_dialog').find('#vin').val());
							}
			            }
			        });
					
				}
			}
			
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}	
		}
		});

	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				$(this).dialog('close');
			},
			'确定': function() {
				var dlgButton = $('.ui-dialog-buttonpane button');
				dlgButton.attr('disabled', 'disabled');
		        dlgButton.removeClass('ui-state-disabled');
				var formObj = $(this).find('#operateForm');
				formObj[0].action = "business/cocCancel!cancelCOC.action";
				formObj[0].submit();
			}
		}
	});

	$("#coc_cancel_display_dialog").dialog({bgiframe: true,autoOpen: false,width: 800,height: 300,modal: true,
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			}
		},
		close: function() {
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}	
		}
		});

	$("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});
	
	$("#cancel").click(function() {
		$('#coc_cancel_dialog').data('title.dialog', '作废COC证书窗口').dialog('open');	
	});

	$(".display").each(function(i){
		  $(this).click(function() {
			  display($(this).attr('value'));
		 });
	  });

	function display(id){
		jQuery.ajax({
	        url: 'business/cocCancel!carCancelInfo.action?vin='+id,		           
	        //data: $('#createForm').serialize(), 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	            var content = json2Bean(data).json;
	            var carCancelObj = eval("("+content.toString()+")"); 
	        	var dialogObj = $('#coc_cancel_display_dialog');
	        	setDialogValue(dialogObj,carCancelObj);					
	       	   	dialogObj.dialog('open');
	        }
	    });

		return false;
	}
	
	function validate(parent){
		var obj = $(parent).find('#vin');
		if(!checkLength(obj,1,100)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'VIN字段不能为空！');			
			return false;
		}
		
		return true;
	}

	function setDialogValue(dialogObj,jsonObj){
		dialogObj.find('#vin').val(jsonObj.id.vin);
		dialogObj.find('#cocNum').val(jsonObj.id.cocNum);
		dialogObj.find('#model').val(jsonObj.model);
		dialogObj.find('#engineNo').val(jsonObj.engineNo);
		dialogObj.find('#engineType').val(jsonObj.engineType);
		dialogObj.find('#color').val(jsonObj.color);
		dialogObj.find('#prodDate').val(jsonObj.prodDate);
		dialogObj.find('#remark').val(jsonObj.remark);
		dialogObj.find('#creator').val(jsonObj.creator);
		dialogObj.find('#time').val(jsonObj.time);
	}
	
	function clear(dialogObj){
		dialogObj.find('#vin').val("");
		dialogObj.find('#cocNum').val("");
		dialogObj.find('#model').val("");
		dialogObj.find('#engineNo').val("");
		dialogObj.find('#engineType').val("");
		dialogObj.find('#color').val("");
		dialogObj.find('#prodDate').val("");
		dialogObj.find('#remark').val("");
		dialogObj.find('#creator').val("");
		dialogObj.find('#time').val("");
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			location.href="cocCancel.action?currentPage="+$('#jump').val()+"&vin="+encodeURI(encodeURI($('#vin').val()))+"&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val()+"&menuid="+menuid;   
   		}   
   		
    });

	$("#query").click(function() {
		var vin = $('#vin').val();
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		
		if(vin==""&&startDate==""&&endDate==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入查询条件！');
			messageObj.dialog("open");
		}else{
			var obj = $('#startDate');
			if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}

			obj = $('#endDate');
			if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			
			location.href="cocCancel.action?currentPage="+$('#jump').val()+"&vin="+encodeURI(encodeURI(vin))+"&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val()+"&menuid="+menuid;
		}
	});

	$("#export").click(function() {
		var vin = $('#vin').val();
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		
		if(vin==""&&startDate==""&&endDate==""){
			var messageObj = $('#message_dialog');
			messageObj.find('#message').text('警告:请输入查询条件！');
			messageObj.dialog("open");
		}else{
			var obj = $('#startDate');
			if(startDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}

			obj = $('#endDate');
			if(endDate!=""&&!checkRegexp(obj,/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)){
				alert("时间格式不正确！提示:时间格式为yyyy-mm-dd");
				obj.focus();
				
				return ;
			}
			
			location.href="business/cocCancel!exportGasVer.action?currentPage="+$('#jump').val()+"&vin="+encodeURI(encodeURI(vin))+"&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val();
		}
	});
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
			  	var vin = $('#vin').val();
				var startDate = $('#startDate').val();
				var endDate = $('#endDate').val();
				
				location.href=$(this).attr('value')+"&vin="+encodeURI(encodeURI(vin))+"&startDate="+startDate+"&endDate="+endDate+"&menuid="+menuid;
		 });
	  });
	
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif', buttonImageOnly: true}, $.datepicker.regional['zh']));
	$("#startDate").datepicker($.datepicker.regional['zh']);
	$("#endDate").datepicker($.datepicker.regional['zh']);
    
});
$(document).ready(function() { 
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

          //给这行添加class值为over，并且当鼠标一出该行时执行函数

          $(this).removeClass("over");})    //移除该行的class


});
</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%">
			<tr>
				<td align="left" colspan="5"><p>VIN:<input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all"  size="18" <s:if test="#request.vin!=null"> value="<s:property value="#request.vin" />"</s:if>/>  			    
				 作废日期:<input type="text" id="startDate" class="text ui-widget-content " size="10" <s:if test="#request.startDate!=null"> value="<s:property value="#request.startDate" />" </s:if>/>-<input type="text" id="endDate" class="text ui-widget-content"  size="10" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />" </s:if>/>
				</td>
				<td align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr><td width="90%"></td>
			  <td width="7%" align="right"></td>
			  <td width="7%" align="right"></td>
			  <td width="7%" align="right"></td>
  			 
  			  <td width="60" align="right"><button id="export" class="ui-button ui-state-default ui-corner-all">导出</button></td>
			   <td width="60" align="right"><button id="cancel" class="ui-button ui-state-default ui-corner-all">作废</button></td>
			</tr>
  </table>
  </tr>
  </td>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
			    <th width="12%">VIN</th>
			    <th width="11%">COC流水号</th>
			    <th width="8%">车型</th>
				<th width="5%">颜色</th>
				<th width="8%">生产日期</th>
				<th width="10%">作废人</th>
				<th width="16%">作废时间</th>
				<th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator value="#request.carCancelPageData" status="obj" > 
					<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><s:property value="id.vin" /></td>
			  		<td><s:property value="id.cocNum" /></td>	
			  		<td><s:property value="model" /></td>		
			  		<td><s:property value="color" /></td>
			  		<td><s:property value="prodDate" /></td>
			  		<td><s:property value="creator" /></td>
			  		<td><s:property value="time" /></td>
			  		<td><a class='display' onclick="return false;" href='#' value="<s:property value="id.vin" />,<s:property value="id.cocNum" />">查看</a></td>
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.carCancelPage.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocCancel.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.carCancelPage.currentPage==#request.carCancelPage.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage'onclick="return false;" href='#'  value="cocCancel.action?currentPage=<s:property value="#request.carCancelPage.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.carCancelPage.currentPage>=#request.carCancelPage.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocCancel.action?currentPage=<s:property value="#request.carCancelPage.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.carCancelPage.currentPage==#request.carCancelPage.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="cocCancel.action?currentPage=<s:property value="#request.carCancelPage.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center"><p>当前页数 <s:property value="#request.carCancelPage.currentPage" />/总页数 <s:property value="#request.carCancelPage.maxPage" /> 总记录数 <s:property value="#request.carCancelPage.pageSum" />条</td>
			  	<td width="15%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>           
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
</div>
<div id="coc_cancel_dialog" title="">
	<p id="validateTips"></p>
	<fieldset> 
	  <table width="100%">
	    	<tr>
	    		<td width="1%"><label><P>VIN:</label></td>
				<td width="17%"><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="20"/></td>
			</tr>		
		</Table>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='vin' name='vin'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="coc_cancel_display_dialog" title="查看窗口">
	<p id="validateTips"></p>
	<fieldset>
	  <table width="100%">
	    	<tr>
	    		<td width="6%"><label><P>VIN</label></td>
				<td width="20%" align="left"><input type="text" id="vin" name="vin" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
				<td width="8%"><label><P>COC流水号</label></td>
				<td width="20%" align="left"><input type="text" id="cocNum" name="cocNum" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
				<td width="6%"><label><P>车型</label></td>
				<td width="20%" align="left"><input type="text" id="model" name="model" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
			</tr>
			<tr>	
				<td><label><P>发动机号</label></td>
				<td><input type="text" id="engineNo" name="engineNo" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
				<td><label><P>发动机类型</label></td>
				<td><input type="text" id="engineType" name="engineType" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
				<td><label><P>颜色</label></td>
				<td><input type="text" id="color" name="color" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
			</tr>
			<tr>
				<td><label><P>生产日期</label></td>
				<td><input type="text" id="prodDate" name="prodDate" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
				<td><label><P>作废人</label></td>
				<td><input type="text" id="creator" name="creator" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
				<td><label><P>作废时间</label></td>
				<td><input type="text" id="time" name="time" class="text ui-widget-content ui-corner-all" size="18" readonly/></td>
			</tr>
		</Table>
	</fieldset>
</div>
<div id="message_dialog" title="提示窗口">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>
</body>
</html>