package com.dawnpro.dfpv.carfilemanager.module.business.share.service.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.business.share.dao.InterfaceLogDAO;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceDefine;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceMsgLog;
import com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceProcdureLog;
import com.dawnpro.dfpv.carfilemanager.module.business.share.service.InterfaceService;

public class InterfaceServiceImpl implements InterfaceService {
	private InterfaceLogDAO interfaceLogDAO = null;
	private PaginationService paginationService = null;
	
	

	public InterfaceLogDAO getInterfaceLogDAO() {
		return interfaceLogDAO;
	}

	public void setInterfaceLogDAO(InterfaceLogDAO interfaceLogDAO) {
		this.interfaceLogDAO = interfaceLogDAO;
	}

	public PaginationService getPaginationService() {
		return paginationService;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public void addInterfaceDefine(InterfaceDefine def) {
		// TODO Auto-generated method stub

	}

	public void addInterfaceMsgLog(InterfaceMsgLog msglog) {
		// TODO Auto-generated method stub

	}

	public void addInterfaceProdLog(InterfaceProcdureLog prodlog) {
		// TODO Auto-generated method stub

	}

	public List<?> paginationDef(Page page, Object[] params) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<?> paginationMsgLog(Page page, Object[] params) {
		// TODO Auto-generated method stub
		return null;
	}

	public InterfaceDefine loadInterfaceDefine(String id) {
		// TODO Auto-generated method stub
		return null;
	}

	public InterfaceMsgLog loadInterfaceMsgLog(String id) {
		// TODO Auto-generated method stub
		return null;
	}

	public InterfaceProcdureLog loadInterfaceProdLog(String id) {
		// TODO Auto-generated method stub
		return null;
	}

	public void updateInterfaceDefine(Object def) {
		// TODO Auto-generated method stub

	}

	public void updateInterfaceMsgLog(InterfaceMsgLog def) {
		// TODO Auto-generated method stub

	}

	public void updateInterfaceProdLog(InterfaceProcdureLog prodlog) {
		// TODO Auto-generated method stub

	}

	public void deleteInterfaceDefine(Object def) {
		// TODO Auto-generated method stub

	}

}
