package com.dawnpro.dfpv.carfilemanager.module.system.action;

import java.io.UnsupportedEncodingException;
import java.util.List;

import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.action.BaseSupportAction;
import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;
import com.dawnpro.dfpv.carfilemanager.module.system.service.SysLogService;

public class SysLogAction extends BaseSupportAction{
	private static final long serialVersionUID = -6371005813002910434L;
	private Logger logger = Logger.getLogger(SysLogAction.class.getName());
	
	private SysLogService service=null;
	
	private String currentPage = null;
	private String startDepdatetime=null;
	private String endDepdatetime=null;
	private String sLoginName=null;
	private String sDescript=null;

	public void setService(SysLogService service) {
		this.service = service;
	}

	public void setCurrentPage(String currentPage) {
		this.currentPage = currentPage;
	}
	
	public void setStartDepdatetime(String startDepdatetime) {
		this.startDepdatetime = startDepdatetime;
	}

	public void setEndDepdatetime(String endDepdatetime) {
		this.endDepdatetime = endDepdatetime;
	}

	public void setsLoginName(String sLoginName) {
		this.sLoginName = sLoginName;
	}

	public void setsDescript(String sDescript) {
		this.sDescript = sDescript;
	}

	public String execute(){
		try{
			SysUser sysuser = (SysUser) this.getSession().get(SYSTEM_USER);
			this.getServletRequest().setAttribute("menuid", this.getMenuid());
			this.getServletRequest().setAttribute("roleid", sysuser.getRoleid());
			
			Page page = new Page();
			List results = null;
			
			if(this.currentPage!=null&&!this.currentPage.trim().equals("")){
				page.setCurrentPage(Integer.valueOf(this.currentPage.trim()));
			}else{
				page.setCurrentPage(1);
			}
			this.sLoginName = sLoginName==null?"":sLoginName;
			this.sDescript = sDescript==null?"":sDescript;
			this.sLoginName = java.net.URLDecoder.decode(this.sLoginName, "UTF-8");
			this.sDescript = java.net.URLDecoder.decode(this.sDescript, "UTF-8");
			if((this.startDepdatetime==null||this.startDepdatetime.equals(""))&&(this.endDepdatetime==null||this.endDepdatetime.equals(""))&&(this.sLoginName==null||this.sLoginName.equals(""))&&(this.sDescript==null||this.sDescript.equals(""))){
				results = this.service.pagination(page,null);
			}else{
				results = this.service.pagination(page,new Object[]{this.startDepdatetime,this.endDepdatetime,this.sLoginName,this.sDescript});
			}
			
			this.getServletRequest().setAttribute("startDepdatetime",this.startDepdatetime==null?"":this.startDepdatetime.trim());
			this.getServletRequest().setAttribute("endDepdatetime",this.endDepdatetime==null?"":this.endDepdatetime.trim());
			this.getServletRequest().setAttribute("sLoginName", this.sLoginName==null?"":this.sLoginName.trim());
			this.getServletRequest().setAttribute("sDescript",this.sDescript==null?"":this.sDescript.trim());
			this.getServletRequest().setAttribute("page", page);
			this.getServletRequest().setAttribute("pageData", results);
		}catch(DataAccessException e){
			logger.error("Action DataAccessException Error:",e);
			throw e;
		}catch(SystemException e){
			logger.error("Action SystemException Error:",e);
			throw e;
		}catch(RuntimeException e){
			logger.error("Action Runtime Error:",e);
			throw new SystemException("Action Runtime Error:",e);
		} catch (UnsupportedEncodingException e) {
			logger.error("Action UnsupportedEncodingException Error:",e);
			e.printStackTrace();
		}
		return SUCCESS;
	}
	
}
