package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryId;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

/**
 *
 * @author:
 * @since: 2010-4-1
 * **/
public interface SysDataDictionaryDAO {
	
	public void addDataDictionary(DataDictionary dataDictionary) throws DataAccessException;
	
	public void deleteDataDictionary(DataDictionary dataDictionary) throws DataAccessException;
	
	public void deleteDataDictionary(DataDictionary[] dataDictionary) throws DataAccessException;
	
	public void updateDataDictionary(DataDictionary dataDictionary) throws DataAccessException;
	
	public List<DataDictionary> findDataDictionary(String sql, String[] params) throws DataAccessException;
	
	public DataDictionary loadDataDictionaryObj(DataDictionaryId id) throws DataAccessException;
	/**
	 * 
	 * @param	sql:
	 * **/
	public List<DataDictionary> findDataDictionary(String sql);
	
}
