package com.dawnpro.dfpv.carfilemanager.module.system.service;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;

import com.dawnpro.dfpv.carfilemanager.base.exception.SystemException;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.common.pagination.model.Page;
import com.dawnpro.dfpv.carfilemanager.common.pagination.service.PaginationService;
import com.dawnpro.dfpv.carfilemanager.module.system.dao.SysRoleDAO;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysRole;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

public class SysRoleServiceImpl implements SysRoleService{
	private Logger logger = Logger.getLogger(SysRoleServiceImpl.class.getName());
	private SysRoleDAO dao;
	private PaginationService paginationService = null;

	public void setDao(SysRoleDAO dao) {
		this.dao = dao;
	}

	public void setPaginationService(PaginationService paginationService) {
		this.paginationService = paginationService;
	}

	public void addRole(SysRole role) throws DataAccessException {
		try{
			this.dao.addRole(role);
		}catch(DataAccessException e){
			logger.error("addRole Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("addRole Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public void deleteRole(SysRole[] role) throws DataAccessException {
		try{
			this.dao.deleteRole(role);
		}catch(DataAccessException e){
			logger.error("deleteRole Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("deleteRole Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public List<SysRole> findRole(String sql) throws DataAccessException {
		try{
			return this.dao.findRole(sql);
		}catch(DataAccessException e){
			logger.error("findRole Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("findRole Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public SysRole loadRole(String roleId) throws DataAccessException {
		SysRole gg = null;
		try{
			gg = this.dao.loadRoleObj(roleId);
		}catch(DataAccessException e){
			throw new SystemException("loadRole Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("loadRole Method Error:",e);
		}
		
		return gg;
	}

	public List<?> pagination(Page page) throws DataAccessException {
		List results = null;
		try{
			this.paginationService.getPage().setCurrentPage(page.getCurrentPage());
			this.paginationService.countPageSum("select count(g.roleid) from SysRole g order by g.time desc");
			results = this.paginationService.pagination("from SysRole g order by g.time desc");
			BeanUtils.copyProperties(page,this.paginationService.getPage());
		}catch(DataAccessException e){
			throw new SystemException("pagination Method Error:",e);
		} catch (IllegalAccessException e) {
			throw new SystemException("pagination Method Error:",e);
		} catch (InvocationTargetException e) {
			throw new SystemException("pagination Method Error:",e);
		}catch(RuntimeException e){
			throw new SystemException("pagination Method Error:",e);
		}
		return results;
	}

	public void updateRole(SysRole role) throws DataAccessException {
		try{
			this.dao.updateRole(role);
		}catch(DataAccessException e){
			logger.error("updateRole Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("updateRole Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
	}

	public boolean isRoleExist(String name) throws DataAccessException {
		try{
			String sql="from SysRole where name='"+name+"'";
			List<SysRole> result=this.dao.findRole(sql);
			if(result.size()>0)
				return true;
		}catch(DataAccessException e){
			logger.error("isRoleExist Method DataAccess Error:",e);
			throw e;
		}catch(RuntimeException e1){
			logger.error("isRoleExist Method Error:",e1);
			throw new SystemException("ogin Method Error:",e1);
		}
		return false;
	}

}
