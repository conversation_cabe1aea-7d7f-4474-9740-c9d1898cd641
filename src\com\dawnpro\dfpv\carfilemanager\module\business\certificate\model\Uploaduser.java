package com.dawnpro.dfpv.carfilemanager.module.business.certificate.model;

/**
 * Uploaduser generated by MyEclipse Persistence Tools
 */

public class Uploaduser implements java.io.Serializable {

	// Fields

	private String username;

	private String password;

	private String qyjmxx;

	private String flag;

	// Constructors

	/** default constructor */
	public Uploaduser() {
	}

	/** minimal constructor */
	public Uploaduser(String username) {
		this.username = username;
	}

	/** full constructor */
	public Uploaduser(String username, String password, String qyjmxx,
			String flag) {
		this.username = username;
		this.password = password;
		this.qyjmxx = qyjmxx;
		this.flag = flag;
	}

	// Property accessors

	public String getUsername() {
		return this.username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getQyjmxx() {
		return this.qyjmxx;
	}

	public void setQyjmxx(String qyjmxx) {
		this.qyjmxx = qyjmxx;
	}

	public String getFlag() {
		return this.flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

}