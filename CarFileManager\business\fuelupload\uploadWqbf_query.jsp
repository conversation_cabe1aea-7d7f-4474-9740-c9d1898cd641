<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
        div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
	.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
	tr.over td {background:#bcd4ec;}
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/dhtmlxcommon.js"></script>
<script type="text/javascript" src="js/dhtmlxtree.js"></script>
<style type="text/css">
    #ui-datepicker-div
    {
        z-index: 9999999;
    }
</style>
<script type="text/javascript">	
$(function() {
	var allFields = null;
	var type = null;
	var impFlg = null;
	var isprint = false;

	var menuid = '<%= String.valueOf(request.getAttribute("menuid"))%>';
	var param = 'menuid=<%= String.valueOf(request.getAttribute("menuid"))%>&roleid=<%= String.valueOf(request.getAttribute("roleid"))%>';

	
	jQuery.ajax({
        url: 'system/sysPermission!menuOperateToRolePermission.action',		           
        data:param,
        type: 'POST',
        dataType:'json',
        beforeSend: function() {
        
        },
        error: function(request) {
            
        },
        success: function(data) {		
        	var content = data.json;
            var operatePerrmission = eval("("+content.toString()+")");
            for(var i=0;i<operatePerrmission.length;i++){
				if(operatePerrmission[i].flag.indexOf("add")!=-1){
					$("#create").attr("disabled", false);
				}
				
            }
        }
    });
 

	$("#query").click(function(){
		//var frm = $("#qfrm");
		//frm.action = "business/uploadWqbfQuery.action";
		//frm.submit();
		var vin = $("#pvin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
		location.href="/CarFileManager/business/uploadWqbfQuery.action?pvin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
	});
	
	
	$("#create").click(function() {
		var id = "";
       	var index = 0;
       	var messageObj = null;
	
      	type = "add";
   	 	$('#uploadWqbfQuery_dialog').data('title.dialog', '新增').dialog('open');
   	   	 
	});

	$("#update").click(function() {
       	var id = "";
       	var index = 0;
       	var messageObj = null;
       	var color = "";
       	var zxcx = "";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
				index++;
				id = this.value; 
             }
      	});
      	
      	
      	
   		if(index==1){
   			type = "update";   			
   			jQuery.ajax({
	            url: 'business/uploadWqbfQuery!getTypicalityNeutral.action',		           
	            data: {'vin' : id}, 
		        type: 'POST',
	            beforeSend: function() {
	            
	            },
	            error: function(request) {
	                alert("系统错误，请与管理员联系！");
	            },
	            success: function(data) {
		            var content = json2Bean(data).json;
		            var carObj = eval("("+content.toString()+")"); 
	            	var dialogObj = $('#uploadWqbfQuery_dialog');
					setDialogValue(dialogObj,carObj);
					dialogObj.find('#vin').attr('readonly',true);
	       	    	dialogObj.data('title.dialog', '修改').dialog('open');
	       	 		$('#vtable').find('tr:eq(0)').hide(); 
	            }
	        });
   			
   	    	
   	   	}else if(index<1){
   	   	 	messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选要修改的数据！');
   	   		messageObj.dialog('open');
   	   	 }else if(index>1){
			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:一次只能修改一条数据！');
	   		messageObj.dialog('open');
   	   	 }
    	
	});

	$("#delete").click(function() {
		var id = "";
		var info = "";
       	var index = 0;
       	var effIndex = 0;
       	var effId = "";
       	var messageObj = null;
       	var state="";
        var checkedObj = $('#users-contain').find("[name='checkPK'][@checked]"); 
      	checkedObj.each(function(){
         	if(this.checked==true){
         		index++;					
				if(id==""){
					id = this.value;
					info = this.parentElement.parentElement.children[1].innerText;
				}else{
					id = id+"&"+this.value; 
					info = info+"&"+this.parentElement.parentElement.children[1].innerText;
				}				
             }
      	});
		
   		if(index==0){
   			messageObj = $('#message_dialog');
   	   		messageObj.find('#message').text('警告:请选择要删除的数据！');
   	   		messageObj.dialog('open');
   	   	}else{
 			type = "delete";
  	   		messageObj = $('#operate_dialog');
  	   		messageObj.find('#message').text('提示:确定删除['+info+'] 共'+index+'条数据');
  	   		messageObj.dialog('open');
  	   		messageObj.find('#ids').val(id);
   	   		
   	   	}
	
	});
	
    //导出COC结果
    $('#exportfueltar').click(function() {
    	//$('#COCexportcoc_dialog').data('title.dialog', '年份').dialog('open');
    	var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
      	window.location.href="uploadWqbfQuery!exportFueltar.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
    	//return false;	
    });
    
    $('#exportfueltardata').click(function() {
    	//$('#COCexportcoc_dialog').data('title.dialog', '年份').dialog('open');
    	var vin = $("#vin").val();
		var pmodel = $("#pmodel").val();
		var uploadType = $("#uploadType").val();
		var uploadState = $("#uploadState").val();
		var beginDate = $("#beginDate").val();
		var endDate = $("#endDate").val();
		var csbeginDate = $("#csbeginDate").val();
		var csendDate = $("#csendDate").val();
      	window.location.href="uploadWqbfQuery!exportFueltarData.action?vin="+vin+"&pmodel="+pmodel+"&uploadType="+uploadType+"&uploadState="+uploadState+"&beginDate="+beginDate+"&endDate="+endDate+"&csbeginDate="+csbeginDate+"&csendDate="+csendDate+"&menuid="+menuid;
    	//return false;	
    });
		
	$(".display").each(function(i){
		  $(this).click(function() {
			var vin=  $(this).attr("value");
			display(vin,"查看窗口");
		 });
	  });
	
    function display(vin,title){
		jQuery.ajax({
	        url: 'business/uploadWqbfQuery!uploaDisplay.action',		           
	        data: {'vin' : vin}, 
	        type: 'POST',
	        beforeSend: function() {
	        
	        },
	        error: function(request) {
	            
	        },
	        success: function(data) {
	           var content = json2Bean(data).json;
	           var carObj = eval("("+content.toString()+")");
	           var dialogObj;
	           	dialogObj = $('#uploadWqbfQuery_display_dialog');
				setDialogValue(dialogObj,carObj);
				dialogObj.data('title.dialog', title).dialog('open');
	        }
	    });

		return false;
	}
  
    function setDialogValue(dialogObj,jsonObj){
    		dialogObj.find(":text").each(function(i){
			$(this).val(jsonObj[$(this).attr("name")]);
		}) ;
	}
    
   	$("#uploadWqbfQuery_display_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		width: 1100,
		height: 350,
		modal: true,        
		buttons: {  
        	'取消': function() {
				$("#uploadWqbfQuery_display_dialog").dialog('close');
				clear($(this));
			}
		}                            
	});
   	
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'取消': function() {
				var parent = $('#typicality_neutral_dialog');
				updateTips($(parent).find('#validateTips'),'');
				$(this).dialog('close');
			},
			'确定': function() {
				var formObj = $(this).find('#operateForm');
				var dialog = $(this);
				var currentPage=$('#currentPage_temp').val();
				if(type=="delete"){					
					formObj[0].action = "business/uploadWqbfQuery!deleteTypicalityNeutral.action?currentPage="+currentPage;
					formObj[0].submit();
				}
			}
		},
		close: function() {
			var parent = $('#typicality_neutral_dialog');
			updateTips($(parent).find('#validateTips'),'');
		}
	});
    	
    $("#message_dialog").dialog({
		bgiframe: true,
		autoOpen: false,
		modal: true,
		buttons: {
		}
	});

    $("#uploadWqbfQuery_dialog").dialog({
    	bgiframe: true,
		autoOpen: false,
		width: 1100,
		height: 350,
		modal: true,  
		buttons: {
			'取消': function() {
				clear($(this));
				updateTips($(this).find('#validateTips'),'');	
				$(this).dialog('close');
			},
			'保存': function() {
				 var dlgButton = $('.ui-dialog-buttonpane button');//	
				 if(type=="update"){
					  var cocprinttime = $('#uploadWqbfQuery_dialog').find('#cocprinttime').val();
					  var rllx = $('#uploadWqbfQuery_dialog').find('#rllx').val();
					  if(cocprinttime.length > 0){
						 updateTips($('#uploadWqbfQuery_dialog').find('#validateTips'),"车型已打印不允许维护!");
						 $('#uploadWqbfQuery_dialog').find('#cocprinttime').focus();	
						 return false;
					  }
					  
					  
					  if(rllx != "2"){
						  updateTips($('#uploadWqbfQuery_dialog').find('#validateTips'),"只允许维护双燃料类型的车!");
						  $('#uploadWqbfQuery_dialog').find('#rllx').focus();		
						  return false;
					  }
				}
				  
				if(validate('#uploadWqbfQuery_dialog')==true){
			        
			        var vin = $('#uploadWqbfQuery_dialog').find('#vin').val();
					var amaxzl  = $('#uploadWqbfQuery_dialog').find('#maxzl').val();
					var acyear = $('#uploadWqbfQuery_dialog').find('#cyear').val();
			        
					if(type=="add"){
						dlgButton.attr('disabled', 'disabled');
				        dlgButton.addClass('ui-state-disabled');
				        
						jQuery.ajax({
				            url: 'business/uploadWqbfQuery!isTypicalityNeutralExist.action',
					        type: 'POST',
					        data: {'vin' : vin,'maxzl':amaxzl,'cyear':acyear}, 
				            beforeSend: function() {
				            
				            },
				            error: function(request) {
				                
				            },
				            success: function(data) {					       									
					            var dialog = $('#uploadWqbfQuery_dialog');
					            if(json2Bean(data).json=="true1"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'VIN数据不存在不能新增！');		
								}else if(json2Bean(data).json=="true2"){
					            	var dlgButton = $('.ui-dialog-buttonpane button');
									dlgButton.attr('disabled', false);
							        dlgButton.removeClass('ui-state-disabled');
									updateTips(dialog.find('#validateTips'),'VIN数据重复不能新增！');		
								}else{
									var currentPage=$('#currentPage_temp').val();
									dialog.find('#createForm')[0].action="business/uploadWqbfQuery!addTypicalityNeutral.action?currentPage="+currentPage;
									dialog.find('#createForm')[0].submit();
								}
				            }
				        });
					}else if(type=="update"){

						  
						  dlgButton.attr('disabled', 'disabled');
					      dlgButton.addClass('ui-state-disabled');
						  var currentPage=$('#currentPage_temp').val();
						  $(this).find('#createForm')[0].action="<%=path%>/business/uploadWqbfQuery!updateTypicalityNeutral.action?currentPage="+currentPage;
						  $(this).find('#createForm')[0].submit();
					
					}
				}
			}
			
		},
		close: function() {
			updateTips($(this).find('#validateTips'),'');
			clear($(this));
			if(allFields!=null){
				allFields.val('').removeClass('ui-state-error');
			}
			type = null;
			//$('#typicality_neutral_dialog').find('#vin').attr('readonly',false);
		}
	});
    
	function clear(dialogObj){	
		dialogObj.find(":text").each(function(i){
			$(this).val("");
		}) ;
		dialogObj.find("select").each(function(i){
			$(this).val("");
		}) ;
	}

	function checkOrDecimal(num){
		//alert(/^([-+]?\d{1,length})(\.\d{1,declength})?$/);
		//var reg = new RegExp("/^([-+]?\d{1,"+length+"})(\.\d{1,"+declength+"})?$/");
		var patten = /^([-+]?\d{1,2})(\.\d{1,2})?$/;;
		return patten.test(num);
		//return num.match(reg)
	}
	
	function checkOrDecimal2(num){
		var patten = /^([-+]?\d{1,3})(\.\d{1,2})?$/;;
		return patten.test(num);
	}
	
	function checkOrDecimal3(num){
		var patten = /^([-+]?\d{1,1})(\.\d{1,2})?$/;
		return patten.test(num);
	}	
	
	function checkOrDate(num){
		var patten = /(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))0229)/;
		return patten.test(num);
	}
	
	function checkNumber(num){
		var patten = /^[0-9]*$/;
		return patten.test(num);
	}	
	
	function checkTestno(value){
		var XC = value.substr(0,2);
		var qehao = value.substr(2,4);
		var qdate = value.substr(6,8);
		var snum  = value.substr(14,4);
		if(XC != "XC"){
			return false;
		}
		
		if(checkNumber(qehao)==false){
			return false;
		}
		
		if(checkOrDate(qdate)==false){
			return false;
		}
		
		if(checkNumber(snum)==false){
			return false;
		}
		return true;
	}
	
	function validate(parent){
		//return true;
		var obj = $(parent).find('#vin');
		if(!checkLength(obj,1,17)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过17！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#rh');
		if(checkOrDecimal(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于2位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,15)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过15！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#et');
		if(checkOrDecimal2(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于3位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,24)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过24！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#ap');
		if(checkOrDecimal2(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于3位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过50！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#testNo');
		if(checkTestno(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'检测报告编号字段格式不正确！');
			obj.focus();			
			return false;
		}
		
		if(!checkLength(obj,1,24)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过24！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#testDate');
		if(checkOrDate(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因日期字段格式为YYYYMMDD格式！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,8)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过8！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#vrhc');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#vlhc');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#vrco');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}

		var obj = $(parent).find('#vlco');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#vrnox');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#vlnox');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#analyManuf');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过30！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#analyName');
		if(!checkLength(obj,1,30)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过30！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#analyModel');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过50！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#analyDate');
		if(checkOrDate(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因日期字段格式为YYYYMMDD格式！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,8)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过8！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#dynoModel');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过50！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#dynoManuf');
		if(!checkLength(obj,1,50)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过50！');
			obj.focus();			
			return false;
		}
		
		
		var obj = $(parent).find('#secvrhc');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#secvlhc');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#secvrco');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}

		var obj = $(parent).find('#secvlco');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#secvrnox');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		var obj = $(parent).find('#secvlnox');
		if(checkOrDecimal3(obj.val()) == false){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因整数位不能大于1位,小数位不能大于2位！');
			obj.focus();			
			return false;
		}
		if(!checkLength(obj,1,3)){
			obj.addClass('ui-state-error');
			updateTips($(parent).find('#validateTips'),'原因必填,并且长度不能超过3！');
			obj.focus();			
			return false;
		}
		
		
		return true;
	}

	$('#jump').bind('keyup',function(event) {  
		var obj = $('#jump');
		if(!checkRegexp(obj,/^([0-9])+$/)){
			obj.val("");
			return ;
		}
		if(event.keyCode==13){ 
			var vin = $('#vin').val();
			var pmodel = $('#pmodel').val();
			var uploadType = $('#uploadType').val();
			var uploadState = $('#uploadState').val();
			var beginDate = $('#beginDate').val();
			var endDate = $('#endDate').val();
			var csbeginDate = $("#csbeginDate").val();
			var csendDate = $("#csendDate").val();
			location.href="uploadWqbfQuery.action?currentPage="+$('#jump').val()+
			"&vin="+encodeURI(encodeURI(vin))+
			"&pmodel="+encodeURI(encodeURI(pmodel))+
			"&uploadType="+encodeURI(encodeURI(uploadType))+
			"&uploadState="+encodeURI(encodeURI(uploadState))+
			"&beginDate="+encodeURI(encodeURI(beginDate))+
			"&endDate="+encodeURI(encodeURI(endDate))+
			"&csbeginDate="+encodeURI(encodeURI(csbeginDate))+
			"&csendDate="+encodeURI(encodeURI(csendDate))+		
			"&menuid="+menuid;
   		}     
   		
    });
	
	$(".jumpPage").each(function(i){
		  $(this).click(function() {
				var vin = $('#vin').val();
				var pmodel = $('#pmodel').val();
				var uploadType = $('#uploadType').val();
				var uploadState = $('#uploadState').val();
				var beginDate = $('#beginDate').val();
				var endDate = $('#endDate').val();
				var csbeginDate = $('#csbeginDate').val();
				var csendDate = $('#csendDate').val();
				
				location.href=$(this).attr('value')+
				"&vin="+encodeURI(encodeURI(vin))+
				"&pmodel="+encodeURI(encodeURI(pmodel))+
				"&uploadType="+encodeURI(encodeURI(uploadType))+
				"&uploadState="+encodeURI(encodeURI(uploadState))+
				"&beginDate="+encodeURI(encodeURI(beginDate))+
				"&endDate="+encodeURI(encodeURI(endDate))+
				"&csbeginDate="+encodeURI(encodeURI(csbeginDate))+
				"&csendDate="+encodeURI(encodeURI(csendDate))+
				"&menuid="+menuid;
		 });
	  });
	  
	
	$.datepicker.setDefaults($.extend({showMonthAfterYear: false,showButtonPanel: true,showOn: 'button', buttonImage: 'images/datePicker.gif',
  	   		buttonImageOnly: true}, $.datepicker.regional['zh']));
	$('#beginDate').datepicker($.datepicker.regional['zh']); 
	$('#endDate').datepicker($.datepicker.regional['zh']); 
	$('#csbeginDate').datepicker($.datepicker.regional['zh']); 
	$('#csendDate').datepicker($.datepicker.regional['zh']); 
});
$(document).ready(function() { 
	
	
	
	 $("#users").find("tr").mouseover(function(){  


		 $(this).addClass("over");}).mouseout(function(){ 

        //给这行添加class值为over，并且当鼠标一出该行时执行函数

        $(this).removeClass("over");})    //移除该行的class
        
        
        
        function validate1(parent){
			//return true;
			var obj = $(parent).find('#cocyears');
			if(!checkLength(obj,1,4)||!checkRegexp(obj,/^(\d+)$/)){
				obj.addClass('ui-state-error');
				updateTips($("#COCexportcoc_dialog").find('#validateTips'),'请输入年份，最大长度为4且必须是整数！');	
				obj.focus();			
				return false;
			}

			return true;
		}
	 
        $("#COCexportcoc_dialog").dialog({
    		bgiframe: true,
    		autoOpen: false,
    		width: 320,
    		height: 200,  
    		modal: true,
    		buttons: {
    			'取消': function() {
    				//clear($(this));
    				updateTips($(this).find('#validateTips'),'');	
    				$(this).dialog('close');
    			},
    		'导出': function() {
    				var cocyears = $('#cocyears').val();
    				if(validate1('#COCexportcoc_dialog')==true){
	    				window.location.href="uploadWqbfQuery!exportFueltar.action?cocyears="+cocyears;
    				}
    				$(this).dialog('close');
    		}
    			},
    		close: function() {
    			updateTips($(this).find('#validateTips'),'');
    			//clear($(this));
    			//if(allFields!=null){
    				//allFields.val('').removeClass('ui-state-error');
    			//}
    				
    			type = null;
    		}
    		
    	});
	 
    

});




</script>
<title>东风乘用车公司随车文件管理系统</title>
</head>
<body>
<div align="center">
<!--form id="qfrm" name="qfrm"-->
 <table width="100%">
  <tr>
  <td width="100%">
  <table width="100%" id="select_condtion">
			<tr>
				<td align="left" colspan="">VIN:<input type="text" id="pvin" name="pvin" class="text ui-widget-content ui-corner-all" <s:if test="#request.pvin!=null"> value="<s:property value="#request.pvin" />"</s:if> size="18" /> 
				排放检测日期：<input type="text" id="beginDate" name="beginDate" class="text ui-widget-content ui-corner-all"  size="14" <s:if test="#request.beginDate!=null"> value="<s:property value="#request.beginDate" />"</s:if>/>  -  <input type="text" id="endDate" name="endDate" class="text ui-widget-content ui-corner-all"  size="14" <s:if test="#request.endDate!=null"> value="<s:property value="#request.endDate" />"</s:if>/>
				</td>
				<td colspan="3"></td>
				<td colspan="3" width="60" align="right"><button id="query" class="ui-button ui-state-default ui-corner-all"/>查询</button></td>
			</tr>
			<tr><td width="80%"></td>
			  <td colspan="3"></td>
			  <td width="60" align="right"><button id="create" class="ui-button ui-state-default ui-corner-all">新增</button></td>
			  <td width="60" align="right"><button id="update" class="ui-button ui-state-default ui-corner-all">修改</button></td>
			  <td width="60" align="right"><button id="delete" class="ui-button ui-state-default ui-corner-all">删除</button></td>
			</tr>
  </table>
  </td>
  </tr>
  <tr >
  <td>
  <div id="users-contain" class="ui-widget">
		
	<table id="users" class="ui-widget ui-widget-content">
		<thead>
			<tr class="ui-widget-header ">
                <th width="4%">选择</th> 
			    <th width="6%">vin</th>
			    <th width="6%">相对湿度 </th>
			    <th width="6%">环境温度 </th>
			    <th width="6%">大气压力 </th>
			    <th width="6%">排气检测方法 </th>  
			    <th width="6%">检测日期</th>
			    <th width="5%">操作</th>
			  </tr>
		</thead>
		<tbody>
			<s:iterator id="item" value="#request.uploadWqbfQueryPageData" status="obj"  > 
				<s:if test="#obj.Even">
					<tr style="background:#f1f9f3">
				</s:if><s:else>
					<tr style="background:#fef7ea">
				</s:else>
			  		<td><input type='checkbox' id='checkPK' name='checkPK' value='<s:property value="vin" />' ></td>
			  		<td><s:property value="vin" /></td>
			  		<td><s:property value="rh" /></td>
			  		<td><s:property value="et" /></td>
			  		<td><s:property value="ap" /></td>
			  		<td>
			  		<s:if test='testtype=="0"'>
			  			免检
			  		</s:if><s:elseif test='testtype=="3"'>
			  			抽检
			  		</s:elseif>	
			  		<td><s:property value="testDate" /></td>  				  		
					</td>
	  			  	<td><a class='display' onclick="return false;" href='#' value='<s:property value="vin" />'>查看</a></td>
			  		
				</tr>
        	</s:iterator>
		</tbody>
	</table>
   </div>
   </td>
   </tr>
   <tr>
   <td>
   <div>
   <table width="100%" >
			<tr>
			  	<td width="8%" align="center" ><p>
			  		<s:if test="#request.page.currentPage==1">
			  			<a>第一页</a>
			  		</s:if>
			  		<s:else >
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadWqbfQuery.action?currentPage=1">第一页</a>
			  		</s:else>
			  	</td>
				<td width="8%"><p>
					<s:if test="#request.page.currentPage==#request.page.previousPage">
			  			<a>上一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadWqbfQuery.action?currentPage=<s:property value="#request.page.previousPage" />">上一页</a>
			  		</s:else>
				</td>
			  	<td width="8%" align="center"><p>
			  		<s:if test="#request.page.currentPage>=#request.page.maxPage">
			  			<a>下一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#'  value="uploadWqbfQuery.action?currentPage=<s:property value="#request.page.nextPagee" />">下一页</a>
			  		</s:else>
			  	</td>
			  	<td width="10%" align="center"><p>
			  		<s:if test="#request.page.currentPage==#request.page.maxPage">
			  			<a>最后一页</a>
			  		</s:if>
			  		<s:else>
			  			<a class='jumpPage' onclick="return false;" href='#' value="uploadWqbfQuery.action?currentPage=<s:property value="#request.page.maxPage" />">最后一页</a>
			  		</s:else>
			  	</td>
			  	<td width="35%" align="center">
			  	<input type="hidden" id="currentPage_temp" name="currentPage_temp" value="<s:property value="#request.page.currentPage" />"/>
			  	<p>当前页数 <s:property value="#request.page.currentPage" />/总页数 <s:property value="#request.page.maxPage" /> 总记录数 <s:property value="#request.page.pageSum" />条</td>
			  	<td width="10%" align="right"><p>跳转:<input type="text" id="jump" class="text ui-widget-content ui-corner-all" size="1" style="padding: .1em"/></td>
			</tr>
   </table>
   </div>
   </td>
   </tr>
</table>
<!--/form-->
</div>

<div id="uploadWqbfQuery_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm" method="post" > 
 	<table id = "tuest" width="100%">
	    	<tr>
				<td align="left"><label><P>VIN:</label></td>
				<td><input type="text" id="vin" name="vin"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="17" />
				</td>
				<td align="left"><label><P>检测报告编号</label></td>
				<td><input type="text" id="testNo" name="testNo"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="18" /></td>
				<td align="left"><label><P>排放检测日期</label></td>
				<td><input type="text" id="testDate" name="testDate"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="8" /></td>
	    	</tr>

	    	
	    	<tr>
				<td align="left"><label><P>相对湿度（单位%）</label></td>
				<td><input type="text" id="rh" name="rh"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="15" /></td>
				<td align="left"><label><P>环境温度（单位°C）</label></td>
				<td><input type="text" id="et" name="et"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="24" /></td>
				<td align="left"><label><P>大气压力（单位kPa）</label></td>
				<td><input type="text" id="ap" name="ap"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="50" /></td>
	    	</tr>	
	    	
	    	
	    	<tr>
				<td align="left"><label><P>简易瞬态HC结果</label></td>
				<td><input type="text" id="vrhc" name="vrhc"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
				<td align="left"><label><P>简易瞬态HC限值</label></td>
				<td><input type="text" id="vlhc" name="vlhc"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    		<td align="left"><label><P>简易瞬态CO结果</label></td>
				<td><input type="text" id="vrco" name="vrco"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    	
	    	</tr>    
	    	
	    	<tr>
				<td align="left"><label><P>简易瞬态CO限值</label></td>
				<td><input type="text" id="vlco" name="vlco"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
				<td align="left"><label><P>简易瞬态NOx结果</label></td>
				<td><input type="text" id="vrnox" name="vrnox"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    		<td align="left"><label><P>简易瞬态NOx限值</label></td>
				<td><input type="text" id="vlnox" name="vlnox"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" ></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>尾气分析仪制造厂</label></td>
				<td><input type="text" id="analyManuf" name="analyManuf"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="30" /></td>
				<td align="left"><label><P>尾气分析仪名称</label></td>
				<td><input type="text" id="analyName" name="analyName"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="30" /></td>
	    		<td align="left"><label><P>底盘测功机生产厂</label></td>
				<td><input type="text" id="dynoManuf" name="dynoManuf"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="50" /></td>
	    		
	    	</tr>  
	    	
	    	<tr>
				<td align="left"><label><P>尾气分析仪型号</label></td>
				<td><input type="text" id="analyModel" name="analyModel"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="50" /></td>
				<td align="left"><label><P>尾气分析仪检定日期</label></td>
				<td><input type="text" id="analyDate" name="analyDate"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="8" /></td>
				<td align="left"><label><P>底盘测功机型号</label></td>
				<td><input type="text" id="dynoModel" name="dynoModel"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="50" /></td>
	    	</tr>   
	    	
	    	<tr>
				<td align="left"><label><P>第二种简易瞬态HC结果</label></td>
				<td><input type="text" id="secvrhc" name="secvrhc"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
				<td align="left"><label><P>第二种简易瞬态HC限值</label></td>
				<td><input type="text" id="secvlhc" name="secvlhc"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    		<td align="left"><label><P>第二种简易瞬态CO结果</label></td>
				<td><input type="text" id="secvrco" name="secvrco"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    	</tr>    
	    	
	    	<tr>
				<td align="left"><label><P>第二种简易瞬态CO限值</label></td>
				<td><input type="text" id="secvlco" name="secvlco"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
				<td align="left"><label><P>第二种简易瞬态NOx结果</label></td>
				<td><input type="text" id="secvrnox" name="secvrnox"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    		<td align="left"><label><P>第二种简易瞬态NOx限值</label></td>
				<td><input type="text" id="secvlnox" name="secvlnox"  class="text ui-widget-content ui-corner-all"  size="22" maxlength="22" /></td>
	    	</tr>
	    	
	    	</div>	    				
	    	<tr>
			</tr>    				
		</Table>
		<div style="display: none;">
			<input type='text' id='cocprinttime' name='cocprinttime' />
			<input type='text' id='rllx' name='rllx'/>
		</div>
		<input type='hidden' id='ids' name='ids'/>
		<input type='hidden' id='type' name='type'/>
		<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
		</form>
	</fieldset>
</div>

<div id="operate_dialog" title="操作窗口" style="display:none">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='ids' name='ids'>
	<input type='hidden' id='menuid' name='menuid' value='<%= String.valueOf(request.getAttribute("menuid"))%>'/>
	</form>
</div>

<div id="message_dialog" title="提示窗口" style="display:none">
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
</div>

<div id="uploadWqbfQuery_display_dialog" style="display:none">
	<p id="validateTips"></p>
	<fieldset>
	<form id="createForm3" method="post" > 
	  	  <table id = "tuest" width="100%">
	    	<tr>
				<td align="left"><label><P>VIN:</label></td>
				<td><input type="text" id="vin" name="vin"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>检测报告编号</label></td>
				<td><input type="text" id="testNo" name="testNo"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="18" readonly="readonly"/></td>
				<td align="left"><label><P>排放检测日期</label></td>
				<td><input type="text" id="testDate" name="testDate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="8" readonly="readonly"/></td>
	    	</tr>

	    	
	    	<tr>
				<td align="left"><label><P>相对湿度（单位%）</label></td>
				<td><input type="text" id="rh" name="rh"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>环境温度（单位°C）</label></td>
				<td><input type="text" id="et" name="et"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>大气压力（单位kPa）</label></td>
				<td><input type="text" id="ap" name="ap"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>	
	    	
	    	
	    	<tr>
				<td align="left"><label><P>简易瞬态HC结果</label></td>
				<td><input type="text" id="vrhc" name="vrhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>简易瞬态HC限值</label></td>
				<td><input type="text" id="vlhc" name="vlhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    		<td align="left"><label><P>简易瞬态CO结果</label></td>
				<td><input type="text" id="vrco" name="vrco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	
	    	</tr>    
	    	
	    	<tr>
				<td align="left"><label><P>简易瞬态CO限值</label></td>
				<td><input type="text" id="vlco" name="vlco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>简易瞬态NOx结果</label></td>
				<td><input type="text" id="vrnox" name="vrnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    		<td align="left"><label><P>简易瞬态NOx限值</label></td>
				<td><input type="text" id="vlnox" name="vlnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	<tr>
				<td align="left"><label><P>尾气分析仪制造厂</label></td>
				<td><input type="text" id="analyManuf" name="analyManuf"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>尾气分析仪名称</label></td>
				<td><input type="text" id="analyName" name="analyName"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    		<td align="left"><label><P>底盘测功机生产厂</label></td>
				<td><input type="text" id="dynoManuf" name="dynoManuf"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    		
	    	</tr>  
	    	
	    	<tr>
				<td align="left"><label><P>尾气分析仪型号</label></td>
				<td><input type="text" id="analyModel" name="analyModel"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>尾气分析仪检定日期</label></td>
				<td><input type="text" id="analyDate" name="analyDate"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>底盘测功机型号</label></td>
				<td><input type="text" id="dynoModel" name="dynoModel"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>   
	    	
	    	<tr>
				<td align="left"><label><P>第二种简易瞬态HC结果</label></td>
				<td><input type="text" id="secvrhc" name="secvrhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>第二种简易瞬态HC限值</label></td>
				<td><input type="text" id="secvlhc" name="secvlhc"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    		<td align="left"><label><P>第二种简易瞬态CO结果</label></td>
				<td><input type="text" id="secvrco" name="secvrco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>    
	    	
	    	<tr>
				<td align="left"><label><P>第二种简易瞬态CO限值</label></td>
				<td><input type="text" id="secvlco" name="secvlco"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
				<td align="left"><label><P>第二种简易瞬态NOx结果</label></td>
				<td><input type="text" id="secvrnox" name="secvrnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    		<td align="left"><label><P>第二种简易瞬态NOx限值</label></td>
				<td><input type="text" id="secvlnox" name="secvlnox"  class="text ui-widget-content ui-corner-all"  size="20" maxlength="20" readonly="readonly"/></td>
	    	</tr>
	    	
	    	</div>	    				
	    	<tr>
			</tr>    				
		</Table>
		</form>
	</fieldset>
</div>


</body>
</html>