package com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.impl;

import java.util.List;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Cartypetemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CartypetemplateId;
import com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.Fuellabeltemplate;
import com.dawnpro.dfpv.carfilemanager.module.business.verManager.dao.CocVerDAO;

public class CocVerDAOImpl extends GenericHibernateDAOImpl<Cartypetemplate> 
	implements CocVerDAO {

	public void addCocVer(Cartypetemplate obj) {
		try{
			this.add(obj);
		}catch(Exception e){
			throw new DataAccessException("addCocVer Method Error:",e);
		}
	}

	public void deleteCocVer(Cartypetemplate[] id) {
		try{
			this.deleteBatch(id);
		}catch(Exception e){
			throw new DataAccessException("deleteCocVer Method Error:",e);
		}
	}

	public Cartypetemplate loadCocVer(CartypetemplateId id) {
		Cartypetemplate obj = null;
		try{
			obj = (Cartypetemplate)this.load(Cartypetemplate.class,id);
			if(obj != null)
			{
				obj.getC2();
			}
		}catch(Exception e){
			throw new DataAccessException("loadCocVer Method Error:",e);
		}
		return obj;
	}

	public void updateCocVer(Cartypetemplate obj) {
		try{
			this.update(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCocVer Method Error:",e);
		}
	}

	public Cartypetemplate findMaxCocVerBySccx(String c1) {
		Cartypetemplate obj = null;
		try{
			List<Cartypetemplate> results = null;
			String sql = "from Cartypetemplate t where t.id.c1=? order by substr(t.id.vercode,length(t.id.vercode)-1,2) desc";
			Object[] params = new Object[]{c1};
			results = this.find(sql,params);
			if(results != null && results.size()>0)
				obj = results.get(0);
		}catch(Exception e){
			throw new DataAccessException("findMaxCocVerBySccx Method Error:",e);
		}
		return obj;
	}

	public List<Cartypetemplate> findCocVer(String hql) {
		List<Cartypetemplate> list = null;
		try{
			list = this.find(hql);
		}catch(Exception e){
			throw new DataAccessException("findCocVer Method Error:",e);
		}
		return list;
	}

	public void addCocVer(Cartypetemplate[] obj) {
		if(obj == null ) return;
		try{
			this.addBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("addCocVer Method Error:",e);
		}
	}

	public void updateCocVer(Cartypetemplate[] obj) {
		if(obj == null ) return;
		try{
			this.updateBatch(obj);
		}catch(Exception e){
			throw new DataAccessException("updateCocVer Method Error:",e);
		}
	}

}
