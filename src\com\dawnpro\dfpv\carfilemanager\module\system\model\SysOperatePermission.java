package com.dawnpro.dfpv.carfilemanager.module.system.model;

/**
 * SysOperatePermission entity.
 * 
 * <AUTHOR> Persistence Tools
 */

public class SysOperatePermission implements java.io.Serializable {

	// Fields

	private SysOperatePermissionId id;
	private String creator;
	private String time;

	// Constructors

	/** default constructor */
	public SysOperatePermission() {
	}

	/** minimal constructor */
	public SysOperatePermission(SysOperatePermissionId id) {
		this.id = id;
	}

	/** full constructor */
	public SysOperatePermission(SysOperatePermissionId id, String creator,
			String time) {
		this.id = id;
		this.creator = creator;
		this.time = time;
	}

	// Property accessors

	public SysOperatePermissionId getId() {
		return this.id;
	}

	public void setId(SysOperatePermissionId id) {
		this.id = id;
	}

	public String getCreator() {
		return this.creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getTime() {
		return this.time;
	}

	public void setTime(String time) {
		this.time = time;
	}

}