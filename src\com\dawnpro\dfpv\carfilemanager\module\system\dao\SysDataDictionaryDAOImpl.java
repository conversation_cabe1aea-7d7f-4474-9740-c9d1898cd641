package com.dawnpro.dfpv.carfilemanager.module.system.dao;

import java.util.List;

import org.hibernate.HibernateException;
import org.springframework.dao.DataAccessResourceFailureException;

import com.dawnpro.dfpv.carfilemanager.base.dao.GenericHibernateDAOImpl;
import com.dawnpro.dfpv.carfilemanager.base.exception.system.DataAccessException;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionary;
import com.dawnpro.dfpv.carfilemanager.module.system.model.DataDictionaryId;
import com.dawnpro.dfpv.carfilemanager.module.system.model.SysUser;

/**
 *
 * @author:
 * @since: 2010-4-1
 * **/
public class SysDataDictionaryDAOImpl extends GenericHibernateDAOImpl<DataDictionary> implements  SysDataDictionaryDAO {

	public List<DataDictionary> findDataDictionary(String sql) {
		try{
			return this.find(sql);
		}catch(Exception e){
			throw new DataAccessException("findDataDictionary Method Error:",e);
		}
	}

	public void addDataDictionary(DataDictionary dataDictionary)
			throws DataAccessException {
		try{
			this.add(dataDictionary);
		}catch (DataAccessResourceFailureException e) {
			throw new DataAccessException("addDataDictionary Error:",e);
		} catch (HibernateException e) {
			throw new DataAccessException("addDataDictionary Error:",e);
		} catch (IllegalStateException e) {
			throw new DataAccessException("addDataDictionary Error:",e);
		} catch(DataAccessException e){
			throw new DataAccessException("addDataDictionary Error:",e);
		}
	}

	public void deleteDataDictionary(DataDictionary[] dataDictionary)
			throws DataAccessException {
		try{
			this.deleteBatch(dataDictionary);
		}catch(Exception e){
			throw new DataAccessException("deleteDataDictionary Method Error:",e);
		}
	}

	public List<DataDictionary> findDataDictionary(String sql, String[] params)
			throws DataAccessException {
		try{
			return this.find(sql, params);
		}catch(Exception e){
			throw new DataAccessException("findDataDictionary Method Error:",e);
		}
	}

	public DataDictionary loadDataDictionaryObj(DataDictionaryId id)
			throws DataAccessException {
		DataDictionary gg = null;
		try{
			List<DataDictionary> result = findDataDictionary("from DataDictionary g where g.id.name='"+id.getName()+"' and g.id.value='"+id.getValue()+"' and g.id.type='"+id.getType()+"'");
			if(result!=null && result.size()>0)
				gg=result.get(0);
		}catch(Exception e){
			throw new DataAccessException("loadDataDictionaryObj Method Error:",e);
		}
		return gg;
	}

	public void updateDataDictionary(DataDictionary dataDictionary)
			throws DataAccessException {
		try{
			this.update(dataDictionary);
		}catch(Exception e){
			throw new DataAccessException("updateDataDictionary Method Error:",e);
		}
	}

	public void deleteDataDictionary(DataDictionary dataDictionary)
			throws DataAccessException {
		try{
			this.delete(dataDictionary);
		}catch(Exception e){
			throw new DataAccessException("deleteDataDictionary Method Error:",e);
		}
	}

}
