﻿/* Danish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> ( <EMAIL>). */
jQuery(function($){
    $.datepicker.regional['da'] = {
		closeText: 'Luk',
        prevText: '&#x3c;Forrige',
		nextText: 'Næste&#x3e;',
		currentText: 'Idag',
        monthNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','April','<PERSON>','Jun<PERSON>',
        'Juli','August','September','Oktober','November','December'],
        monthNamesShort: ['Jan','Feb','Mar','Apr','Maj','Jun',
        'Jul','Aug','Sep','Okt','Nov','Dec'],
		dayNames: ['Søndag','Mandag','Tirsdag','Onsdag','Torsdag','Fred<PERSON>','Lørdag'],
		dayNamesShort: ['<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesMin: ['<PERSON>ø','Ma','Ti','On','<PERSON>','<PERSON>','<PERSON><PERSON>'],
        dateFormat: 'dd-mm-yy', firstDay: 0,
		isRTL: false};
    $.datepicker.setDefaults($.datepicker.regional['da']);
});
