<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<!-- Generated 2018-9-26 15:56:55 by Hibernate Tools 3.6.0.Final -->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceMsgLog" table="INTERFACE_MSG_LOG" schema="VFILE">
        <comment>&#189;&#211;&#191;&#218;&#177;&#168;&#206;&#196;&#180;&#166;&#192;&#237;&#200;&#213;&#214;&#190;</comment>
        <id name="id" type="string">
            <column name="ID" length="40" />
            <generator class="assigned" />
        </id>
        <property name="tradeCode" type="string">
            <column name="TRADE_CODE" length="30">
                <comment>&#189;&#211;&#191;&#218;&#177;&#224;&#186;&#197;</comment>
            </column>
        </property>
        <property name="reqSerialNo" type="string">
            <column name="REQ_SERIAL_NO" length="54">
                <comment>&#193;&#247;&#203;&#174;&#186;&#197;</comment>
            </column>
        </property>
        <property name="tradeTime" type="string">
            <column name="TRADE_TIME" length="23">
                <comment>&#210;&#181;&#206;&#241;&#180;&#166;&#192;&#237;&#202;&#177;&#188;&#228;</comment>
            </column>
        </property>
        <property name="requesterId" type="string">
            <column name="REQUESTER_ID" length="6">
                <comment>&#207;&#181;&#205;&#179;&#177;&#224;&#186;&#197;</comment>
            </column>
        </property>
        <property name="state" type="string">
            <column name="STATE" length="4">
                <comment>&#177;&#168;&#206;&#196;&#215;&#180;&#204;&#172;&#163;&#186;&#206;&#180;&#180;&#166;&#192;&#237;&#161;&#162;&#210;&#209;&#180;&#166;&#192;&#237;</comment>
            </column>
        </property>
        <property name="type" type="string">
            <column name="TYPE" length="4">
                <comment>&#192;&#224;&#208;&#205;</comment>
            </column>
        </property>
        <property name="exceTime" type="string">
            <column name="EXCE_TIME" length="19">
                <comment>&#180;&#166;&#192;&#237;&#202;&#177;&#188;&#228;</comment>
            </column>
        </property>
        <property name="createTime" type="string">
            <column name="CREATE_TIME" length="19">
                <comment>&#189;&#211;&#202;&#220;&#202;&#177;&#188;&#228;</comment>
            </column>
        </property>
        <property name="errInfo" type="string">
            <column name="ERR_INFO" length="1000">
                <comment>
                &#180;&#166;&#192;&#237;&#205;&#234;&#179;&#201;&#208;&#197;&#207;&#162;&#163;&#172;&#176;&#252;&#192;&#168;&#179;&#201;&#185;&#166;&#161;&#162;&#180;&#237;&#206;&#243;&#208;&#197;&#207;&#162;</comment>
            </column>
        </property>
        <property name="path" type="string">
            <column name="PATH" length="200">
                <comment>&#207;&#251;&#207;&#162;&#206;&#196;&#188;&#254;&#180;&#230;&#183;&#197;&#194;&#183;&#190;&#182;</comment>
            </column>
        </property>
        <property name="batchno" type="string">
            <column name="BATCHNO" length="40">
                <comment>&#197;&#250;&#180;&#206;&#186;&#197;</comment>
            </column>
        </property>
    </class>
</hibernate-mapping>
