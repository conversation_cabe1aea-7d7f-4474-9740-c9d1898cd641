<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.certificate.model.CarPublicModelIcon" table="CAR_PUBLIC_MODEL_ICON" lazy="false">
        <id name="pmodel" type="java.lang.String">
            <column name="PMODEL" length="50" />
            <generator class="assigned" />
        </id>
        <property name="printicon" type="java.lang.String">
            <column name="PRINTICON" length="10" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="2" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="createdate" type="java.util.Date">
            <column name="CREATEDATE" length="7" />
        </property>
        <property name="updatedate" type="java.util.Date">
            <column name="UPDATEDATE" length="7" />
        </property>
    </class>
</hibernate-mapping>
