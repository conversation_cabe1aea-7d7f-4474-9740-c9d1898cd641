<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModel" table="CAR_PUBLIC_MODEL" lazy="false">
        <composite-id name="id" class="com.dawnpro.dfpv.carfilemanager.module.business.cardata.model.CarPublicModelId">
            <key-property name="c1" type="java.lang.String">
                <column name="C1" length="50" />
            </key-property>
            <key-property name="vercode" type="java.lang.String">
                <column name="VERCODE" length="15" />
            </key-property>
        </composite-id>
        <property name="c2" type="java.lang.String">
            <column name="C2" length="50" />
        </property>
        <property name="c3" type="java.lang.String">
            <column name="C3" length="50" />
        </property>
        <property name="c4" type="java.lang.String">
            <column name="C4" length="50" />
        </property>
        <property name="c5" type="java.lang.String">
            <column name="C5" length="50" />
        </property>
        <property name="c6" type="java.lang.String">
            <column name="C6" length="50" />
        </property>
        <property name="c7" type="java.lang.String">
            <column name="C7" length="50" />
        </property>
        <property name="c8" type="java.lang.String">
            <column name="C8" length="50" />
        </property>
        <property name="c9" type="java.lang.String">
            <column name="C9" length="50" />
        </property>
        <property name="c10" type="java.lang.String">
            <column name="C10" length="50" />
        </property>
        <property name="c11" type="java.lang.String">
            <column name="C11" length="50" />
        </property>
        <property name="c12" type="java.lang.String">
            <column name="C12" length="50" />
        </property>
        <property name="c13" type="java.lang.String">
            <column name="C13" length="50" />
        </property>
        <property name="c14" type="java.lang.String">
            <column name="C14" length="50" />
        </property>
        <property name="c15" type="java.lang.String">
            <column name="C15" length="50" />
        </property>
        <property name="c16" type="java.lang.String">
            <column name="C16" length="50" />
        </property>
        <property name="c17" type="java.lang.String">
            <column name="C17" length="50" />
        </property>
        <property name="c18" type="java.lang.String">
            <column name="C18" length="50" />
        </property>
        <property name="c19" type="java.lang.String">
            <column name="C19" length="50" />
        </property>
        <property name="c20" type="java.lang.String">
            <column name="C20" length="50" />
        </property>
        <property name="c21" type="java.lang.String">
            <column name="C21" length="50" />
        </property>
        <property name="c22" type="java.lang.String">
            <column name="C22" length="50" />
        </property>
        <property name="c23" type="java.lang.String">
            <column name="C23" length="50" />
        </property>
        <property name="c24" type="java.lang.String">
            <column name="C24" length="50" />
        </property>
        <property name="c25" type="java.lang.String">
            <column name="C25" length="50" />
        </property>
        <property name="c26" type="java.lang.String">
            <column name="C26" length="50" />
        </property>
        <property name="c27" type="java.lang.String">
            <column name="C27" length="50" />
        </property>
        <property name="c28" type="java.lang.String">
            <column name="C28" length="200" />
        </property>
        <property name="c29" type="java.lang.String">
            <column name="C29" length="200" />
        </property>
        <property name="c30" type="java.lang.String">
            <column name="C30" length="200" />
        </property>
        <property name="c31" type="java.lang.String">
            <column name="C31" length="50" />
        </property>
        <property name="c32" type="java.lang.String">
            <column name="C32" length="50" />
        </property>
        <property name="c33" type="java.lang.String">
            <column name="C33" length="50" />
        </property>
        <property name="c34" type="java.lang.String">
            <column name="C34" length="50" />
        </property>
        <property name="c35" type="java.lang.String">
            <column name="C35" length="50" />
        </property>
        <property name="c36" type="java.lang.String">
            <column name="C36" length="50" />
        </property>
        <property name="c37" type="java.lang.String">
            <column name="C37" length="50" />
        </property>
        <property name="c38" type="java.lang.String">
            <column name="C38" length="50" />
        </property>
        <property name="c39" type="java.lang.String">
            <column name="C39" length="50" />
        </property>
        <property name="c40" type="java.lang.String">
            <column name="C40" length="50" />
        </property>
        <property name="c41" type="java.lang.String">
            <column name="C41" length="50" />
        </property>
        <property name="c42" type="java.lang.String">
            <column name="C42" length="50" />
        </property>
        <property name="c43" type="java.lang.String">
            <column name="C43" length="50" />
        </property>
        <property name="c44" type="java.lang.String">
            <column name="C44" length="50" />
        </property>
        <property name="c45" type="java.lang.String">
            <column name="C45" length="50" />
        </property>
        <property name="c46" type="java.lang.String">
            <column name="C46" length="50" />
        </property>
        <property name="c47" type="java.lang.String">
            <column name="C47" length="50" />
        </property>
        <property name="c48" type="java.lang.String">
            <column name="C48" length="50" />
        </property>
        <property name="c49" type="java.lang.String">
            <column name="C49" length="50" />
        </property>
        <property name="c50" type="java.lang.String">
            <column name="C50" length="50" />
        </property>
        <property name="c51" type="java.lang.String">
            <column name="C51" length="50" />
        </property>
        <property name="c52" type="java.lang.String">
            <column name="C52" length="50" />
        </property>
        <property name="c53" type="java.lang.String">
            <column name="C53" length="50" />
        </property>
        <property name="c54" type="java.lang.String">
            <column name="C54" length="50" />
        </property>
        <property name="c55" type="java.lang.String">
            <column name="C55" length="50" />
        </property>
        <property name="c56" type="java.lang.String">
            <column name="C56" length="50" />
        </property>
        <property name="c57" type="java.lang.String">
            <column name="C57" length="50" />
        </property>
        <property name="c58" type="java.lang.String">
            <column name="C58" length="50" />
        </property>
        <property name="c59" type="java.lang.String">
            <column name="C59" length="50" />
        </property>
        <property name="c60" type="java.lang.String">
            <column name="C60" length="50" />
        </property>
        <property name="c61" type="java.lang.String">
            <column name="C61" length="50" />
        </property>
        <property name="c62" type="java.lang.String">
            <column name="C62" length="50" />
        </property>
        <property name="c63" type="java.lang.String">
            <column name="C63" length="50" />
        </property>
        <property name="c64" type="java.lang.String">
            <column name="C64" length="50" />
        </property>
        <property name="c65" type="java.lang.String">
            <column name="C65" length="50" />
        </property>
        <property name="c66" type="java.lang.String">
            <column name="C66" length="50" />
        </property>
        <property name="c67" type="java.lang.String">
            <column name="C67" length="50" />
        </property>
        <property name="c68" type="java.lang.String">
            <column name="C68" length="500" />
        </property>
        <property name="c69" type="java.lang.String">
            <column name="C69" length="50" />
        </property>
        <property name="c70" type="java.lang.String">
            <column name="C70" length="50" />
        </property>
        <property name="c71" type="java.lang.String">
            <column name="C71" length="50" />
        </property>
        <property name="c72" type="java.lang.String">
            <column name="C72" length="50" />
        </property>
        <property name="c73" type="java.lang.String">
            <column name="C73" length="50" />
        </property>
        <property name="c74" type="java.lang.String">
            <column name="C74" length="50" />
        </property>
        <property name="c75" type="java.lang.String">
            <column name="C75" length="50" />
        </property>
        <property name="c76" type="java.lang.String">
            <column name="C76" length="50" />
        </property>
        <property name="c77" type="java.lang.String">
            <column name="C77" length="50" />
        </property>
        <property name="c78" type="java.lang.String">
            <column name="C78" length="50" />
        </property>
        <property name="c79" type="java.lang.String">
            <column name="C79" length="50" />
        </property>
        <property name="c80" type="java.lang.String">
            <column name="C80" length="50" />
        </property>
        <property name="c81" type="java.lang.String">
            <column name="C81" length="50" />
        </property>
        <property name="c82" type="java.lang.String">
            <column name="C82" length="50" />
        </property>
        <property name="c83" type="java.lang.String">
            <column name="C83" length="50" />
        </property>
        <property name="c84" type="java.lang.String">
            <column name="C84" length="50" />
        </property>
        <property name="c85" type="java.lang.String">
            <column name="C85" length="50" />
        </property>
        <property name="c86" type="java.lang.String">
            <column name="C86" length="50" />
        </property>
        <property name="c87" type="java.lang.String">
            <column name="C87" length="50" />
        </property>
        <property name="c88" type="java.lang.String">
            <column name="C88" length="50" />
        </property>
        <property name="c89" type="java.lang.String">
            <column name="C89" length="50" />
        </property>
        <property name="c90" type="java.lang.String">
            <column name="C90" length="50" />
        </property>
        <property name="c91" type="java.lang.String">
            <column name="C91" length="50" />
        </property>
        <property name="c92" type="java.lang.String">
            <column name="C92" length="50" />
        </property>
        <property name="c93" type="java.lang.String">
            <column name="C93" length="50" />
        </property>
        <property name="c94" type="java.lang.String">
            <column name="C94" length="50" />
        </property>
        <property name="c95" type="java.lang.String">
            <column name="C95" length="50" />
        </property>
        <property name="c96" type="java.lang.String">
            <column name="C96" length="50" />
        </property>
        <property name="c97" type="java.lang.String">
            <column name="C97" length="50" />
        </property>
        <property name="c98" type="java.lang.String">
            <column name="C98" length="50" />
        </property>
        <property name="c99" type="java.lang.String">
            <column name="C99" length="50" />
        </property>
        <property name="c100" type="java.lang.String">
            <column name="C100" length="50" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="2" />
        </property>
        <property name="creator" type="java.lang.String">
            <column name="CREATOR" length="20" />
        </property>
        <property name="time" type="java.lang.String">
            <column name="TIME" length="19" />
        </property>
        <property name="flag" type="java.lang.String">
            <column name="flag" length="4" />
        </property>
        <property name="zdjgl" type="java.lang.String">
            <column name="ZDJGL" length="50" />
        </property>
        <property name="mpflag" type="java.lang.String">
            <column name="mpflag" length="4" />
        </property>
        <property name="c101" type="java.lang.String">
            <column name="c101" length="50" />
        </property>
        <property name="activetime" type="java.lang.String">
            <column name="ACTIVETIME" length="19" />
        </property>
        <property name="publishtime" type="java.lang.String">
            <column name="PUBLISHTIME" length="19" />
        </property>
    </class>
</hibernate-mapping>
