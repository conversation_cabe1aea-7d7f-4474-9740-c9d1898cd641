<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<%
	String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + path + "/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>"/>
<link type="text/css" href="js/jquery/themes/base/ui.all.css" rel="stylesheet" />
<style type="text/css">
body {font-family:"宋体";font-size: 10pt; background-color: #f6f6f6;}
         div#users-contain {  width: 100%; margin: 0px; }
		div#users-contain table { margin: 0px; border-collapse: collapse; width: 100%; }
		div#users-contain table td, div#users-contain table th { border: 1px solid #eee; padding: .6em 10px; text-align: center; font-size: 10pt; }
		div#users-contain table td, div#users-contain table tr { border: 1px solid #eee; padding: .3em 10px; text-align: center; font-size: 10pt; }
		input.text { width:12; padding: .2em; }
		.ui-button { outline: 0; margin:0; padding: .2em 0.5em .3em; text-decoration:none;  !important; cursor:pointer; position: relative; text-align:
	center; }
	p {font-family:"宋体";font-size: 10pt;}
	a {TEXT-DECORATION:none; color:black}
	a:hover{color:red}
	fieldset { padding:0; border:0; margin-top:25px; }
</style>
<script type="text/javascript" src="js/jquery/jquery-1.3.2.min.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.dialog.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.core.js"></script>
<script type="text/javascript" src="js/jquery/ui/effects.highlight.js"></script>
<script type="text/javascript" src="js/jquery/external/bgiframe/jquery.bgiframe.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/swfobject.js"></script>
<script type="text/javascript" src="js/jquery/uploadify/jquery.uploadify.v2.1.0.js"></script>
<script type="text/javascript" src="js/jquery/ui/ui.datepicker.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/jquery-ui-i18n.js"></script>
<script type="text/javascript" src="js/jquery/ui/i18n/ui.datepicker-zh-CN.js"></script>
<script type="text/javascript" src="js/common.js"></script>
<script type="text/javascript">
$(function(){
	$("#ok").click(function() {
		var password = $('#password').val();
		var password1 = $('#password1').val();
		var password2 = $('#password2').val();
		var parent = $('#public_pw_dialog');
		if(password1==""){
			updateTips($(parent).find('#validateTips'),'原密码不能为空!');
		}else if(password==""){
			updateTips($(parent).find('#validateTips'),'新密码不能为空!');
		}else if(password2==""){
			updateTips($(parent).find('#validateTips'),'密码确认不能为空!');
		}else if(password2!=password){
			updateTips($(parent).find('#validateTips'),'密码确认和新密码不一致!');
		}else{
			jQuery.ajax({
				url: 'system/sysPasswod!rewritePassword.action', 	           
				data:{'password':password,'ypassword':password1}, 
		        type: 'POST',
		        dataType:'json', 
	            beforeSend: function() {
   				
	            },
	            error: function(request) {
	            	
	            },
	            success: function(data) {
	            	var content = data.json;
	            	if(content=="true"){
			   	   		updateTips($(parent).find('#validateTips'),'修改成功!');
	            	}else{
			   	   		updateTips($(parent).find('#validateTips'),'修改失败!'+content);
	            	}	
	            }
	        });
		}
		
	});
	
	$("#operate_dialog").dialog({
		bgiframe: true,
		resizable: false,
		autoOpen: false,
		width: 400,
		height:200,
		modal: true,
		overlay: {
			backgroundColor: '#000',
			opacity: 0.5
		},
		buttons: {
			'确定': function() {
				$(this).dialog('close');
			}
		}
	});
	
});

</script>
<title></title>
</head>
<body>
<div id="public_pw_dialog">
	<p id="validateTips" align="center"></p>
	<fieldset>
		<form id="createForm" method="post" >
			<table width="380" border="0" align="center" cellpadding="0" cellspacing="0">
			  	<tr>
					<td align="right"><label>原 密 码：</label></td>
					<td><input type="password" id="password1" name="password1" class="text ui-widget-content ui-corner-all" size="18" /></td>
				</tr>
				<tr>
					<td align="right"><label>新 密 码：</label></td>
					<td><input type="password" id="password" name="password" class="text ui-widget-content ui-corner-all" size="18" /></td>
				</tr>
				<tr>
					<td align="right"><label>密码确认：</label></td>
					<td><input type="password" id="password2" name="password2" class="text ui-widget-content ui-corner-all" size="18" /></td>
				</tr>
			  	<tr><td colspan="2"><div align="center"><button id="ok" class="ui-button ui-state-default ui-corner-all">确定</button></div></td></tr>
			</table>
		</form>
	</fieldset>
</div>
<div id="operate_dialog" title="操作窗口">
	<form id="operateForm" method='post'>
	<p id="message"><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span></p>
	<input type='hidden' id='password' name='password'>
	</form>
</div>
</body>
</html>