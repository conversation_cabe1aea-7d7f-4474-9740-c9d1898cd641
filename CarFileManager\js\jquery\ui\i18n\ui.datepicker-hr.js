﻿/* Croatian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON>. */
jQuery(function($){
	$.datepicker.regional['hr'] = {
		closeText: '<PERSON>at<PERSON><PERSON>',
		prevText: '&#x3c;',
		nextText: '&#x3e;',
		currentText: '<PERSON><PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Listop<PERSON>','Studeni','Prosinac'],
		monthNamesShort: ['Sij','Velj','<PERSON><PERSON><PERSON>','<PERSON>ra','<PERSON>vi','Lip',
		'Srp','Kol','Ruj','Lis','Stu','Pro'],
		dayNames: ['<PERSON><PERSON><PERSON>ja','<PERSON>ned<PERSON>ljak','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON>n','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON><PERSON>','<PERSON>','Ut','Sr','Če','Pe','Su'],
		dateFormat: 'dd.mm.yy.', firstDay: 1,
		isRTL: false};
	$.datepicker.setDefaults($.datepicker.regional['hr']);
});