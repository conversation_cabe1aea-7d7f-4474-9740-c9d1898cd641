<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">
<!-- 
    Mapping file autogenerated by MyEclipse Persistence Tools
-->
<hibernate-mapping>
    <class name="com.dawnpro.dfpv.carfilemanager.module.business.share.model.InterfaceMqLog" table="INTERFACE_MQ_LOG" lazy="false">
        <id name="id" type="java.lang.String">
            <column name="ID" precision="20" scale="0" />
            <generator class="sequence">
              <param name="sequence">S_INTERFACE_MQ_LOG</param>
         	</generator>
        </id>
        <property name="type" type="java.lang.String">
            <column name="MODEL" length="20" />
        </property>
        <property name="mqname" type="java.lang.String">
            <column name="MQNAME" length="20" />
        </property>
        <property name="beginlen" type="java.lang.String">
            <column name="BEGINLEN" length="20" />
        </property>
        <property name="endlen" type="java.lang.String">
            <column name="ENDLEN" length="20" />
        </property>
        <property name="allnum" type="java.lang.String">
            <column name="ALLNUM" length="20" />
        </property>
        <property name="successnum" type="java.lang.String">
            <column name="SUCCESSNUM" length="20" />
        </property>
        <property name="failnum" type="java.lang.String">
            <column name="FAILNUM" length="20" />
        </property>
        <property name="state" type="java.lang.String">
            <column name="STATE" length="20" />
        </property>
        <property name="createtime" type="date">
            <column name="CREATETIME" length="7" />
        </property>
        
    </class>
</hibernate-mapping>
